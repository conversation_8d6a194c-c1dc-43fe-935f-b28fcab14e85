classDiagram
    class UDPResource {
        -Long id
        -String resourceName
        -boolean isFullNetwork
        -List~IPRange~ ipRanges
        -LocalDateTime createdAt
        -LocalDateTime updatedAt
        +validateIpRanges()
        +addIpRange(IPRange)
        +removeIpRange(IPRange)
        +updateResourceName(String)
    }

    class IPRange {
        -Long id
        -String startIp
        -String endIp
        -UDPResource resource
        +boolean isValid()
        +boolean overlaps(IPRange)
        +String getStartIp()
        +String getEndIp()
        +void setStartIp(String)
        +void setEndIp(String)
    }

    class ProxyService {
        -Long id
        -String serviceName
        -UDPResource resource
        -String transmissionIp
        -int transmissionPort
        -boolean antiFloodEnabled
        -long trafficLimit
        -ServiceStatus status
        -ConfigurationStatus configStatus
        -LocalDateTime createdAt
        -LocalDateTime updatedAt
        +deploy()
        +start()
        +stop()
        +updateStatus()
        +updateConfiguration()
    }

    class ServiceDeployer {
        -String deployPath
        -ConfigurationManager configManager
        +DeployStatus deploy(ProxyService)
        +void undeploy(ProxyService)
        +ServiceStatus checkStatus(ProxyService)
        -validateDeployment(ProxyService)
        -prepareDeployment(ProxyService)
    }

    class ConfigurationManager {
        -String configPath
        -ConfigValidator validator
        +void saveConfig(ProxyService)
        +Configuration loadConfig(Long)
        +boolean validateConfig(Configuration)
        +void backupConfig(Long)
        -String generateConfigFile(ProxyService)
    }

    class UDPResourceRepository {
        +UDPResource findByName(String)
        +List~UDPResource~ findByIsFullNetwork(boolean)
        +Page~UDPResource~ searchByNameLike(String)
        +boolean existsByName(String)
    }

    class ProxyServiceRepository {
        +List~ProxyService~ findByStatus(ServiceStatus)
        +List~ProxyService~ findByResource(UDPResource)
        +Optional~ProxyService~ findByServiceName(String)
        +boolean existsByServiceName(String)
    }

    class UDPResourceService {
        -UDPResourceRepository repository
        -IPRangeRepository ipRangeRepo
        +Resource createResource(CreateResourceRequest)
        +Resource updateResource(UpdateResourceRequest)
        +void deleteResource(Long)
        +List~Resource~ search(SearchCriteria)
        -validateResourceName(String)
    }

    class ProxyServiceService {
        -ProxyServiceRepository repository
        -UDPResourceService resourceService
        -ConfigurationManager configManager
        -ServiceDeployer deployer
        +Service createService(CreateServiceRequest)
        +void updateStatus(Long, ServiceStatus)
        +void deploy(Long)
        +void start(Long)
        +void stop(Long)
        -validateConfiguration(ProxyService)
    }

    UDPResource "1" -- "*" IPRange
    ProxyService "1" -- "1" UDPResource
    ProxyService "*" -- "1" ServiceDeployer
    ProxyService "*" -- "1" ConfigurationManager
    UDPResourceService ..> UDPResourceRepository
    ProxyServiceService ..> ProxyServiceRepository
    ServiceDeployer ..> ConfigurationManager