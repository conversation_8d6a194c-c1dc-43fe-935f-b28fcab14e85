<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>升级历史 - 安全隔离单向输出模块</title>
    <meta name="_csrf" th:content="${_csrf?.token}"/>
    <meta name="_csrf_header" th:content="${_csrf?.headerName}"/>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/main.css">
    <style>
        .history-container {
            max-width: 900px;
            margin: 0 auto;
        }
        .readme-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            background-color: #f9f9f9;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            display: none;
        }
        .patch-name {
            cursor: pointer;
            color: #0d6efd;
            text-decoration: underline;
        }
        .patch-name:hover {
            color: #0a58ca;
        }
        .empty-history {
            padding: 50px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- 导航栏 -->
    <header th:replace="fragments/navigation :: navbar('upgrade-history')"></header>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div th:replace="fragments/navigation :: sidebar-menu('upgrade-history')"></div>
                </div>
            </div>

            <!-- 主内容区 -->
            <main class="col-md-9 col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">升级历史</h1>
                </div>

                <div class="history-container">
                    <!-- 有历史记录时显示 -->
                    <div th:if="${hasHistory}">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">历史升级记录</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th scope="col">补丁名称</th>
                                                <th scope="col">升级时间</th>
                                                <th scope="col">升级用户</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="history : ${historyList}">
                                                <td>
                                                    <span class="patch-name"
                                                          th:attr="data-uuid=${history.uuid}"
                                                          th:text="${history.patchName}">
                                                    </span>
                                                </td>
                                                <td th:text="${history.time}"></td>
                                                <td th:text="${history.user}"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 补丁说明显示区域 -->
                                <div id="readme-container" class="readme-container">
                                    <div id="readme-content">
                                        <!-- 这里将显示补丁说明内容 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 无历史记录时显示 -->
                    <div th:unless="${hasHistory}" class="empty-history">
                        <i class="bi bi-info-circle text-muted" style="font-size: 48px;"></i>
                        <h4 class="mt-3 text-muted">对不起，本系统未获取到升级记录信息</h4>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="modal fade" id="loadingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-light">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <h5 id="loading-text" class="text-dark">正在获取补丁说明...</h5>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 获取CSRF令牌
            const token = $("meta[name='_csrf']").attr("content");
            const header = $("meta[name='_csrf_header']").attr("content");

            // 设置AJAX的默认请求头
            $.ajaxSetup({
                beforeSend: function(xhr) {
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }
                }
            });

            // 初始化Bootstrap Modal
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'), {
                backdrop: 'static',
                keyboard: false
            });

            // 点击补丁名称时获取补丁说明
            $('.patch-name').on('click', function() {
                const uuid = $(this).data('uuid');
                const patchName = $(this).text();

                // 显示加载中提示
                loadingModal.show();

                // 请求补丁说明
                $.ajax({
                    url: '/upgrade-history/readme',
                    type: 'POST',
                    data: { uuid: uuid },
                    success: function(response) {
                        // 隐藏加载中提示
                        loadingModal.hide();

                        // 显示补丁说明
                        $('#readme-content').text(response.content || '无补丁说明');
                        $('#readme-container').slideDown();
                    },
                    error: function(xhr) {
                        // 隐藏加载中提示
                        loadingModal.hide();

                        // 显示错误信息
                        let errorMsg = '无法获取补丁说明';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMsg = xhr.responseJSON.error;
                        }

                        $('#readme-content').html('<div class="alert alert-danger">' + errorMsg + '</div>');
                        $('#readme-container').slideDown();
                    }
                });
            });

            // ESC键关闭加载提示
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape') {
                    loadingModal.hide();
                }
            });
        });
    </script>
</body>
</html>