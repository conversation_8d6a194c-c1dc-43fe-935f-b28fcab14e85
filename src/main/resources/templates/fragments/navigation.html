<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <style>
        /* 下拉菜单兼容性样式 */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            display: none;
            min-width: 10rem;
            padding: 0.5rem 0;
            margin: 0;
            font-size: 1rem;
            color: #212529;
            text-align: left;
            list-style: none;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
        }

        .dropdown-menu.show {
            display: block !important;
        }

        .dropdown-menu-end {
            right: 0;
            left: auto;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.25rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
            cursor: pointer;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            color: #1e2125;
            background-color: #e9ecef;
            text-decoration: none;
        }

        .dropdown-item.active {
            color: #fff;
            text-decoration: none;
            background-color: #0d6efd;
        }

        .dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }

        /* 低版本浏览器兼容性 */
        .dropdown-toggle {
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* IE兼容性 */
        .dropdown-menu {
            -ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#000000')";
            filter: progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#000000');
        }

        /* 响应式下拉菜单 */
        @media (max-width: 768px) {
            .dropdown-menu {
                min-width: 8rem;
                font-size: 0.875rem;
            }

            .dropdown-item {
                padding: 0.375rem 0.75rem;
            }
        }

        /* 统一下拉菜单样式 */
        .navbar-nav .dropdown-toggle {
            position: relative;
        }

        .navbar-nav .dropdown-toggle.active {
            background-color: rgba(255, 255, 255, 0.2) !important;
            border-radius: 0.25rem;
        }

        .navbar-nav .dropdown-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.25rem;
        }

        /* 确保下拉菜单项的active状态样式 */
        .dropdown-item.active {
            background-color: #0d6efd !important;
            color: white !important;
        }

        /* 下拉菜单显示时的父级样式 */
        .dropdown.show > .dropdown-toggle {
            background-color: rgba(255, 255, 255, 0.2) !important;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- 保留旧的顶部导航栏代码作为备份（可以在过渡期使用） -->
    <nav th:fragment="navbar(activeTab)" class="navbar navbar-expand-lg navbar-dark bg-custom-blue">
        <div class="container">
            <a class="navbar-brand" href="/index">
                <i class="bi bi-shield-lock me-2"></i>安全隔离单向输出模块
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- 首页 -->
                    <li class="nav-item text-center">
                        <a class="nav-link" th:classappend="${activeTab == 'home' ? 'active' : ''}" href="/index">
                            <i class="bi bi-house-door me-2"></i>首页
                        </a>
                    </li>

                    <!-- 系统管理 -->
                    <li class="nav-item dropdown text-center">
                        <a class="nav-link dropdown-toggle" href="#"
                           th:classappend="${activeTab == 'network' || activeTab == 'license' || activeTab == 'management' || activeTab == 'users' || activeTab == 'version-info' ? 'active' : ''}"
                           id="systemManagementDropdown" role="button"
                           data-bs-toggle="dropdown"
                           data-toggle="dropdown"
                           aria-expanded="false"
                           onclick="toggleDropdown(this, event)">
                            <i class="bi bi-gear me-2"></i>系统管理
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="systemManagementDropdown">
                            <!-- 网络配置 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'network' ? 'active' : ''}" href="/network">
                                    <i class="bi bi-diagram-3 me-2"></i>网络配置
                                </a>
                            </li>
                            <!-- 授权管理 -->
                            <li th:if="${session.networkType == null || session.networkType == 0}">
                                <a class="dropdown-item" th:classappend="${activeTab == 'license' ? 'active' : ''}" href="/license">
                                    <i class="bi bi-key me-2"></i>授权管理
                                </a>
                            </li>
                            <!-- 管理平台设置 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'management' ? 'active' : ''}" href="/management">
                                    <i class="bi bi-gear me-2"></i>管理平台设置
                                </a>
                            </li>
                            <!-- 版本信息 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'version-info' ? 'active' : ''}" href="/version-info">
                                    <i class="bi bi-info-circle me-2"></i>版本信息
                                </a>
                            </li>
                            <!-- 用户管理 - 只对管理员显示 -->
                            <li th:if="${session.role == 'ADMIN'}">
                                <a class="dropdown-item" th:classappend="${activeTab == 'users' ? 'active' : ''}" href="/admin/users/">
                                    <i class="bi bi-people me-2"></i>用户管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 业务管理 -->
                    <li class="nav-item dropdown text-center">
                        <a class="nav-link dropdown-toggle" href="#"
                           th:classappend="${activeTab == 'services' || activeTab == 'certificates' ? 'active' : ''}"
                           id="businessManagementDropdown" role="button"
                           data-bs-toggle="dropdown"
                           data-toggle="dropdown"
                           aria-expanded="false"
                           onclick="toggleDropdown(this, event)">
                            <i class="bi bi-briefcase me-2"></i>业务管理
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="businessManagementDropdown">
                            <!-- 服务管理 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'services' ? 'active' : ''}" href="/services">
                                    <i class="bi bi-hdd-network me-2"></i>服务管理
                                </a>
                            </li>
                            <!-- 证书管理 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'certificates' ? 'active' : ''}" href="/certificates">
                                    <i class="bi bi-file-earmark-text me-2"></i>证书管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 统计与审计 -->
                    <li class="nav-item dropdown text-center">
                        <a class="nav-link dropdown-toggle" href="#"
                           th:classappend="${activeTab == 'audit' || activeTab == 'alarms' ? 'active' : ''}"
                           id="statisticsAuditDropdown" role="button"
                           data-bs-toggle="dropdown"
                           data-toggle="dropdown"
                           aria-expanded="false"
                           onclick="toggleDropdown(this, event)">
                            <i class="bi bi-graph-up me-2"></i>统计与审计
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="statisticsAuditDropdown">
                            <!-- 服务审计 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'audit' ? 'active' : ''}" href="/audit">
                                    <i class="bi bi-journal-text me-2"></i>服务审计
                                </a>
                            </li>
                            <!-- 服务报警 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'alarms' ? 'active' : ''}" href="/alarms">
                                    <i class="bi bi-exclamation-triangle me-2"></i>服务报警
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 运维管理 -->
                    <li class="nav-item dropdown text-center" th:if="${session.role == 'ADMIN'}">
                        <a class="nav-link dropdown-toggle" href="#"
                           th:classappend="${activeTab == 'upgrade' || activeTab == 'upgrade-history' ? 'active' : ''}"
                           id="maintenanceDropdown" role="button"
                           data-bs-toggle="dropdown"
                           data-toggle="dropdown"
                           aria-expanded="false"
                           onclick="toggleDropdown(this, event)">
                            <i class="bi bi-tools me-2"></i>运维管理
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="maintenanceDropdown">
                            <!-- 版本升级 - 只对管理员显示 -->
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'upgrade' ? 'active' : ''}" href="/upgrade">
                                    <i class="bi bi-arrow-up-circle me-2"></i>版本升级
                                </a>
                            </li>
                            <!-- 升级历史 - 只对管理员显示
                            <li>
                                <a class="dropdown-item" th:classappend="${activeTab == 'upgrade-history' ? 'active' : ''}" href="/upgrade-history">
                                    <i class="bi bi-clock-history me-2"></i>升级历史
                                </a>
                            </li>-->
                        </ul>
                    </li>
                </ul>

                <!-- 右侧用户菜单 -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                           data-bs-toggle="dropdown"
                           data-toggle="dropdown"
                           onclick="toggleDropdown(this, event)">
                            <i class="bi bi-person-circle me-2"></i>
                            <span th:if="${session.role == 'ADMIN'}" class="badge bg-danger me-1">管理员</span>
                            <span th:text="${session.username != null ? session.username : '用户'}">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/logout-user"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <script>
        // 下拉菜单兼容性JavaScript

        // 检测是否为低版本浏览器
        function detectOldBrowser() {
            const userAgent = navigator.userAgent;

            // 检测IE浏览器
            if (userAgent.indexOf('MSIE') !== -1 || userAgent.indexOf('Trident') !== -1) {
                return true;
            }

            // 检测低版本Chrome
            const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
            if (chromeMatch && parseInt(chromeMatch[1]) < 60) {
                return true;
            }

            // 检测低版本Firefox
            const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
            if (firefoxMatch && parseInt(firefoxMatch[1]) < 55) {
                return true;
            }

            // 检测低版本Safari
            const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
            if (safariMatch && parseInt(safariMatch[1]) < 12) {
                return true;
            }

            return false;
        }

        // 通用下拉菜单切换函数
        function toggleDropdown(element, event) {
            event.preventDefault();
            event.stopPropagation();

            const $toggle = $(element);
            const $menu = $toggle.next('.dropdown-menu');
            const $dropdown = $toggle.closest('.dropdown');

            // 如果Bootstrap正常工作，让Bootstrap处理
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown && !detectOldBrowser()) {
                try {
                    // 尝试使用Bootstrap的下拉菜单
                    const dropdown = new bootstrap.Dropdown(element);
                    dropdown.toggle();

                    // 确保样式统一
                    updateDropdownStyles();
                    return;
                } catch (e) {
                    console.log('Bootstrap下拉菜单失败，使用备用方案:', e);
                }
            }

            // 备用方案：手动切换
            // 关闭其他下拉菜单并移除样式
            $('.dropdown').not($dropdown).removeClass('show');
            $('.dropdown-menu').not($menu).removeClass('show');
            $('.dropdown-toggle').not($toggle).attr('aria-expanded', 'false');

            // 切换当前下拉菜单
            $menu.toggleClass('show');
            $dropdown.toggleClass('show');
            $toggle.attr('aria-expanded', $menu.hasClass('show'));

            // 确保样式统一
            updateDropdownStyles();
        }

        // 更新下拉菜单样式
        function updateDropdownStyles() {
            // 为显示的下拉菜单添加样式
            $('.dropdown.show > .dropdown-toggle').each(function() {
                $(this).css({
                    'background-color': 'rgba(255, 255, 255, 0.2)',
                    'border-radius': '0.25rem'
                });
            });

            // 为隐藏的下拉菜单移除样式
            $('.dropdown:not(.show) > .dropdown-toggle').each(function() {
                $(this).css({
                    'background-color': '',
                    'border-radius': ''
                });
            });
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 检测浏览器版本
            const isOldBrowser = detectOldBrowser();

            if (isOldBrowser) {
                console.log('检测到低版本浏览器，启用下拉菜单兼容模式');

                // 为低版本浏览器设置备用下拉菜单
                $('.dropdown-toggle').each(function() {
                    const $toggle = $(this);
                    const $menu = $toggle.next('.dropdown-menu');

                    // 移除Bootstrap的事件监听，使用自定义实现
                    $toggle.off('click.bs.dropdown');

                    // 添加自定义点击事件
                    $toggle.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // 关闭其他下拉菜单
                        $('.dropdown-menu').not($menu).removeClass('show');
                        $('.dropdown-toggle').not($toggle).attr('aria-expanded', 'false');

                        // 切换当前下拉菜单
                        $menu.toggleClass('show');
                        $toggle.attr('aria-expanded', $menu.hasClass('show'));
                    });
                });
            }

            // 添加全局点击事件来关闭下拉菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.dropdown').length) {
                    $('.dropdown').removeClass('show');
                    $('.dropdown-menu').removeClass('show');
                    $('.dropdown-toggle').attr('aria-expanded', 'false');

                    // 更新样式
                    updateDropdownStyles();
                }
            });

            // 监听Bootstrap下拉菜单事件
            $(document).on('shown.bs.dropdown hidden.bs.dropdown', function() {
                updateDropdownStyles();
            });
        });
    </script>
</body>
</html>