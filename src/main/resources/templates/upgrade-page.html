<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本升级 - 安全隔离单向输出模块</title>
    <meta name="_csrf" th:content="${_csrf?.token}"/>
    <meta name="_csrf_header" th:content="${_csrf?.headerName}"/>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/bootstrap-icons.css">
    <style>
        .upgrade-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .readme-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #aaa;
            background-color: #f5f5f5;
        }
        .upload-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        #file-info {
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <header th:replace="fragments/navigation :: navbar('upgrade')"></header>

    <div class="container">
        <div class="row h-100">

            <!-- 主内容区 -->
            <main class="col-md-9 col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">版本升级</h1>
                </div>

                <div class="upgrade-container">
                    <!-- 上传区域 -->
                    <div id="upload-form-container" class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">补丁文件上传</h5>
                            </div>
                            <div class="card-body">
                                <form id="uploadForm" method="post" action="/upgrade/upload" enctype="multipart/form-data" accept-charset="UTF-8">
                                    <label for="file-input" class="w-100 m-0 p-0">
                                        <div id="upload-area" class="upload-area">
                                            <i class="bi bi-cloud-arrow-up upload-icon"></i>
                                            <h5>点击或拖拽补丁文件到此处</h5>
                                            <p class="text-muted">支持.zip格式的补丁文件，最大200MB</p>
                                            <input type="file" id="file-input" name="file" hidden accept=".zip">
                                            <div id="file-info"></div>
                                        </div>
                                    </label>
                                    <div class="mt-3">
                                        <button id="upload-btn" class="btn btn-primary" disabled type="button">上传文件</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 补丁信息区域 -->
                    <div id="patch-info-container" style="display: none;" class="mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">补丁文件信息</h5>
                                <span id="patch-filename" class="badge bg-secondary"></span>
                            </div>
                            <div class="card-body">
                                <h6>升级说明：</h6>
                                <div id="readme-content" class="readme-container">
                                    <!-- 这里将显示readme.txt的内容 -->
                                </div>

                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button id="check-btn" class="btn btn-info me-2">检查</button>
                                        <button id="install-btn" class="btn btn-success me-2" disabled>执行升级</button>
                                    </div>
                                    <button id="cancel-btn" class="btn btn-outline-secondary">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作结果提示 -->
                    <div id="result-container" style="display: none;" class="mb-4">
                        <div class="alert" role="alert">
                            <h5 id="result-title" class="alert-heading">操作结果</h5>
                            <p id="result-message"></p>
                            <div id="result-actions" class="mt-3" style="display: none;">
                                <button id="restart-btn" class="btn btn-warning me-2">重启系统</button>
                                <button id="close-result-btn" class="btn btn-outline-secondary">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>


    <!-- 加载中遮罩 - 使用Bootstrap Modal代替 -->
    <div class="modal fade" id="loadingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="loadingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-light">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <h5 id="loading-text" class="text-dark">处理中，请稍候...</h5>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 确保页面加载时没有遗留的遮罩层
            resetModalState();

            // 增加对ESC按键的监听，提供额外的关闭方式
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape') {
                    console.log('检测到ESC按键，尝试关闭所有遮罩');
                    hideLoading();
                    resetModalState();
                }
            });

            // 获取CSRF令牌
            var token = $("meta[name='_csrf']").attr("content");
            var header = $("meta[name='_csrf_header']").attr("content");

            // 设置AJAX的默认请求头
            $.ajaxSetup({
                beforeSend: function(xhr) {
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }
                }
            });

            // 初始化Bootstrap Modal
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'), {
                backdrop: 'static',
                keyboard: false
            });

            // 初始化文件上传
            const uploadArea = $('#upload-area');
            const fileInput = $('#file-input');
            const fileInfo = $('#file-info');
            const uploadBtn = $('#upload-btn');

            // 监听拖拽事件
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('border-primary');
            });

            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('border-primary');
            });

            uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('border-primary');

                if (e.originalEvent.dataTransfer && e.originalEvent.dataTransfer.files.length) {
                    fileInput[0].files = e.originalEvent.dataTransfer.files;
                    updateFileInfo();
                }
            });

            // 文件选择改变时更新信息
            fileInput.on('change', updateFileInfo);

            function updateFileInfo() {
                const file = fileInput[0].files[0];
                if (file) {
                    // 检查文件类型
                    if (!file.name.toLowerCase().endsWith('.zip')) {
                        fileInfo.html('<div class="text-danger">错误：只支持.zip格式的补丁文件</div>');
                        uploadBtn.prop('disabled', true);
                        return;
                    }

                    // 检查文件大小（限制为200MB）
                    const maxSizeMB = 200;
                    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                    if (fileSizeMB > maxSizeMB) {
                        fileInfo.html(`<div class="text-danger">错误：文件大小 ${fileSizeMB} MB 超过限制 ${maxSizeMB} MB</div>`);
                        uploadBtn.prop('disabled', true);
                        return;
                    }

                    // 显示文件信息
                    fileInfo.html(`<div class="text-success">已选择: ${file.name} (${fileSizeMB} MB)</div>`);
                    uploadBtn.prop('disabled', false);
                } else {
                    fileInfo.html('');
                    uploadBtn.prop('disabled', true);
                }
            }

            // 上传文件
            uploadBtn.on('click', function() {
                const file = fileInput[0].files[0];
                if (!file) return;

                console.log('准备上传文件:', file.name, '大小:', file.size, '类型:', file.type);

                // 隐藏之前的成功提示
                $('#result-container').hide();

                // 显示加载提示
                showLoading('正在上传补丁文件，请稍候...');

                // 安全定时器，确保在任何情况下都会关闭遮罩
                let uploadSafetyTimer = setTimeout(function() {
                    console.log('上传安全超时触发，强制关闭遮罩层');
                    hideLoading();
                }, 600000); // 10分钟后强制关闭

                // 使用FormData直接从表单创建
                const formData = new FormData(document.getElementById('uploadForm'));

                // 确保文件被正确附加
                if (!formData.has('file')) {
                    console.log('FormData中没有文件，手动添加');
                    formData.delete('file'); // 删除可能存在的空文件字段
                    formData.append('file', file);
                }

                // 检查FormData内容
                console.log('FormData内容:');
                for (const pair of formData.entries()) {
                    console.log(pair[0] + ': ' + (pair[1] instanceof File ? pair[1].name : pair[1]));
                }

                let uploadRequest = $.ajax({
                    url: '/upgrade/upload',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    cache: false,
                    timeout: 300000, // 5分钟超时，给解压过程足够时间
                    success: function(response) {
                        clearTimeout(uploadSafetyTimer);
                        console.log('上传成功，响应:', response);

                        // 确保先关闭遮罩
                        hideLoading();

                        // 显示补丁信息
                        $('#upload-form-container').hide();
                        $('#patch-info-container').show();
                        $('#patch-filename').text(response.filename);
                        $('#readme-content').text(response.readme || '没有可用的升级说明');

                        // 启用检查按钮
                        $('#check-btn').prop('disabled', false);

                        // 显示上传成功提示
                        showResult('上传成功', '补丁文件已成功上传，请检查环境兼容性', 'info', false, true);
                    },
                    error: function(xhr, status, error) {
                        clearTimeout(uploadSafetyTimer);
                        console.error('上传失败:', status, error);
                        console.error('状态码:', xhr.status);
                        console.error('响应文本:', xhr.responseText);

                        // 确保先关闭遮罩
                        hideLoading();

                        let errorMsg = '上传失败，请重试';

                        try {
                            // 检查响应是否为JSON格式
                            if (xhr.responseText && xhr.responseText.trim()) {
                                if (xhr.responseText.trim().startsWith('{') || xhr.responseText.trim().startsWith('[')) {
                                    const response = JSON.parse(xhr.responseText);
                                    if (response.error) {
                                        errorMsg = response.error;
                                    }
                                } else {
                                    // 非JSON响应，可能是HTML错误页面或纯文本
                                    console.error('服务器返回非JSON响应:', xhr.responseText);
                                    if (xhr.responseText.includes('<html>')) {
                                        errorMsg = '服务器返回HTML错误页面，请检查服务器状态和日志';
                                    } else {
                                        errorMsg = '服务器错误: ' + xhr.responseText.substring(0, 100);
                                    }
                                }
                            }
                        } catch (e) {
                            console.error('解析错误响应失败:', e);
                            if (xhr.responseText) {
                                errorMsg += '<br><br>原始响应: ' + xhr.responseText.substring(0, 200);
                            }
                        }

                        // 显示错误但不要清除可能存在的成功提示
                        $('#result-container')
                            .removeClass('alert-success alert-info alert-warning')
                            .addClass('alert-danger');
                        $('#result-title').text('错误');
                        $('#result-message').html(errorMsg);
                        $('#result-container').show();
                        $('#result-actions').hide();
                    },
                    complete: function() {
                        // 确保遮罩一定被关闭
                        clearTimeout(uploadSafetyTimer);
                        console.log('上传请求完成，确保遮罩关闭');
                        hideLoading();
                        resetModalState();
                    }
                });

                // 添加额外的超时保护
                setTimeout(function() {
                    if (uploadRequest && uploadRequest.readyState < 4) {
                        console.log('上传请求超时，强制中断');
                        uploadRequest.abort();
                        clearTimeout(uploadSafetyTimer);
                        hideLoading();
                        resetModalState();
                        // 显示超时错误但不清除可能存在的成功提示
                        $('#result-container')
                            .removeClass('alert-success alert-info alert-warning')
                            .addClass('alert-danger');
                        $('#result-title').text('上传超时');
                        $('#result-message').html('上传文件操作超时，请检查网络连接或尝试上传更小的文件');
                        $('#result-container').show();
                        $('#result-actions').hide();
                    }
                }, 305000); // 比ajax超时多5秒
            });

            // 环境检查按钮
            $('#check-btn').on('click', function() {
                if ($(this).prop('disabled')) {
                    return; // 如果按钮被禁用，直接返回
                }

                // 显示加载提示
                showLoading('正在执行环境检查...');

                // 设置确保弹窗关闭的保护性超时器
                let safetyTimer = setTimeout(function() {
                    console.log('安全超时触发，强制关闭遮罩层');
                    hideLoading();
                }, 15000); // 15秒后强制关闭，无论服务器是否响应

                // 发送环境检查请求
                $.ajax({
                    url: '/upgrade/check',
                    type: 'POST',
                    timeout: 30000, // 设置30秒超时
                    success: function(response) {
                        clearTimeout(safetyTimer); // 清除安全定时器
                        console.log('环境检查返回结果:', response);
                        hideLoading();

                        // 使用正确的响应字段判断结果
                        if (response && response.result === 'success') {
                            // 启用安装按钮
                            $('#install-btn').prop('disabled', false);

                            // 显示检查通过消息
                            $('#result-container').removeClass().addClass('alert alert-success').show();
                            $('#result-title').text('检查通过');
                            $('#result-message').html('环境检查通过，可以执行升级操作。' + (response.message ? '<br>' + response.message : ''));
                            $('#result-actions').hide();
                        } else {
                            // 显示检查失败信息
                            $('#result-container').removeClass().addClass('alert alert-danger').show();
                            $('#result-title').text('检查失败');
                            $('#result-message').html('环境检查未通过，无法执行升级。' + (response.errorMsg ? '<br>' + response.errorMsg : ''));
                            $('#result-actions').hide();

                            // 禁用安装按钮
                            $('#install-btn').prop('disabled', true);
                        }
                    },
                    error: function(xhr, status, error) {
                        clearTimeout(safetyTimer); // 清除安全定时器
                        console.log('环境检查出错', status, error);
                        hideLoading();

                        // 显示错误消息
                        $('#result-container').removeClass().addClass('alert alert-danger').show();
                        $('#result-title').text('检查错误');

                        if (status === 'timeout') {
                            $('#result-message').html('环境检查请求超时，请稍后重试。');
                        } else {
                            try {
                                let errorMsg = xhr.responseJSON ? xhr.responseJSON.message : '未知错误';
                                $('#result-message').html('环境检查失败: ' + errorMsg);
                            } catch (e) {
                                $('#result-message').html('环境检查请求处理过程中发生错误。');
                            }
                        }
                        $('#result-actions').hide();

                        // 禁用安装按钮
                        $('#install-btn').prop('disabled', true);
                    },
                    complete: function() {
                        clearTimeout(safetyTimer); // 再次确保清除安全定时器
                        console.log('环境检查请求完成');

                        // 强制刷新UI状态
                        setTimeout(function() {
                            hideLoading();
                        }, 100);
                    }
                });
            });

            // 安装按钮点击事件
            $('#install-btn').on('click', function() {
                if ($(this).prop('disabled')) {
                    return; // 如果按钮被禁用，直接返回
                }

                // 在执行升级前，隐藏任何现有的结果提示
                $('#result-container').hide();

                // 显示加载提示
                showLoading('正在执行系统升级...');

                // 设置确保弹窗关闭的保护性超时器
                let safetyTimer = setTimeout(function() {
                    console.log('安全超时触发，强制关闭遮罩层');
                    hideLoading();
                }, 30000); // 30秒后强制关闭，无论服务器是否响应

                // 发送升级请求
                $.ajax({
                    url: '/upgrade/install',
                    type: 'POST',
                    timeout: 120000, // 升级可能需要较长时间，设置2分钟超时
                    success: function(response) {
                        clearTimeout(safetyTimer); // 清除安全定时器
                        console.log('升级操作返回结果:', response);
                        hideLoading();

                        if (response && response.result === 'success') {
                            // 显示升级成功消息
                            $('#result-container').removeClass().addClass('alert alert-success').show();
                            $('#result-title').text('升级成功');

                            let successMsg = '系统已成功完成升级。';
                            if(response.needRestart === 'true') {
                                successMsg += '需要重启系统使升级生效。';
                                // 显示重启按钮
                                $('#result-actions').show();
                            } else {
                                successMsg += '无需重启系统。';
                                $('#result-actions').hide();
                            }

                            $('#result-message').html(successMsg);

                            // 重置UI，但保留结果提示
                            resetUploadUI();
                        } else {
                            // 显示升级失败消息
                            $('#result-container').removeClass().addClass('alert alert-danger').show();
                            $('#result-title').text('升级失败');
                            $('#result-message').html(response.errorMsg || '升级过程中发生错误。');
                            $('#result-actions').hide();
                        }
                    },
                    error: function(xhr, status, error) {
                        clearTimeout(safetyTimer); // 清除安全定时器
                        console.log('升级操作出错', status, error);
                        hideLoading();

                        // 显示错误消息
                        $('#result-container').removeClass().addClass('alert alert-danger').show();
                        $('#result-title').text('升级失败');

                        if (status === 'timeout') {
                            $('#result-message').html('升级请求耗时过长，请检查服务器状态。');
                        } else {
                            try {
                                let errorMsg = xhr.responseJSON ? xhr.responseJSON.message : '未知错误';
                                $('#result-message').html('升级失败: ' + errorMsg);
                            } catch (e) {
                                $('#result-message').html('请求处理过程中发生错误。');
                            }
                        }
                        $('#result-actions').hide();
                    },
                    complete: function() {
                        clearTimeout(safetyTimer); // 再次确保清除安全定时器
                        console.log('升级请求完成');

                        // 强制刷新UI状态
                        setTimeout(function() {
                            hideLoading();
                        }, 100);
                    }
                });
            });

            // 取消按钮 - 隐藏补丁信息并返回上传界面
            $('#cancel-btn').on('click', function() {
                // 显示加载提示
                showLoading('正在清理临时文件...');

                // 设置确保弹窗关闭的保护性超时器
                let safetyTimer = setTimeout(function() {
                    console.log('安全超时触发，强制关闭遮罩层');
                    hideLoading();
                    resetUI(true); // 使用完整重置，包括隐藏结果提示
                }, 5000); // 5秒后强制关闭，无论服务器是否响应

                // 发送取消请求
                $.ajax({
                    url: '/upgrade/cancel',
                    type: 'POST',
                    timeout: 10000, // 缩短超时时间，避免等待太久
                    success: function(response) {
                        clearTimeout(safetyTimer); // 清除安全定时器
                        console.log('取消操作成功', response);
                        hideLoading();
                        resetUI(true); // 使用完整重置，包括隐藏结果提示
                    },
                    error: function(xhr, status, error) {
                        clearTimeout(safetyTimer); // 清除安全定时器
                        console.log('取消操作出错', status, error);
                        hideLoading();

                        // 无论发生什么错误，都重置UI并隐藏结果提示
                        resetUI(true);

                        if (status === 'timeout') {
                            console.log('取消操作超时');
                            // 不显示错误消息，静默处理，避免干扰用户体验
                        }
                    },
                    complete: function() {
                        clearTimeout(safetyTimer); // 再次确保清除安全定时器
                        console.log('取消请求完成');

                        // 强制刷新UI状态
                        setTimeout(function() {
                            hideLoading();
                            resetUI(true);
                        }, 100);
                    }
                });

                // 双重保险：直接重置UI
                setTimeout(function() {
                    hideLoading();
                    resetUI(true);
                }, 3000);
            });

            // 重启按钮 - 仅在成功重启后隐藏结果
            $('#restart-btn').on('click', function() {
                if (confirm('确定要重启系统吗？这将会断开所有连接。')) {
                    showLoading('正在重启系统，请稍候...');

                    $.ajax({
                        url: '/upgrade/restart',
                        type: 'POST',
                        timeout: 10000, // 设置10秒超时，重启命令应该很快返回
                        success: function(response) {
                            console.log('重启请求成功响应:', response);
                            setTimeout(function() {
                                hideLoading();
                                showResult('重启成功', '系统正在重启，请稍后刷新页面', 'success');

                                // 定时检查系统是否恢复
                                checkSystemAvailability();
                            }, 2000);
                        },
                        error: function(xhr, status, error) {
                            console.log('重启请求错误:', status, error, xhr.responseText);

                            // 对于重启操作，超时或连接错误通常意味着重启已经开始
                            if (status === 'timeout' || status === 'error' || xhr.status === 0) {
                                setTimeout(function() {
                                    hideLoading();
                                    showResult('重启中', '系统正在重启，请稍后刷新页面', 'info');
                                    // 系统可能正在重启，执行检查
                                    checkSystemAvailability();
                                }, 2000);
                            } else {
                                hideLoading();
                                let errorMsg = '系统重启失败，请手动重启系统';
                                try {
                                    if (xhr.responseJSON && xhr.responseJSON.error) {
                                        errorMsg = xhr.responseJSON.error;
                                    }
                                } catch (e) {
                                    console.log('解析错误响应失败:', e);
                                }
                                showResult('重启失败', errorMsg, 'danger');
                            }
                        }
                    });
                }
            });

            // 关闭结果按钮 - 完全隐藏结果容器
            $('#close-result-btn').on('click', function() {
                $('#result-container').hide();
                // 重置界面并显示上传表单
                resetUI(true);

                // 确保上传表单显示
                $('#upload-form-container').show();
                $('#patch-info-container').hide();

                console.log('关闭结果，返回上传界面');
            });

            // 显示操作结果，添加reset参数控制是否重置已有结果
            function showResult(title, message, type, showRestartButton = false, reset = true) {
                // 如果不重置，且当前有成功结果在显示，且新的结果不是成功类型，则保留成功结果
                if (!reset &&
                    $('#result-container').hasClass('alert-success') &&
                    type !== 'success') {
                    console.log('保留已有的成功提示，不显示新的:', title);
                    return;
                }

                $('#result-title').text(title);
                $('#result-message').html(message);
                $('#result-container')
                    .removeClass('alert-success alert-danger alert-warning alert-info')
                    .addClass('alert-' + type)
                    .show();

                if (showRestartButton) {
                    $('#result-actions').show();
                } else {
                    $('#result-actions').hide();
                }
            }

            // 重置界面
            function resetUI(hideResults = true) {
                // 显示上传表单，隐藏补丁信息
                $('#upload-form-container').show();
                $('#patch-info-container').hide();

                // 仅在指定时隐藏结果
                if (hideResults) {
                    $('#result-container').hide();
                }

                // 重置文件输入
                fileInput.val('');
                fileInfo.html('');
                uploadBtn.prop('disabled', true);
                $('#check-btn').prop('disabled', true); // 确保检查按钮被禁用
                $('#install-btn').prop('disabled', true);

                console.log('界面已完全重置，上传表单已显示');
            }

            // 添加新的UI重置函数，仅重置上传界面，不影响结果显示
            function resetUploadUI() {
                // 仅重置文件上传相关的UI元素，不影响结果显示
                fileInfo.html('');
                $('#file-input').val('');
                $('#patch-info-container').hide();
                $('#upload-btn').prop('disabled', true);

                // 重置环境检查和安装按钮的状态
                $('#check-btn').prop('disabled', true);
                $('#install-btn').prop('disabled', true);

                console.log('上传UI已重置，保留结果提示');
            }

            // 全局变量，跟踪当前的加载保护定时器
            let currentLoadingProtectionTimer = null;

            // 显示加载中遮罩
            function showLoading(text) {
                // 每次显示前先确保隐藏已有的遮罩
                hideLoading();

                // 清除之前的保护定时器
                if (currentLoadingProtectionTimer) {
                    clearTimeout(currentLoadingProtectionTimer);
                    currentLoadingProtectionTimer = null;
                }

                // 设置显示文本
                $('#loading-text').text(text || '处理中，请稍候...');

                try {
                    // 尝试使用Bootstrap API显示
                    if (loadingModal && typeof loadingModal.show === 'function') {
                        loadingModal.show();
                    } else {
                        // 备用方法：直接操作DOM
                        $('#loadingModal').addClass('show').css('display', 'block');
                        $('body').addClass('modal-open').append('<div class="modal-backdrop fade show"></div>');
                    }

                    console.log('显示加载遮罩: ' + text);
                } catch (e) {
                    console.error('显示加载遮罩时出错:', e);
                }

                // 添加安全机制：60秒后自动关闭加载遮罩
                currentLoadingProtectionTimer = setTimeout(function() {
                    console.log('全局保护定时器触发，强制关闭遮罩');
                    hideLoading();
                    resetModalState();
                    showResult('提示', '操作超时，请刷新页面重试', 'warning');
                }, 60000); // 1分钟保护
            }

            // 隐藏加载中遮罩
            function hideLoading() {
                try {
                    console.log('尝试关闭加载遮罩');

                    // 清除保护定时器
                    if (currentLoadingProtectionTimer) {
                        clearTimeout(currentLoadingProtectionTimer);
                        currentLoadingProtectionTimer = null;
                    }

                    // 尝试使用Bootstrap API关闭
                    if (loadingModal && typeof loadingModal.hide === 'function') {
                        loadingModal.hide();
                    }

                    // 确保没有遮罩残留
                    resetModalState();

                    console.log('加载遮罩已关闭');
                } catch (e) {
                    console.error('关闭加载遮罩时出错:', e);

                    // 发生错误时使用更直接的方式强制关闭
                    resetModalState();
                }
            }

            // 重置模态框状态
            function resetModalState() {
                // 移除模态框的show类
                $('#loadingModal').removeClass('show');
                $('#loadingModal').css('display', 'none');

                // 移除遮罩背景
                $('.modal-backdrop').remove();

                // 移除body上的modal-open类和内联样式
                $('body').removeClass('modal-open');
                $('body').css('padding-right', '');
                $('body').css('overflow', '');

                // 重置aria属性
                $('#loadingModal').attr('aria-hidden', 'true');
                $('#loadingModal').attr('aria-modal', 'false');

                // 强制更新DOM
                setTimeout(function() {
                    $('#loadingModal').hide();
                }, 50);
            }

            // 优化检查系统可用性函数
            function checkSystemAvailability() {
                let attempts = 0;
                const maxAttempts = 60; // 增加到60次尝试，每次3秒，总共3分钟
                let startTime = Date.now();

                console.log('开始检查系统可用性...');

                const checkInterval = setInterval(function() {
                    attempts++;
                    let elapsedTime = Math.floor((Date.now() - startTime) / 1000);

                    console.log(`检查系统可用性 - 第${attempts}次尝试 (已等待${elapsedTime}秒)`);

                    // 更新显示的等待时间
                    $('#result-message').html(`系统正在重启中，请稍候... (已等待${elapsedTime}秒)`);

                    $.ajax({
                        url: '/api/health', // 使用专门的健康检查接口
                        type: 'GET',
                        timeout: 3000,
                        success: function(response) {
                            console.log('系统健康检查成功:', response);
                            clearInterval(checkInterval);
                            // 系统恢复可用，显示重启完成提示
                            showResult('重启完成', '系统已重启完成，页面将在3秒后自动刷新', 'success', false, true);

                            // 3秒后自动刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 3000);
                        },
                        error: function(xhr, status, error) {
                            console.log(`第${attempts}次检查失败:`, status, error);

                            if (attempts >= maxAttempts) {
                                clearInterval(checkInterval);
                                let totalTime = Math.floor((Date.now() - startTime) / 1000);
                                showResult('重启检查超时',
                                    `已等待${totalTime}秒，系统可能需要更长时间重启。<br>` +
                                    '<a href="javascript:location.reload()">点击这里手动刷新页面</a>',
                                    'warning', false, true);
                            }
                        }
                    });
                }, 3000); // 每3秒检查一次

                // 添加总体超时保护（5分钟）
                setTimeout(function() {
                    if (checkInterval) {
                        clearInterval(checkInterval);
                        let totalTime = Math.floor((Date.now() - startTime) / 1000);
                        showResult('重启检查超时',
                            `已等待${totalTime}秒，系统重启时间较长。<br>` +
                            '<a href="javascript:location.reload()">点击这里手动刷新页面</a>',
                            'warning', false, true);
                    }
                }, 300000); // 5分钟总超时
            }
        });
    </script>
</body>
</html>