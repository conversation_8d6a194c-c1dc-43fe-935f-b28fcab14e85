<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本信息 - 安全隔离单向输出模块</title>
    <meta name="_csrf" th:content="${_csrf?.token}"/>
    <meta name="_csrf_header" th:content="${_csrf?.headerName}"/>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/main.css">
    <style>

    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- 导航栏 -->
    <header th:replace="fragments/navigation :: navbar('version-info')"></header>

    <div class="container">
        <div class="row">
            <!-- 主内容区 -->
            <main class="col-md-9 col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">版本信息</h1>
                </div>

                <div class="history-container">
                    <!-- 有历史记录时显示 -->
                    <div th:if="${versionInfo}">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">安全隔离单向输出模块</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th scope="col">版本号</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td th:text="${versionInfo.version}"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </main>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 获取CSRF令牌
            const token = $("meta[name='_csrf']").attr("content");
            const header = $("meta[name='_csrf_header']").attr("content");

            // 设置AJAX的默认请求头
            $.ajaxSetup({
                beforeSend: function(xhr) {
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }
                }
            });

            // 初始化Bootstrap Modal
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'), {
                backdrop: 'static',
                keyboard: false
            });

            // 点击补丁名称时获取补丁说明
            $('.patch-name').on('click', function() {
                const uuid = $(this).data('uuid');
                const patchName = $(this).text();

                // 显示加载中提示
                loadingModal.show();

                // 请求补丁说明
                $.ajax({
                    url: '/version-info',
                    type: 'POST',
                    data: { uuid: uuid },
                    success: function(response) {
                        // 隐藏加载中提示
                        loadingModal.hide();


                    },
                    error: function(xhr) {
                        // 隐藏加载中提示
                        loadingModal.hide();

                    }
                });
            });


        });
    </script>
</body>
</html>