<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>证书管理</title>
    <meta name="_csrf" th:content="${_csrf?.token}"/>
    <meta name="_csrf_header" th:content="${_csrf?.headerName}"/>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
    <style>
        .certificate-table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(66, 165, 245, 0.2);
        }

        .certificate-table th, .certificate-table td {
            border: 1px solid rgba(66, 165, 245, 0.2);
            padding: 15px;
            text-align: center;
        }

        .certificate-table th {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1976d2;
            font-weight: 600;
        }

        .certificate-table tbody tr:hover {
            background: rgba(66, 165, 245, 0.05);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 页面标题美化 */
        .page-header {
            background: linear-gradient(135deg, rgba(66, 165, 245, 0.1), rgba(144, 202, 249, 0.1));
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(66, 165, 245, 0.2);
        }

        .page-header h2 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: #666;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- 使用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('certificates')"></div>

    <div class="container mt-4">
        <!-- 警告和成功消息 -->
        <div id="alertContainer">
            <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <span th:text="${error}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                <span th:text="${success}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-shield-lock"></i> 证书管理</h2>
        </div>

        <!-- 固定证书类型表格 -->
        <table class="certificate-table">
            <thead>
                <tr>
                    <th>证书类型</th>
                    <th>证书标识</th>
                    <th>有效期</th>
                    <th>签发者</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 注册证书行 -->
                <tr>
                    <td>注册证书</td>
                    <td th:text="${registrationCert?.alias ?: ''}"></td>
                    <td th:if="${registrationCert != null}"
                        th:text="${#temporals.format(registrationCert.validFrom, 'yyyy-MM-dd')} + ' 至 ' + ${#temporals.format(registrationCert.validTo, 'yyyy-MM-dd')}"></td>
                    <td th:unless="${registrationCert != null}"></td>
                    <td th:text="${registrationCert?.issuer ?: ''}"></td>
                    <td><!-- 注册证书没有操作按钮 --></td>
                </tr>
                <!-- 应用证书行 -->
                <tr>
                    <td>应用证书</td>
                    <td th:text="${applicationCert?.alias ?: ''}"></td>
                    <td th:if="${applicationCert != null}"
                        th:text="${#temporals.format(applicationCert.validFrom, 'yyyy-MM-dd')} + ' 至 ' + ${#temporals.format(applicationCert.validTo, 'yyyy-MM-dd')}"></td>
                    <td th:unless="${applicationCert != null}"></td>
                    <td th:text="${applicationCert?.issuer ?: ''}"></td>
                    <td>
                        <button th:unless="${applicationCert != null}" type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#uploadCertModal">
                            导入
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 证书上传模态框 -->
    <div class="modal fade" id="uploadCertModal" tabindex="-1" aria-labelledby="uploadCertModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form th:action="@{/certificates/application/upload}" method="post" enctype="multipart/form-data">
                    <div class="modal-header">
                        <h5 class="modal-title" id="uploadCertModalLabel">导入应用证书</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 证书文件 -->
                        <div class="mb-3">
                            <label for="certFile" class="form-label">证书文件 <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="certFile" name="file" required accept=".cer,.crt,.pem,.der">
                            <div class="form-text">请上传符合标准的应用证书文件</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">导入</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 获取CSRF令牌
            var token = $("meta[name='_csrf']").attr("content");
            var header = $("meta[name='_csrf_header']").attr("content");

            // 设置AJAX默认头
            $.ajaxSetup({
                beforeSend: function(xhr) {
                    if(header && token) {
                        xhr.setRequestHeader(header, token);
                    }
                }
            });

            // 删除普通证书按钮点击事件
            $('.delete-cert').click(function() {
                var certId = $(this).data('id');
                var certType = $(this).data('type');

                if(confirm('确定要删除' + certType + '吗?')) {
                    console.log("开始删除证书: ID=" + certId + ", 类型=" + certType);

                    $.ajax({
                        url: '/certificates/' + certId + '/delete',
                        type: 'POST',
                        success: function(response) {
                            console.log("删除证书成功:", response);
                            // 刷新页面显示最新证书列表
                            location.reload();
                        },
                        error: function(xhr, status, error) {
                            console.error("删除证书失败:", xhr.responseText);
                            alert('删除证书失败: ' + (xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error));
                        }
                    });
                }
            });

            // 删除应用证书按钮点击事件
            $('.delete-app-cert').click(function() {
                if(confirm('确定要删除应用证书吗?')) {
                    console.log("开始删除应用证书");

                    $.ajax({
                        url: '/certificates/application/delete',
                        type: 'POST',
                        success: function(response) {
                            console.log("删除应用证书成功:", response);
                            alert("应用证书删除成功");
                            // 刷新页面显示最新证书列表
                            location.reload();
                        },
                        error: function(xhr, status, error) {
                            console.error("删除应用证书失败:", xhr.responseText);
                            alert('删除应用证书失败: ' + (xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error));
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>