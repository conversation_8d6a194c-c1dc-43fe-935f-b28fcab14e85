<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/default}">
<head>
    <meta charset="UTF-8">
    <title>首页 - 安全隔离单向输出模块</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
    <style>
        /* 首页特殊装饰效果 */
        .hero-section {
            background: linear-gradient(135deg, rgba(66, 165, 245, 0.1), rgba(144, 202, 249, 0.1));
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(66, 165, 245, 0.1) 0%, transparent 70%);
            animation: heroFloat 15s ease-in-out infinite;
        }

        @keyframes heroFloat {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }

        .status-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            border: 1px solid rgba(66, 165, 245, 0.2);
            position: relative;
            overflow: hidden;
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #42a5f5, #64b5f6, #90caf9, #bbdefb);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .status-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(66, 165, 245, 0.2);
        }

        .health-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        .health-good {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
        }
        .health-warning {
            background: linear-gradient(135deg, #ff9800, #ffb74d);
        }
        .health-danger {
            background: linear-gradient(135deg, #f44336, #ef5350);
        }

        .feature-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-radius: 16px;
            height: 100%;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(66, 165, 245, 0.2);
            position: relative;
        }

        .feature-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(66, 165, 245, 0.05), rgba(144, 202, 249, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::after {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-10px) rotateY(5deg);
            box-shadow: 0 25px 50px rgba(66, 165, 245, 0.25);
        }

        .feature-card .card-header {
            font-weight: 600;
            font-size: 1.1rem;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-bottom: 1px solid rgba(66, 165, 245, 0.2);
        }

        .feature-card .card-body {
            padding: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #42a5f5, #64b5f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: iconGlow 3s ease-in-out infinite;
        }

        @keyframes iconGlow {
            0%, 100% { filter: drop-shadow(0 0 5px rgba(66, 165, 245, 0.3)); }
            50% { filter: drop-shadow(0 0 15px rgba(66, 165, 245, 0.6)); }
        }

        .stats-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(66, 165, 245, 0.2);
            box-shadow: 0 8px 32px rgba(66, 165, 245, 0.1);
        }

        .stat-box {
            padding: 1.5rem;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(66, 165, 245, 0.15);
            text-align: center;
            border: 1px solid rgba(66, 165, 245, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(66, 165, 245, 0.1), transparent);
            transition: left 0.6s;
        }

        .stat-box:hover::before {
            left: 100%;
        }

        .stat-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(66, 165, 245, 0.25);
        }
        .stat-value {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div th:replace="fragments/navigation :: navbar('home')"></div>
    <div layout:fragment="content">
        <div class="container">

            <!-- 系统状态 -->
            <h4 class="mb-3"><i class="bi bi-grid-3x3-gap-fill me-2"></i>系统状态</h4>
            <!-- 系统状态概览卡片 -->
            <div class="row mb-4">
                <!-- 系统运行状态卡片 -->
                <div class="col-md-4 mb-3">
                    <div class="card status-card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-server me-2"></i>系统运行状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <span class="health-indicator health-good"></span>
                                <h6 class="mb-0">系统正常运行中</h6>
                            </div>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>运行时间:</span>
                                    <span id="uptime" th:text="${healthInfo != null ? healthInfo.uptime : '获取中...'}">3天12小时45分</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>启动时间:</span>
                                    <span id="startTime" th:text="${healthInfo != null ? healthInfo.startTime : '获取中...'}">2023-01-01 08:00:00</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 资源使用情况卡片 -->
                <div class="col-md-4 mb-3">
                    <div class="card status-card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-cpu me-2"></i>资源使用情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label>CPU负载</label>
                                <div class="progress" style="height: 20px;">
                                    <div id="cpuLoad" th:if="${healthInfo != null}"
                                         th:class="'progress-bar ' + (${healthInfo.cpuLoad != null && #strings.contains(healthInfo.cpuLoad, '.')
                                             ? (T(java.lang.Double).parseDouble(#strings.replace(healthInfo.cpuLoad, ',', '.')) > 80
                                                 ? 'bg-danger'
                                                 : (T(java.lang.Double).parseDouble(#strings.replace(healthInfo.cpuLoad, ',', '.')) > 60
                                                     ? 'bg-warning'
                                                     : 'bg-success'))
                                             : 'bg-success'})"
                                         th:style="'width:' + (${healthInfo.cpuLoad != null}
                                             ? (${#strings.contains(healthInfo.cpuLoad, '%')}
                                                 ? ${healthInfo.cpuLoad}
                                                 : ${healthInfo.cpuLoad + '%'})
                                             : '0%') + ';'"
                                         th:text="${healthInfo.cpuLoad != null}
                                             ? (${#strings.contains(healthInfo.cpuLoad, '%')}
                                                 ? ${healthInfo.cpuLoad}
                                                 : ${healthInfo.cpuLoad + '%'})
                                             : '0%'">
                                        40%
                                    </div>
                                    <div th:if="${healthInfo == null}" class="progress-bar bg-secondary" style="width:100%">
                                        获取中...
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label>内存使用率</label>
                                <div class="progress" style="height: 20px;">
                                    <div id="memoryUsage" th:if="${healthInfo != null && healthInfo.memoryUsage != null}"
                                         th:with="memValue=${#strings.contains(healthInfo.memoryUsage, '%')
                                             ? #strings.replace(healthInfo.memoryUsage, '%', '')
                                             : healthInfo.memoryUsage}"
                                         th:class="'progress-bar ' + (${T(java.lang.Double).parseDouble(#strings.replace(memValue, ',', '.')) > 80}
                                             ? 'bg-danger'
                                             : (${T(java.lang.Double).parseDouble(#strings.replace(memValue, ',', '.')) > 60}
                                                 ? 'bg-warning'
                                                 : 'bg-success'))"
                                         th:style="'width:' + ${#strings.contains(healthInfo.memoryUsage, '%')
                                             ? healthInfo.memoryUsage
                                             : healthInfo.memoryUsage + '%'} + ';'"
                                         th:text="${#strings.contains(healthInfo.memoryUsage, '%')
                                             ? healthInfo.memoryUsage
                                             : healthInfo.memoryUsage + '%'}">
                                        60%
                                    </div>
                                    <div th:if="${healthInfo == null || healthInfo.memoryUsage == null}" class="progress-bar bg-secondary" style="width:100%">
                                        获取中...
                                    </div>
                                </div>
                                <small id="memoryDetails" class="text-muted" th:text="${healthInfo != null ? (healthInfo.usedMemory + ' / ' + healthInfo.totalMemory) : ''}">
                                    512MB / 1024MB
                                </small>
                            </div>
                            <div class="mt-3">
                                <label>磁盘空间</label>
                                <div class="progress" style="height: 20px;">
                                    <div id="diskUsage" th:if="${healthInfo != null && healthInfo.diskUsagePercent != null}"
                                         th:with="diskValue=${healthInfo.diskUsagePercent}"
                                         th:class="'progress-bar ' + (${diskValue > 90} ? 'bg-danger' : (${diskValue > 75} ? 'bg-warning' : 'bg-success'))"
                                         th:style="'width:' + ${diskValue + '%'} + ';'"
                                         th:text="${diskValue + '%'}">
                                        30%
                                    </div>
                                    <div th:if="${healthInfo == null || healthInfo.diskUsagePercent == null}" class="progress-bar bg-secondary" style="width:100%">
                                        获取中...
                                    </div>
                                </div>
                                <small id="diskSpace" class="text-muted" th:text="${healthInfo != null ? (healthInfo.freeDiskSpace + ' 可用') : ''}">
                                    10GB 可用
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能快捷入口 -->
            <h4 class="mb-3"><i class="bi bi-grid-3x3-gap-fill me-2"></i>功能导航</h4>
            <div class="row mb-4">
                <!-- 服务配置卡片 -->
                <div class="col-md-4 mb-3">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon">
                                <i class="bi bi-hdd-network"></i>
                            </div>
                            <h5 class="card-title">服务配置</h5>
                            <p class="card-text">配置和管理UDP代理服务，设置转发规则和监控服务状态</p>
                            <a href="/services" class="btn btn-primary">
                                <i class="bi bi-arrow-right-circle me-1"></i>服务管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 证书管理卡片 -->
                <div class="col-md-4 mb-3">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon">
                                <i class="bi bi-shield-lock"></i>
                            </div>
                            <h5 class="card-title">证书管理</h5>
                            <p class="card-text">导入、导出和管理系统证书，确保连接安全</p>
                            <a href="/certificates" class="btn btn-secondary">
                                <i class="bi bi-arrow-right-circle me-1"></i>证书管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 用户管理卡片 -->
                <div class="col-md-4 mb-3">
                    <div class="card feature-card">
                        <div class="card-body text-center">
                            <div class="feature-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <h5 class="card-title">用户管理</h5>
                            <p class="card-text">管理系统用户账号，设置用户权限</p>
                            <a href="/admin/users/" class="btn btn-dark">
                                <i class="bi bi-arrow-right-circle me-1"></i>用户管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>


        <!-- 添加获取系统信息的脚本 -->
        <script layout:fragment="scripts">
            document.addEventListener('DOMContentLoaded', function() {
                // 页面加载后不再调用旧的健康信息API
                // 统一使用资源使用情况API
                console.log('页面加载完成，等待资源使用情况初始化');
            });

            // 更新健康信息的函数
            function updateHealthInfo(data) {
                try {
                    // 更新运行时间和启动时间
                    updateElementText('uptime', data.uptime || '未知');
                    updateElementText('startTime', data.startTime || '未知');

                    // 更新CPU和内存使用率
                    let cpuValue = data.cpuLoad || '0';
                    if (!cpuValue.includes('%')) cpuValue += '%';
                    updateProgressBar('cpuLoad', cpuValue);

                    let memValue = data.memoryUsage || '0%';
                    if (!memValue.includes('%')) memValue += '%';
                    updateProgressBar('memoryUsage', memValue);

                    if (document.getElementById('memoryDetails')) {
                        document.getElementById('memoryDetails').textContent =
                            (data.usedMemory && data.totalMemory) ? (data.usedMemory + ' / ' + data.totalMemory) : '';
                    }

                    // 更新磁盘空间
                    if (data.diskUsagePercent !== undefined) {
                        updateProgressBar('diskUsage', data.diskUsagePercent + '%');
                        if (document.getElementById('diskSpace')) {
                            document.getElementById('diskSpace').textContent = data.freeDiskSpace + ' 可用';
                        }
                    }

                    // 更新系统信息
                    updateElementText('osInfo', (data.osName && data.osVersion) ? (data.osName + ' ' + data.osVersion) : '未知');
                    updateElementText('processors', data.availableProcessors || '未知');

                } catch (e) {
                    console.error('更新健康信息时出错:', e);
                }
            }

            // 更新元素文本内容的辅助函数
            function updateElementText(id, text) {
                const element = document.getElementById(id);
                if (element) element.textContent = text;
            }

            // 辅助函数：更新进度条
            function updateProgressBar(id, value) {
                const progressBar = document.getElementById(id);
                if (!progressBar) return;

                try {
                    progressBar.style.width = value;
                    progressBar.textContent = value;

                    // 根据值设置颜色
                    const numValue = parseFloat(value.replace('%', '').replace(',', '.'));
                    if (numValue > 80) {
                        progressBar.className = progressBar.className.replace(/bg-\w+/, 'bg-danger');
                    } else if (numValue > 60) {
                        progressBar.className = progressBar.className.replace(/bg-\w+/, 'bg-warning');
                    } else {
                        progressBar.className = progressBar.className.replace(/bg-\w+/, 'bg-success');
                    }
                } catch (e) {
                    console.error('更新进度条时出错:', e, value);
                }
            }
        </script>
    </div>


    <footer class="bg-light mt-5 py-3">
        <div class="container text-center">
            <p>Copyright ©无锡华通智能交通技术开发有限公司</p>
        </div>
    </footer>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 初始加载系统统计信息
            //loadSystemStats();

            // 刷新状态
            $('#refreshStatus').on('click', function() {
                loadSystemStats();
                loadResourceUsage();
            });

            // 每30秒自动刷新服务状态
            //setInterval(loadSystemStats, 30000);

            // 每10秒自动刷新资源使用情况
            setInterval(loadResourceUsage, 10000);

            // 初始加载资源使用情况
            loadResourceUsage();

            // 启动运行时间实时跳秒
            startUptimeCounter();
        });

        // 加载系统统计信息
        function loadSystemStats() {
            // 从API获取真实服务数据
            $.ajax({
                url: '/services/status',
                type: 'GET',
                success: function(data) {
                    // 更新服务数量统计
                    $('#serviceCount').text(data.totalCount);
                    $('#runningCount').text(data.runningCount);
                },
                error: function() {
                    // 出错时使用默认值
                    $('#serviceCount').text('0');
                    $('#runningCount').text('0');
                }
            });
        }

        // 更新进度条（增强版本，兼容现有代码）
        function updateProgressBar(elementId, value) {
            const progressBar = $('#' + elementId);
            if (progressBar.length > 0) {
                // 移除百分号并转换为数字
                const numValue = parseFloat(value.toString().replace('%', ''));
                progressBar.css('width', numValue + '%');
                progressBar.text(value);

                // 根据使用率设置颜色
                progressBar.removeClass('bg-success bg-warning bg-danger bg-secondary');
                if (numValue < 50) {
                    progressBar.addClass('bg-success');
                } else if (numValue < 80) {
                    progressBar.addClass('bg-warning');
                } else {
                    progressBar.addClass('bg-danger');
                }
            }
        }

        // 更新磁盘空间显示
        function updateDiskSpace(freeSpace) {
            const diskSpaceElement = $('#diskSpace');
            if (diskSpaceElement.length > 0) {
                diskSpaceElement.text(freeSpace + ' 可用');
            }
        }

        // 加载资源使用情况（每10秒调用一次）
        function loadResourceUsage() {
            $.ajax({
                url: '/system/api/resource-usage',
                type: 'GET',
                success: function(data) {
                    console.log('资源使用情况数据:', data);

                    // 检查是否有错误
                    if (data.error) {
                        console.error('配置文件错误:', data.error);
                        // 显示错误信息
                        updateProgressBar('cpuLoad', '0%');
                        updateProgressBar('memoryUsage', '0%');
                        updateProgressBar('diskUsage', '0%');
                        $('#cpuLoad').text('获取失败');
                        $('#memoryUsage').text('获取失败');
                        $('#diskUsage').text('获取失败');
                        return;
                    }

                    // 更新CPU负载
                    if (data.cpuLoad) {
                        // 确保CPU数据格式正确
                        let cpuValue = data.cpuLoad;
                        if (!cpuValue.toString().includes('%')) {
                            cpuValue = cpuValue + '%';
                        }
                        updateProgressBar('cpuLoad', cpuValue);
                        console.log('CPU负载更新:', cpuValue);
                    }

                    // 更新内存使用率
                    if (data.memoryUsage) {
                        updateProgressBar('memoryUsage', data.memoryUsage);
                        if (data.usedMemory && data.totalMemory) {
                            $('#memoryDetails').text(data.usedMemory + ' / ' + data.totalMemory);
                        }
                    }

                    // 更新磁盘空间
                    if (data.diskUsage) {
                        // 提取数字部分用于进度条
                        const diskPercent = parseFloat(data.diskUsage.replace('%', ''));
                        updateProgressBar('diskUsage', diskPercent + '%');
                        if (data.freeSpace) {
                            $('#diskSpace').text(data.freeSpace + ' 可用');
                        }
                    }

                    // 更新运行时间基准（以后端时间为准）
                    if (data.uptimeSeconds !== undefined) {
                        updateUptimeBase(data.uptimeSeconds);
                    }

                    console.log('资源使用情况更新成功');
                },
                error: function(xhr, status, error) {
                    console.error('获取资源使用情况失败:', error);
                    // 出错时保持当前显示，不更新
                }
            });
        }

        // 运行时间管理
        let baseUptimeSeconds = 0;  // 后端获取的基准运行时间（秒）
        let lastUpdateTime = 0;     // 上次更新基准时间的时间戳
        let uptimeInterval = null;  // 定时器

        // 启动运行时间实时跳秒计数器
        function startUptimeCounter() {
            // 清除之前的定时器
            if (uptimeInterval) {
                clearInterval(uptimeInterval);
            }

            // 启动实时计数器（每秒更新）
            uptimeInterval = setInterval(updateUptimeDisplay, 1000);

            console.log('运行时间计数器已启动');
        }

        // 更新运行时间基准（每10秒从后端获取）
        function updateUptimeBase(uptimeSeconds) {
            baseUptimeSeconds = uptimeSeconds;
            lastUpdateTime = Date.now();

            console.log('更新运行时间基准:', uptimeSeconds, '秒');

            // 立即更新显示
            updateUptimeDisplay();
        }

        // 更新运行时间显示（每秒调用）
        function updateUptimeDisplay() {
            if (baseUptimeSeconds <= 0) {
                $('#uptime').text('0天0小时0分0秒');
                return;
            }

            // 计算当前运行时间 = 基准时间 + 经过的秒数
            const elapsedSeconds = Math.floor((Date.now() - lastUpdateTime) / 1000);
            const currentUptimeSeconds = baseUptimeSeconds + elapsedSeconds;

            // 格式化显示
            const days = Math.floor(currentUptimeSeconds / (24 * 60 * 60));
            const hours = Math.floor((currentUptimeSeconds % (24 * 60 * 60)) / (60 * 60));
            const minutes = Math.floor((currentUptimeSeconds % (60 * 60)) / 60);
            const seconds = currentUptimeSeconds % 60;

            const uptimeStr = days + '天' + hours + '小时' + minutes + '分' + seconds + '秒';
            $('#uptime').text(uptimeStr);
        }
    </script>
</body>
</html>