<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务报警</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <style>
        .table-container {
            overflow-x: auto;
        }
        
        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .status-badge {
            font-size: 0.85em;
        }
        
        .severity-info {
            background-color: #0dcaf0;
        }
        
        .severity-warning {
            background-color: #ffc107;
        }
        
        .severity-error {
            background-color: #fd7e14;
        }
        
        .severity-critical {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div th:replace="fragments/navigation :: navbar('alerts')"></div>
    
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-exclamation-triangle"></i> 服务报警</h2>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bi bi-funnel"></i> 筛选
                </button>
            </div>
        </div>
        
        <div class="collapse filter-form" id="filterCollapse">
            <form th:action="@{/alerts/search}" method="get">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="serviceId" class="form-label">服务</label>
                        <select class="form-select" id="serviceId" name="serviceId">
                            <option value="">所有服务</option>
                            <option th:each="service : ${services}" 
                                    th:value="${service.id}" 
                                    th:text="${service.name}"
                                    th:selected="${service.id == selectedServiceId}"></option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="alertType" class="form-label">报警类型</label>
                        <select class="form-select" id="alertType" name="alertType">
                            <option value="">所有类型</option>
                            <option th:each="type : ${alertTypes}" 
                                    th:value="${type}" 
                                    th:text="${type}"
                                    th:selected="${type == selectedAlertType}"></option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="severity" class="form-label">严重程度</label>
                        <select class="form-select" id="severity" name="severity">
                            <option value="">所有级别</option>
                            <option th:each="sev : ${severities}" 
                                    th:value="${sev}" 
                                    th:text="${sev}"
                                    th:selected="${sev == selectedSeverity}"></option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="datetime-local" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                    </div>
                    <div class="col-md-2">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="datetime-local" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                    </div>
                    <div class="col-md-1">
                        <label for="acknowledged" class="form-label">状态</label>
                        <select class="form-select" id="acknowledged" name="acknowledged">
                            <option value="">全部</option>
                            <option value="false" th:selected="${acknowledged != null && acknowledged == false}">未确认</option>
                            <option value="true" th:selected="${acknowledged != null && acknowledged == true}">已确认</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <a href="/alerts" class="btn btn-secondary">重置</a>
                        <button type="button" class="btn btn-success" id="exportAlertsBtn">
                            <i class="bi bi-download"></i> 导出
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>服务</th>
                        <th>报警类型</th>
                        <th>级别</th>
                        <th>来源IP</th>
                        <th>报警消息</th>
                        <th>时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${#lists.isEmpty(alerts)}">
                        <td colspan="9" class="text-center">没有报警记录</td>
                    </tr>
                    <tr th:each="alert : ${alerts}">
                        <td th:text="${alert.id}"></td>
                        <td th:text="${alert.serviceName}"></td>
                        <td>
                            <span th:if="${alert.alertType == 'TRAFFIC_OVERLOAD'}" class="badge bg-warning">流量超限</span>
                            <span th:if="${alert.alertType == 'CONNECTION_LIMIT'}" class="badge bg-primary">连接超限</span>
                            <span th:if="${alert.alertType == 'SECURITY_BREACH'}" class="badge bg-danger">安全威胁</span>
                            <span th:if="${alert.alertType == 'SERVICE_DOWN'}" class="badge bg-secondary">服务宕机</span>
                            <span th:if="${alert.alertType != 'TRAFFIC_OVERLOAD' && alert.alertType != 'CONNECTION_LIMIT' && alert.alertType != 'SECURITY_BREACH' && alert.alertType != 'SERVICE_DOWN'}" 
                                  class="badge bg-info" th:text="${alert.alertType}">其他</span>
                        </td>
                        <td>
                            <span th:if="${alert.severity == 'INFO'}" class="badge severity-info">Info</span>
                            <span th:if="${alert.severity == 'WARNING'}" class="badge severity-warning">Warning</span>
                            <span th:if="${alert.severity == 'ERROR'}" class="badge severity-error">Error</span>
                            <span th:if="${alert.severity == 'CRITICAL'}" class="badge severity-critical">Critical</span>
                        </td>
                        <td th:text="${alert.sourceIp != null ? alert.sourceIp : '-'}"></td>
                        <td th:text="${alert.message}"></td>
                        <td th:text="${#temporals.format(alert.timestamp, 'yyyy-MM-dd HH:mm:ss')}"></td>
                        <td>
                            <span th:if="${alert.acknowledged}" class="badge bg-success">已确认</span>
                            <span th:unless="${alert.acknowledged}" class="badge bg-danger">未确认</span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info" 
                                        data-bs-toggle="modal" 
                                        th:data-bs-target="'#detailsModal-' + ${alert.id}">
                                    <i class="bi bi-eye"></i> 查看
                                </button>
                                <button th:if="${!alert.acknowledged}" type="button" class="btn btn-sm btn-success" 
                                        th:onclick="'acknowledgeAlert(' + ${alert.id} + ')'">
                                    <i class="bi bi-check-lg"></i> 确认
                                </button>
                            </div>
                            
                            <!-- 详情模态框 -->
                            <div class="modal fade" th:id="'detailsModal-' + ${alert.id}" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="detailsModalLabel">报警详情</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <dl class="row">
                                                <dt class="col-sm-4">ID</dt>
                                                <dd class="col-sm-8" th:text="${alert.id}"></dd>
                                                
                                                <dt class="col-sm-4">服务ID</dt>
                                                <dd class="col-sm-8" th:text="${alert.serviceId}"></dd>
                                                
                                                <dt class="col-sm-4">服务名</dt>
                                                <dd class="col-sm-8" th:text="${alert.serviceName}"></dd>
                                                
                                                <dt class="col-sm-4">报警类型</dt>
                                                <dd class="col-sm-8" th:text="${alert.alertType}"></dd>
                                                
                                                <dt class="col-sm-4">严重程度</dt>
                                                <dd class="col-sm-8" th:text="${alert.severity}"></dd>
                                                
                                                <dt class="col-sm-4">来源IP</dt>
                                                <dd class="col-sm-8" th:text="${alert.sourceIp != null ? alert.sourceIp : '-'}"></dd>
                                                
                                                <dt class="col-sm-4">报警消息</dt>
                                                <dd class="col-sm-8" th:text="${alert.message}"></dd>
                                                
                                                <dt class="col-sm-4">报警时间</dt>
                                                <dd class="col-sm-8" th:text="${#temporals.format(alert.timestamp, 'yyyy-MM-dd HH:mm:ss')}"></dd>
                                                
                                                <dt class="col-sm-4">状态</dt>
                                                <dd class="col-sm-8">
                                                    <span th:if="${alert.acknowledged}">已确认</span>
                                                    <span th:unless="${alert.acknowledged}">未确认</span>
                                                </dd>
                                                
                                                <dt th:if="${alert.acknowledged}" class="col-sm-4">确认人</dt>
                                                <dd th:if="${alert.acknowledged}" class="col-sm-8" th:text="${alert.acknowledgedBy}"></dd>
                                                
                                                <dt th:if="${alert.acknowledged}" class="col-sm-4">确认时间</dt>
                                                <dd th:if="${alert.acknowledged}" class="col-sm-8" 
                                                    th:text="${#temporals.format(alert.acknowledgedTime, 'yyyy-MM-dd HH:mm:ss')}"></dd>
                                                
                                                <dt class="col-sm-4">详情</dt>
                                                <dd class="col-sm-8">
                                                    <pre th:text="${alert.details != null ? alert.details : '-'}" style="white-space: pre-wrap;"></pre>
                                                </dd>
                                            </dl>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                            <button th:if="${!alert.acknowledged}" type="button" class="btn btn-success" 
                                                    th:onclick="'acknowledgeAlertAndClose(' + ${alert.id} + ')'">
                                                确认报警
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        function acknowledgeAlert(alertId) {
            if (confirm('确认处理此报警?')) {
                $.ajax({
                    url: '/alerts/acknowledge/' + alertId,
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            alert('报警已确认');
                            location.reload();
                        } else {
                            alert('确认报警失败: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        alert('确认报警请求失败: ' + xhr.responseText);
                    }
                });
            }
        }
        
        function acknowledgeAlertAndClose(alertId) {
            $.ajax({
                url: '/alerts/acknowledge/' + alertId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        $('.modal').modal('hide');
                        location.reload();
                    } else {
                        alert('确认报警失败: ' + response.message);
                    }
                },
                error: function(xhr) {
                    alert('确认报警请求失败: ' + xhr.responseText);
                }
            });
        }
        
        $(document).ready(function() {
            // 格式化日期和时间输入
            $("#startDate, #endDate").on("change", function() {
                const val = $(this).val();
                if (val) {
                    // 确保日期时间格式符合ISO标准
                    try {
                        const date = new Date(val);
                        const isoString = date.toISOString().slice(0, 16);
                        $(this).val(isoString);
                    } catch (e) {
                        console.error("Invalid date format", e);
                    }
                }
            });
            
            // 初始化筛选表单的折叠状态
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('serviceId') || urlParams.has('alertType') || 
                urlParams.has('severity') || urlParams.has('startDate') || 
                urlParams.has('endDate') || urlParams.has('acknowledged')) {
                $('#filterCollapse').addClass('show');
            }

            // 添加报警导出功能
            $('#exportAlertsBtn').on('click', function() {
                const serviceId = $('#serviceId').val();
                const alertType = $('#alertType').val();
                const severity = $('#severity').val();
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();
                const acknowledged = $('#acknowledged').val();
                
                let url = '/alerts/export?';
                if (serviceId) url += `serviceId=${serviceId}&`;
                if (alertType) url += `alertType=${alertType}&`;
                if (severity) url += `severity=${severity}&`;
                if (startDate) url += `startDate=${encodeURIComponent(startDate)}&`;
                if (endDate) url += `endDate=${encodeURIComponent(endDate)}&`;
                if (acknowledged) url += `acknowledged=${acknowledged}&`;
                
                window.location.href = url;
            });
        });
    </script>
</body>
</html>