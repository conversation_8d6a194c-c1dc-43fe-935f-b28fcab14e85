<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>测试页面</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-success">
            <h2>测试页面成功加载！</h2>
            <p>如果您能看到此页面，表示应用程序正常运行。</p>
            <p>当前时间：<span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}"></span></p>
        </div>
        <a href="/login" class="btn btn-primary">返回登录页</a>
    </div>
</body>
</html>