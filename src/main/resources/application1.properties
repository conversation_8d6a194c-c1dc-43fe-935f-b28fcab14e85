# ?????
server.port=8081
server.servlet.context-path=/
server.ssl.enabled=false

# ?????????
# server.ssl.client-auth=none
# server.ssl.trust-store=classpath:truststore.p12
# server.ssl.trust-store-password=12345678
# server.ssl.trust-store-type=PKCS12

# Thymeleaf??
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML

# ??????
spring.web.resources.static-locations=classpath:/static/

# ????
logging.level.root=INFO
logging.level.com.udpproxy=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Jackson??
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# ??????
logging.level.org.thymeleaf=DEBUG
logging.level.org.springframework.web=DEBUG

# ??????????????
config.dir=/etc/unimas/tomcat/config
certificates.dir=/etc/unimas/tomcat/cert

# ??????
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB
