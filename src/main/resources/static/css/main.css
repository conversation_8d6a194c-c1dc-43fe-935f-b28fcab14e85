/* 主样式文件 */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;    
    background-color: #f8f9fa;
}

.footer {
    margin-top: auto;
}

/* 状态指示器 */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.running {
    background-color: #28a745;
}

.status-indicator.stopped {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

/* 表格样式 */
.table th {
    background-color: #f8f9fa;
}

/* 卡片悬停效果 */
.card {
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0,0,0,.125);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0,0,0,.03);
    border-top: 1px solid rgba(0,0,0,.125);
}

/* 向导样式 */
.nav-pills .nav-link.active {
    background-color: #007bff;
}

.tab-pane {
    padding: 20px 0;
}

/* 菜单高亮样式 */
.navbar-nav .nav-link.active {
    font-weight: bold;
    position: relative;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background-color: white;
}

/* 菜单项悬停效果 */
.navbar-nav .nav-link {
    transition: all 0.3s;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-2px);
}

/* 菜单图标样式 */
.navbar-nav .nav-link i {
    margin-right: 5px;
}

/* 用户下拉菜单 */
.user-dropdown {
    cursor: pointer;
}

/* 页面内容容器 */
.content-container {
    padding-top: 20px;
    padding-bottom: 40px;
}

/* 卡片悬停效果 */
.hover-card {
    transition: all 0.3s;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* 查询按钮样式 */
.btn-query {
    min-width: 80px;
}

/* 表格响应式样式 */
.table-responsive {
    overflow-x: auto;
}

/* 筛选表单样式 */
.filter-form {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* 证书卡片样式 */
.certificate-card {
    margin-bottom: 20px;
    transition: all 0.3s;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.cert-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* 有效期样式 */
.validity-ok {
    color: green;
}

.validity-warning {
    color: orange;
}

.validity-expired {
    color: red;
}

/* 添加自定义蓝色导航栏类 */
.bg-custom-blue {
    background-color: #2874A6 !important; /* 这里可以设置你想要的蓝色色值 */
}

/* 可以调整活动项的背景色，使其更好地匹配 */
.navbar-dark .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.25rem;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

/* 表单样式 */
.form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

/* 警告框样式 */
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #e9ecef;
    border-radius: .25rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    padding-left: .5rem;
    color: #6c757d;
    content: "/";
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* 辅助类 */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }

.text-white { color: #fff !important; }
.text-muted { color: #6c757d !important; }
.text-end { text-align: right !important; }

.bg-primary { background-color: #007bff !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-light { background-color: #f8f9fa !important; }

.d-flex { display: flex !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-end { justify-content: flex-end !important; }
.align-items-center { align-items: center !important; } 