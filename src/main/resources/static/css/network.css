/* 网络配置页面样式 */
.status-enabled {
    color: #28a745;
}

.status-disabled {
    color: #dc3545;
}

.table-responsive {
    overflow-x: auto;
}

.actions-column {
    min-width: 180px;
}

/* IP地址有效性指示器 */
.ip-valid {
    border-color: #28a745;
}

.ip-invalid {
    border-color: #dc3545;
}

/* 表单验证提示 */
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.was-validated .form-control:invalid ~ .invalid-feedback {
    display: block;
} 