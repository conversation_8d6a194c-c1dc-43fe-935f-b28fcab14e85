package com.udpproxy.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.udpproxy.service.PatchLogic;

@Service
public class UpgradeService {

    private static final Logger logger = LoggerFactory.getLogger(UpgradeService.class);

    /**
     * 导入补丁文件
     *
     * @param request HTTP请求对象
     * @return 导入结果信息
     * @throws Exception 如果导入过程中发生错误
     */
    public Map<String, String> importPatchFile(HttpServletRequest request) throws Exception {
        logger.info("开始导入补丁文件");
        try {
            PatchLogic patchLogic = PatchLogic.getInstance();
            Map<String, String> result = patchLogic.importfile(request);

            if (result == null) {
                logger.error("PatchLogic.importfile返回null结果");
                throw new Exception("补丁文件处理失败，返回结果为空");
            }

            logger.info("补丁文件导入完成，结果：{}", result);

            // 验证必要字段
            if (!result.containsKey("filename") || !result.containsKey("currtime")) {
                logger.error("补丁文件处理结果缺少必要字段：{}", result);
                throw new Exception("补丁文件处理结果不完整");
            }

            return result;
        } catch (Exception e) {
            logger.error("导入补丁文件时发生异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行补丁检查
     *
     * @param currtime 当前时间戳（用于定位解压目录）
     * @return 检查结果
     * @throws Exception 如果检查过程中发生错误
     */
    public String checkPatch(String currtime) throws Exception {
        logger.info("开始检查补丁文件，时间戳：{}", currtime);
        PatchLogic patchLogic = PatchLogic.getInstance();
        String result = patchLogic.execmd("check", currtime);
        logger.info("补丁检查完成，结果：{}", result);
        return result;
    }

    /**
     * 执行补丁安装
     *
     * @param filename 补丁文件名
     * @param username 用户名
     * @param currtime 当前时间戳（用于定位解压目录）
     * @return 安装结果
     * @throws Exception 如果安装过程中发生错误
     */
    public String installPatch(String filename, String username, String currtime) throws Exception {
        logger.info("开始安装补丁文件：{}，用户：{}，时间戳：{}", filename, username, currtime);
        PatchLogic patchLogic = PatchLogic.getInstance();
        String result = patchLogic.exepatch(filename, username, currtime);
        logger.info("补丁安装完成，结果：{}", result);
        return result;
    }

    /**
     * 获取错误信息
     *
     * @param currtime 当前时间戳（用于定位解压目录）
     * @return 错误信息
     * @throws Exception 如果获取错误信息过程中发生错误
     */
    public String getErrorMessage(String currtime) throws Exception {
        logger.info("获取错误信息，时间戳：{}", currtime);
        PatchLogic patchLogic = PatchLogic.getInstance();
        return patchLogic.geterror(currtime);
    }

    /**
     * 检查是否需要重启系统
     *
     * @param currtime 当前时间戳（用于定位解压目录）
     * @return 是否需要重启
     * @throws Exception 如果检查过程中发生错误
     */
    public boolean isRestartNeeded(String currtime) {
        logger.info("检查是否需要重启，时间戳：{}", currtime);
        PatchLogic patchLogic = PatchLogic.getInstance();
        boolean needRestart = patchLogic.is_restart(currtime);
        logger.info("检查重启需求完成，需要重启：{}", needRestart);
        return needRestart;
    }

    /**
     * 执行系统重启
     *
     * @param currtime 当前时间戳（用于定位解压目录）
     * @return 重启结果
     * @throws Exception 如果重启过程中发生错误
     */
    public String restartSystem(String currtime) throws Exception {
        logger.info("开始重启系统，时间戳：{}", currtime);
        try {
            PatchLogic patchLogic = PatchLogic.getInstance();
            String result = patchLogic.execmd("restart", currtime);
            logger.info("重启脚本执行完成，结果：{}", result);

            // 重启脚本执行成功后，给系统一点时间开始重启过程
            if ("success".equals(result)) {
                logger.info("重启脚本执行成功，系统将开始重启");
                // 异步执行实际的重启操作，避免阻塞响应
                new Thread(() -> {
                    try {
                        Thread.sleep(2000); // 等待2秒让响应返回
                        logger.info("开始执行系统重启...");
                        // 这里可以添加实际的系统重启逻辑
                    } catch (InterruptedException e) {
                        logger.warn("重启线程被中断", e);
                    }
                }).start();
            }

            return result;
        } catch (Exception e) {
            logger.error("重启系统时发生异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清理临时文件
     *
     * @throws Exception 如果清理过程中发生错误
     */
    public void cleanUpTempFiles() throws Exception {
        logger.info("开始清理临时文件");
        PatchLogic patchLogic = PatchLogic.getInstance();
        patchLogic.deleteTemp();
        logger.info("临时文件清理完成");
    }
}