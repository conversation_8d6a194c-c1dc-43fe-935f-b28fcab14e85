package com.udpproxy.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.BufferedReader;
import java.io.FileReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.text.DecimalFormat;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.lang.management.OperatingSystemMXBean;
import com.sun.management.UnixOperatingSystemMXBean;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.management.AttributeList;
import javax.management.Attribute;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.Instant;

@Service
public class SystemHealthService {

    private static final Logger logger = LoggerFactory.getLogger(SystemHealthService.class);

    @Value("${config.dir:config}")
    private String configDir;

    @Value("${hardware.info.path:/dev/shm/wj/hardware.info}")
    private String hardwareInfoPath;

    @Value("${network.config.path:/boot/cfcard}")
    private String networkConfigPath;

    private final RuntimeMXBean runtimeMXBean;
    private final OperatingSystemMXBean osMXBean;
    private final MemoryMXBean memoryMXBean;
    private final MBeanServer mBeanServer;

    public SystemHealthService() {
        this.runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        this.osMXBean = ManagementFactory.getOperatingSystemMXBean();
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
        this.mBeanServer = ManagementFactory.getPlatformMBeanServer();
    }

    /**
     * 检查系统健康状态
     * @return 健康状态检查结果
     */
    public Map<String, Object> checkSystemHealth() {
        Map<String, Object> healthResult = new HashMap<>();

        // 1. 检查配置目录
        boolean configDirExists = checkConfigDir();
        healthResult.put("configDirHealth", configDirExists ? "正常" : "异常");

        // 2. 检查磁盘空间
        Map<String, Object> diskSpaceInfo = checkDiskSpace();
        healthResult.put("diskSpaceHealth",
                (long)diskSpaceInfo.get("freeSpace") > 100 * 1024 * 1024 ? "正常" : "警告");
        healthResult.put("diskSpace", diskSpaceInfo);

        // 3. 检查配置文件
        boolean configFilesValid = checkConfigFiles();
        healthResult.put("configFilesHealth", configFilesValid ? "正常" : "异常");

        // 4. 检查JVM状态
        Map<String, Object> jvmInfo = checkJvmStatus();
        healthResult.put("jvmHealth",
                (double)jvmInfo.get("heapUsagePercent") < 80 ? "正常" : "警告");
        healthResult.put("jvm", jvmInfo);

        // 总体健康状态
        boolean overallHealth = configDirExists && configFilesValid &&
                (long)diskSpaceInfo.get("freeSpace") > 100 * 1024 * 1024 &&
                (double)jvmInfo.get("heapUsagePercent") < 80;

        healthResult.put("overallHealth", overallHealth ? "正常" : "需要注意");

        return healthResult;
    }

    /**
     * 修复系统问题
     * @return 是否修复成功
     */
    public boolean repairSystem() {
        boolean success = true;

        try {
            // 1. 创建配置目录
            if (!checkConfigDir()) {
                File dir = new File(configDir);
                success = dir.mkdirs() && success;
                logger.info("创建配置目录: {}", success);
            }

            // 2. 修复配置文件（如果有需要）
            if (!checkConfigFiles()) {
                success = repairConfigFiles() && success;
                logger.info("修复配置文件: {}", success);
            }

            // 3. 清理临时文件
            success = cleanTempFiles() && success;
            logger.info("清理临时文件: {}", success);

            return success;
        } catch (Exception e) {
            logger.error("修复系统时发生错误", e);
            return false;
        }
    }

    /**
     * 获取系统资源使用情况信息
     * 只从 /dev/shm/wj/hardware.info 配置文件读取
     */
    public Map<String, Object> getResourceUsageInfo() {
        Map<String, Object> resourceInfo = new HashMap<>();

        try {
            // 只从配置文件读取硬件信息
            Map<String, Object> hardwareInfo = readHardwareInfoFromFile();

            if (hardwareInfo != null && !hardwareInfo.isEmpty()) {
                // 使用配置文件中的数据
                resourceInfo.putAll(hardwareInfo);
                logger.info("从配置文件获取资源使用情况成功");
            } else {
                // 配置文件读取失败，返回错误信息
                logger.error("配置文件读取失败，无法获取资源信息");
                resourceInfo.put("error", "配置文件读取失败");
                return resourceInfo;
            }

            // 获取当前运行时间（秒数）
            long uptimeSeconds = getUptimeInSeconds();
            resourceInfo.put("uptimeSeconds", uptimeSeconds);
            resourceInfo.put("uptimeFormatted", formatUptime(uptimeSeconds));

        } catch (Exception e) {
            logger.error("获取资源使用情况失败", e);
            resourceInfo.put("error", "获取资源使用情况失败: " + e.getMessage());
        }

        return resourceInfo;
    }

    /**
     * 从配置的硬件信息文件读取硬件信息
     * 根据网络类型（发送端/接收端）选择相应的数据源
     */
    private Map<String, Object> readHardwareInfoFromFile() {
        Map<String, Object> hardwareInfo = new HashMap<>();
        String filePath = hardwareInfoPath;

        try {
            File file = new File(filePath);
            if (!file.exists()) {
                logger.error("硬件信息文件不存在: {}", filePath);
                return null;
            }

            // 判断当前是发送端还是接收端
            boolean isReceiverDevice = isReceiver();
            logger.info("当前设备类型: {}", isReceiverDevice ? "接收端" : "发送端");

            List<String> lines = Files.readAllLines(file.toPath());
            boolean inDestSection = false;
            boolean inSrcSection = false;
            boolean foundSections = false;

            for (String line : lines) {
                line = line.trim();

                // 检查是否进入[DEST]部分
                if (line.equals("[DEST]")) {
                    inDestSection = true;
                    inSrcSection = false;
                    foundSections = true;
                    continue;
                }

                // 检查是否进入[SRC]部分
                if (line.equals("[SRC]")) {
                    inSrcSection = true;
                    inDestSection = false;
                    foundSections = true;
                    continue;
                }

                // 检查是否离开当前部分
                if (line.startsWith("[") && line.endsWith("]") &&
                    !line.equals("[DEST]") && !line.equals("[SRC]")) {
                    inDestSection = false;
                    inSrcSection = false;
                    continue;
                }

                // 根据设备类型和文件格式决定读取哪部分数据
                boolean shouldParseLine = false;

                if (!foundSections) {
                    // 没有分段，直接读取所有内容（发送端格式）
                    shouldParseLine = true;
                } else if (isReceiverDevice && inDestSection) {
                    // 接收端读取[DEST]部分
                    shouldParseLine = true;
                } else if (!isReceiverDevice && inSrcSection) {
                    // 发送端读取[SRC]部分（如果存在分段格式）
                    shouldParseLine = true;
                }

                if (shouldParseLine) {
                    parseHardwareInfoLine(line, hardwareInfo);
                }
            }

            logger.info("从配置文件读取硬件信息成功 ({}): {}",
                       isReceiverDevice ? "接收端" : "发送端", hardwareInfo);

            // 特别记录CPU数据
            if (hardwareInfo.containsKey("cpuLoad")) {
                logger.info("CPU负载数据: {}", hardwareInfo.get("cpuLoad"));
            }
            return hardwareInfo;

        } catch (Exception e) {
            logger.error("读取硬件信息文件失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * 解析硬件信息行
     */
    private void parseHardwareInfoLine(String line, Map<String, Object> hardwareInfo) {
        if (line.isEmpty() || line.startsWith("#")) {
            return;
        }

        try {
            if (line.startsWith("CpuUsage=")) {
                int cpuUsage = Integer.parseInt(line.substring("CpuUsage=".length()));
                hardwareInfo.put("cpuLoad", String.valueOf(cpuUsage));
                logger.debug("解析CPU使用率: {}%", cpuUsage);
            } else if (line.startsWith("MemSize=")) {
                int memSize = Integer.parseInt(line.substring("MemSize=".length()));
                hardwareInfo.put("totalMemory", memSize + " MB");
            } else if (line.startsWith("MemUsage=")) {
                int memUsage = Integer.parseInt(line.substring("MemUsage=".length()));
                hardwareInfo.put("memoryUsage", memUsage + "%");

                // 计算已使用内存
                String totalMemStr = (String) hardwareInfo.get("totalMemory");
                if (totalMemStr != null) {
                    int totalMem = Integer.parseInt(totalMemStr.replace(" MB", ""));
                    int usedMem = (totalMem * memUsage) / 100;
                    hardwareInfo.put("usedMemory", usedMem + " MB");
                }
            } else if (line.startsWith("DiskSize=")) {
                int diskSize = Integer.parseInt(line.substring("DiskSize=".length()));
                hardwareInfo.put("totalSpace", diskSize + " GB");
            } else if (line.startsWith("DiskUsage=")) {
                int diskUsage = Integer.parseInt(line.substring("DiskUsage=".length()));
                hardwareInfo.put("diskUsage", diskUsage + "%");

                // 计算可用磁盘空间
                String totalSpaceStr = (String) hardwareInfo.get("totalSpace");
                if (totalSpaceStr != null) {
                    int totalSpace = Integer.parseInt(totalSpaceStr.replace(" GB", ""));
                    int freeSpace = totalSpace - (totalSpace * diskUsage) / 100;
                    hardwareInfo.put("freeSpace", freeSpace + " GB");
                }
            }
        } catch (NumberFormatException e) {
            logger.warn("解析硬件信息行失败: {}", line, e);
        }
    }

    /**
     * 获取系统运行时间（秒数）
     */
    private long getUptimeInSeconds() {
        try {
            long currentTime = System.currentTimeMillis();
            long startTime = ManagementFactory.getRuntimeMXBean().getStartTime();
            return (currentTime - startTime) / 1000;
        } catch (Exception e) {
            logger.warn("获取系统运行时间失败", e);
            return 0L;
        }
    }

    /**
     * 格式化运行时间
     */
    private String formatUptime(long uptimeSeconds) {
        if (uptimeSeconds <= 0) {
            return "0天0小时0分0秒";
        }

        long days = uptimeSeconds / (24 * 60 * 60);
        long hours = (uptimeSeconds % (24 * 60 * 60)) / (60 * 60);
        long minutes = (uptimeSeconds % (60 * 60)) / 60;
        long seconds = uptimeSeconds % 60;

        return String.format("%d天%d小时%d分%d秒", days, hours, minutes, seconds);
    }



    /**
     * 判断当前是否为接收端
     * 通过读取 /boot/cfcard 文件中的 network 值判断
     * network=1 表示接收端，network=0 表示发送端
     */
    private boolean isReceiver() {
        try {
            File configFile = new File(networkConfigPath);
            if (!configFile.exists()) {
                logger.warn("网络配置文件不存在: {}, 默认为发送端", networkConfigPath);
                return false; // 默认为发送端
            }

            List<String> lines = Files.readAllLines(configFile.toPath());
            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("network=")) {
                    String networkValue = line.substring("network=".length()).trim();
                    boolean isReceiver = "1".equals(networkValue);
                    logger.info("读取网络配置: network={}, 当前为{}", networkValue, isReceiver ? "接收端" : "发送端");
                    return isReceiver;
                }
            }

            logger.warn("网络配置文件中未找到 network 配置，默认为发送端");
            return false; // 默认为发送端

        } catch (Exception e) {
            logger.error("读取网络配置文件失败: {}", networkConfigPath, e);
            return false; // 默认为发送端
        }
    }

    /**
     * 检查配置目录是否存在
     */
    private boolean checkConfigDir() {
        File dir = new File(configDir);
        return dir.exists() && dir.isDirectory();
    }

    /**
     * 检查磁盘空间
     */
    private Map<String, Object> checkDiskSpace() {
        Map<String, Object> diskInfo = new HashMap<>();
        try {
            // 获取Linux系统磁盘信息
            if (osMXBean.getName().toLowerCase().contains("linux")) {
                try {
                    Process process = Runtime.getRuntime().exec(new String[]{"bash", "-c", "df -k | grep -v 'tmpfs' | grep -v 'devtmpfs' | grep -v 'snap'"});
                    process.waitFor();

                    java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()));

                    long totalSpaceSum = 0;
                    long freeSpaceSum = 0;
                    long usedSpaceSum = 0;
                    String currentLine;

                    // 读取所有文件系统信息并累加
                    while ((currentLine = reader.readLine()) != null) {
                        String[] parts = currentLine.trim().split("\\s+");
                        if (parts.length >= 6) {
                            totalSpaceSum += Long.parseLong(parts[1]) * 1024;  // 转换为字节
                            usedSpaceSum += Long.parseLong(parts[2]) * 1024;   // 转换为字节
                            freeSpaceSum += Long.parseLong(parts[3]) * 1024;   // 转换为字节
                        }
                    }
                    reader.close();

                    if (totalSpaceSum > 0) {
                        diskInfo.put("totalSpace", totalSpaceSum);
                        diskInfo.put("freeSpace", freeSpaceSum);
                        diskInfo.put("usableSpace", freeSpaceSum);
                        diskInfo.put("usedPercent", 100.0 * usedSpaceSum / totalSpaceSum);

                        logger.info("成功通过df命令获取Linux所有文件系统信息");
                        return diskInfo;
                    }
                } catch (Exception e) {
                    logger.warn("通过df命令获取Linux磁盘信息失败，将使用Java API", e);
                }
            }

            // 回退到Java API
            File file = new File("/");
            if (!file.exists() && osMXBean.getName().toLowerCase().contains("windows")) {
                String systemDrive = System.getenv("SystemDrive");
                if (systemDrive != null && !systemDrive.isEmpty()) {
                    file = new File(systemDrive);
                } else {
                    file = new File("C:\\");
                }
            }

            if (!file.exists()) {
                file = new File(".");
            }

            long totalSpace = file.getTotalSpace();
            long freeSpace = file.getFreeSpace();
            long usableSpace = file.getUsableSpace();

            diskInfo.put("totalSpace", totalSpace);
            diskInfo.put("freeSpace", freeSpace);
            diskInfo.put("usableSpace", usableSpace);
            diskInfo.put("usedPercent", 100.0 * (totalSpace - freeSpace) / totalSpace);

        } catch (Exception e) {
            logger.error("检查磁盘空间时发生错误", e);
            diskInfo.put("error", e.getMessage());
        }
        return diskInfo;
    }

    /**
     * 检查配置文件
     */
    private boolean checkConfigFiles() {
        try {
            File dir = new File(configDir);
            if (!dir.exists()) {
                return false;
            }

            // 检查必要的配置文件是否存在
            String[] necessaryFiles = {
                "services_list.xml",
                "user.xml"
            };

            for (String fileName : necessaryFiles) {
                File file = new File(dir, fileName);
                if (!file.exists() || file.length() == 0) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("检查配置文件时发生错误", e);
            return false;
        }
    }

    /**
     * 检查JVM状态
     */
    private Map<String, Object> checkJvmStatus() {
        Map<String, Object> jvmInfo = new HashMap<>();

        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long allocatedMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = allocatedMemory - freeMemory;
        long availableMemory = maxMemory - usedMemory;

        jvmInfo.put("maxMemory", maxMemory);
        jvmInfo.put("allocatedMemory", allocatedMemory);
        jvmInfo.put("freeMemory", freeMemory);
        jvmInfo.put("usedMemory", usedMemory);
        jvmInfo.put("availableMemory", availableMemory);
        jvmInfo.put("heapUsagePercent", 100.0 * usedMemory / maxMemory);

        return jvmInfo;
    }

    /**
     * 修复配置文件
     */
    private boolean repairConfigFiles() {
        try {
            File dir = new File(configDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 创建基本的配置文件结构
            createDefaultConfigFile("services_list.xml", "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<servicesList>\n</servicesList>");
            createDefaultConfigFile("user.xml", "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<users>\n</users>");

            return true;
        } catch (Exception e) {
            logger.error("修复配置文件时发生错误", e);
            return false;
        }
    }

    /**
     * 创建默认配置文件
     */
    private void createDefaultConfigFile(String fileName, String content) throws Exception {
        File file = new File(configDir, fileName);
        if (!file.exists() || file.length() == 0) {
            Path path = Paths.get(file.getAbsolutePath());
            Files.write(path, content.getBytes());
        }
    }

    /**
     * 清理临时文件
     */
    private boolean cleanTempFiles() {
        try {
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            File[] tempFiles = tempDir.listFiles((dir, name) -> name.startsWith("udpproxy_"));

            if (tempFiles != null) {
                for (File file : tempFiles) {
                    if (file.isFile() && file.lastModified() < System.currentTimeMillis() - 86400000) { // 删除超过1天的临时文件
                        file.delete();
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("清理临时文件时发生错误", e);
            return false;
        }
    }

    /**
     * 获取系统健康状态信息
     */
    public Map<String, Object> getSystemHealth() {
        Map<String, Object> healthInfo = new HashMap<>();

        // 系统运行时间
        long uptime = runtimeMXBean.getUptime();
        Duration duration = Duration.ofMillis(uptime);
        long days = duration.toDays();

        // 修复：使用兼容的方式计算小时、分钟和秒
        long hours = TimeUnit.MILLISECONDS.toHours(uptime) % 24;
        long minutes = TimeUnit.MILLISECONDS.toMinutes(uptime) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(uptime) % 60;

        String uptimeStr = String.format("%d天%d小时%d分%d秒", days, hours, minutes, seconds);
        healthInfo.put("uptime", uptimeStr);

        // 获取系统CPU负载
        double cpuLoad;
        try {
            cpuLoad = getSystemCpuLoad();
            if (cpuLoad < 0) { // 获取失败
                cpuLoad = osMXBean.getSystemLoadAverage();
                if (cpuLoad < 0) { // Windows系统可能返回-1
                    cpuLoad = 25.0; // 使用一个默认值
                }
            }
        } catch (Exception e) {
            logger.warn("获取系统CPU负载失败", e);
            cpuLoad = osMXBean.getSystemLoadAverage();
            if (cpuLoad < 0) { // Windows系统可能返回-1
                cpuLoad = 25.0; // 使用一个默认值
            }
        }
        // 存储为数字，不添加百分号
        healthInfo.put("cpuLoad", new DecimalFormat("0.00").format(cpuLoad));

        // 获取系统内存使用情况（不仅仅是JVM的内存）
        try {
            Map<String, Object> systemMemoryInfo = getSystemMemoryInfo();
            healthInfo.putAll(systemMemoryInfo);
        } catch (Exception e) {
            logger.warn("获取系统内存信息失败，将使用JVM内存信息代替", e);
            // 回退到JVM内存使用情况
            long maxMemory = memoryMXBean.getHeapMemoryUsage().getMax() / (1024 * 1024);
            long usedMemory = memoryMXBean.getHeapMemoryUsage().getUsed() / (1024 * 1024);
            long totalMemory = memoryMXBean.getHeapMemoryUsage().getCommitted() / (1024 * 1024);

            healthInfo.put("maxMemory", maxMemory + " MB");
            healthInfo.put("usedMemory", usedMemory + " MB");
            healthInfo.put("totalMemory", totalMemory + " MB");

            // 计算内存使用率 - 存储为数字加百分号
            double memoryUsage = (double) usedMemory / totalMemory * 100;
            healthInfo.put("memoryUsage", new DecimalFormat("0.00").format(memoryUsage) + "%");
        }

        // 操作系统信息
        healthInfo.put("osName", osMXBean.getName());
        healthInfo.put("osVersion", osMXBean.getVersion());
        healthInfo.put("availableProcessors", osMXBean.getAvailableProcessors());

        // Java版本
        healthInfo.put("javaVersion", System.getProperty("java.version"));
        healthInfo.put("javaHome", System.getProperty("java.home"));

        // 启动时间
        Instant startTime = Instant.ofEpochMilli(runtimeMXBean.getStartTime());
        healthInfo.put("startTime", startTime.toString().replace("T", " ").replace("Z", ""));

        //从/opt/unimas/version配置文件中获取产品版本号 读取version: 版本号
        try {
            File versionFile = new File("/opt/unimas/version");
            if (versionFile.exists() && versionFile.length() > 0) {
                BufferedReader reader = new BufferedReader(new FileReader(versionFile));
                String line = reader.readLine();
                if (line != null && line.startsWith("version:")) {
                    String version = line.substring("version:".length()).trim();
                    healthInfo.put("version", version);
                }
            }
        } catch (Exception e) {
            logger.warn("获取产品版本号失败", e);
        }


        // 获取系统磁盘空间（根目录）
        try {
            Map<String, Object> rootDiskInfo = getRootDiskSpace();
            healthInfo.putAll(rootDiskInfo);
        } catch (Exception e) {
            logger.warn("获取根目录磁盘信息失败，将使用当前目录信息代替", e);
            // 回退到当前目录磁盘使用情况
            File file = new File(".");
            long totalSpace = file.getTotalSpace();
            long freeSpace = file.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            double diskUsagePercent = ((double) usedSpace / totalSpace) * 100;

            // 存储为数字，不添加百分号
            healthInfo.put("diskUsagePercent", Math.round(diskUsagePercent));
            healthInfo.put("freeDiskSpace", formatSize(freeSpace));
            healthInfo.put("totalDiskSpace", formatSize(totalSpace));
        }

        return healthInfo;
    }

    /**
     * 格式化文件大小
     */
    private String formatSize(long size) {
        if (size <= 0) return "0 B";

        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    // CPU使用率缓存
    private volatile double cachedCpuLoad = -1.0;
    private volatile long lastCpuLoadTime = 0;
    private static final long CPU_CACHE_DURATION = 30000; // 30秒缓存

    /**
     * 获取系统CPU使用率
     * 使用缓存机制减少频繁的系统调用
     */
    private double getSystemCpuLoad() {
        long currentTime = System.currentTimeMillis();

        // 如果缓存有效，直接返回缓存值
        if (cachedCpuLoad >= 0 && (currentTime - lastCpuLoadTime) < CPU_CACHE_DURATION) {
            return cachedCpuLoad;
        }

        try {
            // 优先使用Java API方式，避免频繁执行外部命令
            ObjectName name = ObjectName.getInstance("java.lang:type=OperatingSystem");
            AttributeList list = mBeanServer.getAttributes(name, new String[]{"SystemCpuLoad"});

            if (!list.isEmpty()) {
                Attribute att = (Attribute) list.get(0);
                Double value = (Double) att.getValue();

                if (value != null && value >= 0) {
                    cachedCpuLoad = value * 100; // 转换为百分比
                    lastCpuLoadTime = currentTime;
                    return cachedCpuLoad;
                }
            }

            // 如果Java API失败，且是Linux系统，才使用外部命令（但要限制频率）
            if (osMXBean.getName().toLowerCase().contains("linux") &&
                (currentTime - lastCpuLoadTime) > CPU_CACHE_DURATION * 2) { // 更长的间隔
                try {
                    Process process = Runtime.getRuntime().exec(new String[]{"bash", "-c",
                            "top -bn1 | grep '%Cpu' | awk '{print $2 + $4}'"});

                    // 设置超时，避免进程阻塞
                    boolean finished = process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS);
                    if (!finished) {
                        process.destroyForcibly();
                        logger.warn("top命令执行超时，已强制终止");
                        return cachedCpuLoad > 0 ? cachedCpuLoad : 25.0;
                    }

                    java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()));
                    String line = reader.readLine();
                    reader.close();

                    if (line != null && !line.trim().isEmpty()) {
                        cachedCpuLoad = Double.parseDouble(line.trim());
                        lastCpuLoadTime = currentTime;
                        logger.debug("通过top命令获取Linux系统CPU使用率: {}%", cachedCpuLoad);
                        return cachedCpuLoad;
                    }
                } catch (Exception e) {
                    logger.warn("通过外部命令获取Linux CPU使用率失败: {}", e.getMessage());
                }
            }

            if (list.isEmpty()) {
                return -1.0;
            }

            Attribute att = (Attribute) list.get(0);
            Double value = (Double) att.getValue();

            // 转换为百分比
            if (value == -1.0) return -1.0; // 不支持
            return value * 100;
        } catch (Exception e) {
            logger.warn("获取系统CPU使用率失败", e);
            return -1.0;
        }
    }

    /**
     * 获取系统内存信息
     */
    private Map<String, Object> getSystemMemoryInfo() {
        Map<String, Object> memoryInfo = new HashMap<>();
        try {
            // 在Linux系统上尝试使用free命令获取真实的内存使用情况
            if (osMXBean.getName().toLowerCase().contains("linux")) {
                try {
                    Process process = Runtime.getRuntime().exec(new String[]{"bash", "-c", "free -m | grep 'Mem:'"});
                    process.waitFor();

                    java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()));
                    String line = reader.readLine();
                    reader.close();

                    if (line != null) {
                        // free命令输出格式: Mem: total used free shared buff/cache available
                        String[] parts = line.trim().split("\\s+");
                        if (parts.length >= 4) {
                            long totalMemory = Long.parseLong(parts[1]);
                            long usedMemory = Long.parseLong(parts[2]);
                            long freeMemory = Long.parseLong(parts[3]);

                            memoryInfo.put("totalMemory", totalMemory + " MB");
                            memoryInfo.put("usedMemory", usedMemory + " MB");
                            memoryInfo.put("freeMemory", freeMemory + " MB");

                            // 计算内存使用率
                            double memoryUsage = (double) usedMemory / totalMemory * 100;
                            memoryInfo.put("memoryUsage", new DecimalFormat("0.00").format(memoryUsage) + "%");

                            logger.info("成功通过free命令获取Linux系统内存信息");
                            return memoryInfo;
                        }
                    }
                } catch (Exception e) {
                    logger.warn("通过free命令获取Linux内存信息失败，将使用Java API", e);
                }
            }

            // 尝试使用UnixOperatingSystemMXBean获取系统内存信息（适用于Linux/Unix系统）
            if (osMXBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsMXBean = (com.sun.management.OperatingSystemMXBean) osMXBean;

                long totalPhysicalMemory = sunOsMXBean.getTotalPhysicalMemorySize() / (1024 * 1024);
                long freePhysicalMemory = sunOsMXBean.getFreePhysicalMemorySize() / (1024 * 1024);
                long usedPhysicalMemory = totalPhysicalMemory - freePhysicalMemory;

                memoryInfo.put("totalMemory", totalPhysicalMemory + " MB");
                memoryInfo.put("usedMemory", usedPhysicalMemory + " MB");
                memoryInfo.put("freeMemory", freePhysicalMemory + " MB");

                // 计算内存使用率
                double memoryUsage = (double) usedPhysicalMemory / totalPhysicalMemory * 100;
                memoryInfo.put("memoryUsage", new DecimalFormat("0.00").format(memoryUsage) + "%");

                return memoryInfo;
            } else if (osMXBean instanceof UnixOperatingSystemMXBean) {
                UnixOperatingSystemMXBean unixOsMXBean = (UnixOperatingSystemMXBean) osMXBean;

                long totalPhysicalMemory = unixOsMXBean.getTotalPhysicalMemorySize() / (1024 * 1024);
                long freePhysicalMemory = unixOsMXBean.getFreePhysicalMemorySize() / (1024 * 1024);
                long usedPhysicalMemory = totalPhysicalMemory - freePhysicalMemory;

                memoryInfo.put("totalMemory", totalPhysicalMemory + " MB");
                memoryInfo.put("usedMemory", usedPhysicalMemory + " MB");
                memoryInfo.put("freeMemory", freePhysicalMemory + " MB");

                // 计算内存使用率
                double memoryUsage = (double) usedPhysicalMemory / totalPhysicalMemory * 100;
                memoryInfo.put("memoryUsage", new DecimalFormat("0.00").format(memoryUsage) + "%");

                return memoryInfo;
            }

            // 如果上述方法都不可用，尝试使用MBean方式获取
            ObjectName name = ObjectName.getInstance("java.lang:type=OperatingSystem");
            AttributeList list = mBeanServer.getAttributes(name,
                    new String[]{"TotalPhysicalMemorySize", "FreePhysicalMemorySize"});

            if (list.size() == 2) {
                long totalPhysicalMemory = ((Long) ((Attribute) list.get(0)).getValue()) / (1024 * 1024);
                long freePhysicalMemory = ((Long) ((Attribute) list.get(1)).getValue()) / (1024 * 1024);
                long usedPhysicalMemory = totalPhysicalMemory - freePhysicalMemory;

                memoryInfo.put("totalMemory", totalPhysicalMemory + " MB");
                memoryInfo.put("usedMemory", usedPhysicalMemory + " MB");
                memoryInfo.put("freeMemory", freePhysicalMemory + " MB");

                // 计算内存使用率
                double memoryUsage = (double) usedPhysicalMemory / totalPhysicalMemory * 100;
                memoryInfo.put("memoryUsage", new DecimalFormat("0.00").format(memoryUsage) + "%");

                return memoryInfo;
            }

            throw new Exception("无法获取系统内存信息");
        } catch (Exception e) {
            logger.warn("获取系统内存信息失败", e);
            throw new RuntimeException("获取系统内存信息失败", e);
        }
    }

    /**
     * 获取根目录磁盘空间
     */
    private Map<String, Object> getRootDiskSpace() {
        Map<String, Object> diskInfo = new HashMap<>();
        try {
            // 获取根目录
            File rootFile;
            boolean useSystemCommand = false;

            if (osMXBean.getName().toLowerCase().contains("linux") ||
                osMXBean.getName().toLowerCase().contains("unix")) {
                rootFile = new File("/");
                useSystemCommand = true;
            } else if (osMXBean.getName().toLowerCase().contains("windows")) {
                // 获取系统盘（通常是C盘）
                String systemDrive = System.getenv("SystemDrive");
                if (systemDrive != null && !systemDrive.isEmpty()) {
                    rootFile = new File(systemDrive);
                } else {
                    rootFile = new File("C:\\");
                }
            } else {
                // 默认使用当前目录
                rootFile = new File(".");
            }

            if (useSystemCommand && osMXBean.getName().toLowerCase().contains("linux")) {
                try {
                    // 直接获取最大的文件系统分区（按空间大小降序排序）
                    Process process = Runtime.getRuntime().exec(new String[]{"bash", "-c", "df -kP | grep -v 'tmpfs' | grep -v 'devtmpfs' | sort -nr -k2 | head -1"});
                    process.waitFor();

                    java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()));
                    String line = reader.readLine();
                    reader.close();

                    if (line != null && !line.trim().isEmpty()) {
                        String[] parts = line.trim().split("\\s+");
                        if (parts.length >= 6) {
                            String filesystem = parts[0];
                            long totalSpace = Long.parseLong(parts[1]) * 1024;  // 转换为字节
                            long usedSpace = Long.parseLong(parts[2]) * 1024;   // 转换为字节
                            long freeSpace = Long.parseLong(parts[3]) * 1024;   // 转换为字节
                            int usagePercent = Integer.parseInt(parts[4].replace("%", ""));
                            String mountPoint = parts[5];

                            diskInfo.put("diskUsagePercent", usagePercent);
                            diskInfo.put("freeDiskSpace", formatSize(freeSpace));
                            diskInfo.put("totalDiskSpace", formatSize(totalSpace));
                            diskInfo.put("rootPath", mountPoint);

                            logger.info("成功获取最大分区{}的磁盘信息: 总空间={}, 使用率={}%",
                                    mountPoint, formatSize(totalSpace), usagePercent);
                            return diskInfo;
                        }
                    }
                } catch (Exception e) {
                    logger.warn("尝试获取最大分区信息失败", e);
                }

                // 如果无法获取最大分区，获取所有文件系统的总和
                try {
                    Process process = Runtime.getRuntime().exec(new String[]{"bash", "-c", "df -kP | grep -v 'tmpfs' | grep -v 'devtmpfs'"});
                    process.waitFor();

                    java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(process.getInputStream()));

                    Map<String, Map<String, Object>> allFileSystems = new HashMap<>();
                    long totalSpaceSum = 0;
                    long freeSpaceSum = 0;
                    long usedSpaceSum = 0;
                    String currentLine;

                    while ((currentLine = reader.readLine()) != null) {
                        String[] parts = currentLine.trim().split("\\s+");
                        if (parts.length >= 6) {
                            long totalSpace = Long.parseLong(parts[1]) * 1024;
                            long usedSpace = Long.parseLong(parts[2]) * 1024;
                            long freeSpace = Long.parseLong(parts[3]) * 1024;
                            int usagePercent = Integer.parseInt(parts[4].replace("%", ""));
                            String mountPoint = parts[5];

                            Map<String, Object> fsInfo = new HashMap<>();
                            fsInfo.put("totalSpace", formatSize(totalSpace));
                            fsInfo.put("usedSpace", formatSize(usedSpace));
                            fsInfo.put("freeSpace", formatSize(freeSpace));
                            fsInfo.put("usagePercent", usagePercent);
                            fsInfo.put("mountPoint", mountPoint);

                            allFileSystems.put(mountPoint, fsInfo);

                            totalSpaceSum += totalSpace;
                            freeSpaceSum += freeSpace;
                            usedSpaceSum += usedSpace;
                        }
                    }
                    reader.close();

                    if (!allFileSystems.isEmpty()) {
                        int diskUsagePercent = (int)Math.round(((double) usedSpaceSum / totalSpaceSum) * 100);

                        diskInfo.put("diskUsagePercent", diskUsagePercent);
                        diskInfo.put("freeDiskSpace", formatSize(freeSpaceSum));
                        diskInfo.put("totalDiskSpace", formatSize(totalSpaceSum));
                        diskInfo.put("rootPath", "全部文件系统");
                        diskInfo.put("fileSystems", allFileSystems);

                        logger.info("成功获取所有文件系统的总体信息: 总空间={}, 使用率={}%",
                                formatSize(totalSpaceSum), diskUsagePercent);
                        return diskInfo;
                    }
                } catch (Exception e) {
                    logger.warn("获取所有文件系统信息失败", e);
                }
            }

            // 回退到Java API方式获取
            long totalSpace = rootFile.getTotalSpace();
            long freeSpace = rootFile.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            int diskUsagePercent = (int)Math.round(((double) usedSpace / totalSpace) * 100);

            diskInfo.put("diskUsagePercent", diskUsagePercent);
            diskInfo.put("freeDiskSpace", formatSize(freeSpace));
            diskInfo.put("totalDiskSpace", formatSize(totalSpace));
            diskInfo.put("rootPath", rootFile.getAbsolutePath());

            return diskInfo;
        } catch (Exception e) {
            logger.warn("获取磁盘空间失败", e);
            throw new RuntimeException("获取磁盘空间失败", e);
        }
    }

    /**
     * 获取磁盘空间信息（用于API）
     */
    private Map<String, Object> getDiskSpaceInfo() {
        Map<String, Object> diskInfo = new HashMap<>();

        try {
            File root = new File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;

            double usagePercent = totalSpace > 0 ? (double) usedSpace / totalSpace * 100 : 0;

            diskInfo.put("totalSpace", formatSize(totalSpace));
            diskInfo.put("freeSpace", formatSize(freeSpace));
            diskInfo.put("usedSpace", formatSize(usedSpace));
            diskInfo.put("diskUsage", String.format("%.1f%%", usagePercent));

            logger.debug("磁盘空间: 总计={}, 已用={}, 可用={}, 使用率={:.1f}%",
                        formatSize(totalSpace), formatSize(usedSpace),
                        formatSize(freeSpace), usagePercent);

        } catch (Exception e) {
            logger.warn("获取磁盘空间信息失败", e);
            diskInfo.put("totalSpace", "100 GB");
            diskInfo.put("freeSpace", "70 GB");
            diskInfo.put("usedSpace", "30 GB");
            diskInfo.put("diskUsage", "30.0%");
        }

        return diskInfo;
    }

    /**
     * 获取内存信息（用于API）
     */
    private Map<String, Object> getMemoryInfo() {
        Map<String, Object> memoryInfo = new HashMap<>();

        try {
            // 尝试获取系统内存信息
            Map<String, Object> systemMemoryInfo = getSystemMemoryInfo();
            return systemMemoryInfo;
        } catch (Exception e) {
            logger.warn("获取系统内存信息失败，使用JVM内存信息", e);

            // 回退到JVM内存信息
            long maxMemory = memoryMXBean.getHeapMemoryUsage().getMax() / (1024 * 1024);
            long usedMemory = memoryMXBean.getHeapMemoryUsage().getUsed() / (1024 * 1024);
            long totalMemory = memoryMXBean.getHeapMemoryUsage().getCommitted() / (1024 * 1024);

            memoryInfo.put("totalMemory", totalMemory + " MB");
            memoryInfo.put("usedMemory", usedMemory + " MB");
            memoryInfo.put("freeMemory", (totalMemory - usedMemory) + " MB");

            // 计算内存使用率
            double memoryUsage = totalMemory > 0 ? (double) usedMemory / totalMemory * 100 : 0;
            memoryInfo.put("memoryUsage", String.format("%.1f%%", memoryUsage));

            return memoryInfo;
        }
    }
}