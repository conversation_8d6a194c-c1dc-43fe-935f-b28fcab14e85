package com.udpproxy.service;

import com.udpproxy.model.User;
import com.udpproxy.model.UserList;
import com.udpproxy.util.EncryptionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Value("${config.dir:./config}")
    private String configDir;

    private static final String USERS_FILE = "user.xml";

    private UserList userList = new UserList();
    private AtomicLong userIdSequence = new AtomicLong(1);

    private volatile LocalDateTime lastLoadTime;

    @PostConstruct
    public void init() {
        createConfigDirIfNotExists();

        // 检查用户配置文件是否存在
        File userFile = new File(getUsersFilePath());
        if (!userFile.exists()) {
            // 用户配置文件不存在，创建默认管理员账户
            initDefaultAdmin();
        } else {
            // 加载现有用户
            loadUsers();
            logger.debug("加载了{}个用户", userList.getUsers().size());
        }

        // 设置最后加载时间
        lastLoadTime = LocalDateTime.now();
        logger.debug("用户配置初始化完成");
    }

    private void createConfigDirIfNotExists() {
        File dir = new File(configDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    private String getUsersFilePath() {
        return Paths.get(configDir.trim(), USERS_FILE).toString();
    }

    // 加载用户
    public UserList loadUsers() {
        File file = new File(getUsersFilePath());
        UserList result = new UserList();

        if (file.exists()) {
            try {
                JAXBContext context = JAXBContext.newInstance(UserList.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                result = (UserList) unmarshaller.unmarshal(file);

                // 找出最大ID，用于后续生成新ID
                result.getUsers().stream()
                        .mapToLong(User::getId)
                        .max()
                        .ifPresent(maxId -> userIdSequence.set(maxId + 1));

                // 将加载的结果分配给userList成员变量
                this.userList = result;
                logger.debug("加载了{}个用户", result.getUsers().size());
            } catch (JAXBException e) {
                logger.error("加载用户失败: {}", e.getMessage(), e);
                // 如果出错，初始化为空列表
                result = new UserList();
                this.userList = result;
            }
        } else {
            logger.warn("用户文件不存在: {}", file.getAbsolutePath());
        }

        return result;
    }

    // 保存用户
    public void saveUsers() {
        try {
            // 确保目录存在
            File dir = new File(configDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 保存用户列表
            File file = new File(configDir + "/user.xml");

            JAXBContext context = JAXBContext.newInstance(UserList.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.marshal(userList, file);

            System.out.println("已保存用户列表到文件: " + file.getAbsolutePath());
        } catch (Exception e) {
            System.err.println("保存用户列表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // 获取所有用户
    public List<User> getAllUsers() {
        return new ArrayList<>(userList.getUsers());
    }

    // 根据ID获取用户
    public Optional<User> getUserById(Long id) {
        return userList.getUsers().stream()
                .filter(user -> user.getId().equals(id))
                .findFirst();
    }

    // 根据用户名获取用户
    public Optional<User> getUserByUsername(String username) {
        // 确保用户数据已加载
        reloadUsersIfNeeded();

        return userList.getUsers().stream()
                .filter(user -> user.getUsername().equalsIgnoreCase(username))
                .findFirst();
    }

    // 添加用户
    public boolean addUser(User user) {
        try {
            logger.info("添加用户: {}", user.getUsername());

            // 确保ID值
            if (user.getId() == null) {
                user.setId(nextUserId());
            }

            // 确保创建时间
            if (user.getCreatedAt() == null) {
                user.setCreatedAt(LocalDateTime.now());
            }

            // 确保密码使用二次MD5加密
            if (user.getPassword() != null && !user.getPassword().isEmpty()) {
                // 检查密码是否已经是MD5格式
                if (isMD5Format(user.getPassword())) {
                    // 如果已经是MD5格式，再执行一次MD5
                    logger.debug("密码已经是MD5格式，执行二次MD5加密");
                    user.setPassword(EncryptionUtil.md5(user.getPassword()));
                } else {
                    // 否则执行二次MD5加密
                    logger.debug("密码不是MD5格式，执行二次MD5加密");
                    user.setPassword(EncryptionUtil.doubleHashPassword(user.getPassword()));
                }

                // 设置密码修改时间
                user.setPasswordModifiedAt(LocalDateTime.now());
            }

            // 添加用户到列表
            userList.getUsers().add(user);

            // 保存到配置文件
            saveUsers();

            logger.info("用户添加成功: {}, 已保存到配置文件", user.getUsername());
            return true;
        } catch (Exception e) {
            logger.error("添加用户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // 更新用户
    public boolean updateUser(Long id, User updatedUser) {
        try {
            logger.info("更新用户, ID: {}", id);

            // 查找用户
            for (int i = 0; i < userList.getUsers().size(); i++) {
                User user = userList.getUsers().get(i);
                if (user.getId().equals(id)) {
                    // 保留不应更新的字段
                    if (updatedUser.getCreatedAt() == null) {
                        updatedUser.setCreatedAt(user.getCreatedAt());
                    }

                    // 如果没有设置active，使用原来的值
                    if (updatedUser.getActive() == null) {
                        updatedUser.setActive(user.getActive());
                    }

                    // 记录是否更新了密码
                    boolean passwordChanged = false;

                    // 如果密码被更新，确保使用二次MD5加密
                    if (updatedUser.getPassword() != null && !updatedUser.getPassword().isEmpty()
                            && !updatedUser.getPassword().equals(user.getPassword())) {
                        // 检查密码是否已经是MD5格式
                        if (isMD5Format(updatedUser.getPassword())) {
                            // 如果已经是MD5格式，再执行一次MD5
                            logger.debug("更新的密码已经是MD5格式，执行二次MD5加密");
                            updatedUser.setPassword(EncryptionUtil.md5(updatedUser.getPassword()));
                        } else {
                            // 否则执行二次MD5加密
                            logger.debug("更新的密码不是MD5格式，执行二次MD5加密");
                            updatedUser.setPassword(EncryptionUtil.doubleHashPassword(updatedUser.getPassword()));
                        }

                        // 更新密码修改时间
                        updatedUser.setPasswordModifiedAt(LocalDateTime.now());
                        passwordChanged = true;
                    } else {
                        // 保留原来的密码和密码修改时间
                        updatedUser.setPassword(user.getPassword());
                        updatedUser.setPasswordModifiedAt(user.getPasswordModifiedAt());
                    }

                    // 处理密码有效期
                    boolean isAdmin = "admin".equalsIgnoreCase(user.getUsername()) || "ADMIN".equals(user.getRole());
                    if (isAdmin) {
                        // 管理员用户密码有效期设为永久，不允许修改
                        logger.info("管理员用户，保持密码有效期不变");
                        updatedUser.setPasswordExpiryDays(user.getPasswordExpiryDays());
                    } else if (updatedUser.getPasswordExpiryDays() != null &&
                        (user.getPasswordExpiryDays() == null ||
                         !updatedUser.getPasswordExpiryDays().equals(user.getPasswordExpiryDays()) ||
                         passwordChanged)) {
                        // 如果密码有效期被更新或密码被更新，则更新密码过期时间
                        logger.info("更新密码有效期: {} -> {}",
                            user.getPasswordExpiryDays(), updatedUser.getPasswordExpiryDays());
                    } else {
                        // 否则保留原来的密码有效期
                        updatedUser.setPasswordExpiryDays(user.getPasswordExpiryDays());
                    }

                    // 如果修改了密码或密码有效期，但修改时间为空，则设置为当前时间
                    boolean expiryDaysChanged = updatedUser.getPasswordExpiryDays() != null &&
                                               (user.getPasswordExpiryDays() == null ||
                                               !updatedUser.getPasswordExpiryDays().equals(user.getPasswordExpiryDays()));

                    if ((passwordChanged || expiryDaysChanged) && updatedUser.getPasswordModifiedAt() == null) {
                        updatedUser.setPasswordModifiedAt(LocalDateTime.now());
                    }

                    // 更新用户
                    userList.getUsers().set(i, updatedUser);
                    saveUsers();

                    logger.info("用户更新成功: ID={}, 用户名={}, 密码已修改={}, 密码有效期已修改={}",
                        id, updatedUser.getUsername(), passwordChanged, expiryDaysChanged);
                    return true;
                }
            }

            // 未找到用户
            logger.warn("未找到用户，ID: {}", id);
            return false;
        } catch (Exception e) {
            logger.error("更新用户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // 删除用户
    public boolean deleteUser(Long id) {
        boolean removed = userList.getUsers().removeIf(user -> user.getId().equals(id));
        if (removed) {
            saveUsers();
        }

        return removed;
    }

    /**
     * 验证用户登录
     * @return 返回登录结果状态：0-成功，1-用户不存在，2-密码错误，3-密码已过期，4-账户未激活
     */
    public int validateUser(String username, String password) {
        Optional<User> userOpt = getUserByUsername(username);

        if (userOpt.isPresent()) {
            User user = userOpt.get();
            logger.debug("验证用户: {}", username);

            // 检查账户是否激活
            if (!user.isActive()) {
                logger.debug("用户账户未激活: {}", username);
                return 4; // 账户未激活
            }

            // 如果是admin用户，跳过密码有效期检查
            boolean isAdmin = "admin".equalsIgnoreCase(username) || "ADMIN".equals(user.getRole());

            // 记录系统当前时间
            LocalDateTime now = LocalDateTime.now();
            logger.info("用户 {} 登录验证 - 当前系统时间: {}", username, now);

            // 检查密码是否过期（非admin用户）
            if (!isAdmin && user.getPasswordExpiryDays() != null && user.getPasswordModifiedAt() != null) {
                LocalDateTime expiryDate = user.getPasswordModifiedAt().plusDays(user.getPasswordExpiryDays());

                // 记录详细的日志信息
                logger.info("用户 {} 密码验证 - 修改时间: {}, 有效期: {}天, 过期时间: {}, 当前时间: {}",
                    username, user.getPasswordModifiedAt(), user.getPasswordExpiryDays(), expiryDate, now);

                if (now.isAfter(expiryDate)) {
                    logger.warn("用户密码已过期: {}, 密码修改时间: {}, 有效期: {}天, 过期时间: {}, 当前时间: {}",
                        username, user.getPasswordModifiedAt(), user.getPasswordExpiryDays(), expiryDate, now);
                    return 3; // 密码已过期
                } else {
                    logger.debug("密码有效期检查通过: {}, 密码修改时间: {}, 有效期: {}天, 过期时间: {}, 当前时间: {}",
                        username, user.getPasswordModifiedAt(), user.getPasswordExpiryDays(), expiryDate, now);
                }
            }

            // 如果输入的是MD5格式密码，需要二次MD5才能与存储的密码比较
            if (isMD5Format(password)) {
                String secondHash = EncryptionUtil.md5(password);
                boolean matches = user.getPassword().equals(secondHash);
                logger.debug("验证MD5密码结果: {}", matches ? "成功" : "失败");

                if (matches) {
                    // 更新最后登录时间
                    user.setLastLogin(LocalDateTime.now());
                    saveUsers();
                    return 0; // 登录成功
                }
            } else {
                // 如果输入的是明文密码，需要进行二次MD5加密
                String doubleHash = EncryptionUtil.doubleHashPassword(password);
                boolean matches = user.getPassword().equals(doubleHash);
                logger.debug("验证明文密码结果: {}", matches ? "成功" : "失败");

                if (matches) {
                    // 更新最后登录时间
                    user.setLastLogin(LocalDateTime.now());
                    saveUsers();
                    return 0; // 登录成功
                }
            }

            return 2; // 密码错误
        } else {
            logger.debug("用户不存在: {}", username);
            return 1; // 用户不存在
        }
    }

    public Optional<User> findByUsername(String username) {
        // 确保用户数据已加载
        reloadUsersIfNeeded();

        logger.debug("正在查找用户: {}", username);
        Optional<User> user = userList.getUsers().stream()
                .filter(u -> u.getUsername().equalsIgnoreCase(username))
                .findFirst();
        logger.debug("用户查找结果: {}", user.isPresent() ? "找到" : "未找到");
        return user;
    }

    /**
     * 重新加载用户配置（如果需要）
     * 可以在系统运行过程中调用，确保最新的用户数据被加载
     */
    public void reloadUsersIfNeeded() {
        // 如果用户列表为空，立即加载
        if (userList == null || userList.getUsers().isEmpty()) {
            logger.info("用户列表为空，重新加载用户数据");
            loadUsers();
            lastLoadTime = LocalDateTime.now();
            return;
        }

        // 如果上次加载时间为null或者已经过了60秒，重新加载
        LocalDateTime now = LocalDateTime.now();
        if (lastLoadTime == null || now.isAfter(lastLoadTime.plusSeconds(60))) {
            logger.debug("定期重新加载用户数据，距上次加载已超过60秒");
            loadUsers();
            lastLoadTime = now;
        }
    }

    private Long nextUserId() {
        return userIdSequence.getAndIncrement();
    }

    /**
     * 判断是否为MD5加密后的字符串
     */
    private boolean isMD5Format(String text) {
        if (text == null || text.length() != 32) {
            return false;
        }
        return text.matches("^[0-9a-fA-F]{32}$");
    }

    /**
     * 初始化默认管理员账户
     */
    private void initDefaultAdmin() {
        logger.debug("创建默认管理员账户");

        // 初始化用户列表
        userList = new UserList();

        // 创建默认管理员账户
        User admin = new User(
                userIdSequence.getAndIncrement(),
                "admin",
                EncryptionUtil.doubleHashPassword("hzhz2003"),
                "ADMIN",
                "System Administrator",
                "<EMAIL>"
        );

        // 设置指定的DN信息
        admin.setDn("EMAILADDRESS=<EMAIL>, CN=admin, OU=dev, O=unimas, L=hangzhou, ST=zhejiang, C=CN");

        // 设置密码修改时间和密码有效期
        admin.setPasswordModifiedAt(LocalDateTime.now());
        admin.setPasswordExpiryDays(9999); // 管理员账户设置为永久有效(9999天)

        // 添加到用户列表
        userList.getUsers().add(admin);

        // 保存到配置文件
        saveUsers();

        logger.debug("默认管理员账户创建成功");
    }

    /**
     * 重置用户密码有效期
     * 当管理员更新用户密码或密码有效期时，重新计算密码有效期
     * @param userId 用户ID
     * @param passwordExpiryDays 新的密码有效期（天数）
     * @param resetPassword 是否同时重置了密码
     * @return 是否成功更新
     */
    public boolean resetPasswordExpiryDate(Long userId, Integer passwordExpiryDays, boolean resetPassword) {
        try {
            Optional<User> userOpt = getUserById(userId);
            if (!userOpt.isPresent()) {
                logger.warn("无法重置密码有效期，用户不存在，ID: {}", userId);
                return false;
            }

            User user = userOpt.get();

            // 检查是否为管理员用户，管理员用户的密码有效期不可修改
            boolean isAdmin = "admin".equalsIgnoreCase(user.getUsername()) || "ADMIN".equals(user.getRole());
            if (isAdmin) {
                logger.info("管理员用户密码有效期为永久，不允许修改，用户名: {}", user.getUsername());
                return true; // 直接返回成功，不修改密码有效期
            }

            // 更新密码有效期天数
            if (passwordExpiryDays != null && passwordExpiryDays > 0) {
                user.setPasswordExpiryDays(passwordExpiryDays);
            }

            // 如果重置了密码或者当前没有密码修改时间，则更新密码修改时间为当前时间
            if (resetPassword || user.getPasswordModifiedAt() == null) {
                user.setPasswordModifiedAt(LocalDateTime.now());
                logger.info("用户密码有效期已重置，用户名: {}, 新的有效期: {}天，计算开始时间: {}",
                    user.getUsername(), user.getPasswordExpiryDays(), user.getPasswordModifiedAt());
            }

            // 保存更改
            saveUsers();
            return true;
        } catch (Exception e) {
            logger.error("重置密码有效期失败", e);
            return false;
        }
    }
}