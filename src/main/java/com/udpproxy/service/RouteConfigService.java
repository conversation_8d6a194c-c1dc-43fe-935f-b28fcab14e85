package com.udpproxy.service;

import com.udpproxy.model.RouteConfig;
import com.udpproxy.model.RouteConfigList;
import com.udpproxy.model.NetworkConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;

@Service
public class RouteConfigService {

    private static final Logger logger = LoggerFactory.getLogger(RouteConfigService.class);
    private static final String ROUTE_CONFIG_FILE = "/etc/unimas/tomcat/conf/route.sh";
    @Autowired
    private NetworkConfigService networkConfigService;



    public RouteConfigService() {
        // 不再需要加载配置文件，所有路由信息从系统命令获取
        logger.info("路由配置服务初始化完成，将从系统命令获取路由信息");
    }



    /**
     * 获取所有路由配置（只从系统命令获取，不再从配置文件获取）
     */
    public List<RouteConfig> getAllRoutes() {
        logger.info("获取所有路由配置，只从系统命令获取");

        // 只获取系统路由信息，不再从配置文件获取
        List<RouteConfig> systemRoutes = getSystemRoutes();

        logger.info("从系统获取到 {} 条路由记录", systemRoutes.size());
        return systemRoutes;
    }

    /**
     * 根据ID获取路由配置（只从系统路由中查找）
     */
    public Optional<RouteConfig> getRouteById(Long id) {
        logger.info("根据ID获取路由配置: {}", id);

        // 只从系统路由中查找
        List<RouteConfig> systemRoutes = getSystemRoutes();
        logger.info("系统中共有 {} 条路由", systemRoutes.size());

        for (RouteConfig route : systemRoutes) {
            logger.debug("检查路由: ID={}, 网络={}", route.getId(), route.getCidrNetwork());
            if (route.getId().equals(id)) {
                logger.info("找到匹配的路由: {}", route.getCidrNetwork());
                return Optional.of(route);
            }
        }

        logger.warn("未找到ID为 {} 的路由配置", id);
        return Optional.empty();
    }

    /**
     * 添加路由配置（直接执行系统命令，不保存到配置文件）
     */
    public RouteConfig addRoute(RouteConfig routeConfig) {
        logger.info("添加路由配置: {} -> {}", routeConfig.getCidrNetwork(), routeConfig.getGateway());

        // 界面添加的路由统一标记为via类型
        routeConfig.setRouteType("via");

        // 检查是否存在相同的路由（从系统路由中检查）
        List<RouteConfig> existingRoutes = getSystemRoutes();
        boolean exists = existingRoutes.stream()
                .anyMatch(r -> r.getTargetNetwork().equals(routeConfig.getTargetNetwork()) &&
                              r.getNetmask().equals(routeConfig.getNetmask()) &&
                              r.getGateway().equals(routeConfig.getGateway()));

        if (exists) {
            throw new IllegalArgumentException("相同的路由规则已存在");
        }

        // 设置路由类型（界面添加的路由统一使用via格式）
        routeConfig.setRouteType("via");

        // 执行路由命令并校验是否成功
        String command = routeConfig.generateRouteCommand();
        logger.info("执行添加路由命令: {}", command);
        boolean success = executeRouteCommandWithValidation(command);

        if (!success) {
            throw new RuntimeException("路由命令执行失败，路由规则未生效");
        }

        // 设置ID和时间戳（用于返回）
        routeConfig.setId(System.currentTimeMillis()); // 使用时间戳作为临时ID
        routeConfig.setCreatedAt(LocalDateTime.now());
        routeConfig.setUpdatedAt(LocalDateTime.now());

        logger.info("路由配置添加成功，路由规则已生效");

        // 将路由命令写入配置文件
        writeRouteToConfigFile(command, "add");

        // 清除缓存，强制下次重新获取路由信息
        invalidateRouteCache();

        // 立即刷新路由缓存，确保添加效果立即生效
        forceRefreshRouteCache();

        return routeConfig;
    }

    /**
     * 更新路由配置（直接执行系统命令）
     */
    public RouteConfig updateRoute(Long id, RouteConfig updatedRoute) {
        logger.info("更新路由配置: ID={}", id);

        // 获取现有路由
        Optional<RouteConfig> existingRoute = getRouteById(id);
        if (!existingRoute.isPresent()) {
            throw new IllegalArgumentException("路由配置不存在");
        }

        RouteConfig route = existingRoute.get();
        logger.info("找到现有路由: {} -> {} dev {}", route.getCidrNetwork(), route.getGateway(), route.getDeviceName());

        // 检查是否为可编辑的路由
        if (!route.isEditable()) {
            throw new IllegalArgumentException("该路由不允许编辑，只有via方式的路由才可以编辑");
        }

        // 检查是否有实际变更
        boolean hasChanges = !route.getGateway().equals(updatedRoute.getGateway()) ||
                            !route.getDeviceName().equals(updatedRoute.getDeviceName()) ||
                            !route.getPriority().equals(updatedRoute.getPriority());

        if (!hasChanges) {
            logger.info("路由配置无变更，直接返回成功");
            route.setUpdatedAt(LocalDateTime.now());
            return route;
        }

        logger.info("检测到路由配置变更: {} -> {} dev {}",
                   route.getCidrNetwork(), updatedRoute.getGateway(), updatedRoute.getDeviceName());

        // 生成正确的删除命令（根据当前路由的实际格式）
        String deleteCommand = generateCorrectDeleteCommand(route);
        boolean deleteSuccess = executeRouteCommandWithValidation(deleteCommand);

        if (!deleteSuccess) {
            logger.warn("删除旧路由失败: {}", deleteCommand);
            // 尝试其他可能的删除命令格式
            String alternativeDeleteCommand = generateAlternativeDeleteCommand(route);
            deleteSuccess = executeRouteCommandWithValidation(alternativeDeleteCommand);
            if (!deleteSuccess) {
                logger.warn("备用删除命令也失败: {}", alternativeDeleteCommand);
            }
        }

        // 更新路由信息
        route.setGateway(updatedRoute.getGateway());
        route.setDeviceName(updatedRoute.getDeviceName());
        route.setPriority(updatedRoute.getPriority());
        route.setUpdatedAt(LocalDateTime.now());

        // 添加新的路由（界面添加的路由统一使用via格式）
        String addCommand = route.generateRouteCommand();
        logger.info("执行添加新路由命令: {}", addCommand);
        boolean addSuccess = executeRouteCommandWithValidation(addCommand);

        if (!addSuccess) {
            throw new RuntimeException("更新路由失败，新路由规则未生效");
        }

        logger.info("路由配置更新成功，新路由规则已生效");

        // 将删除和添加命令写入配置文件
        writeRouteToConfigFile(deleteCommand, "delete");
        writeRouteToConfigFile(addCommand, "add");

        // 清除缓存，强制下次重新获取路由信息
        invalidateRouteCache();

        // 立即刷新路由缓存，确保更新效果立即生效
        forceRefreshRouteCache();

        return route;
    }

    /**
     * 删除路由配置（直接执行系统命令）
     */
    public boolean deleteRoute(Long id) {
        logger.info("删除路由配置: ID={}", id);

        // 获取路由配置
        Optional<RouteConfig> route = getRouteById(id);
        if (!route.isPresent()) {
            logger.warn("路由配置不存在: ID={}", id);
            return false;
        }

        RouteConfig routeConfig = route.get();
        logger.info("找到要删除的路由: {} -> {}", routeConfig.getCidrNetwork(), routeConfig.getGateway());

        // 检查是否为可删除的路由
        if (!routeConfig.isEditable()) {
            logger.error("该路由不允许删除，只有via方式的路由才可以删除: ID={}", id);
            throw new IllegalArgumentException("该路由不允许删除，只有via方式的路由才可以删除");
        }

        // 执行删除路由命令并校验是否成功
        String deleteCommand = routeConfig.generateDeleteRouteCommand();
        boolean success = executeRouteCommandWithValidation(deleteCommand);

        if (!success) {
            logger.error("删除路由失败，路由规则可能仍然存在");
            return false;
        }

        logger.info("路由配置删除成功，路由规则已移除");

        // 将删除命令写入配置文件
        writeRouteToConfigFile(deleteCommand, "delete");

        // 清除缓存，强制下次重新获取路由信息
        invalidateRouteCache();

        // 立即刷新路由缓存，确保删除效果立即生效
        forceRefreshRouteCache();

        return true;
    }

    /**
     * 执行路由命令并校验是否成功
     */
    private boolean executeRouteCommandWithValidation(String command) {
        try {
            logger.info("执行路由命令: {}", command);
            ProcessBuilder pb = new ProcessBuilder("bash", "-c", command);
            Process process = pb.start();

            // 读取标准输出
            BufferedReader outputReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String output = outputReader.lines().collect(Collectors.joining("\n"));
            outputReader.close();

            // 读取错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String errorOutput = errorReader.lines().collect(Collectors.joining("\n"));
            errorReader.close();

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                logger.info("路由命令执行成功，输出: {}", output);
                return true;
            } else {
                logger.error("路由命令执行失败，退出码: {}，标准输出: {}，错误信息: {}", exitCode, output, errorOutput);
                return false;
            }

        } catch (Exception e) {
            logger.error("执行路由命令异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行路由命令（兼容旧方法）
     */
    private void executeRouteCommand(String command) {
        boolean success = executeRouteCommandWithValidation(command);
        if (!success) {
            throw new RuntimeException("路由命令执行失败");
        }
    }

    /**
     * 生成正确的删除命令（根据路由类型），包含metric和table参数
     */
    private String generateCorrectDeleteCommand(RouteConfig route) {
        // 确定优先级，默认为100
        int metricValue = (route.getPriority() != null) ? route.getPriority() : 100;

        // 根据网卡名称确定table
        String table = getTableByDeviceName(route.getDeviceName());

        if ("via".equals(route.getRouteType())) {
            // Via路由：ip route del 20.0.0.0/24 via ******** dev eth0 metric 100 table manager
            return String.format("ip route del %s via %s dev %s metric %d table %s",
                               route.getCidrNetwork(), route.getGateway(), route.getDeviceName(), metricValue, table);
        } else {
            // 直连路由：ip route del 10.0.0.0/24 dev eth0 table manager
            return String.format("ip route del %s dev %s table %s",
                               route.getCidrNetwork(), route.getDeviceName(), table);
        }
    }

    /**
     * 生成备用删除命令，包含metric和table参数
     */
    private String generateAlternativeDeleteCommand(RouteConfig route) {
        // 确定优先级，默认为100
        int metricValue = (route.getPriority() != null) ? route.getPriority() : 100;

        // 根据网卡名称确定table
        String table = getTableByDeviceName(route.getDeviceName());

        if ("direct".equals(route.getRouteType())) {
            // 如果是直连路由，尝试via格式删除
            return String.format("ip route del %s via %s dev %s metric %d table %s",
                               route.getCidrNetwork(), route.getGateway(), route.getDeviceName(), metricValue, table);
        } else {
            // 如果是via路由，尝试简单格式删除
            return String.format("ip route del %s dev %s table %s",
                               route.getCidrNetwork(), route.getDeviceName(), table);
        }
    }

    /**
     * 清除路由缓存
     */
    private void invalidateRouteCache() {
        cachedRoutes = null;
        lastRouteUpdateTime = 0;
        logger.info("路由缓存已清除，下次获取路由时将重新从系统读取");
    }

    /**
     * 强制刷新路由缓存
     */
    private void forceRefreshRouteCache() {
        logger.info("强制刷新路由缓存");
        // 清除缓存
        invalidateRouteCache();
        // 立即重新获取路由信息
        getSystemRoutes();
        logger.info("路由缓存强制刷新完成");
    }

    /**
     * 获取可用的网卡设备列表
     * 过滤掉IP地址以172.18.18开头的网卡设备
     */
    public List<String> getAvailableDevices() {
        return networkConfigService.getAllInterfaces().stream()
                .filter(networkConfig -> {
                    // 过滤掉IP地址以172.18.18开头的网卡
                    String ipAddress = networkConfig.getIpAddress();
                    if (ipAddress != null && ipAddress.startsWith("172.18.18")) {
                        logger.info("过滤掉IP地址为172.18.18开头的网卡: " + networkConfig.getInterfaceName() + " (IP: " + ipAddress + ")");
                        return false;
                    }
                    return true;
                })
                .map(NetworkConfig::getInterfaceName)
                .collect(Collectors.toList());
    }

    /**
     * 验证IP地址格式
     */
    public boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证子网掩码格式
     */
    public boolean isValidNetmask(String netmask) {
        if (!isValidIpAddress(netmask)) {
            return false;
        }

        // 验证子网掩码的连续性
        String[] parts = netmask.split("\\.");
        int binaryMask = 0;

        for (String part : parts) {
            int octet = Integer.parseInt(part);
            binaryMask = (binaryMask << 8) | octet;
        }

        // 检查是否为有效的子网掩码（连续的1后面跟连续的0）
        String binaryString = Integer.toBinaryString(binaryMask);
        return binaryString.matches("1*0*");
    }

    // 路由信息缓存
    private volatile List<RouteConfig> cachedRoutes = null;
    private volatile long lastRouteUpdateTime = 0;
    private static final long ROUTE_CACHE_DURATION = 3000; // 3秒缓存，减少缓存时间提高响应速度

    /**
     * 通过 ip route 命令获取系统路由信息
     * 使用缓存机制减少频繁的系统调用
     */
    private List<RouteConfig> getSystemRoutes() {
        long currentTime = System.currentTimeMillis();

        // 如果缓存有效，直接返回缓存值
        if (cachedRoutes != null && (currentTime - lastRouteUpdateTime) < ROUTE_CACHE_DURATION) {
            logger.debug("使用缓存的路由信息，缓存中有 {} 条路由", cachedRoutes.size());
            return new ArrayList<>(cachedRoutes); // 返回副本避免并发修改
        }

        logger.info("路由缓存已过期或不存在，重新从系统获取路由信息");

        List<RouteConfig> systemRoutes = new ArrayList<>();
        AtomicLong systemIdCounter = new AtomicLong(10000); // 使用较大的ID避免与配置文件路由冲突

        try {
            // 执行 ip route show table all 命令获取完整路由信息
            ProcessBuilder pb = new ProcessBuilder("ip", "route", "show", "table", "all");
            Process process = pb.start();

            // 设置超时，避免进程阻塞
            boolean finished = process.waitFor(10, java.util.concurrent.TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                logger.warn("ip route 命令执行超时，已强制终止");
                return cachedRoutes != null ? new ArrayList<>(cachedRoutes) : new ArrayList<>();
            }

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                RouteConfig route = parseIpRouteOutput(line, systemIdCounter.getAndIncrement());
                if (route != null) {
                    systemRoutes.add(route);
                }
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                logger.warn("ip route 命令执行失败，退出码: " + exitCode);
            }

            reader.close();

            // 更新缓存
            cachedRoutes = systemRoutes;
            lastRouteUpdateTime = currentTime;
            logger.info("路由缓存已更新，共缓存 {} 条路由", systemRoutes.size());

        } catch (Exception e) {
            logger.error("执行 ip route 命令失败: " + e.getMessage(), e);
        }

        return systemRoutes;
    }

    /**
     * 解析 ip route show table all 命令输出的单行内容
     * 支持多种路由格式：
     * 1. 10.0.0.0/24 dev eth0 proto kernel scope link src ******** linkdown
     * 2. 20.0.0.0/24 via ******** dev eth0 linkdown
     * 3. default via ************ dev eth1 onlink
     * 4. ***********/24 via ******** dev eth0 metric 100 table manager
     * 5. default via ******** dev eth0 table business
     */
    private RouteConfig parseIpRouteOutput(String line, Long id) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        try {
            String trimmedLine = line.trim();
            logger.debug("解析路由行: {}", trimmedLine);

            // 跳过默认路由
            if (trimmedLine.startsWith("default")) {
                return null;
            }

            // 模式1: 带有 via 的路由 (支持metric和table参数)
            // 如: 20.0.0.0/24 via ******** dev eth0 metric 100 table manager
            Pattern viaPattern = Pattern.compile("^([0-9.]+)/([0-9]+)\\s+via\\s+([0-9.]+)\\s+dev\\s+(\\w+)(?:.*?metric\\s+(\\d+))?(?:.*?table\\s+(\\w+))?");
            Matcher viaMatcher = viaPattern.matcher(trimmedLine);

            if (viaMatcher.find()) {
                String networkAddress = viaMatcher.group(1);
                int prefixLength = Integer.parseInt(viaMatcher.group(2));
                String gateway = viaMatcher.group(3);
                String deviceName = viaMatcher.group(4);
                String metricStr = viaMatcher.group(5);
                String tableStr = viaMatcher.group(6);

                // 解析metric参数，没有设置时为null
                Integer priority = null;
                if (metricStr != null && !metricStr.isEmpty()) {
                    try {
                        priority = Integer.parseInt(metricStr);
                    } catch (NumberFormatException e) {
                        logger.warn("解析metric参数失败: {}, 设置为null", metricStr);
                    }
                }

                // 将前缀长度转换为子网掩码
                String netmask = prefixLengthToNetmask(prefixLength);

                // 创建路由配置对象
                RouteConfig route = new RouteConfig();
                route.setId(id);
                route.setTargetNetwork(networkAddress);
                route.setNetmask(netmask);
                route.setGateway(gateway);
                route.setDeviceName(deviceName);
                route.setPriority(priority);
                route.setCreatedAt(LocalDateTime.now());
                route.setUpdatedAt(LocalDateTime.now());

                // 标记为via路由
                route.setRouteType("via");

                logger.debug("解析到via路由: {} -> {} via {}, metric: {}, table: {}",
                           networkAddress + "/" + prefixLength, deviceName, gateway, priority, tableStr);
                return route;
            }

            // 模式2: 直连路由 (支持metric和table参数)
            // 如: 10.0.0.0/24 dev eth0 proto kernel scope link src ******** metric 100 table manager
            Pattern directPattern = Pattern.compile("^([0-9.]+)/([0-9]+)\\s+dev\\s+(\\w+).*?src\\s+([0-9.]+)(?:.*?metric\\s+(\\d+))?(?:.*?table\\s+(\\w+))?");
            Matcher directMatcher = directPattern.matcher(trimmedLine);

            if (directMatcher.find()) {
                String networkAddress = directMatcher.group(1);
                int prefixLength = Integer.parseInt(directMatcher.group(2));
                String deviceName = directMatcher.group(3);
                String sourceIp = directMatcher.group(4);
                String metricStr = directMatcher.group(5);
                String tableStr = directMatcher.group(6);

                // 解析metric参数，没有设置时为null
                Integer priority = null;
                if (metricStr != null && !metricStr.isEmpty()) {
                    try {
                        priority = Integer.parseInt(metricStr);
                    } catch (NumberFormatException e) {
                        logger.warn("解析metric参数失败: {}, 设置为null", metricStr);
                    }
                }

                // 将前缀长度转换为子网掩码
                String netmask = prefixLengthToNetmask(prefixLength);

                // 创建路由配置对象
                RouteConfig route = new RouteConfig();
                route.setId(id);
                route.setTargetNetwork(networkAddress);
                route.setNetmask(netmask);
                route.setGateway(sourceIp); // 使用源IP作为网关显示
                route.setDeviceName(deviceName);
                route.setPriority(priority);
                route.setCreatedAt(LocalDateTime.now());
                route.setUpdatedAt(LocalDateTime.now());

                // 标记为直连路由
                route.setRouteType("direct");

                logger.debug("解析到直连路由: {} -> {} src {}, metric: {}, table: {}",
                           networkAddress + "/" + prefixLength, deviceName, sourceIp, priority, tableStr);
                return route;
            }

        } catch (Exception e) {
            logger.warn("解析路由行失败: {} , 错误: {}", line, e.getMessage());
        }

        return null;
    }

    /**
     * 将前缀长度转换为子网掩码
     * 例如：24 -> *************
     */
    private String prefixLengthToNetmask(int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            return "*************"; // 默认值
        }

        int mask = 0xFFFFFFFF << (32 - prefixLength);

        int octet1 = (mask >>> 24) & 0xFF;
        int octet2 = (mask >>> 16) & 0xFF;
        int octet3 = (mask >>> 8) & 0xFF;
        int octet4 = mask & 0xFF;

        return octet1 + "." + octet2 + "." + octet3 + "." + octet4;
    }

    /**
     * 删除系统路由
     */
    private boolean deleteSystemRoute(Long id) {
        try {
            // 获取系统路由信息
            Optional<RouteConfig> systemRoute = getSystemRoutes().stream()
                    .filter(route -> route.getId().equals(id))
                    .findFirst();

            if (!systemRoute.isPresent()) {
                logger.warn("未找到要删除的系统路由，ID: {}", id);
                return false;
            }

            RouteConfig route = systemRoute.get();

            // 构建删除命令
            String deleteCommand = String.format("ip route del %s/%s",
                route.getTargetNetwork(),
                netmaskToPrefixLength(route.getNetmask()));

            // 执行删除命令
            executeRouteCommand(deleteCommand);

            logger.info("删除系统路由成功，ID: {}", id);
            return true;

        } catch (Exception e) {
            logger.error("删除系统路由失败，ID: " + id, e);
            return false;
        }
    }

    /**
     * 更新系统路由
     */
    private RouteConfig updateSystemRoute(Long id, RouteConfig updatedRoute) {
        try {
            // 获取原系统路由信息
            Optional<RouteConfig> systemRoute = getSystemRoutes().stream()
                    .filter(route -> route.getId().equals(id))
                    .findFirst();

            if (!systemRoute.isPresent()) {
                throw new IllegalArgumentException("系统路由不存在，ID: " + id);
            }

            RouteConfig originalRoute = systemRoute.get();

            // 构建删除原路由的命令
            String deleteCommand = String.format("ip route del %s/%s",
                originalRoute.getTargetNetwork(),
                netmaskToPrefixLength(originalRoute.getNetmask()));

            // 构建添加新路由的命令
            String addCommand = String.format("ip route add %s/%s via %s dev %s",
                originalRoute.getTargetNetwork(),
                netmaskToPrefixLength(originalRoute.getNetmask()),
                updatedRoute.getGateway(),
                updatedRoute.getDeviceName());

            // 执行命令
            executeRouteCommand(deleteCommand);
            executeRouteCommand(addCommand);

            // 创建更新后的路由对象
            RouteConfig updatedSystemRoute = new RouteConfig();
            updatedSystemRoute.setId(id);
            updatedSystemRoute.setTargetNetwork(originalRoute.getTargetNetwork());
            updatedSystemRoute.setNetmask(originalRoute.getNetmask());
            updatedSystemRoute.setGateway(updatedRoute.getGateway());
            updatedSystemRoute.setDeviceName(updatedRoute.getDeviceName());
            updatedSystemRoute.setCreatedAt(originalRoute.getCreatedAt());
            updatedSystemRoute.setUpdatedAt(LocalDateTime.now());

            logger.info("更新系统路由成功，ID: {}", id);
            return updatedSystemRoute;

        } catch (Exception e) {
            logger.error("更新系统路由失败，ID: " + id, e);
            throw new RuntimeException("更新系统路由失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将子网掩码转换为前缀长度
     * 例如：************* -> 24
     */
    private int netmaskToPrefixLength(String netmask) {
        try {
            String[] parts = netmask.split("\\.");
            int prefixLength = 0;

            for (String part : parts) {
                int octet = Integer.parseInt(part);
                prefixLength += Integer.bitCount(octet);
            }

            return prefixLength;
        } catch (Exception e) {
            logger.warn("转换子网掩码失败: " + netmask + ", 使用默认值24");
            return 24; // 默认值
        }
    }

    /**
     * 将路由命令写入配置文件
     * @param command 路由命令
     * @param operation 操作类型：add, delete
     */
    private void writeRouteToConfigFile(String command, String operation) {
        try {
            // 确保配置文件目录存在
            File configFile = new File(ROUTE_CONFIG_FILE);
            File configDir = configFile.getParentFile();
            if (!configDir.exists()) {
                configDir.mkdirs();
                logger.info("创建路由配置目录: {}", configDir.getAbsolutePath());
            }

            // 读取现有的路由配置
            List<String> existingRoutes = new ArrayList<>();
            if (configFile.exists()) {
                existingRoutes = readRouteConfigFile();
            }

            // 根据操作类型处理路由命令
            if ("add".equals(operation)) {
                // 添加路由：检查是否已存在，不存在则添加
                if (!existingRoutes.contains(command)) {
                    existingRoutes.add(command);
                    logger.info("添加路由命令到配置文件: {}", command);
                } else {
                    logger.info("路由命令已存在于配置文件中: {}", command);
                }
            } else if ("delete".equals(operation)) {
                // 删除路由：将 del 命令转换为对应的 add 命令并移除
                String addCommand = convertDeleteToAddCommand(command);
                if (addCommand != null && existingRoutes.remove(addCommand)) {
                    logger.info("从配置文件中移除路由命令: {}", addCommand);
                } else {
                    logger.warn("配置文件中未找到对应的路由命令: {}", addCommand);
                }
            }

            // 写入更新后的配置文件
            writeRouteConfigFile(existingRoutes);

            // 设置文件权限为可执行
            setFileExecutable(configFile);

        } catch (Exception e) {
            logger.error("写入路由配置文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 读取路由配置文件
     */
    private List<String> readRouteConfigFile() {
        List<String> routes = new ArrayList<>();
        try {
            File configFile = new File(ROUTE_CONFIG_FILE);
            if (configFile.exists()) {
                try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim();
                        if (!line.isEmpty() && !line.startsWith("#")) {
                            routes.add(line);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("读取路由配置文件失败: {}", e.getMessage(), e);
        }
        return routes;
    }

    /**
     * 写入路由配置文件
     */
    private void writeRouteConfigFile(List<String> routes) {
        try {
            File configFile = new File(ROUTE_CONFIG_FILE);
            try (PrintWriter writer = new PrintWriter(new FileWriter(configFile))) {
                writer.println("#!/bin/bash");
                writer.println("# Route configuration file - Auto-generated, do not edit manually");
                writer.println("");

                for (String route : routes) {
                    writer.println(route);
                }
            }
            logger.info("路由配置文件写入成功，共 {} 条路由", routes.size());
        } catch (Exception e) {
            logger.error("写入路由配置文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 将删除命令转换为对应的添加命令
     */
    private String convertDeleteToAddCommand(String deleteCommand) {
        if (deleteCommand != null && deleteCommand.startsWith("ip route del ")) {
            return deleteCommand.replace("ip route del ", "ip route add ");
        }
        return null;
    }

    /**
     * 设置文件为可执行
     */
    private void setFileExecutable(File file) {
        try {
            if (file.exists()) {
                file.setExecutable(true, false);
                logger.debug("设置文件可执行权限: {}", file.getAbsolutePath());
            }
        } catch (Exception e) {
            logger.warn("设置文件可执行权限失败: {}", e.getMessage());
        }
    }

    /**
     * 刷新路由信息（供网卡配置服务调用）
     */
    public void refreshRoutes() {
        logger.info("刷新路由信息");
        invalidateRouteCache();
        forceRefreshRouteCache();
    }

    /**
     * 根据设备名称确定对应的table
     * eth0 -> manager
     * eth1 -> business
     * 其他 -> manager (默认)
     */
    private String getTableByDeviceName(String deviceName) {
        if (deviceName == null) {
            return "manager";
        }

        if (deviceName.equals("eth1")) {
            return "business";
        } else {
            return "manager";
        }
    }
}
