package com.udpproxy.service;

import com.udpproxy.model.NetworkConfig;
import com.udpproxy.model.NetworkConfigList;
import org.springframework.stereotype.Service;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;

@Service
public class NetworkConfigService {

    private static final String CONFIG_DIR = "config";
    //private static final String G_FILE = "network_configs.xml";
    private static final String NETWORK_INTERFACES = "/etc/network/interfaces";
    private static final String RULE_CONFIG_FILE = "/etc/unimas/tomcat/conf/rule.sh";

    private NetworkConfigList configList;
    private AtomicLong idCounter = new AtomicLong(1);
    private static final Logger logger = LoggerFactory.getLogger(NetworkConfigService.class);


    public List<NetworkConfig> getAllInterfaces() {
        List<NetworkConfig> networkInterfaces = new ArrayList<>();
        AtomicLong id = new AtomicLong(1);
        // 用于跟踪ifconfig中发现的网卡
        Set<String> ifconfigInterfaces = new HashSet<>();
        try {
            // 获取网关信息
            Map<String, String> gatewayMap = getGatewayInfo();
            logger.info("获取到的网关信息: " + gatewayMap);

            // 从interfaces文件获取网卡配置（包括物理网卡和虚拟网卡）
            File interfacesFile = new File(NETWORK_INTERFACES);
            Map<String, NetworkConfig> interfacesMap = new HashMap<>();
            List<NetworkConfig> virtualInterfaces = new ArrayList<>();

            if (interfacesFile.exists()) {
                // 解析接口文件中的所有网卡信息
                List<NetworkConfig> allInterfaces = parseInterfacesFile(interfacesFile);

                // 分离物理网卡和虚拟网卡
                for (NetworkConfig config : allInterfaces) {
                    if (config.getInterfaceName().contains(":")) {
                        // 虚拟网卡
                        virtualInterfaces.add(config);
                    } else {
                        // 物理网卡
                        interfacesMap.put(config.getInterfaceName(), config);
                    }
                }

                logger.info("从interfaces文件找到 " + interfacesMap.size() + " 个物理网卡");
                logger.info("从interfaces文件找到 " + virtualInterfaces.size() + " 个虚拟网卡");
            }



            try{
                // 执行ifconfig命令获取网卡信息
                Process process = Runtime.getRuntime().exec("ifconfig");
                process.waitFor();

                // 读取命令输出
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                NetworkConfig currentInterface = null;

                while ((line = reader.readLine()) != null) {
                    // 检测新网卡开始（格式例如：eth0: flags=4099<UP,BROADCAST,MULTICAST> mtu 1500 或 eth0:1: flags=...）
                    if (line.matches("^\\S+:.*") && !line.trim().isEmpty()) {
                        // 保存之前处理的网卡（如果有）
                        if (currentInterface != null) {
                            // 添加网卡到列表（包括物理网卡和虚拟网卡）
                            networkInterfaces.add(currentInterface);
                        }

                        // 获取网卡名（保留虚拟网卡格式，如eth0:1）
                        String interfaceName;
                        if (line.matches("^\\S+:\\d+:.*")) {
                            // 虚拟网卡格式 eth0:1:
                            interfaceName = line.substring(0, line.indexOf(':', line.indexOf(':') + 1));
                        } else {
                            // 普通网卡格式 eth0:
                            interfaceName = line.substring(0, line.indexOf(':'));
                        }

                        // 跳过非eth开头的网卡
                        if (!interfaceName.startsWith("eth")) {
                            currentInterface = null;
                            continue;
                        }

                        // 记录在ifconfig中发现的网卡
                        ifconfigInterfaces.add(interfaceName);

                        // 创建新的网卡对象
                        currentInterface = new NetworkConfig();
                        currentInterface.setId(id.getAndIncrement());
                        currentInterface.setInterfaceName(interfaceName);

                        // 检测网卡是否启用（通过flags中的UP标志）
                        currentInterface.setEnabled(line.contains("<UP"));

                        // 设置MTU作为端口名
                        String portName = "";
                        if (line.contains("mtu")) {
                            portName = "MTU " + line.substring(line.indexOf("mtu") + 4).trim().split("\\s+")[0];
                        }
                        currentInterface.setPortName(portName);

                        // 设置网关（如果有）
                        if (gatewayMap.containsKey(interfaceName)) {
                            currentInterface.setGateway(gatewayMap.get(interfaceName));
                        }
                    }

                    // 解析IPv4地址行（格式例如：inet ******** netmask ************* broadcast **********）
                    else if (line.trim().startsWith("inet ") && currentInterface != null) {
                        String[] parts = line.trim().split("\\s+");
                        for (int i = 0; i < parts.length; i++) {
                            if (i > 0 && parts[i-1].equals("inet")) {
                                currentInterface.setIpAddress(parts[i]);
                            } else if (i > 0 && parts[i-1].equals("netmask")) {
                                currentInterface.setSubnetMask(parts[i]);
                            }
                        }
                    }
                }

                // 添加最后一个网卡
                if (currentInterface != null) {
                    networkInterfaces.add(currentInterface);
                }

                reader.close();
            }catch(IOException | InterruptedException se){
                logger.error("ifconfig命令获取网卡信息失败!");
                if (se instanceof InterruptedException) {
                    Thread.currentThread().interrupt();
                }
            }


            // 记录找到的网卡信息
            logger.info("通过ifconfig找到 " + networkInterfaces.size() + " 个网卡（包括物理网卡和虚拟网卡）");
            for (NetworkConfig config : networkInterfaces) {
                logger.info("网卡: " + config.getInterfaceName() +
                           ", IP: " + config.getIpAddress() +
                           ", 掩码: " + config.getSubnetMask() +
                           ", 网关: " + config.getGateway() +
                           ", 状态: " + (config.isEnabled() ? "启用" : "禁用"));
            }

            // 分离物理网卡和虚拟网卡
            List<NetworkConfig> physicalNetworkInterfaces = new ArrayList<>();
            List<NetworkConfig> ifconfigVirtualInterfaces = new ArrayList<>();

            for (NetworkConfig config : networkInterfaces) {
                if (config.getInterfaceName().contains(":")) {
                    ifconfigVirtualInterfaces.add(config);
                } else {
                    physicalNetworkInterfaces.add(config);
                }
            }

            // 合并网卡信息，优先使用interfaces文件中的配置，但网卡状态以ifconfig为准
            Map<String, NetworkConfig> mergedInterfaces = new HashMap<>();

            // 先添加ifconfig获取的物理网卡
            for (NetworkConfig config : physicalNetworkInterfaces) {
                if (config.getInterfaceName().startsWith("eth")) {
                    mergedInterfaces.put(config.getInterfaceName(), config);
                }
            }

            // 用interfaces文件中的物理网卡配置覆盖ifconfig获取的网卡（优先使用interfaces文件配置）
            for (Map.Entry<String, NetworkConfig> entry : interfacesMap.entrySet()) {
                String interfaceName = entry.getKey();
                NetworkConfig interfaceConfig = entry.getValue();

                // 跳过非eth开头的网卡
                if (!interfaceName.startsWith("eth")) {
                    continue;
                }

                // 如果ifconfig中已有该网卡，保留ifconfig获取的状态和ID，但使用interfaces文件的配置
                if (mergedInterfaces.containsKey(interfaceName)) {
                    NetworkConfig existingConfig = mergedInterfaces.get(interfaceName);
                    interfaceConfig.setId(existingConfig.getId());
                    interfaceConfig.setEnabled(existingConfig.isEnabled()); // 保留ifconfig获取的状态
                    interfaceConfig.setPortName(existingConfig.getPortName());
                    if (interfaceConfig.getGateway() == null) {
                        interfaceConfig.setGateway(existingConfig.getGateway());
                    }
                } else {
                    // 设置ID
                    interfaceConfig.setId(id.getAndIncrement());
                    // 如果ifconfig中没有该网卡，则设置为禁用状态
                    interfaceConfig.setEnabled(false);
                }

                // 更新或添加到合并后的集合
                mergedInterfaces.put(interfaceName, interfaceConfig);
            }

            // 合并虚拟网卡信息
            Map<String, NetworkConfig> mergedVirtualInterfaces = new HashMap<>();

            // 先添加ifconfig获取的虚拟网卡（状态以ifconfig为准）
            for (NetworkConfig config : ifconfigVirtualInterfaces) {
                if (config.getInterfaceName().startsWith("eth")) {
                    mergedVirtualInterfaces.put(config.getInterfaceName(), config);
                }
            }

            // 用interfaces文件中的虚拟网卡配置覆盖或补充ifconfig获取的虚拟网卡
            for (NetworkConfig interfaceConfig : virtualInterfaces) {
                String interfaceName = interfaceConfig.getInterfaceName();

                // 跳过非eth开头的虚拟网卡
                if (!interfaceName.startsWith("eth")) {
                    continue;
                }

                // 如果ifconfig中已有该虚拟网卡，保留ifconfig获取的状态，但使用interfaces文件的配置
                if (mergedVirtualInterfaces.containsKey(interfaceName)) {
                    NetworkConfig existingConfig = mergedVirtualInterfaces.get(interfaceName);
                    interfaceConfig.setId(existingConfig.getId());
                    interfaceConfig.setEnabled(existingConfig.isEnabled()); // 保留ifconfig获取的状态
                    interfaceConfig.setPortName(existingConfig.getPortName());
                } else {
                    // 设置ID
                    interfaceConfig.setId(id.getAndIncrement());
                    // 如果ifconfig中没有该虚拟网卡，则设置为禁用状态
                    interfaceConfig.setEnabled(false);
                }

                // 更新或添加到合并后的虚拟网卡集合
                mergedVirtualInterfaces.put(interfaceName, interfaceConfig);
            }

            // 转换为列表
            List<NetworkConfig> mergedInterfacesList = new ArrayList<>(mergedInterfaces.values());
            List<NetworkConfig> mergedVirtualInterfacesList = new ArrayList<>(mergedVirtualInterfaces.values());

            // 对网卡进行排序：按照网卡名称的数字部分排序
            mergedInterfacesList.sort((a, b) -> {
                try {
                    // 提取网卡名称中的数字部分（例如从"eth0"中提取"0"）
                    String numA = a.getInterfaceName().replaceAll("[^0-9]", "");
                    String numB = b.getInterfaceName().replaceAll("[^0-9]", "");

                    // 转换为整数进行比较
                    int indexA = Integer.parseInt(numA);
                    int indexB = Integer.parseInt(numB);

                    return Integer.compare(indexA, indexB);
                } catch (NumberFormatException e) {
                    // 如果解析失败，则按字符串比较
                    return a.getInterfaceName().compareTo(b.getInterfaceName());
                }
            });

            // 对虚拟网卡进行排序
            mergedVirtualInterfacesList.sort((a, b) -> {
                return a.getInterfaceName().compareTo(b.getInterfaceName());
            });

            // 记录合并后的网卡信息
            logger.info("合并后共有 " + mergedInterfacesList.size() + " 个物理网卡");
            logger.info("合并后共有 " + mergedVirtualInterfacesList.size() + " 个虚拟网卡");

            // 初始化configList，包含物理网卡和虚拟网卡
            List<NetworkConfig> allConfigs = new ArrayList<>(mergedInterfacesList);
            allConfigs.addAll(mergedVirtualInterfacesList);

            configList = new NetworkConfigList();
            configList.setConfigs(allConfigs);

            // 只返回物理网卡
            return mergedInterfacesList;

        } catch (Exception e) {
            logger.error("获取网卡失败: " + e.getMessage(), e);
        }

        // 如果出现异常，返回空列表
        configList = new NetworkConfigList();
        configList.setConfigs(new ArrayList<>());
        return new ArrayList<>();
    }

    /**
     * 解析/etc/network/interfaces文件获取虚拟网卡配置信息
     * @param interfacesFile interfaces配置文件
     * @return 虚拟网卡配置列表
     */
    private List<NetworkConfig> parseVirtualInterfaces(File interfacesFile) {
        List<NetworkConfig> virtualInterfaces = new ArrayList<>();
        AtomicLong id = new AtomicLong(1000); // 从1000开始，避免与物理网卡ID冲突

        try (BufferedReader reader = new BufferedReader(new FileReader(interfacesFile))) {
            String line;
            NetworkConfig currentInterface = null;

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 跳过注释和空行
                if (line.startsWith("#") || line.isEmpty()) {
                    continue;
                }

                // 检测虚拟网卡定义，格式如: "iface eth1:1 inet static"
                if (line.startsWith("iface") && line.contains(":") && line.contains("inet")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 4) {
                        String interfaceName = parts[1]; // 例如 eth1:1

                        // 确保这是虚拟网卡（包含冒号）
                        if (interfaceName.contains(":")) {
                            // 创建新的虚拟网卡对象
                            currentInterface = new NetworkConfig();
                            currentInterface.setId(id.getAndIncrement());
                            currentInterface.setInterfaceName(interfaceName);

                            // 虚拟网卡默认启用
                            boolean isStatic = "static".equals(parts[3]);
                            currentInterface.setEnabled(isStatic);

                            // 添加到虚拟网卡列表
                            virtualInterfaces.add(currentInterface);
                        } else {
                            currentInterface = null; // 不是虚拟网卡，设为null避免解析错误
                        }
                    }
                }
                // 解析虚拟网卡的IP地址
                else if (line.startsWith("address") && currentInterface != null && currentInterface.getInterfaceName().contains(":")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        currentInterface.setIpAddress(parts[1]);
                    }
                }
                // 解析虚拟网卡的子网掩码
                else if (line.startsWith("netmask") && currentInterface != null && currentInterface.getInterfaceName().contains(":")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        currentInterface.setSubnetMask(parts[1]);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("解析虚拟网卡配置失败: " + e.getMessage(), e);
        }

        return virtualInterfaces;
    }

    /**
     * 解析/etc/network/interfaces文件获取网卡配置信息
     * @param interfacesFile interfaces配置文件
     * @return 网卡配置列表
     */
    private List<NetworkConfig> parseInterfacesFile(File interfacesFile) {
        List<NetworkConfig> interfaces = new ArrayList<>();
        NetworkConfig currentInterface = null;

        try (java.io.BufferedReader reader = new java.io.BufferedReader(new FileReader(interfacesFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 跳过注释和空行
                if (line.startsWith("#") || line.isEmpty()) {
                    continue;
                }

                // 检测auto行，表示网卡自动启动
                if (line.startsWith("auto") && line.split("\\s+").length >= 2) {
                    String interfaceName = line.split("\\s+")[1];
                    // 查找是否已存在该网卡
                    boolean found = false;
                    for (NetworkConfig config : interfaces) {
                        if (config.getInterfaceName().equals(interfaceName)) {
                            currentInterface = config;
                            currentInterface.setEnabled(true);
                            found = true;
                            break;
                        }
                    }

                    // 如果不存在，创建新网卡
                    if (!found) {
                        currentInterface = new NetworkConfig();
                        currentInterface.setInterfaceName(interfaceName);
                        currentInterface.setEnabled(true);
                        interfaces.add(currentInterface);
                    }
                }

                // 检测新的网卡定义
                else if (line.startsWith("iface") && line.contains("inet")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 4) {
                        String interfaceName = parts[1];
                        String addressType = parts[3]; // static 或 dhcp

                        // 查找是否已存在该网卡
                        boolean found = false;
                        for (NetworkConfig config : interfaces) {
                            if (config.getInterfaceName().equals(interfaceName)) {
                                currentInterface = config;
                                found = true;
                                break;
                            }
                        }

                        // 如果不存在，创建新网卡
                        if (!found) {
                            currentInterface = new NetworkConfig();
                            currentInterface.setInterfaceName(interfaceName);
                            currentInterface.setEnabled(false); // 默认禁用，auto行会启用
                            interfaces.add(currentInterface);
                        }

                        // 设置状态（dhcp或static）
                        boolean isStatic = "static".equals(addressType);
                        if (!currentInterface.isEnabled()) {
                            // 如果没有通过auto行设置为启用，则根据addressType判断
                            currentInterface.setEnabled(isStatic);
                        }
                    }
                }
                // 解析IP地址
                else if (line.startsWith("address") && currentInterface != null) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        currentInterface.setIpAddress(parts[1]);
                    }
                }
                // 解析子网掩码
                else if (line.startsWith("netmask") && currentInterface != null) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        currentInterface.setSubnetMask(parts[1]);
                    }
                }
                // 解析网关
                else if (line.startsWith("gateway") && currentInterface != null) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        currentInterface.setGateway(parts[1]);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("解析网络接口配置文件失败", e);
        }

        return interfaces;
    }


    public Optional<NetworkConfig> getInterfaceById(Long id) {
        return configList.getConfigs().stream()
                .filter(i -> i.getId().equals(id))
                .findFirst();
    }

    public NetworkConfig addInterface(NetworkConfig networkConfig) {
        // 检查是否存在相同的接口名和IP
        boolean exists = configList.getConfigs().stream()
                .anyMatch(i -> (i.getInterfaceName().equals(networkConfig.getInterfaceName()) &&
                               i.getPortName().equals(networkConfig.getPortName())) ||
                              i.getIpAddress().equals(networkConfig.getIpAddress()));

        if (exists) {
            throw new IllegalArgumentException("接口名称、网口名或IP地址已存在");
        }

        // 检查网关唯一性约束
        if (networkConfig.getGateway() != null && !networkConfig.getGateway().trim().isEmpty() && networkConfig.isEnabled()) {
            checkGatewayUniqueness(null, networkConfig.getGateway(), networkConfig.isEnabled());
        }

        // 设置ID和时间戳
        networkConfig.setId(idCounter.getAndIncrement());
        networkConfig.setCreatedAt(LocalDateTime.now());
        networkConfig.setUpdatedAt(LocalDateTime.now());

        configList.getConfigs().add(networkConfig);
        saveNetworkConfig();

        // 更新rule规则文件
        updateRuleConfig(networkConfig);

        // 如果启用，执行实际的网络配置
        if (networkConfig.isEnabled()) {
            applyNetworkConfig(networkConfig);
        }

        return networkConfig;
    }

    public Optional<NetworkConfig> updateInterface(Long id, NetworkConfig updatedConfig) {
        Optional<NetworkConfig> existingConfig = getInterfaceById(id);

        if (existingConfig.isPresent()) {
            NetworkConfig networkConfig = existingConfig.get();

            // 检查是否与其他接口冲突
            boolean conflictsWithOther = configList.getConfigs().stream()
                    .filter(i -> !i.getId().equals(id))
                    .anyMatch(i -> (i.getInterfaceName().equals(updatedConfig.getInterfaceName()) &&
                                   i.getPortName().equals(updatedConfig.getPortName())) ||
                                  i.getIpAddress().equals(updatedConfig.getIpAddress()));

            if (conflictsWithOther) {
                throw new IllegalArgumentException("接口名称、网口名或IP地址与其他接口冲突");
            }

            // 检查网关唯一性约束
            if (updatedConfig.getGateway() != null && !updatedConfig.getGateway().trim().isEmpty() && updatedConfig.isEnabled()) {
                checkGatewayUniqueness(id, updatedConfig.getGateway(), updatedConfig.isEnabled());
            }

            // 保存旧的IP地址用于删除旧的rule规则
            String oldIpAddress = networkConfig.getIpAddress();

            // 更新字段
            networkConfig.setInterfaceName(updatedConfig.getInterfaceName());
            //networkConfig.setPortName(updatedConfig.getPortName());
            networkConfig.setIpAddress(updatedConfig.getIpAddress());
            networkConfig.setSubnetMask(updatedConfig.getSubnetMask());
            networkConfig.setGateway(updatedConfig.getGateway());
            networkConfig.setEnabled(updatedConfig.isEnabled());
            networkConfig.setUpdatedAt(LocalDateTime.now());

            saveNetworkConfig();

            // 更新rule规则文件（先删除旧规则，再添加新规则）
            updateRuleConfigForModification(oldIpAddress, networkConfig);

            // 应用网络配置变更
            if (networkConfig.isEnabled()) {
                applyNetworkConfig(networkConfig);
            } else {
                disableNetworkInterface(networkConfig);
            }

            return Optional.of(networkConfig);
        }

        return Optional.empty();
    }

    public boolean deleteInterface(Long id) {
        Optional<NetworkConfig> networkConfig = getInterfaceById(id);

        if (networkConfig.isPresent()) {
            // 如果接口已启用，先禁用它
            if (networkConfig.get().isEnabled()) {
                disableNetworkInterface(networkConfig.get());
            }

            configList.getConfigs().removeIf(i -> i.getId().equals(id));
            saveNetworkConfig();
            return true;
        }

        return false;
    }

    public boolean toggleInterfaceStatus(Long id, boolean enabled) {
        Optional<NetworkConfig> networkConfig = getInterfaceById(id);

        if (networkConfig.isPresent()) {
            NetworkConfig iface = networkConfig.get();

            // 如果要启用网卡且该网卡配置了网关，需要检查网关唯一性约束
            if (enabled && iface.getGateway() != null && !iface.getGateway().trim().isEmpty()) {
                checkGatewayUniqueness(id, iface.getGateway(), enabled);
            }

            iface.setEnabled(enabled);
            iface.setUpdatedAt(LocalDateTime.now());

            saveNetworkConfig();

            // 应用网络配置变更
            if (enabled) {
                applyNetworkConfig(iface);
            } else {
                disableNetworkInterface(iface);
            }

            return true;
        }

        return false;
    }


    private void saveNetworkConfig() {
        // 同时保存到XML文件和interfaces文件
        //saveNetworkConfigToXml();
        saveNetworkConfigToInterfaces();
    }

    /**
     * 保存网卡配置到XML文件（兼容旧代码）
     */
    // private void saveNetworkConfigToXml() {
    //     File configDir = new File(CONFIG_DIR);
    //     if (!configDir.exists()) {
    //         configDir.mkdirs();
    //     }

    //     File configFile = new File(configDir, NETWORK_CONFIG_FILE);
    //     try {
    //         JAXBContext context = JAXBContext.newInstance(NetworkConfigList.class, NetworkConfig.class);
    //         Marshaller marshaller = context.createMarshaller();
    //         marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
    //         marshaller.marshal(configList, configFile);
    //     } catch (JAXBException e) {
    //         e.printStackTrace();
    //     }
    // }

    /**
     * 保存网卡配置到interfaces文件
     * 格式示例：
     * # interfaces(5) file used by ifup(8) and ifdown(8)
     * # Include files from /etc/network/interfaces.d:
     * source-directory /etc/network/interfaces.d
     * #网卡信息
     * auto eth0 #启用状态，没有则为禁用
     * iface eth0 inet static #网卡名
     * address ******** #IP
     * netmask ************* #mask
     */
    private void saveNetworkConfigToInterfaces() {
        try {
            File interfacesFile = new File(NETWORK_INTERFACES);

            // 创建StringBuffer来构建interfaces文件内容
            StringBuilder content = new StringBuilder();

            // 添加头部注释
            content.append("# interfaces(5) file used by ifup(8) and ifdown(8)\n");
            content.append("# Include files from /etc/network/interfaces.d:\n");
            content.append("source-directory /etc/network/interfaces.d\n");

            // 分离物理网卡和虚拟网卡
            List<NetworkConfig> physicalInterfaces = new ArrayList<>();
            List<NetworkConfig> virtualInterfaces = new ArrayList<>();

            for (NetworkConfig config : configList.getConfigs()) {
                if (config.getInterfaceName().contains(":")) {
                    virtualInterfaces.add(config);
                } else {
                    physicalInterfaces.add(config);
                }
            }

            // 先添加物理网卡
            for (NetworkConfig config : physicalInterfaces) {
                // 如果启用了网卡，添加auto行
                if (config.isEnabled()) {
                    content.append("auto ").append(config.getInterfaceName())
                          .append("\n");
                }

                // 添加iface行
                content.append("iface ").append(config.getInterfaceName())
                      .append(" inet static \n");

                // 添加IP地址
                if (config.getIpAddress() != null && !config.getIpAddress().isEmpty()) {
                    content.append("address ").append(config.getIpAddress()).append("\n");
                }

                // 添加子网掩码
                if (config.getSubnetMask() != null && !config.getSubnetMask().isEmpty()) {
                    content.append("netmask ").append(config.getSubnetMask()).append("\n");
                }

                // 添加网关（如果有）
                if (config.getGateway() != null && !config.getGateway().isEmpty()) {
                    content.append("gateway ").append(config.getGateway()).append("\n");
                }

                // 每个网卡配置后添加空行
                content.append("\n");
            }

            // 添加虚拟网卡
            if (!virtualInterfaces.isEmpty()) {
                for (NetworkConfig config : virtualInterfaces) {
                    // 如果启用了网卡，添加auto行
                    if (config.isEnabled()) {
                        content.append("auto ").append(config.getInterfaceName()).append("\n");
                    }

                    // 添加iface行
                    content.append("iface ").append(config.getInterfaceName())
                          .append(" inet static\n");

                    // 添加IP地址
                    if (config.getIpAddress() != null && !config.getIpAddress().isEmpty()) {
                        content.append("address ").append(config.getIpAddress()).append("\n");
                    }

                    // 添加子网掩码
                    if (config.getSubnetMask() != null && !config.getSubnetMask().isEmpty()) {
                        content.append("netmask ").append(config.getSubnetMask()).append("\n");
                    }

                    // 添加空行
                    content.append("\n");
                }
            }

            // 写入到文件
            try (java.io.PrintWriter writer = new java.io.PrintWriter(interfacesFile)) {
                writer.print(content.toString());
            }

            logger.info("已将网卡配置保存到interfaces文件: " + NETWORK_INTERFACES);

        } catch (IOException e) {
            logger.error("保存网卡配置到interfaces文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 应用虚拟网卡配置
     * @param networkConfig 虚拟网卡配置
     */
    private void applyVirtualInterfaceConfig(NetworkConfig networkConfig) {
        try {
            // 确保是虚拟网卡
            if (!networkConfig.getInterfaceName().contains(":")) {
                logger.error("尝试以虚拟网卡方式配置非虚拟网卡: " + networkConfig.getInterfaceName());
                return;
            }

            System.out.println("应用虚拟网卡配置: " + networkConfig.getInterfaceName()
                + " - " + networkConfig.getIpAddress());

            // 构建虚拟网卡配置命令
            ProcessBuilder processBuilder = new ProcessBuilder();
            if (networkConfig.isEnabled()) {
                // 启用虚拟网卡
                processBuilder.command("ip", "addr", "add",
                    networkConfig.getIpAddress() + "/" + getCidr(networkConfig.getSubnetMask()),
                    "dev", networkConfig.getInterfaceName().split(":")[0], // 获取主网卡名
                    "label", networkConfig.getInterfaceName()); // 使用完整标签
            } else {
                // 禁用虚拟网卡
                processBuilder.command("ip", "addr", "del",
                    networkConfig.getIpAddress() + "/" + getCidr(networkConfig.getSubnetMask()),
                    "dev", networkConfig.getInterfaceName().split(":")[0]);
            }

            // 执行命令
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            logger.info("虚拟网卡配置命令执行完成，退出码: " + exitCode);

            // 更新interfaces文件
            updateInterfacesFile(networkConfig);

        } catch (Exception e) {
            logger.error("应用虚拟网卡配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新/etc/network/interfaces文件中的虚拟网卡配置
     * @param networkConfig 虚拟网卡配置
     */
    private void updateInterfacesFile(NetworkConfig networkConfig) {
        File interfacesFile = new File(NETWORK_INTERFACES);
        if (!interfacesFile.exists()) {
            logger.error("interfaces文件不存在，无法更新虚拟网卡配置");
            return;
        }

        try {
            // 读取当前interfaces文件内容
            List<String> fileLines = new ArrayList<>();
            try (BufferedReader reader = new BufferedReader(new FileReader(interfacesFile))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    fileLines.add(line);
                }
            }

            // 检查虚拟网卡是否已存在
            boolean interfaceExists = false;
            int existingStartIndex = -1;
            int existingEndIndex = -1;

            for (int i = 0; i < fileLines.size(); i++) {
                String line = fileLines.get(i).trim();
                if (line.startsWith("iface " + networkConfig.getInterfaceName() + " inet")) {
                    interfaceExists = true;
                    existingStartIndex = i;

                    // 查找此接口配置块的结束
                    for (int j = i + 1; j < fileLines.size(); j++) {
                        String nextLine = fileLines.get(j).trim();
                        if (nextLine.startsWith("iface") || nextLine.startsWith("auto") || nextLine.startsWith("source")) {
                            existingEndIndex = j - 1;
                            break;
                        }
                    }

                    if (existingEndIndex == -1) {
                        existingEndIndex = fileLines.size() - 1;
                    }

                    break;
                }
            }

            // 准备新的配置
            List<String> newConfig = new ArrayList<>();
            newConfig.add("iface " + networkConfig.getInterfaceName() + " inet static");
            newConfig.add("address " + networkConfig.getIpAddress());
            newConfig.add("netmask " + networkConfig.getSubnetMask());

            // 更新或添加配置
            if (interfaceExists) {
                // 替换现有配置
                fileLines.subList(existingStartIndex, existingEndIndex + 1).clear();
                fileLines.addAll(existingStartIndex, newConfig);
            } else {
                // 添加新配置到文件末尾
                fileLines.addAll(newConfig);
            }

            // 写回文件
            try (java.io.PrintWriter writer = new java.io.PrintWriter(interfacesFile)) {
                for (String line : fileLines) {
                    writer.println(line);
                }
            }

            logger.info("已更新interfaces文件中的虚拟网卡配置: " + networkConfig.getInterfaceName());

        } catch (IOException e) {
            logger.error("更新interfaces文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 应用网络配置
     * 注意：这里只是示例方法，实际实现需要根据操作系统和权限进行调整
     */
    private void applyNetworkConfig(NetworkConfig networkConfig) {
        try {
            //执行ifconfig ethName down命令
            ProcessBuilder processBuilder = new ProcessBuilder();
            processBuilder.command("ifconfig", networkConfig.getInterfaceName(), "down");
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            logger.info("ifconfig "+networkConfig.getInterfaceName()+" down命令执行完成，退出码: " + exitCode);
            //执行ifconfig $WANGKA $IP_MOD netmask $MASK_MOD
            processBuilder.command("ifconfig", networkConfig.getInterfaceName(), networkConfig.getIpAddress(), "netmask", networkConfig.getSubnetMask());
            process = processBuilder.start();
            exitCode = process.waitFor();
            logger.info("ifconfig "+networkConfig.getInterfaceName()+" "+networkConfig.getIpAddress()+" netmask "+networkConfig.getSubnetMask()+"命令执行完成，退出码: " + exitCode);
            //执行ifconfig $WANGKA up
            processBuilder.command("ifconfig", networkConfig.getInterfaceName(), "up");
            process = processBuilder.start();
            exitCode = process.waitFor();
            logger.info("ifconfig "+networkConfig.getInterfaceName()+" up命令执行完成，退出码: " + exitCode);


            // 检查是否为虚拟网卡
            // if (networkConfig.getInterfaceName().contains(":")) {
            //     applyVirtualInterfaceConfig(networkConfig);
            //     return;
            // }

            // 这里可以实现实际的网络配置逻辑，例如：
            // 在Linux系统上，可以使用ProcessBuilder执行ip或ifconfig命令
            // 在Windows系统上，可以使用netsh命令

            System.out.println("应用网络配置: " + networkConfig.getInterfaceName()
                + " - " + networkConfig.getIpAddress());

            // 示例：仅打印配置信息，不实际执行命令
            StringBuilder command = new StringBuilder();
            command.append("配置接口: ").append(networkConfig.getInterfaceName())
                  .append("\nIP地址: ").append(networkConfig.getIpAddress())
                  .append("\n子网掩码: ").append(networkConfig.getSubnetMask())
                  .append("\n网关: ").append(networkConfig.getGateway())
                  .append("\n状态: ").append(networkConfig.isEnabled() ? "启用" : "禁用");

            System.out.println(command.toString());


        } catch (Exception e) {
            System.err.println("应用网络配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 禁用网络接口
     */
    private void disableNetworkInterface(NetworkConfig networkConfig) {
        try {
            //执行ifconfig ethName down命令
            ProcessBuilder processBuilder = new ProcessBuilder();
            processBuilder.command("ifconfig", networkConfig.getInterfaceName(), "down");
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            logger.info("ifconfig "+networkConfig.getInterfaceName()+" down命令执行完成，退出码: " + exitCode);

            // 检查是否为虚拟网卡
            // if (networkConfig.getInterfaceName().contains(":")) {
            //     networkConfig.setEnabled(false);
            //     applyVirtualInterfaceConfig(networkConfig);
            //     return;
            // }

            System.out.println("禁用网络接口: " + networkConfig.getInterfaceName());

            // 实际执行命令的示例（注释掉）
            /*
            ProcessBuilder processBuilder = new ProcessBuilder();
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                // Windows命令
                processBuilder.command("netsh", "interface", "set", "interface",
                    networkConfig.getInterfaceName(), "disabled");
            } else {
                // Linux命令
                processBuilder.command("ip", "link", "set", "dev",
                    networkConfig.getInterfaceName(), "down");
            }
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            System.out.println("命令执行完成，退出码: " + exitCode);
            */

        } catch (Exception e) {
            System.err.println("禁用网络接口失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取指定物理网卡的所有虚拟网卡
     * @param parentName 物理网卡名称
     * @return 虚拟网卡列表
     */
    public List<NetworkConfig> getVirtualInterfacesByParent(String parentName) {
        List<NetworkConfig> virtualInterfaces = new ArrayList<>();

        for (NetworkConfig config : configList.getConfigs()) {
            // 检查是否是指定物理网卡的虚拟网卡 (格式如 eth0:1)
            if (config.getInterfaceName().startsWith(parentName + ":")) {
                virtualInterfaces.add(config);
            }
        }

        return virtualInterfaces;
    }

    /**
     * 获取下一个可用的虚拟网卡名称
     * @param parentName 物理网卡名称
     * @return 下一个可用的虚拟网卡名称
     */
    public String getNextVirtualInterfaceName(String parentName) {
        int maxIndex = 0;

        // 查找当前最大的索引号
        for (NetworkConfig config : configList.getConfigs()) {
            if (config.getInterfaceName().startsWith(parentName + ":")) {
                try {
                    String indexStr = config.getInterfaceName().substring((parentName + ":").length());
                    int index = Integer.parseInt(indexStr);
                    if (index > maxIndex) {
                        maxIndex = index;
                    }
                } catch (NumberFormatException e) {
                    // 忽略格式不正确的索引
                }
            }
        }

        // 返回下一个可用的索引号
        return parentName + ":" + (maxIndex + 1);
    }

    /**
     * 添加虚拟网卡
     * @param virtualInterface 虚拟网卡配置
     * @return 添加的虚拟网卡配置
     */
    public NetworkConfig addVirtualInterface(NetworkConfig virtualInterface) {
        // 确保是虚拟网卡（名称包含冒号）
        if (!virtualInterface.getInterfaceName().contains(":")) {
            throw new IllegalArgumentException("不是有效的虚拟网卡名称，应为 interface:index 格式");
        }

        // 检查是否与现有网卡重复
        boolean exists = configList.getConfigs().stream()
                .anyMatch(i -> i.getInterfaceName().equals(virtualInterface.getInterfaceName()) ||
                              i.getIpAddress().equals(virtualInterface.getIpAddress()));

        if (exists) {
            throw new IllegalArgumentException("虚拟网卡名称或IP地址已存在");
        }

        // 设置ID和时间戳
        virtualInterface.setId(idCounter.getAndIncrement());
        virtualInterface.setCreatedAt(LocalDateTime.now());
        virtualInterface.setUpdatedAt(LocalDateTime.now());

        // 添加到配置列表
        configList.getConfigs().add(virtualInterface);
        saveNetworkConfig();

        // 如果启用，应用网络配置
        if (virtualInterface.isEnabled()) {
            applyNetworkConfig(virtualInterface);
        }

        return virtualInterface;
    }

    /**
     * 将子网掩码转换为CIDR格式
     */
    private int getCidr(String subnetMask) {
        int cidr = 0;
        String[] parts = subnetMask.split("\\.");
        for (String part : parts) {
            int value = Integer.parseInt(part);
            for (int i = 7; i >= 0; i--) {
                if ((value & (1 << i)) != 0) {
                    cidr++;
                } else {
                    break;
                }
            }
        }
        return cidr;
    }

    /**
     * 更新rule规则配置文件
     * 根据网卡类型写入不同的table规则
     * eth0 -> table manager
     * eth1 -> table business
     */
    private void updateRuleConfig(NetworkConfig networkConfig) {
        try {
            String ipAddress = networkConfig.getIpAddress();
            String interfaceName = networkConfig.getInterfaceName();

            if (ipAddress == null || ipAddress.trim().isEmpty()) {
                logger.info("网卡 " + interfaceName + " 没有IP地址，跳过rule规则配置");
                return;
            }

            // 确保目录存在
            File ruleFile = new File(RULE_CONFIG_FILE);
            File parentDir = ruleFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
                logger.info("创建rule配置目录: " + parentDir.getAbsolutePath());
            }

            // 读取现有的rule规则
            List<String> existingRules = new ArrayList<>();
            if (ruleFile.exists()) {
                try (BufferedReader reader = new BufferedReader(new FileReader(ruleFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 过滤掉当前IP的规则（避免重复）
                        if (!line.contains("from " + ipAddress + " ")) {
                            existingRules.add(line);
                        }
                    }
                }
            }

            // 根据网卡类型确定table
            String table = getTableByInterfaceName(interfaceName);

            // 添加新的rule规则
            String newRule = "ip rule add from " + ipAddress + " table " + table;
            existingRules.add(newRule);

            // 添加默认路由规则
            String defaultRouteRule = generateDefaultRouteRule(networkConfig);
            if (defaultRouteRule != null) {
                existingRules.add(defaultRouteRule);
            }

            // 写入rule配置文件
            try (PrintWriter writer = new PrintWriter(new FileWriter(ruleFile))) {
                for (String rule : existingRules) {
                    writer.println(rule);
                }
            }

            logger.info("已更新rule配置文件: " + RULE_CONFIG_FILE + ", 添加规则: " + newRule +
                       (defaultRouteRule != null ? ", 默认路由规则: " + defaultRouteRule : ""));

        } catch (Exception e) {
            logger.error("更新rule配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 修改网卡时更新rule规则配置文件
     * 先删除旧IP的规则，再添加新IP的规则
     */
    private void updateRuleConfigForModification(String oldIpAddress, NetworkConfig networkConfig) {
        try {
            String newIpAddress = networkConfig.getIpAddress();
            String interfaceName = networkConfig.getInterfaceName();

            if (newIpAddress == null || newIpAddress.trim().isEmpty()) {
                logger.info("网卡 " + interfaceName + " 没有新IP地址，跳过rule规则配置");
                return;
            }

            // 确保目录存在
            File ruleFile = new File(RULE_CONFIG_FILE);
            File parentDir = ruleFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
                logger.info("创建rule配置目录: " + parentDir.getAbsolutePath());
            }

            // 读取现有的rule规则
            List<String> existingRules = new ArrayList<>();
            if (ruleFile.exists()) {
                try (BufferedReader reader = new BufferedReader(new FileReader(ruleFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 过滤掉旧IP、新IP的规则以及对应网卡的默认路由规则（避免重复）
                        if (!line.contains("from " + oldIpAddress + " ") &&
                            !line.contains("from " + newIpAddress + " ") &&
                            !isDefaultRouteRuleForInterface(line, interfaceName)) {
                            existingRules.add(line);
                        }
                    }
                }
            }

            // 根据网卡类型确定table
            String table = getTableByInterfaceName(interfaceName);

            // 添加新的rule规则
            String newRule = "ip rule add from " + newIpAddress + " table " + table;
            existingRules.add(newRule);

            // 添加默认路由规则
            String defaultRouteRule = generateDefaultRouteRule(networkConfig);
            if (defaultRouteRule != null) {
                existingRules.add(defaultRouteRule);
            }

            // 写入rule配置文件
            try (PrintWriter writer = new PrintWriter(new FileWriter(ruleFile))) {
                for (String rule : existingRules) {
                    writer.println(rule);
                }
            }

            logger.info("已更新rule配置文件: " + RULE_CONFIG_FILE +
                       ", 删除旧规则(IP: " + oldIpAddress + "), 添加新规则: " + newRule +
                       (defaultRouteRule != null ? ", 默认路由规则: " + defaultRouteRule : ""));

        } catch (Exception e) {
            logger.error("修改时更新rule配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据网卡名称确定对应的table
     * eth0 -> manager
     * eth1 -> business
     * 其他 -> manager (默认)
     */
    private String getTableByInterfaceName(String interfaceName) {
        if (interfaceName == null) {
            return "manager";
        }

        if (interfaceName.equals("eth1")) {
            return "business";
        } else {
            return "manager";
        }
    }

    /**
     * 生成默认路由规则
     * 格式：ip route add default via IP dev ETH table business
     * 如果没有网关就是IP地址，如果有网关就是网关地址
     */
    private String generateDefaultRouteRule(NetworkConfig networkConfig) {
        String interfaceName = networkConfig.getInterfaceName();
        String ipAddress = networkConfig.getIpAddress();
        String gateway = networkConfig.getGateway();

        if (interfaceName == null || ipAddress == null || ipAddress.trim().isEmpty()) {
            logger.info("网卡 " + interfaceName + " 缺少必要信息，跳过默认路由规则生成");
            return null;
        }

        // 根据网卡类型确定table
        String table = getTableByInterfaceName(interfaceName);

        // 确定使用的IP地址：如果有网关就用网关地址，否则用IP地址
        String viaAddress;
        if (gateway != null && !gateway.trim().isEmpty()) {
            viaAddress = gateway.trim();
            logger.info("网卡 " + interfaceName + " 使用网关地址: " + viaAddress);
        } else {
            viaAddress = ipAddress.trim();
            logger.info("网卡 " + interfaceName + " 没有网关，使用IP地址: " + viaAddress);
        }

        // 生成默认路由规则
        String defaultRouteRule = "ip route add default via " + viaAddress + " dev " + interfaceName + " table " + table;

        logger.info("生成默认路由规则: " + defaultRouteRule);
        return defaultRouteRule;
    }

    /**
     * 检查规则是否为指定网卡的默认路由规则
     */
    private boolean isDefaultRouteRuleForInterface(String rule, String interfaceName) {
        if (rule == null || interfaceName == null) {
            return false;
        }

        // 检查是否包含默认路由关键字和对应的网卡名
        return rule.contains("ip route add default") &&
               rule.contains("dev " + interfaceName + " ");
    }

    /**
     * 安全获取JSON对象的字符串属性，如果属性不存在，返回空字符串
     */
    private String getStringOrEmpty(JSONObject jsonObj, String key) {
        return jsonObj.has(key) ? jsonObj.getString(key) : "";
    }

    /**
     * 获取网关信息
     * @return 网卡名称到网关IP的映射
     */
    private Map<String, String> getGatewayInfo() {
        Map<String, String> gatewayMap = new HashMap<>();

        try {
            // 执行route -n命令获取路由信息
            Process process = Runtime.getRuntime().exec("route -n");
            process.waitFor();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean headerPassed = false;

            while ((line = reader.readLine()) != null) {
                // 跳过标题行
                if (!headerPassed) {
                    if (line.contains("Destination") && line.contains("Gateway") && line.contains("Iface")) {
                        headerPassed = true;
                    }
                    continue;
                }

                // 解析路由表行
                String[] parts = line.trim().split("\\s+");
                if (parts.length >= 8) {
                    String destination = parts[0];
                    String gateway = parts[1];
                    String interfaceName = parts[7]; // Iface列

                    // 只保存默认网关（0.0.0.0）或有实际网关IP的记录
                    if (destination.equals("0.0.0.0") && !gateway.equals("0.0.0.0")) {
                        gatewayMap.put(interfaceName, gateway);
                    }
                }
            }

            reader.close();

        } catch (IOException | InterruptedException e) {
            logger.error("执行route命令失败: " + e.getMessage(), e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        return gatewayMap;
    }

    /**
     * 检查网关唯一性约束
     * 规则：当某张网卡已经配置了网关地址并且启用后，其他网卡不能再设置网关地址
     * @param excludeId 要排除的网卡ID（用于更新时排除自己）
     * @param gateway 要设置的网关地址
     * @param enabled 网卡是否启用
     * @throws IllegalArgumentException 如果违反网关唯一性约束
     */
    private void checkGatewayUniqueness(Long excludeId, String gateway, boolean enabled) {
        if (gateway == null || gateway.trim().isEmpty() || !enabled) {
            return; // 如果网关为空或网卡未启用，则不需要检查
        }

        // 查找是否已有其他启用的网卡配置了网关
        boolean hasOtherGateway = configList.getConfigs().stream()
                .filter(config -> excludeId == null || !config.getId().equals(excludeId)) // 排除当前编辑的网卡
                .anyMatch(config -> config.isEnabled() &&
                                   config.getGateway() != null &&
                                   !config.getGateway().trim().isEmpty());

        if (hasOtherGateway) {
            throw new IllegalArgumentException("不允许设置网关地址，已有其他网卡配置了网关且处于启用状态");
        }
    }
}