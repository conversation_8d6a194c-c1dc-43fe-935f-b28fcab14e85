package com.udpproxy.controller;

import com.udpproxy.model.User;
import com.udpproxy.service.UserService;
import com.udpproxy.util.MD5Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/admin/users")
public class UserManagementController {

    @Autowired
    private UserService userService;
    
    private static final Logger logger = LoggerFactory.getLogger(UserManagementController.class);

    // 显示用户列表
    @GetMapping(value = {"", "/"})
    public String listUsers(Model model, HttpSession session) {
        // 检查用户是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }
        
        // 检查用户是否为管理员
        String username = (String) session.getAttribute("username");
        if (username == null) {
            return "redirect:/login";
        }
        
        Optional<User> currentUserOpt = userService.findByUsername(username);
        if (!currentUserOpt.isPresent() || !"ADMIN".equals(currentUserOpt.get().getRole())) {
            model.addAttribute("error", "您没有权限访问用户管理");
            return "error";
        }
        
        model.addAttribute("activeTab", "users");
        model.addAttribute("users", userService.getAllUsers());
        return "user-management";
    }

    // 显示添加用户表单
    @GetMapping("/add")
    public String showAddUserForm(Model model) {
        logger.info("显示添加用户表单");
        model.addAttribute("activeTab", "users");
        model.addAttribute("user", new User()); // 创建空用户对象
        return "user-form";
    }

    // 显示编辑用户表单
    @GetMapping("/edit/{id}")
    public String showEditUserForm(@PathVariable String id, Model model) {
        logger.info("显示编辑用户表单, ID={}", id);
        try {
            Optional<User> userOpt = userService.getUserById(Long.valueOf(id));
            if (!userOpt.isPresent()) {
                model.addAttribute("error", "找不到指定ID的用户");
                return "redirect:/admin/users/";
            }
            
            model.addAttribute("activeTab", "users");
            model.addAttribute("user", userOpt.get());
            return "user-form";
        } catch (Exception e) {
            logger.error("加载用户信息出错: " + e.getMessage(), e);
            model.addAttribute("error", "加载用户信息出错: " + e.getMessage());
            return "redirect:/admin/users/";
        }
    }

    // 处理保存用户表单
    @PostMapping("/save")
    public String saveUser(@RequestParam(required = false) Long id,
                          @RequestParam String username,
                          @RequestParam(required = false) String password,
                          @RequestParam(required = false) String confirmPassword,
                          @RequestParam(required = false) String dn,
                          @RequestParam(required = false) Integer passwordExpiryDays,
                          @RequestParam(required = false) String fullName,
                          @RequestParam(required = false) String email,
                          @RequestParam(required = false, defaultValue = "USER") String role,
                          @RequestParam(required = false, defaultValue = "true") boolean active,
                          @RequestParam boolean isNew,
                          RedirectAttributes redirectAttributes) {
        
        logger.info("保存用户, isNew={}, username={}", isNew, username);
        
        try {
            User user = new User();
            user.setId(id);
            user.setUsername(username);
            user.setDn(dn);
            user.setPasswordExpiryDays(passwordExpiryDays != null ? passwordExpiryDays : 30);
            user.setFullName(fullName);
            user.setEmail(email); // email可以为null
            
            // 确保角色设置正确
            user.setRole(role != null && !role.isEmpty() ? role : "USER");
            
            // 确保账户状态正确
            user.setActive(active);
            
            // 处理密码
            if (isNew) {
                if (password == null || password.trim().isEmpty()) {
                    redirectAttributes.addFlashAttribute("error", "新用户必须设置密码");
                    return "redirect:/admin/users/add";
                }
                
                // 验证两次输入的密码是否一致
                if (!password.equals(confirmPassword)) {
                    redirectAttributes.addFlashAttribute("error", "两次输入的密码不一致");
                    return "redirect:/admin/users/add";
                }
                
                // 加密密码
                user.setPassword(MD5Util.encrypt(password));
                userService.addUser(user);
                redirectAttributes.addFlashAttribute("success", "用户 " + username + " 添加成功");
            } else {
                // 如果密码不为空，则更新密码
                if (password != null && !password.trim().isEmpty()) {
                    // 验证两次输入的密码是否一致
                    if (!password.equals(confirmPassword)) {
                        redirectAttributes.addFlashAttribute("error", "两次输入的密码不一致");
                        return "redirect:/admin/users/edit/" + id;
                    }
                    
                    user.setPassword(MD5Util.encrypt(password));
                }
                userService.updateUser(id, user);
                redirectAttributes.addFlashAttribute("success", "用户 " + username + " 更新成功");
            }
            
            return "redirect:/admin/users/";
        } catch (Exception e) {
            logger.error("保存用户出错: " + e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "保存用户失败: " + e.getMessage());
            return "redirect:/admin/users/" + (isNew ? "add" : "edit/" + id);
        }
    }

    // 处理删除用户
    @PostMapping("/delete/{id}")
    public String deleteUser(@PathVariable String id, RedirectAttributes redirectAttributes) {
        logger.info("删除用户, ID={}", id);
        try {
            boolean deleted = userService.deleteUser(Long.valueOf(id));
            if (deleted) {
                redirectAttributes.addFlashAttribute("success", "用户删除成功");
            } else {
                redirectAttributes.addFlashAttribute("error", "用户不存在或已删除");
            }
        } catch (Exception e) {
            logger.error("删除用户出错: " + e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除用户失败: " + e.getMessage());
        }
        return "redirect:/admin/users/";
    }

    // 修改测试入口点，确保与其他方法一致
    @GetMapping("/test")
    public String testUserManagement(Model model) {
        System.out.println("测试用户管理页面");
        model.addAttribute("activeTab", "users");
        model.addAttribute("users", userService.getAllUsers());
        return "user-management";
    }
} 