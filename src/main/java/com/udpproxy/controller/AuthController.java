package com.udpproxy.controller;

import com.udpproxy.service.UserService;
import com.udpproxy.util.MD5Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserService userService;

    @GetMapping("/login")
    public String showLoginPage() {
        return "login"; // 这会返回login.html模板
    }
    
    @GetMapping("/logout-user")
    public String customLogout(HttpServletRequest request, HttpServletResponse response) {
        logger.info("用户请求登出");
        
        // 获取当前认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        
        // 如果认证存在，执行登出操作
        if (auth != null) {
            String username = auth.getName();
            logger.info("用户 {} 登出系统", username);
            
            // 使用SecurityContextLogoutHandler执行登出
            new SecurityContextLogoutHandler().logout(request, response, auth);
            
            // 清除会话
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.invalidate();
                logger.info("用户会话已清除");
            }
        }
        
        // 重定向到登录页面
        return "redirect:/login?logout";
    }

    // 如果需要自定义登录成功处理，可以使用这个方法
    @GetMapping("/login-success")
    public String loginSuccess(HttpSession session) {
        // 设置会话属性
        session.setAttribute("authenticated", true);
        
        // 记录成功登录
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated()) {
            String username = auth.getName();
            session.setAttribute("username", username);
            System.out.println("用户 " + username + " 登录成功，重定向到首页");
        }
        
        // 确保重定向到正确的首页路径
        return "redirect:/index";
    }
} 