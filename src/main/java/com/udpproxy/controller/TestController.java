package com.udpproxy.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/test")
public class TestController {

    @GetMapping("/session")
    public Map<String, Object> getSessionInfo(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取会话ID
        result.put("sessionId", session.getId());
        
        // 获取会话中的属性
        result.put("authenticated", session.getAttribute("authenticated"));
        result.put("username", session.getAttribute("username"));
        
        // 获取Spring Security认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            result.put("security_authenticated", auth.isAuthenticated());
            result.put("security_principal", auth.getPrincipal());
            result.put("security_name", auth.getName());
            result.put("security_authorities", auth.getAuthorities());
        }
        
        return result;
    }
    
    @GetMapping("/goto-index")
    public String gotoIndex() {
        return "redirect:/index";
    }

    @GetMapping("/test")
    @ResponseBody
    public String test() {
        return "应用程序正常运行！当前时间: " + new java.util.Date();
    }
    
    @GetMapping("/login")
    public String login() {
        return "login";
    }

    // 添加一个简单的HTML测试页面
    @GetMapping("/test-page")
    public String testPage() {
        return "test-page";
    }
} 