package com.udpproxy.controller;

import com.udpproxy.model.NetworkConfig;
import com.udpproxy.model.RouteConfig;
import com.udpproxy.service.NetworkConfigService;
import com.udpproxy.service.RouteConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/network")
public class NetworkController {

    @Autowired
    private NetworkConfigService networkConfigService;

    @Autowired
    private RouteConfigService routeConfigService;

    @GetMapping
    public String listInterfaces(Model model, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        List<NetworkConfig> interfaces = networkConfigService.getAllInterfaces();
        List<RouteConfig> routes = routeConfigService.getAllRoutes();
        model.addAttribute("interfaces", interfaces);
        model.addAttribute("routes", routes);
        return "network-page";
    }

    @GetMapping("/new")
    public String newInterfaceForm(Model model, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        model.addAttribute("networkInterface", new NetworkConfig());
        model.addAttribute("isNew", true);
        return "network-config-page";
    }

    @GetMapping("/edit/{id}")
    public String editInterfaceForm(@PathVariable Long id, Model model, HttpSession session, RedirectAttributes redirectAttributes) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        Optional<NetworkConfig> networkInterface = networkConfigService.getInterfaceById(id);
        if (!networkInterface.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "网络接口不存在");
            return "redirect:/network";
        }

        model.addAttribute("networkInterface", networkInterface.get());
        model.addAttribute("isNew", false);
        return "network-config-page";
    }

    @PostMapping("/save")
    public String saveInterface(@ModelAttribute NetworkConfig networkInterface,
                             @RequestParam("isNew") boolean isNew,
                             RedirectAttributes redirectAttributes,
                             HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        try {
            if (isNew) {
                networkConfigService.addInterface(networkInterface);
                redirectAttributes.addFlashAttribute("success", "网络接口创建成功");
            } else {
                networkConfigService.updateInterface(networkInterface.getId(), networkInterface);
                redirectAttributes.addFlashAttribute("success", "网络接口更新成功");
            }

            // 刷新路由信息
            routeConfigService.refreshRoutes();

            return "redirect:/network";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "保存网络接口失败: " + e.getMessage());
            return isNew ? "redirect:/network/new" : "redirect:/network/edit/" + networkInterface.getId();
        }
    }

    @PostMapping("/toggle/{id}")
    @ResponseBody
    public Map<String, Object> toggleInterface(@PathVariable Long id, @RequestParam("enabled") boolean enabled, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录，请先登录");
            return response;
        }

        try {
            boolean updated = networkConfigService.toggleInterfaceStatus(id, enabled);

            if (updated) {
                // 刷新路由信息
                routeConfigService.refreshRoutes();

                response.put("success", true);
                response.put("message", enabled ? "接口已启用" : "接口已禁用");
            } else {
                response.put("success", false);
                response.put("message", "接口不存在");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新接口状态失败: " + e.getMessage());
            e.printStackTrace();
        }

        return response;
    }

    @PostMapping("/delete/{id}")
    @ResponseBody
    public Map<String, Object> deleteInterface(@PathVariable Long id, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录，请先登录");
            return response;
        }

        try {
            boolean deleted = networkConfigService.deleteInterface(id);

            if (deleted) {
                // 刷新路由信息
                routeConfigService.refreshRoutes();

                response.put("success", true);
                response.put("message", "接口已删除");
            } else {
                response.put("success", false);
                response.put("message", "接口不存在");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除接口失败: " + e.getMessage());
            e.printStackTrace();
        }

        return response;
    }

    @GetMapping("/detect")
    @ResponseBody
    public ResponseEntity<List<NetworkConfig>> detectInterfaces(HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return ResponseEntity.status(401).build();
        }

        // 这里应该实现实际的网络接口检测逻辑
        // 为简单起见，这里返回一个空列表
        List<NetworkConfig> interfaces = new ArrayList<>();

        return ResponseEntity.ok(interfaces);
    }

    // 虚拟网卡相关接口

    /**
     * 获取指定物理网卡下的所有虚拟网卡
     */
    @GetMapping("/virtual/{parentName}")
    @ResponseBody
    public List<NetworkConfig> getVirtualInterfaces(@PathVariable String parentName, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return new ArrayList<>();
        }

        return networkConfigService.getVirtualInterfacesByParent(parentName);
    }

    /**
     * 获取虚拟网卡详情
     */
    @GetMapping("/virtual/detail/{id}")
    @ResponseBody
    public ResponseEntity<?> getVirtualInterfaceDetail(@PathVariable Long id, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("未登录");
        }

        Optional<NetworkConfig> virtualInterface = networkConfigService.getInterfaceById(id);
        if (!virtualInterface.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("虚拟网卡不存在");
        }

        return ResponseEntity.ok(virtualInterface.get());
    }

    /**
     * 获取下一个可用的虚拟网卡名称
     */
    @GetMapping("/virtual/nextName/{parentName}")
    @ResponseBody
    public String getNextVirtualInterfaceName(@PathVariable String parentName, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "";
        }

        return networkConfigService.getNextVirtualInterfaceName(parentName);
    }

    /**
     * 保存虚拟网卡
     */
    @PostMapping("/virtual/save")
    @ResponseBody
    public Map<String, Object> saveVirtualInterface(@RequestBody Map<String, Object> request, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录");
            return response;
        }

        try {
            // 构建虚拟网卡对象
            NetworkConfig virtualInterface = new NetworkConfig();

            // 如果是编辑，设置ID
            if (request.get("id") != null && !request.get("isNew").toString().equals("true")) {
                virtualInterface.setId(Long.parseLong(request.get("id").toString()));
            }

            virtualInterface.setInterfaceName(request.get("interfaceName").toString());
            virtualInterface.setIpAddress(request.get("ipAddress").toString());
            virtualInterface.setSubnetMask(request.get("subnetMask").toString());
            virtualInterface.setEnabled((Boolean) request.get("enabled"));

            // 保存虚拟网卡
            if (request.get("isNew").toString().equals("true")) {
                networkConfigService.addVirtualInterface(virtualInterface);
                response.put("message", "虚拟网卡创建成功");
            } else {
                networkConfigService.updateInterface(virtualInterface.getId(), virtualInterface);
                response.put("message", "虚拟网卡更新成功");
            }

            // 刷新路由信息
            routeConfigService.refreshRoutes();

            response.put("success", true);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存虚拟网卡失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 删除虚拟网卡
     */
    @PostMapping("/virtual/delete/{id}")
    @ResponseBody
    public Map<String, Object> deleteVirtualInterface(@PathVariable Long id, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录");
            return response;
        }

        try {
            boolean deleted = networkConfigService.deleteInterface(id);
            if (deleted) {
                // 刷新路由信息
                routeConfigService.refreshRoutes();

                response.put("success", true);
                response.put("message", "虚拟网卡删除成功");
            } else {
                response.put("success", false);
                response.put("message", "虚拟网卡不存在");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除虚拟网卡失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 切换虚拟网卡状态
     */
    @PostMapping("/virtual/toggle/{id}")
    @ResponseBody
    public Map<String, Object> toggleVirtualInterfaceStatus(@PathVariable Long id,
                                                     @RequestParam("enabled") boolean enabled,
                                                     HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录");
            return response;
        }

        try {
            boolean updated = networkConfigService.toggleInterfaceStatus(id, enabled);
            if (updated) {
                // 刷新路由信息
                routeConfigService.refreshRoutes();

                response.put("success", true);
                response.put("message", (enabled ? "启用" : "禁用") + "虚拟网卡成功");
            } else {
                response.put("success", false);
                response.put("message", "虚拟网卡不存在");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", (enabled ? "启用" : "禁用") + "虚拟网卡失败: " + e.getMessage());
        }

        return response;
    }

    // 路由配置相关接口

    /**
     * 获取所有路由配置
     */
    @GetMapping("/routes")
    @ResponseBody
    public List<RouteConfig> getAllRoutes(HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return new ArrayList<>();
        }

        return routeConfigService.getAllRoutes();
    }

    /**
     * 获取可用的网卡设备列表
     */
    @GetMapping("/routes/devices")
    @ResponseBody
    public List<String> getAvailableDevices(HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return new ArrayList<>();
        }

        return routeConfigService.getAvailableDevices();
    }

    /**
     * 添加路由配置
     */
    @PostMapping("/routes/add")
    @ResponseBody
    public Map<String, Object> addRoute(@RequestBody Map<String, Object> request, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录");
            return response;
        }

        try {
            // 验证输入参数
            String targetNetwork = (String) request.get("targetNetwork");
            String netmask = (String) request.get("netmask");
            String gateway = (String) request.get("gateway");
            String deviceName = (String) request.get("deviceName");

            if (targetNetwork == null || targetNetwork.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "目标网络不能为空");
                return response;
            }

            if (netmask == null || netmask.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "子网掩码不能为空");
                return response;
            }

            if (gateway == null || gateway.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "路由地址不能为空");
                return response;
            }

            if (deviceName == null || deviceName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "网卡设备不能为空");
                return response;
            }

            // 验证IP地址格式
            if (!routeConfigService.isValidIpAddress(targetNetwork)) {
                response.put("success", false);
                response.put("message", "目标网络IP地址格式不正确");
                return response;
            }

            if (!routeConfigService.isValidIpAddress(gateway)) {
                response.put("success", false);
                response.put("message", "路由地址格式不正确");
                return response;
            }

            if (!routeConfigService.isValidNetmask(netmask)) {
                response.put("success", false);
                response.put("message", "子网掩码格式不正确");
                return response;
            }

            // 创建路由配置
            RouteConfig routeConfig = new RouteConfig();
            routeConfig.setTargetNetwork(targetNetwork.trim());
            routeConfig.setNetmask(netmask.trim());
            routeConfig.setGateway(gateway.trim());
            routeConfig.setDeviceName(deviceName.trim());

            // 添加路由
            RouteConfig savedRoute = routeConfigService.addRoute(routeConfig);

            response.put("success", true);
            response.put("message", "路由配置添加成功");
            response.put("route", savedRoute);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "添加路由配置失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 更新路由配置
     */
    @PostMapping("/routes/update/{id}")
    @ResponseBody
    public Map<String, Object> updateRoute(@PathVariable Long id, @RequestBody Map<String, Object> request, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录");
            return response;
        }

        try {
            // 验证输入参数
            String gateway = (String) request.get("gateway");
            String deviceName = (String) request.get("deviceName");

            if (gateway == null || gateway.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "路由地址不能为空");
                return response;
            }

            if (deviceName == null || deviceName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "网卡设备不能为空");
                return response;
            }

            // 验证IP地址格式
            if (!routeConfigService.isValidIpAddress(gateway)) {
                response.put("success", false);
                response.put("message", "路由地址格式不正确");
                return response;
            }

            // 创建更新的路由配置
            RouteConfig updatedRoute = new RouteConfig();
            updatedRoute.setGateway(gateway.trim());
            updatedRoute.setDeviceName(deviceName.trim());

            // 更新路由
            RouteConfig savedRoute = routeConfigService.updateRoute(id, updatedRoute);

            response.put("success", true);
            response.put("message", "路由配置更新成功");
            response.put("route", savedRoute);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新路由配置失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 删除路由配置
     */
    @PostMapping("/routes/delete/{id}")
    @ResponseBody
    public Map<String, Object> deleteRoute(@PathVariable Long id, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录");
            return response;
        }

        try {
            boolean deleted = routeConfigService.deleteRoute(id);
            if (deleted) {
                response.put("success", true);
                response.put("message", "路由配置删除成功");
            } else {
                response.put("success", false);
                response.put("message", "路由配置不存在");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除路由配置失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取路由配置详情
     */
    @GetMapping("/routes/{id}")
    @ResponseBody
    public ResponseEntity<?> getRouteDetail(@PathVariable Long id, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("未登录");
        }

        Optional<RouteConfig> route = routeConfigService.getRouteById(id);
        if (!route.isPresent()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("路由配置不存在");
        }

        return ResponseEntity.ok(route.get());
    }
}