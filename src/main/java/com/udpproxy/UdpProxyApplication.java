package com.udpproxy;

import com.udpproxy.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com.udpproxy", "com.example.udp"})
public class UdpProxyApplication {

    private static final Logger logger = LoggerFactory.getLogger(UdpProxyApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(UdpProxyApplication.class, args);
    }


}