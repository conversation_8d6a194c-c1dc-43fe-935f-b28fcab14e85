package com.udpproxy.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 调试日志工具类
 */
public class DebugLogger {
    private static final Logger logger = LoggerFactory.getLogger(DebugLogger.class);
    
    /**
     * 记录调试信息
     */
    public static void log(String message) {
        System.out.println("[DEBUG] " + message);
        logger.debug(message);
    }
    
    /**
     * 记录错误信息
     */
    public static void error(String message, Throwable e) {
        System.err.println("[ERROR] " + message);
        if (e != null) {
            e.printStackTrace();
        }
        logger.error(message, e);
    }
    
    /**
     * 记录错误信息（无异常对象）
     */
    public static void error(String message) {
        error(message, null);
    }
    
    /**
     * 记录警告信息
     */
    public static void warn(String message) {
        System.out.println("[WARN] " + message);
        logger.warn(message);
    }
    
    /**
     * 记录信息级别日志
     */
    public static void info(String message) {
        System.out.println("[INFO] " + message);
        logger.info(message);
    }
    
    /**
     * 记录对象信息
     */
    public static void logObject(String prefix, Object obj) {
        System.out.println("[DEBUG] " + prefix + ": " + (obj != null ? obj.toString() : "null"));
        logger.debug(prefix + ": {}", obj);
    }
} 