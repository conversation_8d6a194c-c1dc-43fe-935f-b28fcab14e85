package com.udpproxy.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.math.BigInteger;

public class EncryptionUtil {
    
    /**
     * 对字符串进行单次MD5加密
     * @param input 需要加密的字符串
     * @return MD5加密后的字符串
     */
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            BigInteger no = new BigInteger(1, messageDigest);
            String hashtext = no.toString(16);
            while (hashtext.length() < 32) {
                hashtext = "0" + hashtext;
            }
            return hashtext;
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 对输入的密码进行二次MD5加密 - 用于存储密码
     * @param password 原始密码
     * @return 二次MD5加密后的密码
     */
    public static String doubleHashPassword(String password) {
        return md5(md5(password));
    }
    
    /**
     * 验证用户的一次MD5加密的密码是否与存储的二次MD5加密密码匹配
     * @param md5Password 前端传来的一次MD5加密后的密码
     * @param storedPassword 存储的二次MD5加密密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String md5Password, String storedPassword) {
        String secondHash = md5(md5Password);
        return secondHash.equals(storedPassword);
    }
} 