package com.udpproxy.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

@XmlRootElement(name = "networkConfigs")
@XmlAccessorType(XmlAccessType.FIELD)
public class NetworkConfigList {
    
    @XmlElement(name = "networkConfig")
    private List<NetworkConfig> configs;
    
    public NetworkConfigList() {
        this.configs = new ArrayList<>();
    }
    
    public List<NetworkConfig> getConfigs() {
        return configs;
    }
    
    public void setConfigs(List<NetworkConfig> configs) {
        this.configs = configs;
    }
} 