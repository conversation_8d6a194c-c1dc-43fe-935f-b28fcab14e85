package com.udpproxy.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.time.LocalDateTime;

@XmlRootElement(name = "serviceInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class UdpServiceInfo {
    
    @XmlElement
    private Long id; 
    
    @XmlElement
    private String name;
    
    @XmlElement
    private String status;
    
    @XmlElement
    private String transportIp;
    
    @XmlElement
    private Integer startPort;
    
    @XmlElement
    private Integer endPort;
    
    @XmlElement
    private String auditOption;
    
    @XmlElement
    private String description;
    
    @XmlElement
    private Integer network;
    
    @XmlElement
    private String syncStatus;
    
    @XmlElement
    private String updateStatus;
    
    @XmlElement
    private LocalDateTime createdAt;
    
    @XmlElement
    private LocalDateTime updatedAt;
    
    @XmlElement
    private String messageType;
    
    @XmlElement
    private String multicast;
    
    @XmlElement
    private String sendaddrmap;
    
    // 无参构造函数
    public UdpServiceInfo() {
    }
    
    // 所有属性的构造函数
    public UdpServiceInfo(Long id, String name, String status, String transportIp, 
                          Integer startPort, Integer endPort, String auditOption) {
        this.id = id;
        this.name = name;
        this.status = status;
        this.transportIp = transportIp;
        this.startPort = startPort;
        this.endPort = endPort;
        this.auditOption = auditOption;
    }
    
    // 从UdpService创建UdpServiceInfo的构造函数
    public UdpServiceInfo(UdpService service) {
        this.id = service.getId();
        this.name = service.getName();
        this.status = service.getStatus();
        this.transportIp = service.getTransportIp();
        this.startPort = service.getStartPort();
        this.endPort = service.getEndPort();
        this.auditOption = service.getAudit();
        this.network = service.getNetwork();
        this.description = service.getDescription();
        this.messageType = service.getMessageType();
        this.createdAt = service.getCreatedAt();
        this.updatedAt = service.getUpdatedAt();
        this.multicast = service.getMulticast();
        this.sendaddrmap = service.getSendaddrmap();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getTransportIp() {
        return transportIp;
    }
    
    public void setTransportIp(String transportIp) {
        this.transportIp = transportIp;
    }
    
    public Integer getStartPort() {
        return startPort;
    }
    
    public void setStartPort(Integer startPort) {
        this.startPort = startPort;
    }
    
    public Integer getEndPort() {
        return endPort;
    }
    
    public void setEndPort(Integer endPort) {
        this.endPort = endPort;
    }
    
    public String getAuditOption() {
        return auditOption;
    }
    
    public void setAuditOption(String auditOption) {
        this.auditOption = auditOption;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getNetwork() {
        return network;
    }
    
    public void setNetwork(Integer network) {
        this.network = network;
    }
    
    public String getSyncStatus() {
        return syncStatus;
    }
    
    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }
    
    public String getUpdateStatus() {
        return updateStatus;
    }
    
    public void setUpdateStatus(String updateStatus) {
        this.updateStatus = updateStatus;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getMessageType() {
        return messageType;
    }
    
    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
    
    public String getMulticast() {
        return multicast;
    }
    
    public void setMulticast(String multicast) {
        this.multicast = multicast;
    }
    
    public String getSendaddrmap() {
        return sendaddrmap;
    }
    
    public void setSendaddrmap(String sendaddrmap) {
        this.sendaddrmap = sendaddrmap;
    }
} 