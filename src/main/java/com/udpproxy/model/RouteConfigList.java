package com.udpproxy.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

@XmlRootElement(name = "routeConfigs")
@XmlAccessorType(XmlAccessType.FIELD)
public class RouteConfigList {
    
    @XmlElement(name = "routeConfig")
    private List<RouteConfig> routes;
    
    public RouteConfigList() {
        this.routes = new ArrayList<>();
    }
    
    public List<RouteConfig> getRoutes() {
        return routes;
    }
    
    public void setRoutes(List<RouteConfig> routes) {
        this.routes = routes;
    }
}
