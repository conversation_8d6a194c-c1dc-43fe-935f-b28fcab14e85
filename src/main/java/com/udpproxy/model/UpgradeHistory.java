package com.udpproxy.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

public class UpgradeHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("patch_name")
    private String patchName;
    
    @JsonProperty("time")
    private String time;
    
    @JsonProperty("user")
    private String user;
    
    @JsonProperty("uuid")
    private String uuid;

    public UpgradeHistory() {
    }

    public UpgradeHistory(String patchName, String time, String user, String uuid) {
        this.patchName = patchName;
        this.time = time;
        this.user = user;
        this.uuid = uuid;
    }

    public String getPatchName() {
        return patchName;
    }

    public void setPatchName(String patchName) {
        this.patchName = patchName;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
} 