package com.udpproxy.model;

import com.udpproxy.util.LocalDateTimeAdapter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.time.LocalDateTime;

@XmlRootElement(name = "routeConfig")
@XmlAccessorType(XmlAccessType.FIELD)
public class RouteConfig {

    @XmlElement
    private Long id;

    @XmlElement
    private String targetNetwork; // 目标网络IP

    @XmlElement
    private String netmask; // 子网掩码

    @XmlElement
    private String gateway; // 路由地址(网关)

    @XmlElement
    private String deviceName; // 网卡设备名称

    @XmlElement
    private String routeType; // 路由类型：via（通过网关）或 direct（直连）

    @XmlElement
    private String routeSource; // 路由来源：ui（界面添加）或 system（系统路由）

    @XmlElement
    private Integer priority; // 路由优先级：0-65535，默认100

    @XmlElement
    @XmlJavaTypeAdapter(LocalDateTimeAdapter.class)
    private LocalDateTime createdAt;

    @XmlElement
    @XmlJavaTypeAdapter(LocalDateTimeAdapter.class)
    private LocalDateTime updatedAt;

    public RouteConfig() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTargetNetwork() {
        return targetNetwork;
    }

    public void setTargetNetwork(String targetNetwork) {
        this.targetNetwork = targetNetwork;
    }

    public String getNetmask() {
        return netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }

    public String getRouteSource() {
        return routeSource;
    }

    public void setRouteSource(String routeSource) {
        this.routeSource = routeSource;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取CIDR格式的网络地址
     */
    public String getCidrNetwork() {
        if (targetNetwork != null && netmask != null) {
            int cidr = convertNetmaskToCidr(netmask);
            return targetNetwork + "/" + cidr;
        }
        return targetNetwork;
    }

    /**
     * 将子网掩码转换为CIDR格式
     */
    private int convertNetmaskToCidr(String netmask) {
        String[] parts = netmask.split("\\.");
        int cidr = 0;

        for (String part : parts) {
            int octet = Integer.parseInt(part);
            cidr += Integer.bitCount(octet);
        }

        return cidr;
    }

    /**
     * 生成路由命令 - 界面添加的路由统一使用via格式
     */
    public String generateRouteCommand() {
        // 界面添加的路由统一使用via格式
        return String.format("ip route add %s via %s dev %s",
                getCidrNetwork(), gateway, deviceName);
    }

    /**
     * 生成删除路由命令 - 界面添加的路由统一使用via格式删除
     */
    public String generateDeleteRouteCommand() {
        if ("via".equals(routeType)) {
            // Via路由：ip route del 20.0.0.0/24 via ******** dev eth0
            return String.format("ip route del %s via %s dev %s",
                               getCidrNetwork(), gateway, deviceName);
        } else {
            // 直连路由：ip route del 10.0.0.0/24 dev eth0
            return String.format("ip route del %s dev %s",
                               getCidrNetwork(), deviceName);
        }
    }

    /**
     * 判断路由是否可编辑删除
     * 只有via方式的路由可以编辑删除（无论来源）
     */
    public boolean isEditable() {
        return "via".equals(routeType);
    }

    /**
     * 判断路由是否为via路由
     */
    public boolean isViaRoute() {
        return "via".equals(routeType);
    }
}
