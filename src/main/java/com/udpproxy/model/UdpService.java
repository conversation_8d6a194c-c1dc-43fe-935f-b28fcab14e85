package com.udpproxy.model;

import java.time.LocalDateTime;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import com.udpproxy.util.LocalDateTimeAdapter;

@XmlRootElement(name = "service")
@XmlAccessorType(XmlAccessType.FIELD)
public class UdpService {
    private Long id;
    private String name;
    private Long resourceId;
    private String resourceName;
    private String transportIp;
    private Integer startPort;
    private Integer endPort;
    private String syncConfig; // "auto", "manual", "scheduled"
    private SecurityConfig security;
    private String status; // "running", "stopped", "deployed", "restarting", "warning"

    @XmlJavaTypeAdapter(LocalDateTimeAdapter.class)
    private LocalDateTime createdAt;

    @XmlJavaTypeAdapter(LocalDateTimeAdapter.class)
    private LocalDateTime updatedAt;

    private Integer port; // 单端口模式
    private Integer trafficLimit; // 流量限制 KB/s

    @XmlElement
    private String auditOption = "no_audit"; // 默认值为"不审计"

    private String description;  // 添加描述字段

    // 新增字段: 网段类型 0=发送端, 1=接收端
    private Integer network = 0;

    // 新增字段: 消息类型
    private String messageType;

    // 发送端特有字段
    private String proxyIp;      // 发送端监听地址
    private Integer proxyPort;   // 发送端监听端口
    private Integer contentKeyCheck = 0; // 关键字检测：0不检测，1检测
    private String contentKeywords;      // 关键字（当contentKeyCheck=1时使用）
    private String protocolFilter;       // 协议过滤
    private String unpassDeal;           // 告警后处理

    // 告警处理相关字段
    private Integer vehiclePlateCheck = 0; // 机动车牌校验：0关闭，1开启
    private Integer idCardCheck = 0;       // 身份证校验：0关闭，1开启
    private Integer crc16FormatCheck = 0;  // crc16格式过滤：0关闭，1开启
    private Integer asnFormatCheck = 0;    // asn格式过滤：0关闭，1开启

    // 接收端特有字段
    private String serverIp;     // 接收端服务器地址
    private Integer serverPort;  // 接收端服务器端口
    private Long senderServiceId; // 关联的发送端服务ID

    // 新增字段: 服务部署状态, 用于标识接收端与发送端的同步状态
    // NEW：新增服务，配置未发送
    // CONFIG_SENT：配置已发送到接收端
    // SERVICE_CREATED：接收端服务已创建
    // CONFIG_UPDATED：发送端配置已更新，接收端需要同步
    private String deploymentState = "NEW";

    // 新增字段：是否支持组播功能
    private String multicast = "false";

    // 新增字段：组播IP地址
    private String multicastIp;

    // 新增字段：是否为模板
    private String istemplate = "false";

    // 新增字段：端口范围
    private String tprange;

    // 新增字段：是否启用抗UDP flood攻击
    private String udpFlood = "0";

    // 新增字段：流量限制(MB/s)
    private String flowlevel = "5";

    // 新增字段：传输IP地址
    private String ipproxy;

    // 新增字段：规则
    private String rules;

    // 新增字段：客户端连接端口
    private String portclient;

    // 新增字段：审计选项
    private String audit = "1";

    // 新增字段：显示名称
    private String displayname = "udp";

    // 新增字段：源资源标签
    private String srcResLabel;

    // 新增字段：连接超时时间
    private String confidedtime = "1";

    // 新增字段：代理模式
    private String proxymode = "udp";

    // 新增字段：指定源地址
    private String special_value;

    // 新增字段：地址映射
    private String sendaddrmap;

    // 新增字段：指定网段设置
    private String iprange;

    @XmlRootElement(name = "securityConfig")
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class SecurityConfig {
        private boolean floodProtection;
        private Integer packetRateLimit;
        private Integer connectionLimit;
        private String securityPolicy; // "balanced", "strict", "permissive", "custom"

        public SecurityConfig() {
        }

        public boolean isFloodProtection() {
            return floodProtection;
        }

        public void setFloodProtection(boolean floodProtection) {
            this.floodProtection = floodProtection;
        }

        public Integer getPacketRateLimit() {
            return packetRateLimit;
        }

        public void setPacketRateLimit(Integer packetRateLimit) {
            this.packetRateLimit = packetRateLimit;
        }

        public Integer getConnectionLimit() {
            return connectionLimit;
        }

        public void setConnectionLimit(Integer connectionLimit) {
            this.connectionLimit = connectionLimit;
        }

        public String getSecurityPolicy() {
            return securityPolicy;
        }

        public void setSecurityPolicy(String securityPolicy) {
            this.securityPolicy = securityPolicy;
        }
    }

    // 无参构造函数（JAXB需要）
    public UdpService() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = "stopped"; // 默认状态为已停止
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getTransportIp() {
        return transportIp;
    }

    public void setTransportIp(String transportIp) {
        this.transportIp = transportIp;
    }

    public Integer getStartPort() {
        return startPort;
    }

    public void setStartPort(Integer startPort) {
        this.startPort = startPort;
    }

    public Integer getEndPort() {
        return endPort;
    }

    public void setEndPort(Integer endPort) {
        this.endPort = endPort;
    }

    public String getSyncConfig() {
        return syncConfig;
    }

    public void setSyncConfig(String syncConfig) {
        this.syncConfig = syncConfig;
    }

    public SecurityConfig getSecurity() {
        return security;
    }

    public void setSecurity(SecurityConfig security) {
        this.security = security;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 便捷方法，获取端口范围字符串
    public String getPortRange() {
        return startPort + "-" + endPort;
    }

    // 检查服务是否已部署
    public boolean isDeployed() {
        return "deployed".equals(status) || "running".equals(status) || "stopped".equals(status);
    }

    // 检查服务是否正在运行
    public boolean isRunning() {
        return "running".equals(status);
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Integer getTrafficLimit() {
        return trafficLimit;
    }

    public void setTrafficLimit(Integer trafficLimit) {
        this.trafficLimit = trafficLimit;
    }

    public boolean isFloodProtection() {
        return security != null && security.isFloodProtection();
    }

    public void setFloodProtection(boolean floodProtection) {
        if (security == null) {
            security = new SecurityConfig();
        }
        security.setFloodProtection(floodProtection);
    }

    /**
     * 获取审计选项
     * @return 审计选项
     */
    public String getAuditOption() {
        return auditOption;
    }

    /**
     * 设置审计选项
     * @param auditOption 审计选项
     */
    public void setAuditOption(String auditOption) {
        this.auditOption = auditOption;
    }

    /**
     * 获取服务描述
     * @return 服务描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置服务描述
     * @param description 服务描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取消息类型
     * @return 消息类型
     */
    public String getMessageType() {
        return messageType;
    }

    /**
     * 设置消息类型
     * @param messageType 消息类型
     */
    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    /**
     * 获取网段类型
     * @return 网段类型 0=发送端, 1=接收端
     */
    public Integer getNetwork() {
        return network;
    }

    /**
     * 设置网段类型
     * @param network 网段类型 0=发送端, 1=接收端
     */
    public void setNetwork(Integer network) {
        this.network = network;
    }

    /**
     * 是否为发送端
     * @return 是否为发送端
     */
    public boolean isSender() {
        return network != null && network.equals(0);
    }

    /**
     * 是否为接收端
     * @return 是否为接收端
     */
    public boolean isReceiver() {
        return network != null && network.equals(1);
    }

    /**
     * 获取发送端监听地址
     * @return 发送端监听地址
     */
    public String getProxyIp() {
        return proxyIp;
    }

    /**
     * 设置发送端监听地址
     * @param proxyIp 发送端监听地址
     */
    public void setProxyIp(String proxyIp) {
        this.proxyIp = proxyIp;
    }

    /**
     * 获取发送端监听端口
     * @return 发送端监听端口
     */
    public Integer getProxyPort() {
        return proxyPort;
    }

    /**
     * 设置发送端监听端口
     * @param proxyPort 发送端监听端口
     */
    public void setProxyPort(Integer proxyPort) {
        this.proxyPort = proxyPort;
    }

    /**
     * 获取关键字检测设置
     * @return 关键字检测设置 0=不检测,1=检测
     */
    public Integer getContentKeyCheck() {
        return contentKeyCheck;
    }

    /**
     * 设置关键字检测设置
     * @param contentKeyCheck 关键字检测设置 0=不检测,1=检测
     */
    public void setContentKeyCheck(Integer contentKeyCheck) {
        this.contentKeyCheck = contentKeyCheck;
    }

    /**
     * 获取关键字
     * @return 关键字
     */
    public String getContentKeywords() {
        return contentKeywords;
    }

    /**
     * 设置关键字
     * @param contentKeywords 关键字
     */
    public void setContentKeywords(String contentKeywords) {
        this.contentKeywords = contentKeywords;
    }

    /**
     * 获取协议过滤设置
     * @return 协议过滤
     */
    public String getProtocolFilter() {
        return protocolFilter;
    }

    /**
     * 设置协议过滤
     * @param protocolFilter 协议过滤
     */
    public void setProtocolFilter(String protocolFilter) {
        this.protocolFilter = protocolFilter;
    }

    /**
     * 获取告警后处理方式
     * @return 告警后处理方式
     */
    public String getUnpassDeal() {
        return unpassDeal;
    }

    /**
     * 设置告警后处理方式
     * @param unpassDeal 告警后处理方式
     */
    public void setUnpassDeal(String unpassDeal) {
        this.unpassDeal = unpassDeal;
    }

    /**
     * 获取接收端服务器地址
     * @return 接收端服务器地址
     */
    public String getServerIp() {
        return serverIp;
    }

    /**
     * 设置接收端服务器地址
     * @param serverIp 接收端服务器地址
     */
    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    /**
     * 获取接收端服务器端口
     * @return 接收端服务器端口
     */
    public Integer getServerPort() {
        return serverPort;
    }

    /**
     * 设置接收端服务器端口
     * @param serverPort 接收端服务器端口
     */
    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    /**
     * 获取关联的发送端服务ID
     * @return 发送端服务ID
     */
    public Long getSenderServiceId() {
        return senderServiceId;
    }

    /**
     * 设置关联的发送端服务ID
     * @param senderServiceId 发送端服务ID
     */
    public void setSenderServiceId(Long senderServiceId) {
        this.senderServiceId = senderServiceId;
    }

    /**
     * 获取部署状态
     * @return 部署状态
     */
    public String getDeploymentState() {
        return deploymentState;
    }

    /**
     * 设置部署状态
     * @param deploymentState 部署状态
     */
    public void setDeploymentState(String deploymentState) {
        this.deploymentState = deploymentState;
    }

    public Integer getVehiclePlateCheck() {
        return vehiclePlateCheck;
    }

    public void setVehiclePlateCheck(Integer vehiclePlateCheck) {
        this.vehiclePlateCheck = vehiclePlateCheck;
    }

    public Integer getIdCardCheck() {
        return idCardCheck;
    }

    public void setIdCardCheck(Integer idCardCheck) {
        this.idCardCheck = idCardCheck;
    }

    public Integer getCrc16FormatCheck() {
        return crc16FormatCheck;
    }

    public void setCrc16FormatCheck(Integer crc16FormatCheck) {
        this.crc16FormatCheck = crc16FormatCheck;
    }

    public Integer getAsnFormatCheck() {
        return asnFormatCheck;
    }

    public void setAsnFormatCheck(Integer asnFormatCheck) {
        this.asnFormatCheck = asnFormatCheck;
    }

    public String getMulticast() {
        return multicast;
    }

    public void setMulticast(String multicast) {
        this.multicast = multicast;
    }

    public String getMulticastIp() {
        return multicastIp;
    }

    public void setMulticastIp(String multicastIp) {
        this.multicastIp = multicastIp;
    }

    /**
     * 获取是否为模板
     * @return 是否为模板
     */
    public String getIstemplate() {
        return istemplate;
    }

    /**
     * 设置是否为模板
     * @param istemplate 是否为模板
     */
    public void setIstemplate(String istemplate) {
        this.istemplate = istemplate;
    }

    /**
     * 获取端口范围
     * @return 端口范围
     */
    public String getTprange() {
        return tprange;
    }

    /**
     * 设置端口范围
     * @param tprange 端口范围
     */
    public void setTprange(String tprange) {
        this.tprange = tprange;
    }

    /**
     * 获取是否启用抗UDP flood攻击
     * @return 是否启用抗UDP flood攻击
     */
    public String getUdpFlood() {
        return udpFlood;
    }

    /**
     * 设置是否启用抗UDP flood攻击
     * @param udpFlood 是否启用抗UDP flood攻击
     */
    public void setUdpFlood(String udpFlood) {
        this.udpFlood = udpFlood;
    }

    /**
     * 获取流量限制
     * @return 流量限制(MB/s)
     */
    public String getFlowlevel() {
        return flowlevel;
    }

    /**
     * 设置流量限制
     * @param flowlevel 流量限制(MB/s)
     */
    public void setFlowlevel(String flowlevel) {
        this.flowlevel = flowlevel;
    }

    /**
     * 获取流量限制（大写L版本 - 用于Thymeleaf模板）
     * @return 流量限制(MB/s)
     */
    public String getFlowLevel() {
        return flowlevel;
    }

    /**
     * 设置流量限制（大写L版本 - 用于Thymeleaf模板）
     * @param flowLevel 流量限制(MB/s)
     */
    public void setFlowLevel(String flowLevel) {
        this.flowlevel = flowLevel;
    }

    /**
     * 获取传输IP地址
     * @return 传输IP地址
     */
    public String getIpproxy() {
        return ipproxy;
    }

    /**
     * 设置传输IP地址
     * @param ipproxy 传输IP地址
     */
    public void setIpproxy(String ipproxy) {
        this.ipproxy = ipproxy;
    }

    /**
     * 获取规则
     * @return 规则
     */
    public String getRules() {
        return rules;
    }

    /**
     * 设置规则
     * @param rules 规则
     */
    public void setRules(String rules) {
        this.rules = rules;
    }

    /**
     * 获取客户端连接端口
     * @return 客户端连接端口
     */
    public String getPortclient() {
        return portclient;
    }

    /**
     * 设置客户端连接端口
     * @param portclient 客户端连接端口
     */
    public void setPortclient(String portclient) {
        this.portclient = portclient;
    }

    /**
     * 获取审计选项
     * @return 审计选项
     */
    public String getAudit() {
        return audit;
    }

    /**
     * 设置审计选项
     * @param audit 审计选项
     */
    public void setAudit(String audit) {
        this.audit = audit;
    }

    /**
     * 获取显示名称
     * @return 显示名称
     */
    public String getDisplayname() {
        return displayname;
    }

    /**
     * 设置显示名称
     * @param displayname 显示名称
     */
    public void setDisplayname(String displayname) {
        this.displayname = displayname;
    }

    /**
     * 获取源资源标签
     * @return 源资源标签
     */
    public String getSrcResLabel() {
        return srcResLabel;
    }

    /**
     * 设置源资源标签
     * @param srcResLabel 源资源标签
     */
    public void setSrcResLabel(String srcResLabel) {
        this.srcResLabel = srcResLabel;
    }

    /**
     * 获取连接超时时间
     * @return 连接超时时间
     */
    public String getConfidedtime() {
        return confidedtime;
    }

    /**
     * 设置连接超时时间
     * @param confidedtime 连接超时时间
     */
    public void setConfidedtime(String confidedtime) {
        this.confidedtime = confidedtime;
    }

    /**
     * 获取代理模式
     * @return 代理模式
     */
    public String getProxymode() {
        return proxymode;
    }

    /**
     * 设置代理模式
     * @param proxymode 代理模式
     */
    public void setProxymode(String proxymode) {
        this.proxymode = proxymode;
    }

    /**
     * 获取指定源地址
     * @return 指定源地址（逗号分隔的IP地址）
     */
    public String getSpecial_value() {
        return special_value;
    }

    /**
     * 设置指定源地址
     * @param special_value 指定源地址（逗号分隔的IP地址）
     */
    public void setSpecial_value(String special_value) {
        this.special_value = special_value;
    }

    /**
     * 获取地址映射
     * @return 地址映射
     */
    public String getSendaddrmap() {
        return sendaddrmap;
    }

    /**
     * 设置地址映射
     * @param sendaddrmap 地址映射
     */
    public void setSendaddrmap(String sendaddrmap) {
        this.sendaddrmap = sendaddrmap;
    }

    /**
     * 获取指定网段设置
     * @return 指定网段设置
     */
    public String getIprange() {
        return iprange;
    }

    /**
     * 设置指定网段设置
     * @param iprange 指定网段设置
     */
    public void setIprange(String iprange) {
        this.iprange = iprange;
    }

    @Override
    public String toString() {
        return "UdpService{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", resourceId=" + resourceId +
                ", resourceName='" + resourceName + '\'' +
                ", transportIp='" + transportIp + '\'' +
                ", startPort=" + startPort +
                ", endPort=" + endPort +
                ", syncConfig='" + syncConfig + '\'' +
                ", status='" + status + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", port=" + port +
                ", trafficLimit=" + trafficLimit +
                ", network=" + network +
                ", messageType='" + messageType + '\'' +
                ", proxyIp='" + proxyIp + '\'' +
                ", proxyPort=" + proxyPort +
                ", serverIp='" + serverIp + '\'' +
                ", serverPort=" + serverPort +
                ", multicast='" + multicast + '\'' +
                ", multicastIp='" + multicastIp + '\'' +
                ", istemplate='" + istemplate + '\'' +
                ", tprange='" + tprange + '\'' +
                ", udpFlood='" + udpFlood + '\'' +
                ", flowlevel='" + flowlevel + '\'' +
                ", ipproxy='" + ipproxy + '\'' +
                ", rules='" + rules + '\'' +
                ", portclient='" + portclient + '\'' +
                ", audit='" + audit + '\'' +
                ", displayname='" + displayname + '\'' +
                ", srcResLabel='" + srcResLabel + '\'' +
                ", confidedtime='" + confidedtime + '\'' +
                ", proxymode='" + proxymode + '\'' +
                ", special_value='" + special_value + '\'' +
                ", deploymentState='" + deploymentState + '\'' +
                ", sendaddrmap='" + sendaddrmap + '\'' +
                ", iprange='" + iprange + '\'' +
                '}';
    }

    /**
     * 创建当前服务对象的深拷贝
     * @return 新的服务对象实例
     */
    public UdpService clone() {
        UdpService clone = new UdpService();
        clone.setId(this.id);
        clone.setName(this.name);
        clone.setResourceId(this.resourceId);
        clone.setResourceName(this.resourceName);
        clone.setTransportIp(this.transportIp);
        clone.setStartPort(this.startPort);
        clone.setEndPort(this.endPort);
        clone.setSyncConfig(this.syncConfig);
        clone.setSecurity(this.security); // 注意：这可能需要深拷贝
        clone.setStatus(this.status);
        clone.setCreatedAt(this.createdAt);
        clone.setUpdatedAt(this.updatedAt);
        clone.setPort(this.port);
        clone.setTrafficLimit(this.trafficLimit);
        clone.setAuditOption(this.auditOption);
        clone.setDescription(this.description);
        clone.setNetwork(this.network);
        clone.setMessageType(this.messageType);

        // 发送端特有字段
        clone.setProxyIp(this.proxyIp);
        clone.setProxyPort(this.proxyPort);
        clone.setContentKeyCheck(this.contentKeyCheck);
        clone.setContentKeywords(this.contentKeywords);
        clone.setProtocolFilter(this.protocolFilter);
        clone.setUnpassDeal(this.unpassDeal);

        // 告警处理相关字段
        clone.setVehiclePlateCheck(this.vehiclePlateCheck);
        clone.setIdCardCheck(this.idCardCheck);
        clone.setCrc16FormatCheck(this.crc16FormatCheck);
        clone.setAsnFormatCheck(this.asnFormatCheck);

        // 接收端特有字段
        clone.setServerIp(this.serverIp);
        clone.setServerPort(this.serverPort);
        clone.setSenderServiceId(this.senderServiceId);
        clone.setDeploymentState(this.deploymentState);

        // 新增字段
        clone.setMulticast(this.multicast);
        clone.setMulticastIp(this.multicastIp);
        clone.setIstemplate(this.istemplate);
        clone.setTprange(this.tprange);
        clone.setUdpFlood(this.udpFlood);
        clone.setFlowlevel(this.flowlevel);
        clone.setIpproxy(this.ipproxy);
        clone.setRules(this.rules);
        clone.setPortclient(this.portclient);
        clone.setAudit(this.audit);
        clone.setDisplayname(this.displayname);
        clone.setSrcResLabel(this.srcResLabel);
        clone.setConfidedtime(this.confidedtime);
        clone.setProxymode(this.proxymode);
        clone.setSpecial_value(this.special_value);
        clone.setSendaddrmap(this.sendaddrmap);
        clone.setIprange(this.iprange);

        return clone;
    }
}