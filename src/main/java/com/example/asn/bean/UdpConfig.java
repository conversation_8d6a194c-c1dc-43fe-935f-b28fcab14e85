package com.example.asn.bean;

public class UdpConfig {
    private String sid;
    private String eqs = "";
    private String weekDay = "1;2;3;4;5;6;7";
    private String special_value = "";
    private String runtime = "#*#";
    private String rules = "";
    private String ipproxy;
    private String multicast = "false";
    private String srcResLabel = "proxyclinet_1";
    private String configedtime = "1";
    private String portclient;
    private String audit = "1";
    private String displayname;
    private String iprange = "#*#";
    private String udpFlood = "0";
    private String istemplate = "false";
    private String proxymode = "udp";
    private String multicastip = "";
    private String flowlevel = "10";
    private String carcheck; //0表示关闭机动车牌效验，1表示开启该功能
    private String idcheck; //0表示关闭cn身份证效验，1表示开启该功能
    private String crcfilter; //0表示关闭crc16格式过滤，1表示开启改功能
    private String asnfilter; //0表示关闭asn格式过滤，1表示开启改功能
    private String unpassdeal; //0表示配置的格式过滤或效验规则不通过时仅报警数据正常传输，1表示报警+数据丢弃不转发

    private String hostip;
    private String destResLabel = "proxyclinet_2";
    private String sendaddrmap = "";
    private String hostport;
    private String srcPort;

    public String getHostip() {
        return hostip;
    }

    public void setHostip(String hostip) {
        this.hostip = hostip;
    }

    public String getDestResLabel() {
        return destResLabel;
    }

    public void setDestResLabel(String destResLabel) {
        this.destResLabel = destResLabel;
    }

    public String getSendaddrmap() {
        return sendaddrmap;
    }

    public void setSendaddrmap(String sendaddrmap) {
        this.sendaddrmap = sendaddrmap;
    }

    public String getHostport() {
        return hostport;
    }

    public void setHostport(String hostport) {
        this.hostport = hostport;
    }

    public String getSrcPort() {
        return srcPort;
    }

    public void setSrcPort(String srcPort) {
        this.srcPort = srcPort;
    }

    public String getCarcheck() {
        return carcheck;
    }

    public void setCarcheck(String carcheck) {
        this.carcheck = carcheck;
    }

    public String getIdcheck() {
        return idcheck;
    }

    public void setIdcheck(String idcheck) {
        this.idcheck = idcheck;
    }

    public String getCrcfilter() {
        return crcfilter;
    }

    public void setCrcfilter(String crcfilter) {
        this.crcfilter = crcfilter;
    }

    public String getAsnfilter() {
        return asnfilter;
    }

    public void setAsnfilter(String asnfilter) {
        this.asnfilter = asnfilter;
    }

    public String getUnpassdeal() {
        return unpassdeal;
    }

    public void setUnpassdeal(String unpassdeal) {
        this.unpassdeal = unpassdeal;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getEqs() {
        return eqs;
    }

    public void setEqs(String eqs) {
        this.eqs = eqs;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay;
    }

    public String getSpecial_value() {
        return special_value;
    }

    public void setSpecial_value(String special_value) {
        this.special_value = special_value;
    }

    public String getRuntime() {
        return runtime;
    }

    public void setRuntime(String runtime) {
        this.runtime = runtime;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public String getIpproxy() {
        return ipproxy;
    }

    public void setIpproxy(String ipproxy) {
        this.ipproxy = ipproxy;
    }

    public String getMulticast() {
        return multicast;
    }

    public void setMulticast(String multicast) {
        this.multicast = multicast;
    }

    public String getSrcResLabel() {
        return srcResLabel;
    }

    public void setSrcResLabel(String srcResLabel) {
        this.srcResLabel = srcResLabel;
    }

    public String getConfigedtime() {
        return configedtime;
    }

    public void setConfigedtime(String configedtime) {
        this.configedtime = configedtime;
    }

    public String getPortclient() {
        return portclient;
    }

    public void setPortclient(String portclient) {
        this.portclient = portclient;
    }

    public String getAudit() {
        return audit;
    }

    public void setAudit(String audit) {
        this.audit = audit;
    }

    public String getDisplayname() {
        return displayname;
    }

    public void setDisplayname(String displayname) {
        this.displayname = displayname;
    }

    public String getIprange() {
        return iprange;
    }

    public void setIprange(String iprange) {
        this.iprange = iprange;
    }

    public String getUdpFlood() {
        return udpFlood;
    }

    public void setUdpFlood(String udpFlood) {
        this.udpFlood = udpFlood;
    }

    public String getIstemplate() {
        return istemplate;
    }

    public void setIstemplate(String istemplate) {
        this.istemplate = istemplate;
    }

    public String getProxymode() {
        return proxymode;
    }

    public void setProxymode(String proxymode) {
        this.proxymode = proxymode;
    }

    public String getMulticastip() {
        return multicastip;
    }

    public void setMulticastip(String multicastip) {
        this.multicastip = multicastip;
    }

    public String getFlowlevel() {
        return flowlevel;
    }

    public void setFlowlevel(String flowlevel) {
        this.flowlevel = flowlevel;
    }
}
