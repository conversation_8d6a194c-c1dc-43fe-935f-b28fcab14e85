package com.example.asn.bean;

import com.example.asn.servicemanager.servicemanagementhttp.ErrorResponse;
import com.example.asn.servicemanager.servicemanagementhttp.ServiceConfigResponse;

public class RequestException extends Exception {
    private ErrorResponse errorResponse;

    public RequestException(ErrorResponse errorResponse) {
        this.errorResponse = errorResponse;
    }

    public RequestException(String message, ErrorResponse errorResponse) {
        super(message);
        this.errorResponse = errorResponse;
    }

    public RequestException(String message, Throwable cause, ErrorResponse errorResponse) {
        super(message, cause);
        this.errorResponse = errorResponse;
    }

    public RequestException(Throwable cause, ErrorResponse errorResponse) {
        super(cause);
        this.errorResponse = errorResponse;
    }

    public RequestException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, ErrorResponse errorResponse) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.errorResponse = errorResponse;
    }

    public ErrorResponse getErrorResponse() {
        return errorResponse;
    }
    public void setErrorResponse(ErrorResponse errorResponse) {}
}
