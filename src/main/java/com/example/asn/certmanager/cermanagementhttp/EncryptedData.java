/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: manager */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Thu Mar 27 13:22:58 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc C:/Users/<USER>/Desktop/cert.asn
 * C:/Users/<USER>/Desktop/manager.asn
 */


package com.example.asn.certmanager.cermanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the EncryptedData ASN.1 type included in the CerManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class EncryptedData extends Sequence {
    
    /**
     * The default constructor.
     */
    public EncryptedData()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public EncryptedData(OctetString encryptData)
    {
	setEncryptData(encryptData);
    }
    
    public void initComponents()
    {
	mComponents[0] = new OctetString();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[1];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new OctetString();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "encryptData"
    public OctetString getEncryptData()
    {
	return (OctetString)mComponents[0];
    }
    
    public void setEncryptData(OctetString encryptData)
    {
	mComponents[0] = encryptData;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.cermanagementhttp",
	    "EncryptedData"
	),
	new QName (
	    "CerManagementHTTP",
	    "EncryptedData"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"OctetString"
			    ),
			    new QName (
				"builtin",
				"OCTET STRING"
			    ),
			    536603,
			    new SizeConstraint (
				new ValueRangeConstraint (
				    new AbstractBounds(
					new INTEGER(97),
					new INTEGER(200),
					0
				    )
				)
			    ),
			    new Bounds (
				Long.valueOf(97),
				Long.valueOf(200)
			    )
			)
		    ),
		    "encryptData",
		    0,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' EncryptedData object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' EncryptedData object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for EncryptedData
