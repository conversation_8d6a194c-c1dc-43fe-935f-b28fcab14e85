/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.cermanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the MessageResponseFrame ASN.1 type included in the CerManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class MessageResponseFrame extends Sequence {
    
    /**
     * The default constructor.
     */
    public MessageResponseFrame()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public MessageResponseFrame(com.example.asn.certmanager.programmingtypes.Uint8 version, 
		    Content content)
    {
	setVersion(version);
	setContent(content);
    }
    
    public void initComponents()
    {
	mComponents[0] = new com.example.asn.certmanager.programmingtypes.Uint8();
	mComponents[1] = new Content();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new com.example.asn.certmanager.programmingtypes.Uint8();
	    case 1:
		return new Content();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "version"
    public com.example.asn.certmanager.programmingtypes.Uint8 getVersion()
    {
	return (com.example.asn.certmanager.programmingtypes.Uint8)mComponents[0];
    }
    
    public void setVersion(com.example.asn.certmanager.programmingtypes.Uint8 version)
    {
	mComponents[0] = version;
    }
    
    
    // Methods for field "content"
    public Content getContent()
    {
	return (Content)mComponents[1];
    }
    
    public void setContent(Content content)
    {
	mComponents[1] = content;
    }
    
    
    
    /**
     * Define the Content ASN.1 type included in the CerManagementHTTP ASN.1 module.
     * @see Choice
     */
    public static class Content extends Choice {
	
	/**
	 * The default constructor.
	 */
	public Content()
	{
	}
	
	public static final  int  publicKeyGet_chosen = 1;
	public static final  int  certificationWrite_chosen = 2;
	public static final  int  certificationQuery_chosen = 3;
	public static final  int  certificationDelete_chosen = 4;
	public static final  int  identityAuthentication_chosen = 5;
	public static final  int  encryptedCiphertext_chosen = 6;
	public static final  int  authenticationState_chosen = 7;
	public static final  int  error_chosen = 8;
	
	// Methods for field "publicKeyGet"
	public static Content createContentWithPublicKeyGet(PubKeyResponse publicKeyGet)
	{
	    Content __object = new Content();

	    __object.setPublicKeyGet(publicKeyGet);
	    return __object;
	}
	
	public boolean hasPublicKeyGet()
	{
	    return getChosenFlag() == publicKeyGet_chosen;
	}
	
	public PubKeyResponse getPublicKeyGet()
	{
	    if (hasPublicKeyGet())
		return (PubKeyResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setPublicKeyGet(PubKeyResponse publicKeyGet)
	{
	    setChosenValue(publicKeyGet);
	    setChosenFlag(publicKeyGet_chosen);
	}
	
	
	// Methods for field "certificationWrite"
	public static Content createContentWithCertificationWrite(CertWriteResponse certificationWrite)
	{
	    Content __object = new Content();

	    __object.setCertificationWrite(certificationWrite);
	    return __object;
	}
	
	public boolean hasCertificationWrite()
	{
	    return getChosenFlag() == certificationWrite_chosen;
	}
	
	public CertWriteResponse getCertificationWrite()
	{
	    if (hasCertificationWrite())
		return (CertWriteResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setCertificationWrite(CertWriteResponse certificationWrite)
	{
	    setChosenValue(certificationWrite);
	    setChosenFlag(certificationWrite_chosen);
	}
	
	
	// Methods for field "certificationQuery"
	public static Content createContentWithCertificationQuery(CertInquiryResponse certificationQuery)
	{
	    Content __object = new Content();

	    __object.setCertificationQuery(certificationQuery);
	    return __object;
	}
	
	public boolean hasCertificationQuery()
	{
	    return getChosenFlag() == certificationQuery_chosen;
	}
	
	public CertInquiryResponse getCertificationQuery()
	{
	    if (hasCertificationQuery())
		return (CertInquiryResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setCertificationQuery(CertInquiryResponse certificationQuery)
	{
	    setChosenValue(certificationQuery);
	    setChosenFlag(certificationQuery_chosen);
	}
	
	
	// Methods for field "certificationDelete"
	public static Content createContentWithCertificationDelete(CertDeleteResponse certificationDelete)
	{
	    Content __object = new Content();

	    __object.setCertificationDelete(certificationDelete);
	    return __object;
	}
	
	public boolean hasCertificationDelete()
	{
	    return getChosenFlag() == certificationDelete_chosen;
	}
	
	public CertDeleteResponse getCertificationDelete()
	{
	    if (hasCertificationDelete())
		return (CertDeleteResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setCertificationDelete(CertDeleteResponse certificationDelete)
	{
	    setChosenValue(certificationDelete);
	    setChosenFlag(certificationDelete_chosen);
	}
	
	
	// Methods for field "identityAuthentication"
	public static Content createContentWithIdentityAuthentication(AuthenticationCertTransmit identityAuthentication)
	{
	    Content __object = new Content();

	    __object.setIdentityAuthentication(identityAuthentication);
	    return __object;
	}
	
	public boolean hasIdentityAuthentication()
	{
	    return getChosenFlag() == identityAuthentication_chosen;
	}
	
	public AuthenticationCertTransmit getIdentityAuthentication()
	{
	    if (hasIdentityAuthentication())
		return (AuthenticationCertTransmit)mChosenValue;
	    else
		return null;
	}
	
	public void setIdentityAuthentication(AuthenticationCertTransmit identityAuthentication)
	{
	    setChosenValue(identityAuthentication);
	    setChosenFlag(identityAuthentication_chosen);
	}
	
	
	// Methods for field "encryptedCiphertext"
	public static Content createContentWithEncryptedCiphertext(EncryptedCiphertext encryptedCiphertext)
	{
	    Content __object = new Content();

	    __object.setEncryptedCiphertext(encryptedCiphertext);
	    return __object;
	}
	
	public boolean hasEncryptedCiphertext()
	{
	    return getChosenFlag() == encryptedCiphertext_chosen;
	}
	
	public EncryptedCiphertext getEncryptedCiphertext()
	{
	    if (hasEncryptedCiphertext())
		return (EncryptedCiphertext)mChosenValue;
	    else
		return null;
	}
	
	public void setEncryptedCiphertext(EncryptedCiphertext encryptedCiphertext)
	{
	    setChosenValue(encryptedCiphertext);
	    setChosenFlag(encryptedCiphertext_chosen);
	}
	
	
	// Methods for field "authenticationState"
	public static Content createContentWithAuthenticationState(AuthenticationStateResponse authenticationState)
	{
	    Content __object = new Content();

	    __object.setAuthenticationState(authenticationState);
	    return __object;
	}
	
	public boolean hasAuthenticationState()
	{
	    return getChosenFlag() == authenticationState_chosen;
	}
	
	public AuthenticationStateResponse getAuthenticationState()
	{
	    if (hasAuthenticationState())
		return (AuthenticationStateResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setAuthenticationState(AuthenticationStateResponse authenticationState)
	{
	    setChosenValue(authenticationState);
	    setChosenFlag(authenticationState_chosen);
	}
	
	
	// Methods for field "error"
	public static Content createContentWithError(ErrorResponse error)
	{
	    Content __object = new Content();

	    __object.setError(error);
	    return __object;
	}
	
	public boolean hasError()
	{
	    return getChosenFlag() == error_chosen;
	}
	
	public ErrorResponse getError()
	{
	    if (hasError())
		return (ErrorResponse)mChosenValue;
	    else
		return null;
	}
	
	public void setError(ErrorResponse error)
	{
	    setChosenValue(error);
	    setChosenFlag(error_chosen);
	}
	
	
	// Method to create a specific choice instance
	public AbstractData createInstance(int chosen)
	{
	    switch (chosen) {
		case publicKeyGet_chosen:
		    return new PubKeyResponse();
		case certificationWrite_chosen:
		    return new CertWriteResponse();
		case certificationQuery_chosen:
		    return new CertInquiryResponse();
		case certificationDelete_chosen:
		    return new CertDeleteResponse();
		case identityAuthentication_chosen:
		    return new AuthenticationCertTransmit();
		case encryptedCiphertext_chosen:
		    return new EncryptedCiphertext();
		case authenticationState_chosen:
		    return new AuthenticationStateResponse();
		case error_chosen:
		    return new ErrorResponse();
		default:
		    throw new InternalError("Choice.createInstance()");
	    }
	    
	}
	
	/**
	 * Initialize the type descriptor.
	 */
	private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	    new Tags (
		new short[] {
		    (short)0x8001
		}
	    ),
	    new QName (
		"com.example.asn.certmanager.cermanagementhttp",
		"MessageResponseFrame$Content"
	    ),
	    new QName (
		"builtin",
		"CHOICE"
	    ),
	    536607,
	    null,
	    new FieldsList (
		new FieldInfo[] {
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8000
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "PubKeyResponse"
				),
				new QName (
				    "CerManagementHTTP",
				    "PubKeyResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"PubKeyResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"PubKeyResponse"
				    )
				),
				0
			    )
			),
			"publicKeyGet",
			0,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8001
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "CertWriteResponse"
				),
				new QName (
				    "CerManagementHTTP",
				    "CertWriteResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"CertWriteResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"CertWriteResponse"
				    )
				),
				0
			    )
			),
			"certificationWrite",
			1,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8002
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "CertInquiryResponse"
				),
				new QName (
				    "CerManagementHTTP",
				    "CertInquiryResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"CertInquiryResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"CertInquiryResponse"
				    )
				),
				0
			    )
			),
			"certificationQuery",
			2,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8003
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "CertDeleteResponse"
				),
				new QName (
				    "CerManagementHTTP",
				    "CertDeleteResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"CertDeleteResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"CertDeleteResponse"
				    )
				),
				0
			    )
			),
			"certificationDelete",
			3,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8004
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "AuthenticationCertTransmit"
				),
				new QName (
				    "CerManagementHTTP",
				    "AuthenticationCertTransmit"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"AuthenticationCertTransmit"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"AuthenticationCertTransmit"
				    )
				),
				0
			    )
			),
			"identityAuthentication",
			4,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8005
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "EncryptedCiphertext"
				),
				new QName (
				    "CerManagementHTTP",
				    "EncryptedCiphertext"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"EncryptedCiphertext"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"EncryptedCiphertext"
				    )
				),
				0
			    )
			),
			"encryptedCiphertext",
			5,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8006
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "AuthenticationStateResponse"
				),
				new QName (
				    "CerManagementHTTP",
				    "AuthenticationStateResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"AuthenticationStateResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"AuthenticationStateResponse"
				    )
				),
				0
			    )
			),
			"authenticationState",
			6,
			2
		    ),
		    new FieldInfo (
			new TypeInfoRef (
			    new SequenceInfo (
				new Tags (
				    new short[] {
					(short)0x8007
				    }
				),
				new QName (
				    "com.example.asn.certmanager.cermanagementhttp",
				    "ErrorResponse"
				),
				new QName (
				    "CerManagementHTTP",
				    "ErrorResponse"
				),
				536607,
				null,
				new FieldsRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"ErrorResponse"
				    )
				),
				0,
				new TagDecodersRef (
				    new QName (
					"com.example.asn.certmanager.cermanagementhttp",
					"ErrorResponse"
				    )
				),
				0
			    )
			),
			"error",
			7,
			2
		    )
		}
	    ),
	    0,
	    new TagDecoder (
		new TagDecoderElement[] {
		    new TagDecoderElement((short)0x8000, 0),
		    new TagDecoderElement((short)0x8001, 1),
		    new TagDecoderElement((short)0x8002, 2),
		    new TagDecoderElement((short)0x8003, 3),
		    new TagDecoderElement((short)0x8004, 4),
		    new TagDecoderElement((short)0x8005, 5),
		    new TagDecoderElement((short)0x8006, 6),
		    new TagDecoderElement((short)0x8007, 7)
		}
	    )
	);
	
	/**
	 * Get the type descriptor (TypeInfo) of 'this' Content object.
	 */
	public TypeInfo getTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Get the static type descriptor (TypeInfo) of 'this' Content object.
	 */
	public static TypeInfo getStaticTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Check the current selection on unknown extension
	 */
	public final boolean hasUnknownExtension()
	{
	    return getChosenFlag() > 8;
	}
	
    } // End class definition for Content

    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.cermanagementhttp",
	    "MessageResponseFrame"
	),
	new QName (
	    "CerManagementHTTP",
	    "MessageResponseFrame"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"Uint8"
			    ),
			    new QName (
				"ProgrammingTypes",
				"Uint8"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new com.example.asn.certmanager.programmingtypes.Uint8(0), 
				    new com.example.asn.certmanager.programmingtypes.Uint8(255),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(0),
				Long.valueOf(255)
			    ),
			    null,
			    1
			)
		    ),
		    "version",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new QName (
			    "com.example.asn.certmanager.cermanagementhttp",
			    "MessageResponseFrame$Content"
			)
		    ),
		    "content",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' MessageResponseFrame object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' MessageResponseFrame object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for MessageResponseFrame
