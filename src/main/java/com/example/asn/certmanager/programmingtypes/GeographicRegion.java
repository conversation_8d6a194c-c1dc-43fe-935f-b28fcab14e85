/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the GeographicRegion ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Choice
 */

public class GeographicRegion extends Choice {
    
    /**
     * The default constructor.
     */
    public GeographicRegion()
    {
    }
    
    public static final  int  circularRegion_chosen = 1;
    public static final  int  rectangularRegion_chosen = 2;
    public static final  int  polygonalRegion_chosen = 3;
    public static final  int  identifiedRegion_chosen = 4;
    
    // Methods for field "circularRegion"
    public static GeographicRegion createGeographicRegionWithCircularRegion(CircularRegion circularRegion)
    {
	GeographicRegion __object = new GeographicRegion();

	__object.setCircularRegion(circularRegion);
	return __object;
    }
    
    public boolean hasCircularRegion()
    {
	return getChosenFlag() == circularRegion_chosen;
    }
    
    public CircularRegion getCircularRegion()
    {
	if (hasCircularRegion())
	    return (CircularRegion)mChosenValue;
	else
	    return null;
    }
    
    public void setCircularRegion(CircularRegion circularRegion)
    {
	setChosenValue(circularRegion);
	setChosenFlag(circularRegion_chosen);
    }
    
    
    // Methods for field "rectangularRegion"
    public static GeographicRegion createGeographicRegionWithRectangularRegion(SequenceOfRectangularRegion rectangularRegion)
    {
	GeographicRegion __object = new GeographicRegion();

	__object.setRectangularRegion(rectangularRegion);
	return __object;
    }
    
    public boolean hasRectangularRegion()
    {
	return getChosenFlag() == rectangularRegion_chosen;
    }
    
    public SequenceOfRectangularRegion getRectangularRegion()
    {
	if (hasRectangularRegion())
	    return (SequenceOfRectangularRegion)mChosenValue;
	else
	    return null;
    }
    
    public void setRectangularRegion(SequenceOfRectangularRegion rectangularRegion)
    {
	setChosenValue(rectangularRegion);
	setChosenFlag(rectangularRegion_chosen);
    }
    
    
    // Methods for field "polygonalRegion"
    public static GeographicRegion createGeographicRegionWithPolygonalRegion(PolygonalRegion polygonalRegion)
    {
	GeographicRegion __object = new GeographicRegion();

	__object.setPolygonalRegion(polygonalRegion);
	return __object;
    }
    
    public boolean hasPolygonalRegion()
    {
	return getChosenFlag() == polygonalRegion_chosen;
    }
    
    public PolygonalRegion getPolygonalRegion()
    {
	if (hasPolygonalRegion())
	    return (PolygonalRegion)mChosenValue;
	else
	    return null;
    }
    
    public void setPolygonalRegion(PolygonalRegion polygonalRegion)
    {
	setChosenValue(polygonalRegion);
	setChosenFlag(polygonalRegion_chosen);
    }
    
    
    // Methods for field "identifiedRegion"
    public static GeographicRegion createGeographicRegionWithIdentifiedRegion(SequenceOfIdentifiedRegion identifiedRegion)
    {
	GeographicRegion __object = new GeographicRegion();

	__object.setIdentifiedRegion(identifiedRegion);
	return __object;
    }
    
    public boolean hasIdentifiedRegion()
    {
	return getChosenFlag() == identifiedRegion_chosen;
    }
    
    public SequenceOfIdentifiedRegion getIdentifiedRegion()
    {
	if (hasIdentifiedRegion())
	    return (SequenceOfIdentifiedRegion)mChosenValue;
	else
	    return null;
    }
    
    public void setIdentifiedRegion(SequenceOfIdentifiedRegion identifiedRegion)
    {
	setChosenValue(identifiedRegion);
	setChosenFlag(identifiedRegion_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case circularRegion_chosen:
		return new CircularRegion();
	    case rectangularRegion_chosen:
		return new SequenceOfRectangularRegion();
	    case polygonalRegion_chosen:
		return new PolygonalRegion();
	    case identifiedRegion_chosen:
		return new SequenceOfIdentifiedRegion();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "GeographicRegion"
	),
	new QName (
	    "ProgrammingTypes",
	    "GeographicRegion"
	),
	536607,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"CircularRegion"
			    ),
			    new QName (
				"ProgrammingTypes",
				"CircularRegion"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CircularRegion"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CircularRegion"
				)
			    ),
			    0
			)
		    ),
		    "circularRegion",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfRectangularRegion"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfRectangularRegion"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "RectangularRegion"
				)
			    )
			)
		    ),
		    "rectangularRegion",
		    1,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"PolygonalRegion"
			    ),
			    new QName (
				"ProgrammingTypes",
				"PolygonalRegion"
			    ),
			    536603,
			    new SizeConstraint (
				new ValueRangeConstraint (
				    new AbstractBounds(
					new INTEGER(3),
					null,
					0
				    )
				)
			    ),
			    new Bounds (
				Long.valueOf(3),
				null
			    ),
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "TwoDLocation"
				)
			    )
			)
		    ),
		    "polygonalRegion",
		    2,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfIdentifiedRegion"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfIdentifiedRegion"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "IdentifiedRegion"
				)
			    )
			)
		    ),
		    "identifiedRegion",
		    3,
		    2
		)
	    }
	),
	0,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1),
		new TagDecoderElement((short)0x8002, 2),
		new TagDecoderElement((short)0x8003, 3)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' GeographicRegion object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' GeographicRegion object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Check the current selection on unknown extension
     */
    public final boolean hasUnknownExtension()
    {
	return getChosenFlag() > 4;
    }
    
} // End class definition for GeographicRegion
