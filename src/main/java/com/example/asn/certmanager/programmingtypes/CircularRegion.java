/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the CircularRegion ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Sequence
 */

public class CircularRegion extends Sequence {
    
    /**
     * The default constructor.
     */
    public CircularRegion()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public CircularRegion(TwoDLocation center, Uint16 radius)
    {
	setCenter(center);
	setRadius(radius);
    }
    
    public void initComponents()
    {
	mComponents[0] = new TwoDLocation();
	mComponents[1] = new Uint16();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new TwoDLocation();
	    case 1:
		return new Uint16();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "center"
    public TwoDLocation getCenter()
    {
	return (TwoDLocation)mComponents[0];
    }
    
    public void setCenter(TwoDLocation center)
    {
	mComponents[0] = center;
    }
    
    
    // Methods for field "radius"
    public Uint16 getRadius()
    {
	return (Uint16)mComponents[1];
    }
    
    public void setRadius(Uint16 radius)
    {
	mComponents[1] = radius;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "CircularRegion"
	),
	new QName (
	    "ProgrammingTypes",
	    "CircularRegion"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"TwoDLocation"
			    ),
			    new QName (
				"ProgrammingTypes",
				"TwoDLocation"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "TwoDLocation"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "TwoDLocation"
				)
			    ),
			    0
			)
		    ),
		    "center",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"Uint16"
			    ),
			    new QName (
				"ProgrammingTypes",
				"Uint16"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint16(0), 
				    new Uint16(65535),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(0),
				Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "radius",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' CircularRegion object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' CircularRegion object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for CircularRegion
