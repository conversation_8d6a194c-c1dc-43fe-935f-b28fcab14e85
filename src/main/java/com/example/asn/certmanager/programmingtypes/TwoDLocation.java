/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the TwoDLocation ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Sequence
 */

public class TwoDLocation extends Sequence {
    
    /**
     * The default constructor.
     */
    public TwoDLocation()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public TwoDLocation(Latitude latitude, Longitude longitude)
    {
	setLatitude(latitude);
	setLongitude(longitude);
    }
    
    public void initComponents()
    {
	mComponents[0] = new Latitude();
	mComponents[1] = new Longitude();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new Latitude();
	    case 1:
		return new Longitude();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "latitude"
    public Latitude getLatitude()
    {
	return (Latitude)mComponents[0];
    }
    
    public void setLatitude(Latitude latitude)
    {
	mComponents[0] = latitude;
    }
    
    
    // Methods for field "longitude"
    public Longitude getLongitude()
    {
	return (Longitude)mComponents[1];
    }
    
    public void setLongitude(Longitude longitude)
    {
	mComponents[1] = longitude;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "TwoDLocation"
	),
	new QName (
	    "ProgrammingTypes",
	    "TwoDLocation"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"Latitude"
			    ),
			    new QName (
				"ProgrammingTypes",
				"Latitude"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Latitude(-900000000), 
				    new Latitude(900000001),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(-900000000),
				Long.valueOf(900000001)
			    ),
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"min",
					-900000000
				    ),
				    new MemberListElement (
					"max",
					900000000
				    ),
				    new MemberListElement (
					"unknown",
					900000001
				    )
				}
			    ),
			    4
			)
		    ),
		    "latitude",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"Longitude"
			    ),
			    new QName (
				"ProgrammingTypes",
				"Longitude"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Longitude(-1799999999), 
				    new Longitude(1800000001),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(-1799999999),
				Long.valueOf(1800000001)
			    ),
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"min",
					-1799999999
				    ),
				    new MemberListElement (
					"max",
					1800000000
				    ),
				    new MemberListElement (
					"unknown",
					1800000001
				    )
				}
			    ),
			    4
			)
		    ),
		    "longitude",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' TwoDLocation object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' TwoDLocation object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for TwoDLocation
