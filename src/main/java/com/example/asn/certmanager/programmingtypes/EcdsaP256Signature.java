/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the EcdsaP256Signature ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Sequence
 */

public class EcdsaP256Signature extends Sequence {
    
    /**
     * The default constructor.
     */
    public EcdsaP256Signature()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public EcdsaP256Signature(EccP256CurvePoint rSig, OctetString sSig)
    {
	setRSig(rSig);
	setSSig(sSig);
    }
    
    public void initComponents()
    {
	mComponents[0] = new EccP256CurvePoint();
	mComponents[1] = new OctetString();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new EccP256CurvePoint();
	    case 1:
		return new OctetString();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "rSig"
    public EccP256CurvePoint getRSig()
    {
	return (EccP256CurvePoint)mComponents[0];
    }
    
    public void setRSig(EccP256CurvePoint rSig)
    {
	mComponents[0] = rSig;
    }
    
    
    // Methods for field "sSig"
    public OctetString getSSig()
    {
	return (OctetString)mComponents[1];
    }
    
    public void setSSig(OctetString sSig)
    {
	mComponents[1] = sSig;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "EcdsaP256Signature"
	),
	new QName (
	    "ProgrammingTypes",
	    "EcdsaP256Signature"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"EccP256CurvePoint"
			    ),
			    new QName (
				"ProgrammingTypes",
				"EccP256CurvePoint"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "EccP256CurvePoint"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1),
				    new TagDecoderElement((short)0x8002, 2),
				    new TagDecoderElement((short)0x8003, 3),
				    new TagDecoderElement((short)0x8004, 4)
				}
			    )
			)
		    ),
		    "rSig",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"OctetString"
			    ),
			    new QName (
				"builtin",
				"OCTET STRING"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(32)
				)
			    ),
			    new Bounds (
				Long.valueOf(32),
				Long.valueOf(32)
			    )
			)
		    ),
		    "sSig",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' EcdsaP256Signature object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' EcdsaP256Signature object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for EcdsaP256Signature
