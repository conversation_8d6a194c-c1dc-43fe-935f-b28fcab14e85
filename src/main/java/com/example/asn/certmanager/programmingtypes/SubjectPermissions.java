/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the SubjectPermissions ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Choice
 */

public class SubjectPermissions extends Choice {
    
    /**
     * The default constructor.
     */
    public SubjectPermissions()
    {
    }
    
    public static final  int  explicit_chosen = 1;
    public static final  int  all_chosen = 2;
    
    // Methods for field "explicit"
    public static SubjectPermissions createSubjectPermissionsWithExplicit(SequenceOfAidSspRange explicit)
    {
	SubjectPermissions __object = new SubjectPermissions();

	__object.setExplicit(explicit);
	return __object;
    }
    
    public boolean hasExplicit()
    {
	return getChosenFlag() == explicit_chosen;
    }
    
    public SequenceOfAidSspRange getExplicit()
    {
	if (hasExplicit())
	    return (SequenceOfAidSspRange)mChosenValue;
	else
	    return null;
    }
    
    public void setExplicit(SequenceOfAidSspRange explicit)
    {
	setChosenValue(explicit);
	setChosenFlag(explicit_chosen);
    }
    
    
    // Methods for field "all"
    public static SubjectPermissions createSubjectPermissionsWithAll(Null all)
    {
	SubjectPermissions __object = new SubjectPermissions();

	__object.setAll(all);
	return __object;
    }
    
    public boolean hasAll()
    {
	return getChosenFlag() == all_chosen;
    }
    
    public Null getAll()
    {
	if (hasAll())
	    return (Null)mChosenValue;
	else
	    return null;
    }
    
    public void setAll(Null all)
    {
	setChosenValue(all);
	setChosenFlag(all_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case explicit_chosen:
		return new SequenceOfAidSspRange();
	    case all_chosen:
		return new Null();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "SubjectPermissions"
	),
	new QName (
	    "ProgrammingTypes",
	    "SubjectPermissions"
	),
	536607,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfAidSspRange"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfAidSspRange"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "AidSspRange"
				)
			    )
			)
		    ),
		    "explicit",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new TypeInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"Null"
			    ),
			    new QName (
				"builtin",
				"NULL"
			    ),
			    1585179,
			    null
			)
		    ),
		    "all",
		    1,
		    2
		)
	    }
	),
	0,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' SubjectPermissions object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' SubjectPermissions object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Check the current selection on unknown extension
     */
    public final boolean hasUnknownExtension()
    {
	return getChosenFlag() > 2;
    }
    
} // End class definition for SubjectPermissions
