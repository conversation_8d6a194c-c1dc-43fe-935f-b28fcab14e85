/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

                      
import com.oss.metadata.*;

/**
 * Define the KnownLatitude ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see NinetyDegreeInt
 */

public class KnownLatitude extends NinetyDegreeInt {
    
    /**
     * The default constructor.
     */
    public KnownLatitude()
    {
    }
    
    public KnownLatitude(short value)
    {
	super(value);
    }
    
    public KnownLatitude(int value)
    {
	super(value);
    }
    
    public KnownLatitude(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "KnownLatitude"
	),
	new QName (
	    "ProgrammingTypes",
	    "KnownLatitude"
	),
	536603,
	new Intersection (
	    new ValueRangeConstraint (
		new AbstractBounds(
		    new KnownLatitude(-900000000), 
		    new KnownLatitude(900000001),
		    0
		)
	    ),
	    new ValueRangeConstraint (
		new AbstractBounds(
		    new KnownLatitude(-900000000), 
		    new KnownLatitude(900000000),
		    0
		)
	    )
	),
	new Bounds (
	    Long.valueOf(-900000000),
	    Long.valueOf(900000000)
	),
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "min",
		    -900000000
		),
		new MemberListElement (
		    "max",
		    900000000
		),
		new MemberListElement (
		    "unknown",
		    900000001
		)
	    }
	),
	4
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' KnownLatitude object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' KnownLatitude object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for KnownLatitude
