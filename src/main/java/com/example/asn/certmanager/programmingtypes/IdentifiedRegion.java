/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the IdentifiedRegion ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Choice
 */

public class IdentifiedRegion extends Choice {
    
    /**
     * The default constructor.
     */
    public IdentifiedRegion()
    {
    }
    
    public static final  int  countryOnly_chosen = 1;
    public static final  int  countryAndRegions_chosen = 2;
    public static final  int  countryAndSubregions_chosen = 3;
    
    // Methods for field "countryOnly"
    public static IdentifiedRegion createIdentifiedRegionWithCountryOnly(long countryOnly)
    {
	return createIdentifiedRegionWithCountryOnly(new CountryOnly(countryOnly));
    }
    
    public static IdentifiedRegion createIdentifiedRegionWithCountryOnly(CountryOnly countryOnly)
    {
	IdentifiedRegion __object = new IdentifiedRegion();

	__object.setCountryOnly(countryOnly);
	return __object;
    }
    
    public boolean hasCountryOnly()
    {
	return getChosenFlag() == countryOnly_chosen;
    }
    
    public CountryOnly getCountryOnly()
    {
	if (hasCountryOnly())
	    return (CountryOnly)mChosenValue;
	else
	    return null;
    }
    
    public void setCountryOnly(long countryOnly)
    {
	setCountryOnly(new CountryOnly(countryOnly));
    }
    
    public void setCountryOnly(CountryOnly countryOnly)
    {
	setChosenValue(countryOnly);
	setChosenFlag(countryOnly_chosen);
    }
    
    
    // Methods for field "countryAndRegions"
    public static IdentifiedRegion createIdentifiedRegionWithCountryAndRegions(CountryAndRegions countryAndRegions)
    {
	IdentifiedRegion __object = new IdentifiedRegion();

	__object.setCountryAndRegions(countryAndRegions);
	return __object;
    }
    
    public boolean hasCountryAndRegions()
    {
	return getChosenFlag() == countryAndRegions_chosen;
    }
    
    public CountryAndRegions getCountryAndRegions()
    {
	if (hasCountryAndRegions())
	    return (CountryAndRegions)mChosenValue;
	else
	    return null;
    }
    
    public void setCountryAndRegions(CountryAndRegions countryAndRegions)
    {
	setChosenValue(countryAndRegions);
	setChosenFlag(countryAndRegions_chosen);
    }
    
    
    // Methods for field "countryAndSubregions"
    public static IdentifiedRegion createIdentifiedRegionWithCountryAndSubregions(CountryAndSubregions countryAndSubregions)
    {
	IdentifiedRegion __object = new IdentifiedRegion();

	__object.setCountryAndSubregions(countryAndSubregions);
	return __object;
    }
    
    public boolean hasCountryAndSubregions()
    {
	return getChosenFlag() == countryAndSubregions_chosen;
    }
    
    public CountryAndSubregions getCountryAndSubregions()
    {
	if (hasCountryAndSubregions())
	    return (CountryAndSubregions)mChosenValue;
	else
	    return null;
    }
    
    public void setCountryAndSubregions(CountryAndSubregions countryAndSubregions)
    {
	setChosenValue(countryAndSubregions);
	setChosenFlag(countryAndSubregions_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case countryOnly_chosen:
		return new CountryOnly();
	    case countryAndRegions_chosen:
		return new CountryAndRegions();
	    case countryAndSubregions_chosen:
		return new CountryAndSubregions();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "IdentifiedRegion"
	),
	new QName (
	    "ProgrammingTypes",
	    "IdentifiedRegion"
	),
	536607,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"CountryOnly"
			    ),
			    new QName (
				"ProgrammingTypes",
				"CountryOnly"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new CountryOnly(0), 
				    new CountryOnly(65535),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(0),
				Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "countryOnly",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"CountryAndRegions"
			    ),
			    new QName (
				"ProgrammingTypes",
				"CountryAndRegions"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CountryAndRegions"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CountryAndRegions"
				)
			    ),
			    0
			)
		    ),
		    "countryAndRegions",
		    1,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"CountryAndSubregions"
			    ),
			    new QName (
				"ProgrammingTypes",
				"CountryAndSubregions"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CountryAndSubregions"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CountryAndSubregions"
				)
			    ),
			    0
			)
		    ),
		    "countryAndSubregions",
		    2,
		    2
		)
	    }
	),
	0,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1),
		new TagDecoderElement((short)0x8002, 2)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' IdentifiedRegion object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' IdentifiedRegion object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Check the current selection on unknown extension
     */
    public final boolean hasUnknownExtension()
    {
	return getChosenFlag() > 3;
    }
    
} // End class definition for IdentifiedRegion
