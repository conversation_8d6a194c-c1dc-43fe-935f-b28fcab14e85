/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the NinetyDegreeInt ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see INTEGER
 */

public class NinetyDegreeInt extends INTEGER {
    
    /**
     * The default constructor.
     */
    public NinetyDegreeInt()
    {
    }
    
    public NinetyDegreeInt(short value)
    {
	super(value);
    }
    
    public NinetyDegreeInt(int value)
    {
	super(value);
    }
    
    public NinetyDegreeInt(long value)
    {
	super(value);
    }
    
    /**
      An inner class that contains numeric values for ASN.1 INTEGER type with named numbers.
      The values can be used in switch/case statements.
    */
    public static final class Value {
	public static final long min = -900000000;
	public static final long max = 900000000;
	public static final long unknown = 900000001;
    }
    // Named list definitions.
    private final static NinetyDegreeInt cNamedNumbers[] = {
	new NinetyDegreeInt(-900000000), 
	new NinetyDegreeInt(900000000), 
	new NinetyDegreeInt(900000001)
    };
    public static final NinetyDegreeInt min = cNamedNumbers[0];
    public static final NinetyDegreeInt max = cNamedNumbers[1];
    public static final NinetyDegreeInt unknown = cNamedNumbers[2];
    
    protected final static long cFirstNumber = -900000000;
    protected final static boolean cLinearNumbers = false;
    
    public INTEGER[] getNamedNumbers()
    {
	return cNamedNumbers;
    }
    
    public boolean hasLinearNumbers()
    {
	return cLinearNumbers;
    }
    
    public long getFirstNumber()
    {
	return cFirstNumber;
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "NinetyDegreeInt"
	),
	new QName (
	    "ProgrammingTypes",
	    "NinetyDegreeInt"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new NinetyDegreeInt(-900000000), 
		new NinetyDegreeInt(900000001),
		0
	    )
	),
	new Bounds (
	    Long.valueOf(-900000000),
	    Long.valueOf(900000001)
	),
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "min",
		    -900000000
		),
		new MemberListElement (
		    "max",
		    900000000
		),
		new MemberListElement (
		    "unknown",
		    900000001
		)
	    }
	),
	4
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' NinetyDegreeInt object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' NinetyDegreeInt object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for NinetyDegreeInt
