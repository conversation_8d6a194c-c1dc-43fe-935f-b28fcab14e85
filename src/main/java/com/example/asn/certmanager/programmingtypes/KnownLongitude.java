/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

                      
import com.oss.metadata.*;

/**
 * Define the KnownLongitude ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see OneEightyDegreeInt
 */

public class KnownLongitude extends OneEightyDegreeInt {
    
    /**
     * The default constructor.
     */
    public KnownLongitude()
    {
    }
    
    public KnownLongitude(short value)
    {
	super(value);
    }
    
    public KnownLongitude(int value)
    {
	super(value);
    }
    
    public KnownLongitude(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "KnownLongitude"
	),
	new QName (
	    "ProgrammingTypes",
	    "KnownLongitude"
	),
	536603,
	new Intersection (
	    new ValueRangeConstraint (
		new AbstractBounds(
		    new KnownLongitude(-1799999999), 
		    new KnownLongitude(1800000001),
		    0
		)
	    ),
	    new ValueRangeConstraint (
		new AbstractBounds(
		    new KnownLongitude(-1799999999), 
		    new KnownLongitude(1800000000),
		    0
		)
	    )
	),
	new Bounds (
	    Long.valueOf(-1799999999),
	    Long.valueOf(1800000000)
	),
	new MemberList (
	    new MemberListElement[] {
		new MemberListElement (
		    "min",
		    -1799999999
		),
		new MemberListElement (
		    "max",
		    1800000000
		),
		new MemberListElement (
		    "unknown",
		    1800000001
		)
	    }
	),
	4
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' KnownLongitude object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' KnownLongitude object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for KnownLongitude
