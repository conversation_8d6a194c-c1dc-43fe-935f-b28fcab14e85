/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Uint16 ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see INTEGER
 */

public class Uint16 extends INTEGER {
    
    /**
     * The default constructor.
     */
    public Uint16()
    {
    }
    
    public Uint16(short value)
    {
	super(value);
    }
    
    public Uint16(int value)
    {
	super(value);
    }
    
    public Uint16(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "Uint16"
	),
	new QName (
	    "ProgrammingTypes",
	    "Uint16"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new Uint16(0), 
		new Uint16(65535),
		0
	    )
	),
	new Bounds (
	    Long.valueOf(0),
	    Long.valueOf(65535)
	),
	null,
	2
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Uint16 object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Uint16 object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for Uint16
