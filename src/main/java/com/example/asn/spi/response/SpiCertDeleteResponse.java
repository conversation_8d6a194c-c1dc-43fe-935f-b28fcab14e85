package com.example.asn.spi.response;

import com.example.asn.certmanager.cermanagementhttp.CertApplyType;
import com.example.asn.certmanager.cermanagementhttp.CertDeleteResponse;
import com.example.asn.certmanager.programmingtypes.CertificateId;
import com.example.asn.certmanager.programmingtypes.EcsigP256Signature;
import com.example.asn.certmanager.programmingtypes.SignatureValue;
import com.example.asn.spi.SPIClient;
import com.oss.asn1.OctetString;

import java.io.IOException;
import java.util.Arrays;

/**
 * 证书删除响应类
 * 对应MSG_TYPE_CERT_DELETE响应
 */
public class SpiCertDeleteResponse extends AbstractSpiResponse {
    
    private final byte[] certId;     // 证书ID (12字节)
    private final byte[] signature;  // 删除结果签名 (64字节)
    
    /**
     * 构造函数
     * @param statusCode 状态码
     * @param rawData 原始响应数据
     */
    public SpiCertDeleteResponse(byte statusCode, byte[] rawData) {
        super(statusCode, rawData);
        
        // 初始化默认值
        this.certId = new byte[12];
        this.signature = new byte[64];
        
        // 解析数据
        if (isSuccess() && rawData != null && rawData.length >= 77) {
            // 状态码(1) + 证书ID(12) + 删除结果签名(64)
            System.arraycopy(rawData, 1, this.certId, 0, 12);
            System.arraycopy(rawData, 13, this.signature, 0, 64);
        }
    }

    /**
     * 获取证书ID
     * @return 证书ID
     */
    public byte[] getCertId() {
        return certId;
    }

    /**
     * 获取删除结果签名
     * @return 删除结果签名
     */
    public byte[] getSignature() {
        return signature;
    }
    
    /**
     * 获取签名中的R值
     * @return R值 (32字节)
     */
    public byte[] getSignatureR() {
        return Arrays.copyOfRange(signature, 0, 32);
    }
    
    /**
     * 获取签名中的S值
     * @return S值 (32字节)
     */
    public byte[] getSignatureS() {
        return Arrays.copyOfRange(signature, 32, 64);
    }
    
    @Override
    public String toString() {
        return "CertDeleteResponse{" +
                "statusCode=" + getStatusCode() +
                ", statusMessage='" + getStatusMessage() + '\'' +
                ", certId=" + bytesToHex(certId) +
                ", signature=" + bytesToHex(signature) +
                '}';
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
    public  CertDeleteResponse wrap( byte certType) throws IOException {
        if (this.rawData == null) {
            throw new IOException("响应包为空");
        }

        // 检查状态码是否为成功
        if (!this.isSuccess()) {
            throw new IOException("删除证书失败，状态码: 0x" + String.format("%02X", this.getStatusCode()) +
                    ", 错误信息: " + this.getStatusMessage());
        }

        // 创建CertDeleteResponse对象
        CertDeleteResponse deleteResponse = new CertDeleteResponse();

        // 设置证书应用类型 - 使用OSS Nokalva生成的枚举类型预定义常量
        CertApplyType certApplyType;
        switch(certType) {
            case 0:
                certApplyType = CertApplyType.registerCertType;
                break;
            case 1:
                certApplyType = CertApplyType.applicationCertType;
                break;
            case 2:
                certApplyType = CertApplyType.registerCaCertType;
                break;
            case 3:
                certApplyType = CertApplyType.applicationCaCertType;
                break;
            default:
                throw new IOException("不支持的证书类型: " + certType);
        }
        deleteResponse.setCertType(certApplyType);

        // 设置证书ID - 使用OSS Nokalva生成的CertificateId类
        CertificateId certificateId = new CertificateId();
        certificateId.setBinaryId(new OctetString(certId));
        deleteResponse.setCertId(certificateId);

        // 设置签名值 - 使用OSS Nokalva生成的SignatureValue和EcsigP256Signature类
        SignatureValue signatureValue = new SignatureValue();
        EcsigP256Signature sm2Signature = createEcsigP256SignatureFromSM2(signature);
        signatureValue.setSm2Signature(sm2Signature);
        deleteResponse.setSignatureValue(signatureValue);

        return deleteResponse;
    }

    /**
     * 从SM2签名字节创建ASN.1的EcsigP256Signature对象
     * SM2签名通常是64字节，前32字节是r值，后32字节是s值
     */
    private static EcsigP256Signature createEcsigP256SignatureFromSM2(byte[] signatureBytes) {
        // SM2签名分为r和s两部分，各32字节
        byte[] rBytes = new byte[32];
        byte[] sBytes = new byte[32];
        System.arraycopy(signatureBytes, 0, rBytes, 0, 32);
        System.arraycopy(signatureBytes, 32, sBytes, 0, 32);

        // 使用OSS Nokalva的OctetString替代BerOctetString
        OctetString rSig = new OctetString(rBytes);
        OctetString sSig = new OctetString(sBytes);

        // 使用构造函数创建EcsigP256Signature对象
        return new EcsigP256Signature(rSig, sSig);
    }
}
