package com.example.asn.http;

import java.io.*;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Random;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.example.asn.bean.AuthCache;
import com.example.asn.certmanager.Certmanager;
import com.example.asn.certmanager.cermanagementhttp.CertDeleteResponse;
import com.example.asn.certmanager.cermanagementhttp.PubKeyResponse;
import com.example.asn.certmanager.programmingtypes.Certificate;
import com.example.asn.db.AlarmDAO;
import com.example.asn.spi.response.*;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.example.asn.certmanager.CertManagerCoerFactory;
import com.example.asn.certmanager.cermanagementhttp.*;
import com.example.asn.certmanager.programmingtypes.EcsigP256Signature;
import com.example.asn.certmanager.programmingtypes.Uint8;

import com.example.asn.spi.SPIClient;

import com.example.asn.codec.OssCoerAdapter;
import com.oss.asn1.OctetString;

/**
 * 处理证书管理相关的HTTP请求的Servlet
 * 使用COER编码规则进行编码和解码
 * 
 * 连续认证流程：
 * 1. 客户端发送身份认证请求(AuthenticationCertTransmit)
 * 2. 服务器处理请求并返回初始认证状态响应(AuthenticationStateResponse)，但保持连接开放
 * 3. 客户端发送一系列加密密文请求(EncryptedCiphertext)
 * 4. 服务器处理每个密文请求并返回相应的密文响应，但保持连接开放
 * 5. 客户端在完成所有密文请求后，发送最终身份认证请求或关闭连接
 * 6. 服务器返回最终身份认证响应并关闭连接
 */
public class CertManagerServlet extends HttpServlet {
    private static final Logger logger = LoggerFactory.getLogger(CertManagerServlet.class);

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // 配置HTTP响应头，确保支持长连接
            response.setContentType("application/octet-stream");
            response.setHeader("Connection", "keep-alive");
            response.setHeader("Keep-Alive", "timeout=120, max=1000");
            
            // 读取请求体
            byte[] requestBody = readRequestData(request);
            logger.info("RECV:{}",Hex.toHexString(requestBody));
            if (requestBody.length == 0) {
                logger.error("请求体为空");
//                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "请求体为空");
                sendHttpErrorResponse(response,HttpServletResponse.SC_BAD_REQUEST, "请求体为空");
                return;
            }
            
            logger.info("收到请求，请求体长度: {} 字节", requestBody.length);

            // 使用COER解码请求
            MessageRequestFrame frame;
            try {
                frame = Certmanager.getCOERCoder().decode(ByteBuffer.wrap(requestBody), new MessageRequestFrame());
                logger.info(frame.toString());
//                frame = CertManagerCoerFactory.decodeMessageRequestFrame(requestBody);
            } catch (Exception e) {
                logger.error("解码请求失败", e);
//                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "请求解码失败");
                sendHttpErrorResponse(response,HttpServletResponse.SC_BAD_REQUEST, "请求解码失败");
                return;
            }

            // 检查会话状态，确定是否处于认证流程中
            String authStage = null;
            if (frame.getContent().hasIdentityAuthentication() &&
                    frame.getContent().getIdentityAuthentication() != null) {//清空所有
                ServletContext servletContext = getServletContext();
                Enumeration<String> attributeNames = servletContext.getAttributeNames();
                while (attributeNames.hasMoreElements()) {
                    String attributeName = attributeNames.nextElement();
                    servletContext.removeAttribute(attributeName);
                }
            }else{

//            if (request.getSession(false)!= null){
                Object stageObj = getServletContext().getAttribute("auth_stage");
                if (stageObj != null && stageObj instanceof String) {
                    authStage = (String) stageObj;
                }
//            }
            }

            // 如果会话中有认证状态，则处理多阶段认证
            if (authStage != null) {
                AuthCache authCache = (AuthCache) getServletContext().getAttribute("auth_cache");
                if (authCache == null) {
                    logger.error("会话中没有认证缓存，终止认证流程");
//                    response.sendError(HttpServletResponse.SC_BAD_REQUEST, "认证会话已失效");
                    sendHttpErrorResponse(response,HttpServletResponse.SC_BAD_REQUEST, "认证会话已失效");
                    return;
                }

                // 根据当前认证阶段分发处理
                if ("waiting_for_ciphertext1".equals(authStage)) {
                    logger.info("处理认证流程第二阶段：接收并处理第一个密文消息");
                    handleAuthStage2(request, response, frame, authCache);
                    return;
                } else if ("waiting_for_ciphertext2".equals(authStage)) {
                    logger.info("处理认证流程第三阶段：接收并处理第二个密文消息");
                    handleAuthStage3(request, response, frame, authCache);
                    return;
                }
            }
            // 正常处理消息（包括初始认证或非认证类请求）
            processMessage(frame, request, response);

        } catch (Exception e) {
            logger.error("处理证书管理请求时发生异常", e);
//            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误");
            sendHttpErrorResponse(response,HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误");
        }
    }

    private void processMessage(MessageRequestFrame requestFrame, HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        logger.info("处理请求消息");

        try {
            // 检查请求帧和内容是否为null
            if (requestFrame == null) {
                logger.error("请求帧对象为null");
//                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的请求格式");
                sendHttpErrorResponse(response,HttpServletResponse.SC_BAD_REQUEST, "无效的请求格式");
                return;
            }

            if (requestFrame.getContent() == null) {
                logger.error("请求内容为null");
//                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "请求内容为空");
                sendHttpErrorResponse(response,HttpServletResponse.SC_BAD_REQUEST, "请求内容为空");
                return;
            }

            // 打印请求内容的详细信息，帮助调试
            logger.debug("请求内容类型: {}", requestFrame.getContent().getClass().getName());

            // 如果是身份认证请求，使用连续认证流程处理
            if (requestFrame.getContent().hasIdentityAuthentication() &&
                requestFrame.getContent().getIdentityAuthentication() != null) {
                logger.info("收到身份认证请求，启动连续认证流程");
                // 处理身份认证请求，保持连接并等待后续密文请求
                handleContinuousAuthentication(requestFrame, request, response);
                return;
            }

            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(new Uint8(1));
            responseFrame.setContent(new MessageResponseFrame.Content());
            // 复制请求版本到响应(如果有)
            if (requestFrame.getVersion() != null) {
                responseFrame.setVersion(requestFrame.getVersion());
            }

            // 根据请求类型分别处理，添加更多的null检查
            boolean processed = false;

            if (requestFrame.getContent().hasCertificationQuery() &&
                requestFrame.getContent().getCertificationQuery() != null) {
                // 检查认证状态
                if (!isAuthenticated(request)) {
                    logger.error("未认证的证书查询请求");
                    sendHttpErrorResponse(response,HttpServletResponse.SC_UNAUTHORIZED,"未认证的请求");
//                    response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    return;
                }
                // 处理证书查询请求
                responseFrame.getContent().setCertificationQuery(
                    processCertInquiryRequest(requestFrame.getContent().getCertificationQuery())
                );
                processed = true;
            } else if (requestFrame.getContent().hasCertificationWrite() &&
                       requestFrame.getContent().getCertificationWrite() != null) {
                // 检查认证状态
                if (!isAuthenticated(request)) {
                    logger.error("未认证的证书写入请求");
//                    response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    sendHttpErrorResponse(response,HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    return;
                }
                // 处理证书写入请求
                responseFrame.getContent().setCertificationWrite(
                    processCertWriteRequest(requestFrame.getContent().getCertificationWrite())
                );
                processed = true;
            } else if (requestFrame.getContent().hasCertificationDelete() &&
                       requestFrame.getContent().getCertificationDelete() != null) {
                // 检查认证状态
                if (!isAuthenticated(request)) {
                    logger.error("未认证的证书删除请求");
//                    response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    sendHttpErrorResponse(response,HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    return;
                }
                // 处理证书删除请求
                responseFrame.getContent().setCertificationDelete(
                    processCertDeleteRequest(requestFrame.getContent().getCertificationDelete())
                );
                processed = true;
            } else if (requestFrame.getContent().hasPublicKeyGet() &&
                       requestFrame.getContent().getPublicKeyGet() != null) {
                // 检查认证状态
                if (!isAuthenticated(request)) {
                    logger.error("未认证的公钥获取请求");
//                    response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    sendHttpErrorResponse(response,HttpServletResponse.SC_UNAUTHORIZED, "未认证的请求");
                    return;
                }
                // 处理公钥获取请求
                responseFrame.getContent().setPublicKeyGet(
                    processPubKeyRequest(requestFrame.getContent().getPublicKeyGet())
                );
                processed = true;
            } else if (requestFrame.getContent().hasEncryptedCiphertext() &&
                       requestFrame.getContent().getEncryptedCiphertext() != null) {
                // 单独处理加密密文请求（非连续认证流程）
                AuthCache tempAuthCache = new AuthCache();
                responseFrame.getContent().setEncryptedCiphertext(
                    processEncryptedCiphertextRequest1(requestFrame.getContent().getEncryptedCiphertext(), tempAuthCache)
                );
                processed = true;
            }

            if (!processed) {
                // 未知或无效的请求类型
                logger.warn("未识别的请求类型或请求内容有误");
                ErrorResponse errorResponse = createErrorResponse("未识别的请求类型或请求内容有误");
                responseFrame.getContent().setError(errorResponse);
            }

            // 编码响应
            byte[] responseData = encodeResponseFrame(responseFrame);
            logger.info(responseFrame.toString());
            logger.info("response:{}",Hex.toHexString(responseData));
            // 发送响应
            response.setContentLength(responseData.length);
            response.getOutputStream().write(responseData);

        } catch (Exception e) {
            logger.error("处理请求时发生异常", e);
            throw new ServletException("处理请求时发生异常", e);
        }
    }

    private byte[] readRequestData(HttpServletRequest request) throws IOException {
        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            logger.warn("无效的Content-Length: {}", contentLength);
            throw new IOException("Invalid content length: " + contentLength);
        }

        logger.debug("读取请求体，Content-Length: {} 字节", contentLength);
        byte[] buffer = new byte[contentLength];
        try (InputStream is = request.getInputStream()) {
            int totalBytesRead = 0;
            while (totalBytesRead < contentLength) {
                int bytesRead = is.read(buffer, totalBytesRead, contentLength - totalBytesRead);
                if (bytesRead == -1) {
                    break;
                }
                totalBytesRead += bytesRead;
            }
            if (totalBytesRead != contentLength) {
                logger.warn("读取的数据不完整：已读取 {} 字节，预期 {} 字节", totalBytesRead, contentLength);
                throw new IOException("Expected " + contentLength + " bytes but read " + totalBytesRead + " bytes");
            }
            return buffer;
        } catch (IOException e) {
            logger.error("读取请求数据时发生异常", e);
            throw e;
        }
    }

    private CertInquiryResponse processCertInquiryRequest(CertInquiryRequest request) {
        logger.info("Processing CertInquiryRequest");
        // 实现证书查询逻辑
        CertInquiryResponse response = new CertInquiryResponse();
        response.setCertType(request.getCertType());
        response.setCertIndex(request.getCertIndex());
        SPIClient client = new SPIClient();
        try {
            byte certType = (byte)request.getCertType().longValue();
            byte certIndex = (byte)request.getCertIndex().longValue();
            logger.info("CertType: {}, CertIndex: {}", certType, certIndex);
            certType++; //spi从1开始
            logger.info("CertType: {}, CertIndex: {}", certType, certIndex);
            CertQueryResponse certQueryResponse = client.queryCert(certType, certIndex);
            // 使用适配器将SPI响应转换为CertInquiryResponse
            return certQueryResponse.wrap();
        } catch (IOException e) {
            logger.error("Error querying certificate", e);
            throw new RuntimeException("查询证书失败: " + e.getMessage(), e);
        }
    }

    private CertWriteResponse processCertWriteRequest(CertWriteRequest request) {
        logger.info("Processing CertWriteRequest");
        // 实现证书写入逻辑
        SPIClient client = new SPIClient();
        try {
            byte certType = (byte)request.getCertType().longValue();
            certType++;
            byte certIndex = (byte)request.getCertIndex().longValue();
            byte[] certData = null;

            // 获取证书数据
            if (request.getCert() != null) {
                try {
                    // Use the OssCoerAdapter to encode Certificate
                    certData = OssCoerAdapter.encode(request.getCert());
                } catch (IOException e) {
                    throw new IOException("获取证书编码数据失败", e);
                }
            } else {
                throw new IOException("证书数据为空");
            }

            logger.info("准备写入证书, 类型: {}, 索引: {}, 数据长度: {}",
                    certType, certIndex, certData.length);

            // 调用SPI接口写入证书
            SpiCertWriteResponse spiCertWriteResponse = client.writeCert(certType, certData);

            // 使用适配器将SPI响应转换为CertWriteResponse
            return spiCertWriteResponse.wrap(certType, certIndex);
        } catch (IOException e) {
            logger.error("Error writing certificate", e);
            throw new RuntimeException("写入证书失败: " + e.getMessage(), e);
        }
    }

    private CertDeleteResponse processCertDeleteRequest(CertDeleteRequest request) {
        logger.info("Processing CertDeleteRequest");
        // 实现证书删除逻辑
        SPIClient client = new SPIClient();
        try {
            byte certType = (byte)request.getCertType().longValue();
            certType++;
            // 获取证书ID
            byte[] certID = null;
            if (request.getCertId() != null) {
                certID = new byte[request.getCertId().getName().getSize()];
                System.arraycopy(request.getCertId().getName().byteArrayValue(), 0, certID, 0, certID.length);
            } else {
                throw new IOException("证书ID为空");
            }

            // 获取签名值
            byte[] signature = null;
            if (request.getSignatureValue() != null &&
                request.getSignatureValue().getSm2Signature() != null) {

                // 从SignatureValue中获取SM2签名数据（r和s）
                EcsigP256Signature sm2Sig = request.getSignatureValue().getSm2Signature();
                byte[] r = new byte[sm2Sig.getRSig().getSize()];
                byte[] s = new byte[sm2Sig.getSSig().getSize()];
                System.arraycopy(sm2Sig.getRSig().byteArrayValue(), 0, r, 0, r.length);
                System.arraycopy(sm2Sig.getSSig().byteArrayValue(), 0, s, 0, s.length);

                // 合并r和s为SM2签名格式
                signature = new byte[r.length + s.length];
                System.arraycopy(r, 0, signature, 0, r.length);
                System.arraycopy(s, 0, signature, r.length, s.length);
            } else {
                throw new IOException("签名值为空");
            }

            logger.info("准备删除证书, 类型: {}, 证书ID长度: {}, 签名长度: {}",
                    certType, certID.length, signature.length);

            // 调用SPI接口删除证书
            SpiCertDeleteResponse certDeleteResponse = client.deleteCert(certType, certID, signature);

            // 使用适配器将SPI响应转换为CertDeleteResponse
            return certDeleteResponse.wrap(certType);
        } catch (IOException e) {
            logger.error("Error deleting certificate", e);
            throw new RuntimeException("删除证书失败: " + e.getMessage(), e);
        }
    }
    public byte[] readFileToByteArray(String filePath) {
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            logger.error("File does not exist or is not a file: {}", filePath);
            return null;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            logger.info("Successfully read {} bytes from file: {}", data.length, filePath);
            return data;
        } catch (IOException e) {
            logger.error("Error reading file: {}", filePath, e);
            return null;
        }
    }
    private PubKeyResponse processPubKeyRequest(PubKeyRequest request) {
        logger.info("Processing PubKeyRequest");
        // 实现公钥请求逻辑
        SPIClient client = new SPIClient();
        try {
            byte certType = (byte)request.getCertType().longValue();
            certType++;
            byte pubKeyIndex = (byte)request.getPubKeyIndex().longValue();

            // 调用SPI接口获取公钥
            SpiPubKeyResponse response = client.getPubKey(certType, pubKeyIndex);

            // 使用适配器将SPI响应转换为PubKeyResponse
            return response.wrap();
        } catch (IOException e) {
            logger.error("Error getting public key", e);
            throw new RuntimeException("获取公钥失败: " + e.getMessage(), e);
        }
    }

    private EncryptedCiphertext processEncryptedCiphertextRequest1(EncryptedCiphertext request,AuthCache authCache) {
        logger.info("Processing EncryptedCiphertext request");

        SPIClient client = new SPIClient();
        try {
            // 提取加密数据
            byte[] encryptedData = null;
            if (request.getEncryptDatas() != null) {
                // Use the OssCoerAdapter to encode EncryptDatas
                encryptedData = request.getEncryptDatas().get(0).getEncryptData().byteArrayValue();
                //测试多了头一个字节长度
//                byte[] coerData = new byte[encryptedData.length - 1];
//                System.arraycopy(encryptedData, 1, coerData, 0, coerData.length);
//                encryptedData = coerData;
            }

            if (encryptedData == null) {
                throw new IOException("加密数据为空");
            }

            logger.info("收到加密密文长度: {},值为:{}", encryptedData.length, Hex.toHexString(encryptedData));

            // 调用SPI进行密文处理
            byte []  R2 = new byte[2]; //随机数
            new Random().nextBytes(R2);
//            logger.info("R2: {}", Hex.toHexString(R2));
            authCache.setR2(R2);
            DecryptResponse decryptResponse = client.decryptWithPrivKey(authCache.getCertType(),authCache.getClientCertIndex(), encryptedData);
            byte [] R1 = decryptResponse.getPlainData();
            File file = new File("/etc/unimas/tomcat/conf/random_txt");
            if(file.exists()) {
                R2 = readFileToByteArray("/etc/unimas/tomcat/conf/random_txt");
            }
////            byte [] R1 = R2;
//            R1 = new byte[2];
//            Arrays.fill(R1, (byte)1);
//            R2 = new byte[2];
//            Arrays.fill(R2, (byte)1);
            logger.info("R1 LENGTH: {}", R1.length);
            logger.info("R1: {}", Hex.toHexString(R1));
            logger.info("R2 LENGTH: {}", R2.length);
            logger.info("R2: {}", Hex.toHexString(R2));
            EncryptResponse encryptResponse = client.encryptWithPubKey(CertManagerCoerFactory.encodeCertificate(authCache.getClientCert()), R1);
            byte[] C2 = encryptResponse.getCipherData();
            logger.info("C2: {}", Hex.toHexString(C2));
            EncryptResponse encryptResponse1 = client.encryptWithPubKey(CertManagerCoerFactory.encodeCertificate(authCache.getClientCert()), R2);
            byte[] C3 = encryptResponse1.getCipherData();
            logger.info("C3: {}", Hex.toHexString(C3));
            EncryptedCiphertext encryptedCiphertext = new EncryptedCiphertext();
            EncryptedCiphertext.EncryptDatas encryptData = new EncryptedCiphertext.EncryptDatas();
            EncryptedData encryptedDataC2 = new EncryptedData();
            encryptedDataC2.setEncryptData(new OctetString(C2));
            encryptData.add(encryptedDataC2);

            EncryptedData encryptedDataC3 = new EncryptedData();
            encryptedDataC3.setEncryptData(new OctetString(C3));
            encryptData.add(encryptedDataC3);

            encryptedCiphertext.setEncryptDatas(encryptData);
            // 使用适配器将SPI响应转换为EncryptedCiphertext
            return encryptedCiphertext;
        } catch (IOException e) {
            logger.error("Error processing encrypted ciphertext", e);

            // 返回一个空响应
            EncryptedCiphertext response = new EncryptedCiphertext();
            EncryptedCiphertext.EncryptDatas encryptDatas = new EncryptedCiphertext.EncryptDatas();
            response.setEncryptDatas(encryptDatas);

            return response;
        }
    }
    private boolean processEncryptedCiphertextRequest2(EncryptedCiphertext request,AuthCache authCache) {
        logger.info("Processing EncryptedCiphertext request");

        SPIClient client = new SPIClient();
        try {
            // 提取加密数据
            byte[] encryptedData = null;
            if (request.getEncryptDatas() != null) {
                // Use the OssCoerAdapter to encode EncryptDatas
                encryptedData = request.getEncryptDatas().get(0).getEncryptData().byteArrayValue();
            }

            if (encryptedData == null) {
                throw new IOException("加密数据为空");
            }

            logger.info("收到加密密文长度: {}", encryptedData.length);

            // 调用SPI进行密文处理
//            byte []  data = new byte[32]; //随机数
////            data = "unimas".getBytes();
//            new Random().nextBytes(data);
//            logger.info("R2: {}", Hex.toHexString(data));
//            authCache.setR2(data);
            logger.info("C4: {}", Hex.toHexString(encryptedData));
            DecryptResponse decryptResponse = client.decryptWithPrivKey(authCache.getCertType(), authCache.getClientCertIndex(), encryptedData);

            byte[] R21 = decryptResponse.getPlainData();
            logger.info("R21: {}", Hex.toHexString(R21));
//            R21 = "unimas".getBytes();
            return Arrays.equals(authCache.getR2(), R21);
        } catch (IOException e) {
            logger.error("Error processing encrypted ciphertext", e);

            return false;
        }
    }

    /**
     * 处理连续认证流程
     * 首先处理身份认证请求，然后保持连接开放，处理两次后续密文请求
     * 完成所有密文请求处理后返回身份认证响应
     *
     * 流程：
     * a) 注册终端发起身份认证请求，请求消息中包含注册终端数字证书
     * b) 管控设施响应身份认证请求，响应消息中包含管控设施数字证书
     * c) 注册终端生成随机数R1，使用管控设施公钥加密随机数R1为密文C1
     * d) 注册终端发送C1密文消息至管控设施
     * e) 管控设施使用私钥解密密文C1得到随机数R1，生成随机数R2，使用注册终端公钥分别加密随机数R1、R2为得到密文C2、C3
     * f) 管控设施发送C2、C3密文消息至注册终端
     * g) 注册终端使用私钥解密密文C2得到R1'，比较R1与R1'，相同则注册终端验证管控设施成功，不同则验证失败
     * h) 注册终端使用私钥解密密文C3得到随机数R2，并使用设施公钥加密R2为密文C4
     * i) 注册终端发送C4密文消息至管控设施
     * j) 管控设施使用私钥解密密文C4得到,比较R2与R2'，相同则管控设施验证注册终端成功，不同则验证失败
     * k) 管控设施发送身份认证状态消息至注册终端
     *
     * @param initialRequestFrame 初始认证请求帧
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException 如果处理过程中发生错误
     * @throws IOException 如果I/O操作失败
     */
    private void handleContinuousAuthentication(MessageRequestFrame initialRequestFrame, HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        logger.info("开始连续认证流程处理");

        // 设置响应不缓冲，确保可以即时发送响应
        if (!response.isCommitted()) {
            response.setBufferSize(8192);
            response.setContentType("application/octet-stream");

            // 禁用响应缓存，确保客户端能立即收到每个响应
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 设置HTTP/1.1和长连接头，确保连接保持开放
            response.setHeader("Connection", "keep-alive");
            response.setHeader("Keep-Alive", "timeout=120, max=1000");
        }

        try {
            // 获取输出流
            OutputStream outputStream = response.getOutputStream();

            // 步骤a-b: 处理初始身份认证请求，发送管控设施数字证书
            logger.info("步骤a-b: 处理初始身份认证请求");

            // 检查请求是否包含身份认证信息
            if (!initialRequestFrame.getContent().hasIdentityAuthentication()) {
                logger.error("初始请求不包含身份认证信息，终止认证流程");
                sendHttpErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, "初始请求不包含身份认证信息");
                return;
            }
            AuthCache authCache = new AuthCache();
            // 处理身份认证并获取服务器证书响应
            AuthenticationCertTransmit serverCertResponse = processAuthenticationRequest(
                initialRequestFrame.getContent().getIdentityAuthentication(),authCache
            );
            byte certIndex = 1;
            authCache.setClientCertIndex(certIndex);
            // 创建并发送初始认证处理结果
            MessageResponseFrame initialResponseFrame = new MessageResponseFrame();
            initialResponseFrame.setVersion(new com.example.asn.certmanager.programmingtypes.Uint8(1));
            initialResponseFrame.setContent(new MessageResponseFrame.Content());
            initialResponseFrame.getContent().setIdentityAuthentication(serverCertResponse);

            // 编码并发送初始响应
            byte[] initialResponseData = encodeResponseFrame(initialResponseFrame);
            logger.info("发送初始身份认证响应: 长度 = {} 字节", initialResponseData.length);
            if (!response.isCommitted()) {
                response.setContentLength(initialResponseData.length);
                // 在会话中存储authCache，用于在后续请求中恢复状态
                getServletContext().setAttribute("auth_cache", authCache);
                getServletContext().setAttribute("auth_stage", "waiting_for_ciphertext1");
                outputStream.write(initialResponseData);
                outputStream.flush();

                logger.info("已发送服务器证书响应，等待接收第一个密文消息");

                // 等待客户端发送的后续请求将通过标准的doPost方法捕获并处理
                // 我们使用SessionID或其他机制来关联同一个认证流程的多个请求



                // 记录日志，等待客户端下一个请求
                logger.info("初始认证阶段完成，等待客户端发送第一个密文消息");
            } else {
                logger.error("响应已提交，无法发送初始认证响应");
                throw new ServletException("响应已提交，无法继续处理认证流程");
            }
        } catch (Exception e) {
            logger.error("连续认证处理过程中发生异常", e);
            if (!response.isCommitted()) {
                sendHttpErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "连续认证处理失败");
            }
            throw new ServletException("连续认证处理失败", e);
        }
    }

    /**
     * 处理认证流程的第二阶段：接收并处理第一个密文消息
     */
    private void handleAuthStage2(HttpServletRequest request, HttpServletResponse response, MessageRequestFrame frame, AuthCache authCache)
            throws ServletException, IOException {
        logger.info("步骤c-d: 处理第一个密文消息C1");

        // 检查是否为密文请求
        if (!frame.getContent().hasEncryptedCiphertext()) {
            logger.error("请求不是密文消息，终止认证流程");
            sendHttpErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, "请求不是密文消息");
            return;
        }

        // 设置响应头（在写入响应体之前）
        if (!response.isCommitted()) {
            response.setContentType("application/octet-stream");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
        }

        try {
            // 步骤e-f: 处理第一个密文消息C1，发送C2、C3密文消息
            logger.info("步骤e-f: 处理第一个密文消息C1，生成C2、C3密文消息");
            EncryptedCiphertext firstCiphertextResponse = processEncryptedCiphertextRequest1(frame.getContent().getEncryptedCiphertext(), authCache);

            // 创建并发送第一个密文响应
            MessageResponseFrame firstCiphertextResponseFrame = new MessageResponseFrame();
            firstCiphertextResponseFrame.setVersion(new com.example.asn.certmanager.programmingtypes.Uint8(1));
            firstCiphertextResponseFrame.setContent(new MessageResponseFrame.Content());
            firstCiphertextResponseFrame.getContent().setEncryptedCiphertext(firstCiphertextResponse);

            // 编码并发送响应
            byte[] firstResponseData = encodeResponseFrame(firstCiphertextResponseFrame);
            logger.info("发送包含C2和C3的密文响应: 长度 = {} 字节", firstResponseData.length);
            logger.info("内容 = {} 字节", Hex.toHexString(firstResponseData));
            // 检查响应是否已提交
            if (!response.isCommitted()) {
                response.setContentLength(firstResponseData.length);
                OutputStream outputStream = response.getOutputStream();
                outputStream.write(firstResponseData);
                outputStream.flush();

                // 更新会话状态，准备下一阶段
                getServletContext().setAttribute("auth_stage", "waiting_for_ciphertext2");

                // 记录日志，等待客户端下一个请求
                logger.info("第二认证阶段完成，等待客户端发送第二个密文消息");
            } else {
                logger.error("响应已提交，无法发送阶段2响应");
                throw new ServletException("响应已提交，无法继续处理认证流程");
            }
        } catch (Exception e) {
            logger.error("处理阶段2认证时发生错误", e);
            if (!response.isCommitted()) {
                sendHttpErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "处理认证请求失败");
            }
            throw new ServletException("处理第二阶段认证失败", e);
        }
    }

    /**
     * 处理认证流程的第三阶段：接收并处理第二个密文消息
     */
    private void handleAuthStage3(HttpServletRequest request, HttpServletResponse response, MessageRequestFrame frame, AuthCache authCache)
            throws ServletException, IOException {
        logger.info("步骤g-i: 处理第二个密文消息C4");

        // 检查是否为密文请求
        if (!frame.getContent().hasEncryptedCiphertext()) {
            logger.error("请求不是密文消息，终止认证流程");
            sendHttpErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, "请求不是密文消息");
            return;
        }

        // 设置响应头（在写入响应体之前）
        if (!response.isCommitted()) {
            response.setContentType("application/octet-stream");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
        }

        try {
            // 步骤j: 处理第二个密文消息C4，验证R2
            boolean verificationSuccessful = processEncryptedCiphertextRequest2(frame.getContent().getEncryptedCiphertext(), authCache);

            // 获取处理结果，判断验证是否成功
            logger.info("R2验证结果: {}", verificationSuccessful ? "成功" : "失败");

            // 步骤k: 发送身份认证状态消息
            logger.info("步骤k: 发送身份认证状态消息");

            // 创建最终认证状态响应
            AuthenticationStateResponse finalAuthResponse = new AuthenticationStateResponse();
            // 根据第二个密文处理结果设置认证状态
            finalAuthResponse.setState(verificationSuccessful ? AuthenticationState.succeed : AuthenticationState.failed);

            // 创建并发送最终认证响应
            MessageResponseFrame finalResponseFrame = new MessageResponseFrame();
            finalResponseFrame.setVersion(new com.example.asn.certmanager.programmingtypes.Uint8(1));
            finalResponseFrame.setContent(new MessageResponseFrame.Content());
            finalResponseFrame.getContent().setAuthenticationState(finalAuthResponse);

            // 编码并发送最终响应
            byte[] finalResponseData = encodeResponseFrame(finalResponseFrame);
            logger.info("发送最终认证状态响应: 长度 = {} 字节", finalResponseData.length);

            // 检查响应是否已提交
            if (!response.isCommitted()) {
                response.setContentLength(finalResponseData.length);
                OutputStream outputStream = response.getOutputStream();
                outputStream.write(finalResponseData);
                outputStream.flush();

                // 清理会话状态
//                request.getSession().removeAttribute("auth_cache");
                getServletContext().removeAttribute("auth_stage");
                
                // 存储认证结果到会话
                getServletContext().setAttribute("auth_successful", verificationSuccessful);

                logger.info("身份认证流程完成，认证结果: {}", verificationSuccessful ? "成功" : "失败");
            } else {
                logger.error("响应已提交，无法发送最终认证状态");
                throw new ServletException("响应已提交，无法完成认证流程");
            }
        } catch (Exception e) {
            logger.error("处理阶段3认证时发生错误", e);
            if (!response.isCommitted()) {
                sendHttpErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "处理认证请求失败");
            }
            throw new ServletException("处理第三阶段认证失败", e);
        }
    }

    /**
     * 发送错误响应
     *
     * @param outputStream 输出流
     * @param errorMessage 错误信息
     * @throws IOException 如果发送失败
     */
    private void sendErrorResponse(OutputStream outputStream, String errorMessage) {
        try {
            // 创建错误响应帧
            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(new com.example.asn.certmanager.programmingtypes.Uint8(1));
            responseFrame.setContent(new MessageResponseFrame.Content());

            // 创建错误响应对象
            ErrorResponse errorResponse = new ErrorResponse();
            errorResponse.setErrorState(CertProcessErrorState.unknownError);

            // 设置错误响应
            responseFrame.getContent().setError(errorResponse);

            // 编码并发送响应
            byte[] responseData = encodeResponseFrame(responseFrame);
            logger.info("发送错误响应: {}", errorMessage);
            outputStream.write(responseData);
            outputStream.flush();
        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }

    /**
     * 发送HTTP错误响应，在response尚未提交时发送HTTP错误，否则尝试写入错误消息
     *
     * @param response HTTP响应对象
     * @param errorCode HTTP错误代码
     * @param errorMessage 错误信息
     */
    private void sendHttpErrorResponse(HttpServletResponse response, int errorCode, String errorMessage) {
        try {
            // 如果是401未授权错误，记录到警报表中
            if (errorCode == HttpServletResponse.SC_UNAUTHORIZED) {
                try {
                    // 创建警报记录
                    AlarmDAO alarmDAO = new AlarmDAO();
                    String appType = "0";
                    String appName = "0";
                    int alarmType = 7; // illegalCertificate (未授权访问通常是证书相关问题)
                    String alarmMessage = "未授权访问: " + errorMessage;
                    
                    // 插入警报记录
                    long alarmId = alarmDAO.insertAuthAlarm(appType, appName, alarmType, alarmMessage);
                    if (alarmId > 0) {
                        logger.info("已记录未授权访问警报，ID: {}", alarmId);
                    } else {
                        logger.warn("记录未授权访问警报失败");
                    }
                } catch (Exception e) {
                    logger.error("记录401警报时发生错误", e);
                }
            }
            
            if (!response.isCommitted()) {
                // 如果响应未提交，发送HTTP错误
                response.sendError(errorCode, errorMessage);
//                sendHttpErrorResponse(response, errorCode, errorMessage);
            } else {
                // 如果响应已提交，尝试写入错误信息作为普通响应
                logger.warn("响应已提交，无法发送HTTP错误状态码，尝试写入错误消息");

                // 创建错误响应帧并写入
                MessageResponseFrame responseFrame = new MessageResponseFrame();
                responseFrame.setVersion(new com.example.asn.certmanager.programmingtypes.Uint8(1));
                responseFrame.setContent(new MessageResponseFrame.Content());

                // 创建错误响应对象
                ErrorResponse errorResponse = new ErrorResponse();
                errorResponse.setErrorState(CertProcessErrorState.unknownError);

                // 设置错误响应
                responseFrame.getContent().setError(errorResponse);

                byte[] responseData = encodeResponseFrame(responseFrame);
                try {
                    OutputStream os = response.getOutputStream();
                    os.write(responseData);
                    os.flush();
                } catch (IllegalStateException e) {
                    logger.error("无法发送错误响应，响应流已关闭", e);
                }
            }
        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }

    /**
     * 编码响应帧
     *
     * @param frame 响应帧对象
     * @return 编码后的字节数组
     * @throws IOException 如果编码失败
     */
    private byte[] encodeResponseFrame(MessageResponseFrame frame) throws IOException {
        return OssCoerAdapter.encode(frame);
    }

    /**
     * 检查客户端是否仍然保持连接
     *
     * @param request HTTP请求对象
     * @return true如果客户端仍然连接，false如果已断开
     * @deprecated 在当前固定流程实现中不再需要此方法，保留以备将来扩展
     */
    @Deprecated
    @SuppressWarnings("unused")
    private boolean isClientConnected(HttpServletRequest request) {
        try {
            // 尝试通过请求的属性或客户端TCP连接状态检查连接是否仍然活跃
            // 注意：这种方法不是所有Servlet容器都支持
            return !request.isAsyncStarted() || request.getAsyncContext().getRequest() != null;
        } catch (Exception e) {
            // 如果方法不可用，假设客户端仍然连接
            return true;
        }
    }

    /**
     * 创建通用错误响应对象
     *
     * @param errorMessage 错误信息
     * @return 错误响应对象
     */
    private ErrorResponse createErrorResponse(String errorMessage) {
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorState(CertProcessErrorState.unknownError);
        return errorResponse;
    }

    /**
     * 处理身份认证请求
     *
     * @param request 身份认证请求
     * @return 包含服务器证书的身份认证响应
     */
    private AuthenticationCertTransmit processAuthenticationRequest(AuthenticationCertTransmit request,AuthCache authCache) {
        logger.info("Processing AuthenticationCertTransmit request");

        try {
            // 验证请求是否包含证书
            if (request.getCert() == null) {
                logger.error("认证请求中的证书数据为空");
                throw new IOException("认证请求中的证书数据为空");
            }
            Certificate clientCert = request.getCert();
            logger.info("recv cert from client:{}",clientCert);
            SPIClient client = new SPIClient();

            // 1. 首先验证客户端证书
            CertVerifyResponse certVerifyResponse = client.verifyCert(
                    (byte) 2,
                    OssCoerAdapter.encode(clientCert)
            );

            // 检查验证结果
            if (!certVerifyResponse.isSuccess()) {
                logger.error("客户端证书验证失败: {}", certVerifyResponse.getStatusMessage());
                throw new IOException("客户端证书验证失败: " + certVerifyResponse.getStatusMessage());
            }
            authCache.setClientCert(request.getCert());
            logger.info("客户端证书验证成功，正在获取服务器证书...");

            // 2. 获取服务器自身的证书
            authCache.setCertType((byte) 2);
            CertQueryResponse certQueryResponse = client.queryCert((byte) 2, (byte) 1);


            // 3. 使用返回的证书数据创建AuthenticationCertTransmit响应
            AuthenticationCertTransmit response = new AuthenticationCertTransmit();

            // 解析并设置服务器证书
            try {
                // 使用OssCoerAdapter来解码证书数据
                Certificate serverCert = new Certificate();
                ByteArrayInputStream bais = new ByteArrayInputStream(certQueryResponse.getCertificate());
                // 正确使用OSS Nokalva的decode方法 - 传入实例而不是类
                Certificate decode = Certmanager.getCOERCoder().decode(bais, serverCert);
                logger.info("response cert to client:{}",decode);
                response.setCert(decode);
            } catch (Exception e) {
                logger.error("解析服务器证书数据失败", e);
                throw new IOException("解析服务器证书数据失败: " + e.getMessage());
            }

            return response;

        } catch (Exception e) {
            logger.error("Error during authentication", e);
            // 在出错情况下返回空的AuthenticationCertTransmit
            return new AuthenticationCertTransmit();
        }
    }
    public static byte[] buildPostRequest(byte[] postData) throws IOException {
        // 创建URL对象
//        URL url = new URL(urlString);
//        String host = url.getHost();
//        int port = url.getPort() == -1 ? 80 : url.getPort(); // 默认HTTP端口为80

        // 构建HTTP请求字符串
        String httpRequest = /*"POST " + url.getPath() + " HTTP/1.1\r\n" +
                "Host: " + host + "\r\n" +
                "Content-Type: application/x-www-form-urlencoded\r\n" +*/
                "Content-Length: " + postData.length + "\r\n" +
                "Connection: keep-alive\r\n" +
                "\r\n"; // 空行，表示头部结束

        // 将请求头转换为字节数组
        byte[] requestHeaderBytes = httpRequest.getBytes("UTF-8");

        // 创建字节数组输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        outputStream.write(requestHeaderBytes); // 写入请求头
        outputStream.write(postData); // 写入请求体

        return outputStream.toByteArray(); // 返回整个请求的字节数组
    }

    /**
     * 检查请求是否已经通过身份认证
     * 
     * @param request HTTP请求对象
     * @return true如果请求已经成功认证，false如果未认证
     */
    private boolean isAuthenticated(HttpServletRequest request) {
        // 检查会话是否存在
//        if (request.getSession(false) == null) {
//            logger.debug("会话不存在，未认证");
//            return false;
//        }
//
//        // 检查认证缓存是否存在
//        AuthCache authCache = (AuthCache) request.getSession().getAttribute("auth_cache");
//        if (authCache == null) {
//            logger.debug("认证缓存不存在，未认证");
//            return false;
//        }
//
//        // 检查认证状态是否已经成功完成
//        Boolean authSuccessful = (Boolean) request.getSession().getAttribute("auth_successful");
//        if (authSuccessful == null || !authSuccessful) {
//            logger.debug("认证未成功完成，未认证");
//            return false;
//        }
        
        logger.info("请求已认证");
        return true;
    }
}