package com.example.asn;

import com.example.asn.db.AlarmDAO;
import com.example.asn.db.DatabaseManager;
import com.example.asn.client.AlarmReportClient;
import com.example.asn.servicemanager.servicemanagementhttp.AlarmReportRequest;
import com.example.asn.util.AlarmTracker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Service that periodically checks for alarms in the database and sends reports
 */
public class AlarmMonitoringService {
    private static final Logger logger = LoggerFactory.getLogger(AlarmMonitoringService.class);
    private static final int DEFAULT_QUERY_INTERVAL_SECONDS = 10;
    
    private final AlarmDAO alarmDAO;
    private final AlarmReportClient reportClient;
    private final ScheduledExecutorService scheduler;
    private final int queryIntervalSeconds;
    
    /**
     * Creates a new AlarmMonitoringService with default interval of 10 seconds
     */
    public AlarmMonitoringService() {
        this(DEFAULT_QUERY_INTERVAL_SECONDS);
    }
    
    /**
     * Creates a new AlarmMonitoringService with specified interval
     * @param queryIntervalSeconds interval in seconds between database queries
     */
    public AlarmMonitoringService(int queryIntervalSeconds) {
        this.alarmDAO = new AlarmDAO();
        this.reportClient = new AlarmReportClient();
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.queryIntervalSeconds = queryIntervalSeconds;
    }
    
    /**
     * Starts the monitoring service
     */
    public void start() {
        logger.info("Starting alarm monitoring service with {} second interval", queryIntervalSeconds);
        
        scheduler.scheduleAtFixedRate(this::checkAndReportAlarms, 0, queryIntervalSeconds, TimeUnit.SECONDS);
    }
    
    /**
     * Stops the monitoring service
     */
    public void stop() {
        logger.info("Stopping alarm monitoring service");
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // Close database connections
        DatabaseManager.closeDataSource();
    }
    
    /**
     * Queries database for alarms and sends reports
     * This is the main method that runs on the scheduled thread
     */
    private void checkAndReportAlarms() {
        try {
            // Process alarms from udp_alarm table
            processUdpAlarms();
            
            // Process alarms from alarm_message table
            processAlarmMessages();
        } catch (Exception e) {
            logger.error("Error in alarm monitoring thread", e);
        }
    }
    
    /**
     * Processes alarms from the udp_alarm table
     */
    private void processUdpAlarms() {
        try {
            logger.debug("Querying udp_alarm table for alarms with syslog_status=0...");
            
            // Get all alarms from udp_alarm table with syslog_status=0
            List<AlarmReportRequest> alarms = alarmDAO.getAllAlarms();
            
            logger.info("Found {} alarms in udp_alarm table with syslog_status=0", alarms.size());
            
            if (alarms.isEmpty()) {
                return;
            }
            
            // List to keep track of successfully reported alarm IDs
            List<Long> reportedAlarmIds = new ArrayList<>();
            
            // Send each alarm report
            for (AlarmReportRequest alarm : alarms) {
                boolean sent = reportClient.sendAlarmReport(alarm);
                if (sent) {
                    logger.debug("Successfully sent alarm report for service: {}", alarm.getServiceId());
                    
                    // Get the alarm ID from alarmCode field (where we stored it during mapping)
                    try {
                        Long alarmId = alarm.getAlarmCode().longValue();
                        reportedAlarmIds.add(alarmId);
                    } catch (NumberFormatException e) {
                        logger.error("Invalid alarm ID format: {}", alarm.getAlarmCode(), e);
                    }
                } else {
                    logger.warn("Failed to send alarm report for service: {}", alarm.getServiceId());
                }
            }
            
            // Update syslog_status for successfully reported alarms
            if (!reportedAlarmIds.isEmpty()) {
                int updatedCount = alarmDAO.updateSyslogStatusBatch(reportedAlarmIds);
                logger.info("Updated syslog_status to 1 for {} out of {} reported alarms from udp_alarm", 
                        updatedCount, reportedAlarmIds.size());
            }
        } catch (Exception e) {
            logger.error("Error processing udp_alarms", e);
        }
    }
    
    /**
     * Processes alarms from the alarm_message table
     */
    private void processAlarmMessages() {
        try {
            logger.debug("Querying alarm_message table for alarms with syslog_status=0...");
            
            // Get all alarm messages with syslog_status=0
            List<AlarmReportRequest> alarms = alarmDAO.getAllAlarmMessages();
            
            logger.info("Found {} alarms in alarm_message table with syslog_status=0", alarms.size());
            
            if (alarms.isEmpty()) {
                return;
            }
            
            // List to keep track of successfully reported alarm message IDs
            List<Long> reportedAlarmIds = new ArrayList<>();
            
            // Send each alarm report
            for (AlarmReportRequest alarm : alarms) {
                boolean sent = reportClient.sendAlarmReport(alarm);
                if (sent) {
                    logger.debug("Successfully sent alarm message report for service: {}", alarm.getServiceId());
                    
                    // Get the ID from our tracker
                    Long alarmId = AlarmTracker.getAlarmMessageId(alarm);
                    if (alarmId != null) {
                        reportedAlarmIds.add(alarmId);
                    } else {
                        logger.warn("Could not find ID for reported alarm message");
                    }
                } else {
                    logger.warn("Failed to send alarm message report for service: {}", alarm.getServiceId());
                }
            }
            
            // Update syslog_status for successfully reported alarm messages
            if (!reportedAlarmIds.isEmpty()) {
                int updatedCount = alarmDAO.updateAlarmMessageSyslogStatusBatch(reportedAlarmIds);
                logger.info("Updated syslog_status to 1 for {} out of {} reported alarms from alarm_message", 
                        updatedCount, reportedAlarmIds.size());
            }
            // Clear the alarm tracker
            AlarmTracker.clearAlarms();
        } catch (Exception e) {
            logger.error("Error processing alarm_messages", e);
        }
    }

    /**
     * 从文件读取字节数组
     * @param filePath 文件路径
     * @return 字节数组，如果读取失败则返回null
     */
    public byte[] readFileToByteArray(String filePath) {
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            logger.error("File does not exist or is not a file: {}", filePath);
            return null;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            logger.info("Successfully read {} bytes from file: {}", data.length, filePath);
            return data;
        } catch (IOException e) {
            logger.error("Error reading file: {}", filePath, e);
            return null;
        }
    }
}
