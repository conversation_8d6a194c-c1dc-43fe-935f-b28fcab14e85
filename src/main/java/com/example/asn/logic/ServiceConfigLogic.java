package com.example.asn.logic;

import com.example.asn.bean.RequestException;
import com.example.asn.bean.UdpConfig;

import com.example.asn.servicemanager.servicemanagementhttp.*;
import com.example.asn.util.ConfigXmlOperator;
import com.example.asn.util.Constant;
import com.example.asn.util.IPUtil;
import com.example.asn.util.ServiceXmlOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.File;

import java.util.HashMap;

import java.util.Map;

public class ServiceConfigLogic {

//    private static final String SERVICE_PRE_PATH = "";
//    private static final String SERVICE_FILE_EXT = ".xml";
    private static final Logger log = LoggerFactory.getLogger(ServiceConfigLogic.class);

    public ServiceConfigResponse serviceConfigRequest(ServiceConfigRequest request) throws RequestException {
        ServiceConfigResponse ret = null;
        if(request.getAddServiceRequest() != null) {
            ret = addService(request.getAddServiceRequest());
        }else if(request.getUpdateServiceRequest() != null) {
            ret = updateService(request.getUpdateServiceRequest());
        }else if(request.getDeleteServiceRequest() != null) {
            ret = deleteService(request.getDeleteServiceRequest());
        }

        return ret;
    }
    private ServiceConfigResponse addService(AddServiceRequest request) throws RequestException {
        ServiceConfigResponse ret = null;
        if(request != null){
            String serviceName = new String(request.getDisplayname().byteArrayValue());
            if(serviceName!=null && !serviceName.isEmpty()){
                ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
                if(!configXmlOperator.isDisplayNameExist(serviceName)) {
                    // 创建UdpConfig对象
                    UdpConfig udpConfig = new UdpConfig();
                    udpConfig.setDisplayname(serviceName);
                    
                    // 根据网络类型设置不同参数
                    Network network = request.getNetwork();
                    if (network == Network.sender) {
                        // 发送端特有字段
                        String proxyIp = IPUtil.ipAddressToString(request.getProxyIp());
                        Integer proxyPort = request.getProxyPort().intValue();
                        udpConfig.setIpproxy(proxyIp);
                        udpConfig.setPortclient(proxyPort != null ? proxyPort.toString() : null);
                        
                        // 处理ContentKeyCheck位图
                        if (request.getContentKeyCheck() != null) {
                            byte[] contentKeyCheck = request.getContentKeyCheck().byteArrayValue();
                            if (contentKeyCheck.length > 0) {
                                // 检查第0位 - 机动车牌校验
                                udpConfig.setCarcheck(((contentKeyCheck[0] & 0x01) != 0) ? "1" : "0");
                                // 检查第1位 - 身份证校验
                                udpConfig.setIdcheck(((contentKeyCheck[0] & 0x02) != 0) ? "1" : "0");
                            }
                        } else {
                            // 默认值
                            udpConfig.setCarcheck("0");
                            udpConfig.setIdcheck("0");
                        }
                        
                        // 处理ProtocolFilter位图
                        if (request.getProtocolFilter() != null) {
                            byte[] protocolFilter = request.getProtocolFilter().byteArrayValue();
                            if (protocolFilter.length > 0) {
                                // 检查第0位 - CRC16格式过滤
                                udpConfig.setCrcfilter(((protocolFilter[0] & 0x01) != 0) ? "1" : "0");
                                // 检查第1位 - ASN格式过滤
                                udpConfig.setAsnfilter(((protocolFilter[0] & 0x02) != 0) ? "1" : "0");
                            }
                        } else {
                            // 默认值
                            udpConfig.setCrcfilter("0");
                            udpConfig.setAsnfilter("0");
                        }
                        
                        // 处理UnpassDeal枚举
                        if (request.getUnpassDeal() != null) {
                            if(request.getUnpassDeal() == PermissionState.allow){
                                udpConfig.setUnpassdeal("0"); // 仅报警数据正常传输
                            }else if(request.getUnpassDeal() == PermissionState.forbidden){
                                udpConfig.setUnpassdeal("1"); // 报警+数据丢弃不转发
                            }else{
                                udpConfig.setUnpassdeal("0");
                            }
                        } else {
                            udpConfig.setUnpassdeal("0");
                        }
                        
                    } else if (network == Network.receiver) {
                        // 接收端特有字段
                        String serverIp = IPUtil.ipAddressToString(request.getServerIp());
                        Integer serverPort = request.getServerPort().intValue();
                        udpConfig.setHostip(serverIp);
                        udpConfig.setHostport(serverPort != null ? serverPort.toString() : null);
                    }
                    
                    // 设置其他默认值
//                    udpConfig.setDestResLabel("proxyclient_2");
//                    udpConfig.setSendaddrmap("");
                    
                    //保存config.xml
                    int serviceId = configXmlOperator.add(builderConfigNode(udpConfig));
                    udpConfig.setSid(serviceId + "");
                    //保存service.xml
                    ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId+"");
                    serviceXmlOperator.saveUdpConfig(udpConfig);
//                    serviceXmlOperator.setAttributes(builderServiceConfigNode(udpConfig));
                    //保存properties
                    serviceXmlOperator.generateProperties(Constant.SERVICE_PRE_PATH+serviceId+".properties",udpConfig);
                    ServiceConfigResponse response = new ServiceConfigResponse();
                    response.setMessageType(ContentMessageType.addService);
                    response.setServiceId(new ServiceId(serviceId));
                    ret = response;
                }else{ //服务名重复
                    log.error("service name is conflict ... ");
//                    throw new Exception("service name is conflict ... ");
                      throw new RequestException(new ErrorResponse(ContentMessageType.addService, ProcessErrorState.displayNameConflictError));
//                    ret = new ErrorResponse(ContentMessageType.addService, ProcessErrorState.displayNameConflictError);
                }
            }
        }else{

        }
        return ret;
    }
    private Map<String,String> builderConfigNode(UdpConfig udpConfig){
        Map<String,String> map = new HashMap<>();
        map.put("creator","unimas");
        map.put("isAudit","true");
        map.put("templateid","");
        map.put("type","11");
        map.put("importServiceId","");
        map.put("servicetype","udp");
        map.put("configedtime","1");
        map.put("displayname",udpConfig.getDisplayname());
        map.put("secstate","1");
        map.put("isRun","false");
        map.put("istemplate","false");
        map.put("seclevel","4");
        map.put("flowlevel","10");
        return map;
    }
    private Map<String,String>builderServiceConfigNode(UdpConfig udpConfig){
        Map<String,String> map = new HashMap<>();
        map.put("level","");
        map.put("eqs","");
        map.put("weekday","1;2;3;4;5;6;7");
        map.put("special_value","");
        map.put("runtime","#*#");
        map.put("rules","");
        map.put("ipproxy",udpConfig.getIpproxy());
        map.put("multicast","false");
        map.put("srcResLabel","proxyclient_1");
        map.put("configedtime","1");
        map.put("portclient",udpConfig.getPortclient());
        map.put("audit","1");
        map.put("displayname",udpConfig.getDisplayname());
        map.put("iprange","#*#");
        map.put("udpFlood","0");
        map.put("istemplate","false");
        map.put("proxymode","udp");
        map.put("multicastip","");
        map.put("flowlevel","10");
        
        // 添加5个新属性
        if (udpConfig.getHostip() != null) {
            map.put("hostip", udpConfig.getHostip());
        }
        if (udpConfig.getDestResLabel() != null) {
            map.put("destResLabel", udpConfig.getDestResLabel());
        } else {
            map.put("destResLabel", "proxyclient_2"); // 默认值
        }
        if (udpConfig.getSendaddrmap() != null) {
            map.put("sendaddrmap", udpConfig.getSendaddrmap());
        } else {
            map.put("sendaddrmap", "");
        }
        if (udpConfig.getHostport() != null) {
            map.put("hostport", udpConfig.getHostport());
        }
        if (udpConfig.getSrcPort() != null) {
            map.put("srcPort", udpConfig.getSrcPort());
        }
        
        // 检测和过滤相关属性
        if (udpConfig.getCarcheck() != null) {
            map.put("carcheck", udpConfig.getCarcheck());
        } else {
            map.put("carcheck", "0"); // 默认关闭
        }
        if (udpConfig.getIdcheck() != null) {
            map.put("idcheck", udpConfig.getIdcheck());
        } else {
            map.put("idcheck", "0"); // 默认关闭
        }
        if (udpConfig.getCrcfilter() != null) {
            map.put("crcfilter", udpConfig.getCrcfilter());
        } else {
            map.put("crcfilter", "0"); // 默认关闭
        }
        if (udpConfig.getAsnfilter() != null) {
            map.put("asnfilter", udpConfig.getAsnfilter());
        } else {
            map.put("asnfilter", "0"); // 默认关闭
        }
        if (udpConfig.getUnpassdeal() != null) {
            map.put("unpassdeal", udpConfig.getUnpassdeal());
        } else {
            map.put("unpassdeal", "0"); // 默认仅报警数据正常传输
        }
        
        return map;
    }
    private ServiceConfigResponse deleteService(DeleteServiceRequest request) {
        ServiceConfigResponse ret = null;
        if(request != null){
            int serviceId = request.getServiceId().intValue();
            //TODO 停止服务
            ServiceStatusLogic serviceStatusLogic = new ServiceStatusLogic();
            boolean b = serviceStatusLogic.stopService(serviceId + "");
            log.info("stop service " + serviceId + " " + b);
            //删除properties文件
            File propertiesFile = new File(Constant.SERVICE_PRE_PATH+serviceId+".properties");
            boolean delRet = propertiesFile.delete();
            log.info("properties file deleted : "+delRet);
            //删除service.xml
            File serciceConfigFile = new File(Constant.SERVICE_PRE_PATH+serviceId+Constant.SERVICE_FILE_EXT);
            delRet = serciceConfigFile.delete();
            log.info("serciceConfig file deleted : "+delRet);
            //删除config.xml节点
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
            configXmlOperator.deleteNode(serviceId+"");


            ret = new ServiceConfigResponse();
            ret.setServiceId(new ServiceId(serviceId));
            ret.setMessageType(ContentMessageType.deleteService);
        }
        return ret;
    }
    private ServiceConfigResponse updateService(UpdateServiceRequest request) throws RequestException {
        ServiceConfigResponse ret = null;
        if(request != null) {
            int serviceId = request.getServiceId().intValue();
            Network network = request.getNetwork();

            try {
                //判断启停，停止才能修改
                ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
                if(configXmlOperator.getIsRun(serviceId + "")){
                    log.info("service " + serviceId + " is running");
                    throw  new Exception(ProcessErrorState.illegalOperationError.toString());
//                    return new ErrorResponse(request.getMessageType(),ProcessErrorState.IllegalOperationError);
                }
                // 读取现有配置
                ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId+"");
                UdpConfig udpConfig = serviceXmlOperator.loadUdpConfig();
                
                // 更新udpConfig对象的属性
                if (network == Network.sender) {
                    // 发送端特有字段
                    if (request.getProxyIp() != null) {
                        udpConfig.setIpproxy(IPUtil.ipAddressToString(request.getProxyIp()));
                    }
                    if (request.getProxyPort() != null) {
                        udpConfig.setPortclient(request.getProxyPort().intValue()+"");
                    }
                    
                    // 更新检测和过滤相关属性
                    if (request.getContentKeyCheck() != null) {
                        // 从ContentKeyCheck位图中提取值
                        byte[] contentKeyCheck = request.getContentKeyCheck().byteArrayValue();
                        if (contentKeyCheck.length > 0) {
                            // 检查第0位 - 机动车牌校验
                            udpConfig.setCarcheck(((contentKeyCheck[0] & 0x01) != 0) ? "1" : "0");
                            // 检查第1位 - 身份证校验
                            udpConfig.setIdcheck(((contentKeyCheck[0] & 0x02) != 0) ? "1" : "0");
                        }
                    }
                    
                    if (request.getProtocolFilter() != null) {
                        // 从ProtocolFilter位图中提取值
                        byte[] protocolFilter = request.getProtocolFilter().byteArrayValue();
                        if (protocolFilter.length > 0) {
                            // 检查第0位 - CRC16格式过滤
                            udpConfig.setCrcfilter(((protocolFilter[0] & 0x01) != 0) ? "1" : "0");
                            // 检查第1位 - ASN格式过滤
                            udpConfig.setAsnfilter(((protocolFilter[0] & 0x02) != 0) ? "1" : "0");
                        }
                    }

                    if (request.getUnpassDeal() != null) {
                        if(request.getUnpassDeal() == PermissionState.allow){
                            udpConfig.setUnpassdeal("0"); // 仅报警数据正常传输
                        }else if(request.getUnpassDeal() == PermissionState.forbidden){
                            udpConfig.setUnpassdeal("1"); // 报警+数据丢弃不转发
                        }else{
                            udpConfig.setUnpassdeal("0");
                        }
                    } else {
                        udpConfig.setUnpassdeal("0");
                    }
                    
                } else if (network == Network.receiver) {
                    // 接收端特有字段
                    if (request.getServerIp() != null) {
                        udpConfig.setHostip(IPUtil.ipAddressToString(request.getServerIp()));
                    }
                    if (request.getServerPort() != null) {
                        udpConfig.setHostport(request.getServerPort().intValue()+"");
                    }
                }
                
                // 保存服务配置
                Map<String, String> configAttributes = builderServiceConfigNode(udpConfig);
                serviceXmlOperator.setAttributes(configAttributes);
                
                // 更新properties文件
                serviceXmlOperator.generateProperties(Constant.SERVICE_PRE_PATH+serviceId+".properties", udpConfig);

                // 创建响应
                ret = new ServiceConfigResponse();
                ret.setServiceId(new ServiceId(serviceId));
                ret.setMessageType(ContentMessageType.updateService);
                
            } catch (Exception e) {
                log.error("更新服务配置失败", e);
//                return new ErrorResponse(CommonTypes.ContentMessageType.UPDATE_SERVICE, ProcessErrorState.IllegalArgumentError);
//                throw new Exception(ProcessErrorState.illegalArgumentError.toString());
                throw new RequestException(new ErrorResponse(ContentMessageType.updateService, ProcessErrorState.illegalArgumentError));
            }
        } else {
            log.error("更新服务请求为空");
            throw new RequestException(new ErrorResponse(ContentMessageType.updateService, ProcessErrorState.messageStructureError));
        }
        
        return ret;
    }
}
