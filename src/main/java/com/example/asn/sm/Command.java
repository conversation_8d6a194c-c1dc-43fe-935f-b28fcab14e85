package com.example.asn.sm;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

public interface Command {
    Logger logger = LoggerFactory.getLogger(Command.class);
    public static final Parameter APPNAME = new Parameter("appName", "业务名");
    /*********************************** udp命令 *******************************/
    /** 启动服务 **/
    public static final CommandData UDP_START_APP = new CommandData(new byte[] { 0x00, 0x00, 0x00, 0x0a }, APPNAME);
    /** 停止服务 **/
    public static final CommandData UDP_STOP_APP = new CommandData(new byte[] { 0x00, 0x00, 0x00, 0x0b }, APPNAME);
    /**
     * 处理命令数据并执行命令
     * 
     * @param cd 命令数据
     * @param data 参数数据
     * @return 命令执行结果
     * @throws Exception 执行过程中的异常
     */
    Object process(CommandData cd, Map<String, String> data) throws Exception;
}