package com.example.asn.sm;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.Map;

public class SMProcess implements Command{
    @Override
    public Object process(CommandData cd, Map<String, String> data) throws Exception {
        String logs = hashCode() + "数据库指令头信息:";
        for (byte b : cd.getDataTypeByte()) {
            logs += b;
        }
        logs += "\t参数数为[";
        String[] values = new String[3]; // 只有1个业务号输入，不可能超多个
        for (int i = 0; i < cd.getParams().length; i++) {
            values[i] = data.get(cd.getParams()[i].getField());
            if (values[i] == null) {
                throw new RuntimeException(cd.getParams()[i].getMean());
            }
            logs += values[i] + ",";
        }
        DataInputStream in = null;
        DataOutputStream out = null;
        Socket socket = null;

        try {
            InetSocketAddress address = new InetSocketAddress("127.0.0.1", 9999);
            // Socket 连接
            socket = new Socket();
            socket.connect(address, 60000); // 设置60秒超时
            in = new DataInputStream(socket.getInputStream());
            out = new DataOutputStream(socket.getOutputStream());

            byte[] changeByte = changeByte(cd.getDataTypeByte(), values);
            out.write(changeByte);
            boolean changeResult = changeResult(in);
            logger.info(hashCode() + "返回的结果:" + changeResult);
            return changeResult;
        } catch (ConnectException e) {
            e.printStackTrace();
            throw new Exception("与引擎无法建立连接，请检查sm是否正常运行中！", e);
        } catch (SocketTimeoutException e) {
            e.printStackTrace();
            throw new Exception("与引擎连接超时，请检查sm是否正常运行中！", e);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("udp执行命令失败！", e);
        } finally {
            if (in != null) {
                in.close();
            }
            if (out != null) {
                out.close();
            }
            if (socket != null) {
                socket.close();
            }
        }
    }
    private byte[] changeByte(byte[] command, String[] values) throws UnsupportedEncodingException {
        byte[] sendData = new byte[18]; // 固定长度 4+8+2+4
        System.arraycopy(command,0, sendData, 0,  4); // 命令号
        System.arraycopy(new byte[] { (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff }, 0, sendData, 4, 8); // 为版本2.3,无用字节
        System.arraycopy(getRequest(values[0], 2),  0, sendData, 12, 2); // 业务号
        System.arraycopy(getRequest( "0", 4),  0, sendData, 14, 4); // 命令长度
        return sendData;
    }

    private byte[] getRequest(String param, int length) {
        if (param == null || param.equals("")) {
            param = "0";
        }
        byte[] request = new byte[length];
        long id = Long.parseLong(param);
        if (id < 0)
            id = -id;
        for (int i = 0; i < length - 1; i++) {
            int j = length - 1 - i;
            long b = (id >>> j * 8) % 256;
            request[i] = new Long(b).byteValue();
        }
        long b7 = id % 256;
        request[length - 1] = new Long(b7).byteValue();
        return request;
    }
    private boolean changeResult(DataInputStream in) throws IOException {
        in.readLong();
        int id = in.readInt();
        return id != -1;
    }
}
