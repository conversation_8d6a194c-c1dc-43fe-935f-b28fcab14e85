package com.example.asn.util;

import com.example.asn.servicemanager.servicemanagementhttp.AlarmReportRequest;

import java.util.HashMap;
import java.util.Map;
public class AlarmTracker{
    /**
     * Helper class to track alarm IDs for multiple alarm sources
     */
    private static final Map<AlarmReportRequest, Long> alarmMessageIds = new HashMap<>();
    private static final Map<AlarmReportRequest, String> alarmMessages = new HashMap<>();

    /**
     * Register an alarm message with its database ID
     */
    public static void registerAlarmMessage(AlarmReportRequest alarm, long id) {
        alarmMessageIds.put(alarm, id);
    }

    /**
     * Store alarm message content
     */
    public static void storeAlarmMessage(AlarmReportRequest alarm, String message) {
        alarmMessages.put(alarm, message);
    }

    /**
     * Get the database ID for an alarm message
     * @param alarm The alarm report request
     * @return The database ID if registered, or null if not found
     */
    public static Long getAlarmMessageId(AlarmReportRequest alarm) {
        return alarmMessageIds.get(alarm);
    }

    /**
     * Get the message content for an alarm
     * @param alarm The alarm report request
     * @return The message if stored, or null if not found
     */
    public static String getAlarmMessage(AlarmReportRequest alarm) {
        return alarmMessages.get(alarm);
    }

    /**
     * Clear all tracked alarms
     */
    public static void clearAlarms() {
        alarmMessageIds.clear();
        alarmMessages.clear();
    }

}

