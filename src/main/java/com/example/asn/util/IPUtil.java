package com.example.asn.util;

import com.example.asn.servicemanager.servicemanagementhttp.IPAddress;
import com.example.asn.servicemanager.servicemanagementhttp.IPv4Address;
import com.example.asn.servicemanager.servicemanagementhttp.IPv6Address;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class IPUtil {
    /**
     * 根据IP字符串自动检测版本并创建IPAddress对象
     */
    public static IPAddress createIPAddress(String ipStr) throws Exception {
        if (ipStr == null || ipStr.trim().isEmpty()) {
            return null; // 对于可选字段，返回null
        }

        // 判断是IPv4还是IPv6
        if (ipStr.contains(".") && !ipStr.contains(":")) {
            return createIPv4Address(ipStr);
        } else if (ipStr.contains(":")) {
            return createIPv6Address(ipStr);
        } else {
            throw new IllegalArgumentException("无法识别的IP地址格式: " + ipStr);
        }
    }
    /**
     * 将IPv4字符串转换为ASN.1 IPAddress对象
     */
    public static IPAddress createIPv4Address(String ipStr) throws Exception {
        // 1. 解析IP字符串，转换为字节数组
        String[] parts = ipStr.split("\\.");
        if (parts.length != 4) {
            throw new IllegalArgumentException("无效的IPv4地址格式: " + ipStr);
        }

        // 2. 创建大小为4的字节数组
        byte[] ipv4Bytes = new byte[4];
        for (int i = 0; i < 4; i++) {
            int value = Integer.parseInt(parts[i]);
            if (value < 0 || value > 255) {
                throw new IllegalArgumentException("IP地址段无效: " + parts[i]);
            }
            ipv4Bytes[i] = (byte) value;
        }

        // 3. 创建IPv4Address并设置字节数组
        IPv4Address ipv4Address = new IPv4Address(ipv4Bytes);

        // 4. 创建IPAddress并设置IPv4地址
        return IPAddress.createIPAddressWithIpV4(ipv4Address);
    }
    /**
     * 将IPv6字符串转换为ASN.1 IPAddress对象
     */
    public static IPAddress createIPv6Address(String ipStr) throws Exception {
        // 1. 使用Java内置的InetAddress解析IPv6地址
        InetAddress inetAddress = InetAddress.getByName(ipStr);
        if (!(inetAddress instanceof Inet6Address)) {
            throw new IllegalArgumentException("不是有效的IPv6地址: " + ipStr);
        }

        // 2. 获取字节表示
        byte[] ipv6Bytes = inetAddress.getAddress();
        if (ipv6Bytes.length != 16) {
            throw new IllegalArgumentException("IPv6地址字节长度应为16，实际为: " + ipv6Bytes.length);
        }

        // 3. 创建IPv6Address并设置字节数组
        IPv6Address ipv6Address = new IPv6Address(ipv6Bytes);

        // 4. 创建IPAddress并设置IPv6地址
        return IPAddress.createIPAddressWithIpV6(ipv6Address);
    }
    public static String ipAddressToString(IPAddress ipAddress) {
        if (ipAddress == null) {
            return null;
        }

        // 判断是IPv4还是IPv6
        if (ipAddress.hasIpV4()) {
            return ipv4AddressToString(ipAddress.getIpV4());
        } else if (ipAddress.hasIpV6()) {
            return ipv6AddressToString(ipAddress.getIpV6());
        } else {
            return "未设置IP地址";
        }
    }

    /**
     * 将IPv4Address转换为字符串
     */
    private static String ipv4AddressToString(IPv4Address ipv4Address) {
        if (ipv4Address == null) {
            return null;
        }

        byte[] bytes = ipv4Address.byteArrayValue();
        if (bytes == null || bytes.length != 4) {
            throw new IllegalArgumentException("无效的IPv4地址字节数组");
        }

        // 将每个字节转换为无符号整数(0-255)，然后用点连接
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            if (i > 0) {
                sb.append('.');
            }
            // 将有符号字节转换为无符号整数(0-255)
            sb.append(bytes[i] & 0xFF);
        }

        return sb.toString();
    }

    /**
     * 将IPv6Address转换为字符串
     */
    private static String ipv6AddressToString(IPv6Address ipv6Address) {
        if (ipv6Address == null) {
            return null;
        }

        byte[] bytes = ipv6Address.byteArrayValue();
        if (bytes == null || bytes.length != 16) {
            throw new IllegalArgumentException("无效的IPv6地址字节数组");
        }

        // 使用Java内置的InetAddress格式化IPv6地址
        try {
            InetAddress inetAddress = InetAddress.getByAddress(bytes);
            // 获取标准IPv6表示形式，去掉可能的主机名
            String hostAddress = inetAddress.getHostAddress();
            if (hostAddress.contains("%")) {
                // 去掉范围标识符（如fe80::1%eth0中的%eth0）
                hostAddress = hostAddress.substring(0, hostAddress.indexOf('%'));
            }
            return hostAddress;
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("无法格式化IPv6地址: " + e.getMessage(), e);
        }
    }
}
