/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.example.asn.servicemanager.Servicemanager */
/* Created: Tu<PERSON> Mar 25 17:12:18 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc C:/Users/<USER>/Desktop/service.asn
 */


package com.example.asn.servicemanager;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
  A class for the Servicemanager ASN.1/Java  project.
*/
public class Servicemanager extends ASN1Project {

    /**
     * Initialize the pdu decoder.
     */
    private static final ProjectInfo c_projectInfo = new ProjectInfo (
	null,
	new byte[] {
	    (byte)0x0b, (byte)0xfd, (byte)0x9d, (byte)0xdf, (byte)0x7a,
	    (byte)0x2e, (byte)0xd2, (byte)0x94, (byte)0xac, (byte)0x2e,
	    (byte)0x17, (byte)0x9a, (byte)0xc4, (byte)0xa2, (byte)0x39,
	    (byte)0x9a, (byte)0xdb, (byte)0x30, (byte)0x02, (byte)0x6d,
	    (byte)0x97, (byte)0x33, (byte)0x75, (byte)0xf6, (byte)0xa1,
	    (byte)0x3b, (byte)0x30, (byte)0xe9, (byte)0x70, (byte)0xfe,
	    (byte)0xd5, (byte)0xf6, (byte)0x88, (byte)0x8c, (byte)0x9e,
	    (byte)0x44, (byte)0x37, (byte)0x19, (byte)0xab, (byte)0xc6,
	    (byte)0x84, (byte)0x1d, (byte)0x77, (byte)0x17, (byte)0x8a,
	    (byte)0x0c, (byte)0xcc, (byte)0x83, (byte)0x0f, (byte)0x0a,
	    (byte)0xb3, (byte)0xa7, (byte)0x73, (byte)0x90, (byte)0x0f,
	    (byte)0xa0, (byte)0xea, (byte)0xd0, (byte)0x23, (byte)0x70,
	    (byte)0x2c, (byte)0x3e, (byte)0x53, (byte)0xa1, (byte)0xf6,
	    (byte)0x75, (byte)0x33, (byte)0x9f, (byte)0xbe, (byte)0xa7,
	    (byte)0x7b, (byte)0x7e, (byte)0xd5, (byte)0x9c, (byte)0x74,
	    (byte)0x17, (byte)0x7a, (byte)0x86, (byte)0xcd, (byte)0x7f,
	    (byte)0xdb, (byte)0xc6, (byte)0x4b
	},
	"2025/04/27"
    );
    
    /**
     * Get the project descriptor of 'this' object.
     */
    public ProjectInfo getProjectInfo()
    {
	return c_projectInfo;
    }
    
    private static final ASN1Project c_project = new Servicemanager();

    /**
     * Methods for accessing Coders.
     */
    public static Coder getDefaultCoder()
    {
	return createCOERCoder(c_project);
    }
    
    public static OERCoder getOERCoder()
    {
	return createOERCoder(c_project);
    }
    
    public static COERCoder getCOERCoder()
    {
	return createCOERCoder(c_project);
    }
    
}
