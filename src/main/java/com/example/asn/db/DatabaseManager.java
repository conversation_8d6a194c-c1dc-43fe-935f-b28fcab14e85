package com.example.asn.db;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

/**
 * Database connection manager using HikariCP connection pool
 */
public class DatabaseManager {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseManager.class);
    private static HikariDataSource dataSource;

    static {
        try {
            initDataSource();
//            initDatabase();
        } catch (IOException e) {
            logger.error("Failed to initialize database", e);
        }
    }

    private static void initDataSource() throws IOException {
        Properties properties = new Properties();
        try (InputStream inputStream = DatabaseManager.class.getClassLoader().getResourceAsStream("application.properties")) {
            if (inputStream == null) {
                throw new IOException("Could not find application.properties");
            }
            properties.load(inputStream);
        }

        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(properties.getProperty("db.url"));
        config.setUsername(properties.getProperty("db.username"));
        config.setPassword(properties.getProperty("db.password"));
        config.setMaximumPoolSize(Integer.parseInt(properties.getProperty("db.poolSize", "10")));
        config.setAutoCommit(true);
        
        // Additional configurations
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

        dataSource = new HikariDataSource(config);
        logger.info("Database connection pool initialized successfully");
    }
    
    /**
     * Initialize database tables if they don't exist
     */
    private static void initDatabase() throws SQLException {
        String createUdpAlarmTable = 
                "CREATE TABLE IF NOT EXISTS `udp_alarm` (" +
                "`id` bigint(20) NOT NULL AUTO_INCREMENT," +
                "`appname` varchar(20) DEFAULT NULL," +
                "`time` datetime DEFAULT NULL," +
                "`alarmtype` int(11) DEFAULT '0'," +
                "`size` int(11) DEFAULT '0'," +
                "`port` int(11) DEFAULT '0'," +
                "`ip` varchar(50) DEFAULT NULL," +
                "`message` varchar(2000) DEFAULT NULL," +
                "`status` int(11) NOT NULL DEFAULT '0'," +
                "`syslog_status` int(11) DEFAULT '0'," +
                "PRIMARY KEY (`id`)," +
                "KEY `udp_alarm_index` (`time`,`ip`,`port`,`id`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=gbk";
        
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute(createUdpAlarmTable);
            logger.info("Database tables initialized successfully");
        }
    }

    /**
     * Get a database connection from the pool
     * @return Connection object
     * @throws SQLException if connection cannot be obtained
     */
    public static Connection getConnection() throws SQLException {
        if (dataSource == null) {
            try {
                initDataSource();
            } catch (IOException e) {
                throw new SQLException("Failed to initialize data source", e);
            }
        }
        return dataSource.getConnection();
    }

    /**
     * Close the connection pool
     */
    public static void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("Database connection pool closed");
        }
    }
}
