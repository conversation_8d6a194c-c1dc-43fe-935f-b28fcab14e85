package com.example.asn.client;


import com.example.asn.codec.OssCoerAdapter;
import com.example.asn.servicemanager.servicemanagementhttp.AlarmReportRequest;
import com.example.asn.servicemanager.servicemanagementhttp.MessageRequestFrame;
import com.example.asn.servicemanager.servicemanagementhttp.Uint8;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;
import java.io.InputStream;

/**
 * Client for sending alarm reports to a specified IP and port
 */
public class AlarmReportClient {
    private static final Logger logger = LoggerFactory.getLogger(AlarmReportClient.class);
//    private static final ObjectMapper objectMapper = new ObjectMapper();
    HttpClient client = new HttpClient("http://localhost:8080");
    private String destinationIp;
    private int destinationPort;
    
    public AlarmReportClient() {
        loadConfiguration();
    }
    
    /**
     * Loads network configuration from application.properties
     */
    private void loadConfiguration() {
        Properties properties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("application.properties")) {
            if (inputStream == null) {
                throw new IOException("Could not find application.properties");
            }
            properties.load(inputStream);
            
            destinationIp = properties.getProperty("alarm.report.destination.ip", "127.0.0.1");
            destinationPort = Integer.parseInt(properties.getProperty("alarm.report.destination.port", "8080"));
            
            logger.info("Alarm report client configured to send to {}:{}", destinationIp, destinationPort);
            // 创建HTTP客户端
            client = new HttpClient("http://"+destinationIp+":"+destinationPort);

        } catch (IOException e) {
            logger.error("Failed to load network configuration", e);
            // Set default values
            destinationIp = "127.0.0.1";
            destinationPort = 8080;
        }
    }
    
    /**
     * Sends an AlarmReportRequest to the configured destination
     * @param request AlarmReportRequest to send
     * @return true if send was successful, false otherwise
     */
    public boolean sendAlarmReport(AlarmReportRequest request) {


        try {
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setAlarmReportRequest(request);
            // 编码消息
            byte[] encodedData = null;
            encodedData = OssCoerAdapter.encode(frame);

            System.out.println("Sending request...");
            // 发送请求
            client.post("/sg", encodedData);
            return true;
        } catch (IOException e) {
            logger.error("IO error sending alarm report", e);
        }
        return false;
    }
}
