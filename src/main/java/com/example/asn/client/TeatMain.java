package com.example.asn.client;

import com.example.asn.spi.SPIClientDemo;

import java.util.InputMismatchException;
import java.util.Scanner;

public class TeatMain {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            // 显示主菜单
            System.out.println("\n=== 服务管理系统 ===");
            System.out.println("1. 服务配置管理");
            System.out.println("2. 证书管理");
            System.out.println("3. SPI测试");
            System.out.println("0. 退出程序");
            System.out.print("请选择功能 (0-2): ");
            
            try {
                int choice = scanner.nextInt();
                
                switch (choice) {
                    case 1:
                        MessageServletClient.main(args);
                        break;
                    case 2:
                        CertManagerServletClient.main(args);
                        break;
                    case 3:
                        SPIClientDemo.main(args);
                        break;
                    case 0:
                        System.out.println("程序已退出");
                        scanner.close();
                        return;
                    default:
                        System.out.println("无效的选择，请重新输入");
                }
            } catch (InputMismatchException e) {
                System.out.println("输入无效，请输入数字");
                scanner.nextLine(); // 清除输入缓冲
            }
        }
    }
}
