# æå¡å¨ç«¯å£
server.port=8081
server.servlet.context-path=/
server.compression.enabled=true
server.compression.min-response-size=2048
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain,text/css,application/javascript

# åºç¨ä¿¡æ¯
app.version=1.0.0
app.name=å®å¨éç¦»ååè¾åºæ¨¡å

# æ°æ®æä»¶å­å¨è·¯å¾
config.dir=config

# éæèµæºä¼å
spring.resources.chain.compressed=true
spring.resources.chain.cache=true
spring.resources.cache.period=86400

# æ¥å¿éç½®
logging.level.root=INFO
logging.level.com.udpproxy=DEBUG
logging.file.name=/etc/unimas/tomcat/udpproxy/logs/udpproxy.log
logging.file.max-size=10MB
logging.file.max-history=10

# Spring Actuatoréç½®
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when_authorized

# Thymeleaféç½®
spring.thymeleaf.cache=true
spring.thymeleaf.check-template-location=true
spring.thymeleaf.encoding=UTF-8

# Alarm Reportéç½®
alarm.report.destination.ip=************
alarm.report.destination.port=8080