<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<preferences EXTERNAL_XML_VERSION="1.0">
  <root type="system">
    <map/>
    <node name="apps">
      <map>
        <entry key="servicenum" value="80"/>
        <entry key="servicecount" value="0"/>
        <entry key="isSrc" value="true"/>
        <entry key="appcount" value="BTM0_app1"/>
        <entry key="channelcount" value="0"/>
      </map>
      <node name="BTM0_app1">
        <map>
          <entry key="departmentName" value="unimas"/>
          <entry key="competentPhone" value="123456"/>
          <entry key="isImport" value="false"/>
          <entry key="competentName" value="unimas"/>
          <entry key="displayname" value="unimas"/>
        </map>
        <node name="11">
          <map>
            <entry key="scheduleid" value="11"/>
            <entry key="displayname" value="unimastest"/>
            <entry key="status" value="configured"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_CONFIG"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
            <entry key="network" value="1"/>
          </map>
        </node>
        <node name="12">
          <map>
            <entry key="scheduleid" value="12"/>
            <entry key="displayname" value="unimastest2"/>
            <entry key="status" value="configured"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_CONFIG"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
            <entry key="network" value="1"/>
          </map>
        </node>
        <node name="13">
          <map>
            <entry key="scheduleid" value="13"/>
            <entry key="displayname" value="udp333"/>
            <entry key="status" value="configured"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_CONFIG"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
            <entry key="network" value="1"/>
          </map>
        </node>
        <node name="15">
          <map>
            <entry key="scheduleid" value="15"/>
            <entry key="displayname" value="udp15"/>
            <entry key="status" value="configured"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
            <entry key="network" value="1"/>
          </map>
        </node>
      </node>
    </node>
  </root>
</preferences>
