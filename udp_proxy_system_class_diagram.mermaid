classDiagram
    class UDPResource {
        -Long id
        -String resourceName
        -boolean isFullNetwork
        -List~IPRange~ ipRanges
        -LocalDateTime createdAt
        -LocalDateTime updatedAt
        +validateIpRanges()
    }

    class IPRange {
        -Long id
        -String startIp
        -String endIp
        -UDPResource resource
        +boolean isValid()
        +boolean overlaps(IPRange other)
    }

    class ProxyService {
        -Long id
        -String serviceName
        -UDPResource resource
        -String transmissionIp
        -int transmissionPort
        -boolean antiFloodEnabled
        -long trafficLimit
        -ServiceStatus status
        -ConfigurationStatus configStatus
        -LocalDateTime createdAt
        -LocalDateTime updatedAt
        +deploy()
        +start()
        +stop()
        +updateStatus()
    }

    class UDPResourceController {
        -UDPResourceService resourceService
        +List~ResourceDTO~ getAllResources()
        +ResourceDTO createResource(CreateResourceRequest)
        +ResourceDTO updateResource(Long, UpdateResourceRequest)
        +void deleteResource(Long)
        +Page~ResourceDTO~ searchResources(SearchCriteria)
    }

    class ProxyServiceController {
        -ProxyServiceService proxyService
        +ServiceDTO getServiceStatus(Long)
        +ServiceDTO createService(CreateServiceRequest)
        +ServiceDTO updateService(Long, UpdateServiceRequest)
        +void deployService(Long)
        +void startService(Long)
        +void stopService(Long)
    }

    class UDPResourceService {
        -UDPResourceRepository repository
        -IPRangeRepository ipRangeRepo
        +Resource createResource(CreateResourceRequest)
        +Resource updateResource(UpdateResourceRequest)
        +void deleteResource(Long)
        +List~Resource~ search(SearchCriteria)
        -validateResourceName(String)
    }

    class ProxyServiceService {
        -ProxyServiceRepository repository
        -UDPResourceService resourceService
        -ConfigurationManager configManager
        -ServiceDeployer deployer
        +Service createService(CreateServiceRequest)
        +void updateStatus(Long, ServiceStatus)
        +void deploy(Long)
        +void start(Long)
        +void stop(Long)
        -validateConfiguration(ProxyService)
    }

    class ConfigurationManager {
        -String configPath
        +void saveConfiguration(ProxyService)
        +Configuration loadConfiguration(Long)
        +void validateConfiguration(Configuration)
        +void backupConfiguration(Long)
    }

    class ServiceDeployer {
        -String deploymentPath
        +void deploy(ProxyService)
        +void undeploy(Long)
        +DeploymentStatus checkStatus(Long)
        -prepareDeployment(ProxyService)
    }

    UDPResource "1" -- "*" IPRange
    ProxyService "1" -- "1" UDPResource
    UDPResourceController ..> UDPResourceService
    ProxyServiceController ..> ProxyServiceService
    UDPResourceService ..> UDPResource
    ProxyServiceService ..> ProxyService
    ProxyServiceService ..> ConfigurationManager
    ProxyServiceService ..> ServiceDeployer