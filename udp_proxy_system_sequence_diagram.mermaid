sequenceDiagram
    participant C as Client
    participant RC as ResourceController
    participant RS as ResourceService
    participant PC as ProxyController
    participant PS as ProxyService
    participant C<PERSON> as ConfigManager
    participant SD as ServiceDeployer
    participant DB as Database

    %% Create UDP Resource
    C->>RC: POST /api/resources
    RC->>RS: createResource(request)
    RS->>DB: validateResourceName()
    DB-->>RS: name validation result
    RS->>DB: save resource
    DB-->>RS: saved resource
    RS-->>RC: ResourceDTO
    RC-->>C: 201 Created

    %% Configure Proxy Service
    C->>PC: POST /api/proxy-services
    PC->>PS: createService(request)
    PS->>RS: getResource(resourceId)
    RS->>DB: find resource
    DB-->>RS: resource
    RS-->>PS: resource
    PS->>DB: save service
    DB-->>PS: saved service
    PS->>CM: saveConfiguration(service)
    CM-->>PS: config saved
    PS-->>PC: ServiceDTO
    PC-->>C: 201 Created

    %% Deploy Service
    C->>PC: POST /api/proxy-services/{id}/deploy
    PC->>PS: deployService(id)
    PS->>DB: find service
    DB-->>PS: service
    PS->>CM: loadConfiguration(id)
    CM-->>PS: configuration
    PS->>SD: deploy(service)
    SD-->>PS: deployment result
    PS->>DB: update status
    DB-->>PS: updated
    PS-->>PC: DeploymentResult
    PC-->>C: 200 OK

    %% Start Service
    C->>PC: POST /api/proxy-services/{id}/start
    PC->>PS: startService(id)
    PS->>SD: start(id)
    SD-->>PS: started
    PS->>DB: update status
    DB-->>PS: updated
    PS-->>PC: ServiceStatus
    PC-->>C: 200 OK

    %% Monitor Service Status
    C->>PC: GET /api/proxy-services/{id}/status
    PC->>PS: getServiceStatus(id)
    PS->>SD: checkStatus(id)
    SD-->>PS: status
    PS-->>PC: ServiceStatus
    PC-->>C: 200 OK