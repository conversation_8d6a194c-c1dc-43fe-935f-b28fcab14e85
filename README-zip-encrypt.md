# ZIP文件加密工具

这个工具可以对ZIP文件进行加密，使其需要密码才能解压。

## 功能特点

- 使用AES加密算法对ZIP文件进行加密
- 支持两种加密方式：
  1. 基本的AES加密
  2. 基于Zip4j库的标准ZIP加密（如果lib中有zip4j依赖）
- 生成的ZIP文件需要密码才能解压缩

## 使用方法

### 方法1：运行批处理文件（推荐）

1. 双击运行 `encrypt_zip.bat`
2. 脚本将自动编译并运行程序
3. 默认会将 `E:\qd\log.zip` 文件加密为 `E:\qd\test1.zip`，密码为 `unimas`

### 方法2：直接调用类库

如果您需要在自己的程序中使用这个加密功能，可以直接调用相关类：

```java
// 使用基本加密方法
ZipEncryptor.encryptZipFile("源文件路径.zip", "加密后文件路径.zip", "密码");

// 使用Zip4j加密（如果有依赖）
Zip4jEncryptor.encryptZipFile("源文件路径.zip", "加密后文件路径.zip", "密码");
```

## 参数说明

默认情况下，程序会使用以下参数：

- 源文件路径：`E:\qd\log.zip`
- 加密后文件路径：`E:\qd\test1.zip`
- 加密密码：`unimas`

如果需要修改这些参数，您可以编辑 `ZipPasswordApp.java` 文件中的相关变量。

## 解压加密后的文件

使用解压软件（如WinRAR、7-Zip等）打开加密后的ZIP文件时，会提示输入密码。输入正确的密码（默认为`unimas`）后才能解压文件。

## 技术说明

本工具提供了两种加密实现：

1. **基本AES加密**：使用Java内置的加密API实现，将整个ZIP文件进行AES加密。
2. **标准ZIP加密**：使用Zip4j库实现，符合ZIP标准规范的加密方式，兼容性更好。

两种方式都能实现加密ZIP的目的，但标准ZIP加密兼容性更好，建议优先使用。 