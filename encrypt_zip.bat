@echo off
echo ====== ZIP文件加密工具 ======
echo.

REM 设置Java环境变量
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_301
if not exist "%JAVA_HOME%\bin\java.exe" (
  echo 未找到Java环境，尝试使用系统PATH中的Java
  set JAVA=java
) else (
  set JAVA="%JAVA_HOME%\bin\java"
)

REM 检查Maven是否存在
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo 未找到Maven，将使用已编译的JAR文件
  goto RunJar
) else (
  echo 找到Maven，将编译项目
)

REM 使用Maven编译项目
echo 正在编译项目...
call mvn -f pom-encrypt.xml clean package

if %ERRORLEVEL% NEQ 0 (
  echo Maven编译失败，尝试运行已编译的JAR
  goto RunJar
)

echo 编译成功！

:RunJar
echo 正在运行ZIP加密工具...
echo.

REM 尝试运行JAR文件
set JAR_FILE=target\zip-encrypt-util-1.0-SNAPSHOT.jar
if exist "%JAR_FILE%" (
  %JAVA% -jar "%JAR_FILE%"
) else (
  echo 未找到JAR文件：%JAR_FILE%
  echo 尝试直接运行主类...
  %JAVA% -cp "target\classes;lib\*" com.udpproxy.util.ZipPasswordApp
)

echo.
echo 处理完成！
pause 