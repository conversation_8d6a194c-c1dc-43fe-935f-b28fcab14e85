# Ant 构建说明

本项目提供了 Ant 构建脚本，可以用来编译和打包项目为 JAR 文件。

## 前提条件

1. 安装 JDK 1.8 或更高版本
2. 安装 Apache Ant 1.9 或更高版本

## 使用方法

### 创建 lib 目录

在使用 Ant 构建之前，需要创建 `lib` 目录并包含所有项目依赖的 JAR 文件。有两种方式获取依赖：

#### 方式一：从现有 Maven 构建中复制依赖

如果已经有使用 Maven 构建的项目，可以执行：

```bash
# 创建 lib 目录
mkdir -p lib

# 复制 Maven 仓库中的依赖到 lib 目录
# 如果使用 Windows，可以手动复制 .m2/repository 下的相关 JAR 文件到 lib 目录
```

#### 方式二：从现有 JAR 包中提取依赖

如果已经有打包好的 JAR 文件，可以从中提取依赖：

```bash
# 创建临时目录
mkdir -p temp_extract

# 解压现有 JAR 包
jar -xf E:/qd/udp/target/udp-proxy-system-0.0.1-SNAPSHOT.jar -C temp_extract

# 创建 lib 目录并复制依赖
mkdir -p lib
cp temp_extract/BOOT-INF/lib/* lib/

# 清理临时目录
rm -rf temp_extract
```

### 编译和打包项目

在项目根目录下执行以下命令：

```bash
# 清理并打包
ant clean package
```

构建成功后，JAR 文件将生成在 `target` 目录下，文件名为 `udp-proxy-system-0.0.1-SNAPSHOT.jar`。

### 其他可用的 Ant 目标

- `ant clean` - 清理构建目录
- `ant compile` - 只编译代码不打包
- `ant init` - 初始化必要的目录
- `ant mvn-package` - 与 `package` 相同，提供 Maven 兼容的命令名称

## 注意事项

1. 构建脚本假设 Spring Boot 加载器 JAR 文件在 `lib` 目录中，文件名符合 `spring-boot-loader-*.jar` 格式
2. 如果没有 Spring Boot 加载器 JAR，构建过程会失败
3. 确保 `src/main/java` 目录包含所有 Java 源文件，`src/main/resources` 目录包含所有资源文件
4. 打包的 JAR 文件将命名为 `udp-proxy-system-0.0.1-SNAPSHOT.jar`

## 构建输出

成功运行 `ant package` 命令后，您应该在控制台看到如下输出：

```
init:
    [mkdir] Created dir: build
    [mkdir] Created dir: build/classes
    [mkdir] Created dir: target

compile:
     [echo] 编译Java源代码到 build/classes
    [javac] Compiling files...
     [copy] Copying files...

package:
     [echo] 创建JAR文件: target/udp-proxy-system-0.0.1-SNAPSHOT.jar
    [unzip] Expanding: lib/spring-boot-loader-x.x.x.jar into build/loader
      [jar] Building jar: target/udp-proxy-system-0.0.1-SNAPSHOT.jar
     [echo] JAR文件创建完成: target/udp-proxy-system-0.0.1-SNAPSHOT.jar

BUILD SUCCESSFUL
``` 