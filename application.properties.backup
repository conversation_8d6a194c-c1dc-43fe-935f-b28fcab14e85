# 基本配置
server.port=8081
server.ssl.enabled=false

# 模板引擎配置
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# 启用静态资源
spring.web.resources.add-mappings=true
# 静态资源路径配置
spring.web.resources.static-locations=classpath:/static/
# 静态资源缓存控制
spring.web.resources.cache.period=0
spring.mvc.static-path-pattern=/static/**

# 日志配置
logging.level.root=INFO
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.udpproxy=DEBUG

# 身份验证日志
logging.level.org.springframework.security=DEBUG

# 确保没有设置或设置为空
server.servlet.context-path= 

# 授权管理配置
license.hardware-serial=UDPSystem-2023-001-XXXX
license.base-modules=基础模块
license.file.path=/etc/unimas/tomcat/license/system-license.dat

# 应用配置目录
config.dir=/etc/unimas/tomcat/config

# 模拟远程配置目录 - 用于文件同步
remote.config.dir=/etc/unimas/tomcat/remote_config

# 启用配置同步功能
sync.enabled=true
