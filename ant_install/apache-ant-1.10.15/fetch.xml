<?xml version="1.0"?>

<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!--
  =======================================================================
    Build file to fetch optional libraries for Apache Ant
  =======================================================================
-->
<project name="fetch" default="all" basedir=".">

  <description>
This build file downloads JAR files that optional Ant tasks use,
and installs them in a location that is accessible the next time Ant runs.

You can choose three locations, by going -Ddest=LOCATION on the command line
-Ddest=user     user lib dir  ${user.home}/.ant/lib
-Ddest=system   ant lib dir   ${ant.home}/lib
-Ddest=optional optional dir  $${basedir}/lib/optional  (for Ant developers)

You may also need to set proxy settings. On Java 1.5, Ant tries to get
this from the OS, unless you use the -noproxy option.

Proxies can be configured manually setting the JVM proxy values in the
ANT_OPTS environment variable.

For example, to set the proxy up in the tcsh shell, the command would
be something like:

For csh/tcsh:
 setenv ANT_OPTS "-Dhttp.proxyHost=proxy -Dhttp.proxyPort=8080"
For bash:
 export ANT_OPTS="-Dhttp.proxyHost=proxy -Dhttp.proxyPort=8080"
For Windows, set the environment variable in the appropriate dialog box
and open a new console. or, by hand
 set ANT_OPTS = -Dhttp.proxyHost=proxy -Dhttp.proxyPort=8080
  </description>

  <!-- Give user a chance to override without editing this file
       (and without typing -D each time it compiles it) -->
  <property file="${user.home}/.ant/ant.properties"/>
  <property name="lib.dir" location="lib"/>
  <property name="optional.dir" location="${lib.dir}/optional"/>
  <property name="userlib.dir" location="${user.home}/.ant/lib"/>

  <!-- Load in our properties table -->
  <property file="${lib.dir}/libraries.properties"/>

  <!-- Temporary cache for working files -->
  <property name="temp.dir" location="${user.home}/.ant/tempcache"/>
  <property name="keep.temp.dir" value="true"/>

  <import file="get-m2.xml"/>

  <target name="pick-dest">
    <fail>
      <condition>
        <not>
          <isset property="dest"/>
        </not>
      </condition>ERROR
Set -Ddest=LOCATION on the command line
  -Ddest=user     user lib dir  ${user.home}/.ant/lib
  -Ddest=system   ant lib dir   ${ant.home}/lib
  -Ddest=optional optional dir  $${basedir}/lib/optional  (for Ant developers)
    </fail>

    <condition property="dest.dir"
               value="${lib.dir}">
      <equals arg1="${dest}" arg2="system"/>
    </condition>
    <condition property="dest.dir"
               value="${optional.dir}">
      <equals arg1="${dest}" arg2="optional"/>
    </condition>
    <condition property="dest.dir"
               value="${userlib.dir}">
      <equals arg1="${dest}" arg2="user"/>
    </condition>
    <fail unless="dest.dir">Unknown destination : ${dest}</fail>
    <echo>Downloading to ${dest.dir}</echo>
    <property name="m2.dest.dir" value="${dest.dir}"/>
  </target>


  <target name="macros" depends="pick-dest,get-m2"
          xmlns:resolver="antlib:org.apache.maven.resolver.ant">
    <fail>
      Cannot execute multiple targets due to the bug in Maven Ant tasks
      <condition>
        <contains string="${ant.project.invoked-targets}" substring=","/>
      </condition>
    </fail>
    <macrodef name="f2">
      <attribute name="project"/>
      <attribute name="archive" default="@{project}"/>
      <attribute name="repository" default="${m2.repo}"/>
      <attribute name="id" default="central"/>
      <sequential>
        <fail>
          Unknown archive @{archive} -no property @{archive}.version defined in ${lib.dir}/libraries.properties.
          <condition>
            <not>
              <isset property="@{archive}.version"/>
            </not>
          </condition>
        </fail>
        <resolver:resolve>
          <resolver:remoterepo url="@{repository}" id="@{id}"/>
          <dependencies id="@{archive}.path">
            <dependency groupId="@{project}"
                        artifactId="@{archive}"
                        version="${@{archive}.version}"
                        scope="runtime">
              <!-- exclude dependencies of ant-antunit (they should be in optional scope) -->
              <exclusion groupId="org.apache.ant" artifactId="ant"/>
              <exclusion groupId="org.apache.ant" artifactId="ant-launcher"/>
            </dependency>
          </dependencies>
          <!-- create a path containing all these resolved dependencies -->
          <path refid="@{archive}.path"/>
        </resolver:resolve>
        <!-- now we are left with the problem of getting the files into our directory -->
        <copy todir="${dest.dir}">
          <path refid="@{archive}.path"/>
          <flattenmapper/>
        </copy>
      </sequential>
    </macrodef>
  </target>

  <target name="nonm2-macros" depends="pick-dest">
    <macrodef name="get-ftp-file">
      <attribute name="host"/>
      <attribute name="port" default="21"/>
      <attribute name="remotedir"/>
      <attribute name="filename"/>
      <attribute name="localdir" default="${dest.dir}"/>
      <attribute name="user" default="anonymous"/>
      <attribute name="pw" default="anonymous"/>
      <sequential>
        <ftp server="@{host}" port="@{port}" userid="@{user}" password="@{pw}" passive="true"
             remotedir="@{remotedir}" action="get" depends="true" preserveLastModified="true"
             skipFailedTransfers="true">
          <fileset dir="@{localdir}">
            <include name="@{filename}"/>
          </fileset>
        </ftp>
      </sequential>
    </macrodef>
  </target>


  <!-- any init stuff -->
  <target name="init" depends="macros"/>

  <target name="init-no-m2" depends="nonm2-macros"/>

  <target name="init-cache">
    <available property="temp.cache.already.exists" file="${temp.dir}" type="dir"/>
    <condition property="user.wants.temp.cache">
      <and>
        <isset property="keep.temp.dir"/>
        <not>
          <or>
            <equals arg1="${keep.temp.dir}" arg2="false" casesensitive="false"/>
            <equals arg1="${keep.temp.dir}" arg2="no" casesensitive="false"/>
            <equals arg1="${keep.temp.dir}" arg2="off" casesensitive="false"/>
          </or>
        </not>
      </and>
    </condition>
    <condition property="delete.temp.cache">
      <and>
        <not>
          <isset property="temp.cache.already.exists"/>
        </not>
        <not>
          <isset property="user.wants.temp.cache"/>
        </not>
      </and>
    </condition>
  </target>

  <target name="-setup-temp-cache" depends="init-cache" unless="temp.cache.already.exists"
          description="Set up temporary cache for downloaded files">
    <mkdir dir="${temp.dir}"/>
  </target>

  <target name="-cleanup-temp-cache" depends="init-cache" if="delete.temp.cache"
          description="Get rid of the temporary cache directory">
    <delete dir="${temp.dir}"/>
  </target>


  <target name="diag" depends="init">
    <echoproperties/>
  </target>

  <target name="antunit"
          description="load AntUnit library"
          depends="init">
    <f2 project="org.apache.ant" archive="ant-antunit"/>
  </target>

  <target name="ivy"
          description="load Ivy dependency manager"
          depends="init">
    <f2 project="org.apache.ivy" archive="ivy"/>
  </target>

  <target name="logging"
          description="load logging libraries (Commons and Log4j)"
          depends="init">
    <f2 project="log4j"/>
    <f2 project="commons-logging" archive="commons-logging-api"/>
  </target>

  <target name="junit"
          description="load JUnit libraries"
          depends="init">
    <f2 project="junit"/>
    <f2 project="org.hamcrest" archive="hamcrest-library"/>
  </target>

  <target name="junitlauncher"
    description="load junitlauncher libraries"
    depends="init">
    <f2 project="org.junit.platform" archive="junit-platform-launcher" />
  </target>

  <target name="junit-engine-jupiter"
          description="load junit jupiter engine libraries (necessary only for internal Ant project tests)"
          depends="init">
    <f2 project="org.junit.jupiter" archive="junit-jupiter-engine" />
    <f2 project="org.junit.jupiter" archive="junit-jupiter-params" />
  </target>

  <target name="junit-engine-vintage"
          description="load junit vintage engine libraries (necessary only for internal Ant project tests)"
          depends="init">
    <f2 project="org.junit.vintage" archive="junit-vintage-engine" />
  </target>

  <target name="xml"
          description="load full XML libraries (Xalan and xml-resolver)"
          depends="init">
    <f2 project="xalan"/>
    <f2 project="xalan" archive="serializer"/>
    <f2 project="xml-resolver"/>
  </target>

  <target name="networking"
          description="load networking libraries (commons-net and JSch)"
          depends="init">
    <f2 project="commons-net"/>
    <f2 project="com.jcraft" archive="jsch"/>
  </target>

  <target name="regexp"
          description="load regexp libraries"
          depends="init">
    <f2 project="jakarta-regexp"/>
    <f2 project="oro"/>
  </target>

  <target name="antlr"
          description="load ANother Tool for Language Recognition (ANTLR)"
          depends="init">
    <f2 project="antlr"/>
  </target>

  <target name="bcel"
          description="load Byte Code Engineering Library (BCEL)"
          depends="init">
    <f2 project="org.apache.bcel" archive="bcel"/>
  </target>

  <target name="jdepend"
          description="load JDepend libraries"
          depends="init">
    <f2 project="jdepend"/>
  </target>

  <target name="bsf"
          description="load Bean Scripting Framework"
          depends="init">
    <f2 project="bsf"/>
  </target>

  <target name="jruby"
          description="load JRuby"
          depends="bsf">
    <f2 project="org.jruby" archive="jruby"/>
  </target>

  <target name="beanshell"
          description="load BeanShell support"
          depends="bsf">
    <f2 project="org.beanshell" archive="bsh"/>
  </target>

  <target name="jython"
          description="load Jython"
          depends="bsf">
    <f2 project="org.python" archive="jython"/>
  </target>

  <target name="rhino"
          description="load Rhino"
          depends="bsf">
    <f2 project="org.mozilla" archive="rhino"/>
  </target>

  <target name="graal.js"
          description="load Graal.js">
    <f2 project="org.graalvm.js" archive="js"/>
    <f2 project="org.graalvm.js" archive="js-scriptengine"/>
  </target>

  <target name="script"
          description="load script languages (except Jython)"
          depends="bsf,jruby,beanshell,rhino,graal.js"/>

  <target name="debugging"
    description="internal Ant debugging"
    depends="init">
    <f2 project="which"/>
  </target>

  <target name="javamail"
          description="load Java Mail"
          depends="init">
    <f2 project="com.sun.mail" archive="javax.mail"/>
  </target>

  <target name="jakartamail"
          description="load Jakarta Mail"
          depends="init">
    <!-- We only need this one dependency as per
      the project doc https://eclipse-ee4j.github.io/mail/
      This alone should bring in all necessary dependencies (including the API
       jars and the activation jars -->
    <f2 project="com.sun.mail" archive="jakarta.mail"/>
  </target>

  <target name="jspc"
          description="load Jasper"
          depends="init">
    <f2 project="tomcat" archive="jasper-compiler"/>
    <f2 project="tomcat" archive="jasper-runtime"/>
    <f2 project="javax.servlet" archive="servlet-api"/>
  </target>

  <target name="jai"
          description="load Java Advanced Imaging"
          depends="init">
    <f2 project="javax.media" archive="jai-core" id="jboss"
        repository="https://repository.jboss.org/nexus/content/groups/public/"/>
    <f2 project="com.sun.media" archive="jai-codec" id="jboss"
        repository="https://repository.jboss.org/nexus/content/groups/public/"/>
  </target>

  <target name="netrexx"
          description="load NetRexx compiler"
          depends="init-no-m2">
    <get src="https://netrexx.org/files/NetRexxC-${netrexx.version}.jar"
         dest="${dest.dir}" skipexisting="true"/>
  </target>

  <target name="xz"
          description="load XZ for Java"
          depends="init">
    <f2 project="org.tukaani" archive="xz"/>
  </target>

  <target name="all"
    description="load all the libraries (except jython)"
    depends="antunit,ivy,logging,junit,junitlauncher,xml,networking,regexp,antlr,bcel,jdepend,bsf,debugging,script,
      javamail,jakartamail,jspc,jai,xz,junit-engine-vintage,junit-engine-jupiter,netrexx"/>
</project>
