<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For All Packages</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="org/apache/tools/ant/package-tree.html">org.apache.tools.ant</a>, </li>
<li><a href="org/apache/tools/ant/attribute/package-tree.html">org.apache.tools.ant.attribute</a>, </li>
<li><a href="org/apache/tools/ant/dispatch/package-tree.html">org.apache.tools.ant.dispatch</a>, </li>
<li><a href="org/apache/tools/ant/filters/package-tree.html">org.apache.tools.ant.filters</a>, </li>
<li><a href="org/apache/tools/ant/filters/util/package-tree.html">org.apache.tools.ant.filters.util</a>, </li>
<li><a href="org/apache/tools/ant/helper/package-tree.html">org.apache.tools.ant.helper</a>, </li>
<li><a href="org/apache/tools/ant/input/package-tree.html">org.apache.tools.ant.input</a>, </li>
<li><a href="org/apache/tools/ant/launch/package-tree.html">org.apache.tools.ant.launch</a>, </li>
<li><a href="org/apache/tools/ant/listener/package-tree.html">org.apache.tools.ant.listener</a>, </li>
<li><a href="org/apache/tools/ant/loader/package-tree.html">org.apache.tools.ant.loader</a>, </li>
<li><a href="org/apache/tools/ant/property/package-tree.html">org.apache.tools.ant.property</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/package-tree.html">org.apache.tools.ant.taskdefs</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/compilers/package-tree.html">org.apache.tools.ant.taskdefs.compilers</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/condition/package-tree.html">org.apache.tools.ant.taskdefs.condition</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/cvslib/package-tree.html">org.apache.tools.ant.taskdefs.cvslib</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/email/package-tree.html">org.apache.tools.ant.taskdefs.email</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/launcher/package-tree.html">org.apache.tools.ant.taskdefs.launcher</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/modules/package-tree.html">org.apache.tools.ant.taskdefs.modules</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/package-tree.html">org.apache.tools.ant.taskdefs.optional</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/ccm/package-tree.html">org.apache.tools.ant.taskdefs.optional.ccm</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/clearcase/package-tree.html">org.apache.tools.ant.taskdefs.optional.clearcase</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/depend/package-tree.html">org.apache.tools.ant.taskdefs.optional.depend</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/package-tree.html">org.apache.tools.ant.taskdefs.optional.depend.constantpool</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/ejb/package-tree.html">org.apache.tools.ant.taskdefs.optional.ejb</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/extension/package-tree.html">org.apache.tools.ant.taskdefs.optional.extension</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/package-tree.html">org.apache.tools.ant.taskdefs.optional.extension.resolvers</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/i18n/package-tree.html">org.apache.tools.ant.taskdefs.optional.i18n</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/image/package-tree.html">org.apache.tools.ant.taskdefs.optional.image</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/j2ee/package-tree.html">org.apache.tools.ant.taskdefs.optional.j2ee</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/javacc/package-tree.html">org.apache.tools.ant.taskdefs.optional.javacc</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/javah/package-tree.html">org.apache.tools.ant.taskdefs.optional.javah</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/jdepend/package-tree.html">org.apache.tools.ant.taskdefs.optional.jdepend</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/jlink/package-tree.html">org.apache.tools.ant.taskdefs.optional.jlink</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/jsp/package-tree.html">org.apache.tools.ant.taskdefs.optional.jsp</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/package-tree.html">org.apache.tools.ant.taskdefs.optional.jsp.compilers</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/junit/package-tree.html">org.apache.tools.ant.taskdefs.optional.junit</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/package-tree.html">org.apache.tools.ant.taskdefs.optional.junitlauncher</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/package-tree.html">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/package-tree.html">org.apache.tools.ant.taskdefs.optional.native2ascii</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/net/package-tree.html">org.apache.tools.ant.taskdefs.optional.net</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/pvcs/package-tree.html">org.apache.tools.ant.taskdefs.optional.pvcs</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/script/package-tree.html">org.apache.tools.ant.taskdefs.optional.script</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/sos/package-tree.html">org.apache.tools.ant.taskdefs.optional.sos</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/sound/package-tree.html">org.apache.tools.ant.taskdefs.optional.sound</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/splash/package-tree.html">org.apache.tools.ant.taskdefs.optional.splash</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/ssh/package-tree.html">org.apache.tools.ant.taskdefs.optional.ssh</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/testing/package-tree.html">org.apache.tools.ant.taskdefs.optional.testing</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/unix/package-tree.html">org.apache.tools.ant.taskdefs.optional.unix</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/vss/package-tree.html">org.apache.tools.ant.taskdefs.optional.vss</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/windows/package-tree.html">org.apache.tools.ant.taskdefs.optional.windows</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/optional/xz/package-tree.html">org.apache.tools.ant.taskdefs.optional.xz</a>, </li>
<li><a href="org/apache/tools/ant/taskdefs/rmic/package-tree.html">org.apache.tools.ant.taskdefs.rmic</a>, </li>
<li><a href="org/apache/tools/ant/types/package-tree.html">org.apache.tools.ant.types</a>, </li>
<li><a href="org/apache/tools/ant/types/mappers/package-tree.html">org.apache.tools.ant.types.mappers</a>, </li>
<li><a href="org/apache/tools/ant/types/optional/package-tree.html">org.apache.tools.ant.types.optional</a>, </li>
<li><a href="org/apache/tools/ant/types/optional/depend/package-tree.html">org.apache.tools.ant.types.optional.depend</a>, </li>
<li><a href="org/apache/tools/ant/types/optional/image/package-tree.html">org.apache.tools.ant.types.optional.image</a>, </li>
<li><a href="org/apache/tools/ant/types/optional/imageio/package-tree.html">org.apache.tools.ant.types.optional.imageio</a>, </li>
<li><a href="org/apache/tools/ant/types/optional/xz/package-tree.html">org.apache.tools.ant.types.optional.xz</a>, </li>
<li><a href="org/apache/tools/ant/types/resolver/package-tree.html">org.apache.tools.ant.types.resolver</a>, </li>
<li><a href="org/apache/tools/ant/types/resources/package-tree.html">org.apache.tools.ant.types.resources</a>, </li>
<li><a href="org/apache/tools/ant/types/resources/comparators/package-tree.html">org.apache.tools.ant.types.resources.comparators</a>, </li>
<li><a href="org/apache/tools/ant/types/resources/selectors/package-tree.html">org.apache.tools.ant.types.resources.selectors</a>, </li>
<li><a href="org/apache/tools/ant/types/selectors/package-tree.html">org.apache.tools.ant.types.selectors</a>, </li>
<li><a href="org/apache/tools/ant/types/selectors/modifiedselector/package-tree.html">org.apache.tools.ant.types.selectors.modifiedselector</a>, </li>
<li><a href="org/apache/tools/ant/types/spi/package-tree.html">org.apache.tools.ant.types.spi</a>, </li>
<li><a href="org/apache/tools/ant/util/package-tree.html">org.apache.tools.ant.util</a>, </li>
<li><a href="org/apache/tools/ant/util/depend/package-tree.html">org.apache.tools.ant.util.depend</a>, </li>
<li><a href="org/apache/tools/ant/util/depend/bcel/package-tree.html">org.apache.tools.ant.util.depend.bcel</a>, </li>
<li><a href="org/apache/tools/ant/util/facade/package-tree.html">org.apache.tools.ant.util.facade</a>, </li>
<li><a href="org/apache/tools/ant/util/java15/package-tree.html">org.apache.tools.ant.util.java15</a>, </li>
<li><a href="org/apache/tools/ant/util/optional/package-tree.html">org.apache.tools.ant.util.optional</a>, </li>
<li><a href="org/apache/tools/ant/util/regexp/package-tree.html">org.apache.tools.ant.util.regexp</a>, </li>
<li><a href="org/apache/tools/bzip2/package-tree.html">org.apache.tools.bzip2</a>, </li>
<li><a href="org/apache/tools/mail/package-tree.html">org.apache.tools.mail</a>, </li>
<li><a href="org/apache/tools/tar/package-tree.html">org.apache.tools.tar</a>, </li>
<li><a href="org/apache/tools/zip/package-tree.html">org.apache.tools.zip</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.util.depend.<a href="org/apache/tools/ant/util/depend/AbstractAnalyzer.html" class="type-name-link" title="class in org.apache.tools.ant.util.depend">AbstractAnalyzer</a> (implements org.apache.tools.ant.util.depend.<a href="org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.depend.bcel.<a href="org/apache/tools/ant/util/depend/bcel/AncestorAnalyzer.html" class="type-name-link" title="class in org.apache.tools.ant.util.depend.bcel">AncestorAnalyzer</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/AntAnalyzer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend">AntAnalyzer</a></li>
<li class="circle">org.apache.tools.ant.util.depend.bcel.<a href="org/apache/tools/ant/util/depend/bcel/FullAnalyzer.html" class="type-name-link" title="class in org.apache.tools.ant.util.depend.bcel">FullAnalyzer</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AbstractClasspathResource.ClassLoaderWithFlag.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource.ClassLoaderWithFlag</a></li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractCollection.html" class="type-name-link external-link" title="class or interface in java.util">AbstractCollection</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;E&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractList.html" class="type-name-link external-link" title="class or interface in java.util">AbstractList</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" class="type-name-link external-link" title="class or interface in java.util">Vector</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/RandomAccess.html" title="class or interface in java.util" class="external-link">RandomAccess</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.NodeListImpl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeListImpl</a> (implements org.w3c.dom.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/w3c/dom/NodeList.html" title="class or interface in org.w3c.dom" class="external-link">NodeList</a>)</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" class="type-name-link external-link" title="class or interface in java.util">Stack</a>&lt;E&gt;
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/IdentityStack.html" class="type-name-link" title="class in org.apache.tools.ant.util">IdentityStack</a>&lt;E&gt;</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/VectorSet.html" class="type-name-link" title="class in org.apache.tools.ant.util">VectorSet</a>&lt;E&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AbstractCvsTask.Module.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask.Module</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/AbstractHotDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a> (implements org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/GenericHotDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">GenericHotDeploymentTool</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/JonasHotDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">JonasHotDeploymentTool</a> (implements org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/WebLogicHotDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">WebLogicHotDeploymentTool</a> (implements org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a>)</li>
</ul>
</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractMap.html" class="type-name-link external-link" title="class or interface in java.util">AbstractMap</a>&lt;K,<wbr>V&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/HashMap.html" class="type-name-link external-link" title="class or interface in java.util">HashMap</a>&lt;K,<wbr>V&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">junit.framework.JUnit4TestAdapterCache
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/CustomJUnit4TestAdapterCache.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">CustomJUnit4TestAdapterCache</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/AbstractSshMessage.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">AbstractSshMessage</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpFromMessage.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpFromMessageBySftp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessageBySftp</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpToMessage.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessage</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpToMessageBySftp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessageBySftp</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/AbstractUnicodeExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">AbstractUnicodeExtraField</a> (implements org.apache.tools.zip.<a href="org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>)
<ul>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnicodeCommentExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">UnicodeCommentExtraField</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnicodePathExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">UnicodePathExtraField</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Ant.TargetElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Ant.TargetElement</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.resolvers.<a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/AntResolver.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension.resolvers">AntResolver</a> (implements org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionResolver.html" title="interface in org.apache.tools.ant.taskdefs.optional.extension">ExtensionResolver</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sound.<a href="org/apache/tools/ant/taskdefs/optional/sound/AntSoundPlayer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sound">AntSoundPlayer</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>, javax.sound.sampled.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.desktop/javax/sound/sampled/LineListener.html" title="class or interface in javax.sound.sampled" class="external-link">LineListener</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/AntTypeDefinition.html" class="type-name-link" title="class in org.apache.tools.ant">AntTypeDefinition</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PreSetDef.PreSetDefinition</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/AntXMLContext.html" class="type-name-link" title="class in org.apache.tools.ant.helper">AntXMLContext</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ArgumentProcessorRegistry.html" class="type-name-link" title="class in org.apache.tools.ant">ArgumentProcessorRegistry</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/AsiExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">AsiExtraField</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.zip.<a href="org/apache/tools/zip/UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a>, org.apache.tools.zip.<a href="org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Assertions.BaseAssertion.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions.BaseAssertion</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Assertions.DisabledAssertion.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions.DisabledAssertion</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Assertions.EnabledAssertion.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions.EnabledAssertion</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/AttributeNamespace.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">AttributeNamespace</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Base64Converter.html" class="type-name-link" title="class in org.apache.tools.ant.util">Base64Converter</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.Base64Converter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Get.Base64Converter</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/BatchTest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/BriefJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">BriefJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/BuiltinNative2Ascii.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">BuiltinNative2Ascii</a> (implements org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/Native2AsciiAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a>)</li>
<li class="circle">org.apache.xml.resolver.Catalog
<ul>
<li class="circle">org.apache.tools.ant.types.resolver.<a href="org/apache/tools/ant/types/resolver/ApacheCatalog.html" class="type-name-link" title="class in org.apache.tools.ant.types.resolver">ApacheCatalog</a></li>
</ul>
</li>
<li class="circle">org.apache.xml.resolver.tools.CatalogResolver (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>, javax.xml.transform.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/transform/URIResolver.html" title="class or interface in javax.xml.transform" class="external-link">URIResolver</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resolver.<a href="org/apache/tools/ant/types/resolver/ApacheCatalogResolver.html" class="type-name-link" title="class in org.apache.tools.ant.types.resolver">ApacheCatalogResolver</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.filters.util.<a href="org/apache/tools/ant/filters/util/ChainReaderHelper.html" class="type-name-link" title="class in org.apache.tools.ant.filters.util">ChainReaderHelper</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/ChangeLogWriter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogWriter</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/ChecksumAlgorithm.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ChecksumAlgorithm</a> (implements org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend">ClassFile</a></li>
<li class="circle">org.apache.tools.ant.types.optional.depend.<a href="org/apache/tools/ant/types/optional/depend/ClassfileSet.ClassRoot.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet.ClassRoot</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFileUtils.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend">ClassFileUtils</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" class="type-name-link external-link" title="class or interface in java.lang">ClassLoader</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/AntClassLoader.html" class="type-name-link" title="class in org.apache.tools.ant">AntClassLoader</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, org.apache.tools.ant.<a href="org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a>)
<ul>
<li class="circle">org.apache.tools.ant.loader.<a href="org/apache/tools/ant/loader/AntClassLoader2.html" class="type-name-link" title="class in org.apache.tools.ant.loader">AntClassLoader2</a></li>
<li class="circle">org.apache.tools.ant.loader.<a href="org/apache/tools/ant/loader/AntClassLoader5.html" class="type-name-link" title="class in org.apache.tools.ant.loader">AntClassLoader5</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/SplitClassLoader.html" class="type-name-link" title="class in org.apache.tools.ant.util">SplitClassLoader</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jlink.<a href="org/apache/tools/ant/taskdefs/optional/jlink/ClassNameReader.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jlink">ClassNameReader</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ClasspathUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">ClasspathUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ClasspathUtils.Delegate.html" class="type-name-link" title="class in org.apache.tools.ant.util">ClasspathUtils.Delegate</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/CollectionUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">CollectionUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/CollectionUtils.EmptyEnumeration.html" class="type-name-link" title="class in org.apache.tools.ant.util">CollectionUtils.EmptyEnumeration</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;E&gt;)</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/ColorMapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">ColorMapper</a></li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/ColorMapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">ColorMapper</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/CommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">CommandLauncher</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/CommandLauncherProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">CommandLauncherProxy</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/MacCommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">MacCommandLauncher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/OS2CommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">OS2CommandLauncher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/PerlScriptCommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">PerlScriptCommandLauncher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/ScriptCommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">ScriptCommandLauncher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/WinNTCommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">WinNTCommandLauncher</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/Java13CommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">Java13CommandLauncher</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.launcher.<a href="org/apache/tools/ant/taskdefs/launcher/VmsCommandLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.launcher">VmsCommandLauncher</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Commandline.html" class="type-name-link" title="class in org.apache.tools.ant.types">Commandline</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Commandline.Marker.html" class="type-name-link" title="class in org.apache.tools.ant.types">Commandline.Marker</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/CommandlineJava.html" class="type-name-link" title="class in org.apache.tools.ant.types">CommandlineJava</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/CommonsLoggingListener.html" class="type-name-link" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>, org.apache.tools.ant.<a href="org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/Compatability.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatability</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/Compatibility.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapterFactory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterFactory</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ComponentHelper.html" class="type-name-link" title="class in org.apache.tools.ant">ComponentHelper</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ClassCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ClassCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantCPInfo</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/DoubleCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">DoubleCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/DynamicCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">DynamicCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/FloatCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">FloatCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/IntegerCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">IntegerCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/InvokeDynamicCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">InvokeDynamicCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/LongCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">LongCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodTypeCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodTypeCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ModuleCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ModuleCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/PackageCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">PackageCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/StringCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">StringCPInfo</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/FieldRefCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">FieldRefCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/InterfaceMethodRefCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">InterfaceMethodRefCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodHandleCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodHandleCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodRefCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/NameAndTypeCPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">NameAndTypeCPInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/Utf8CPInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">Utf8CPInfo</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/Constants.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">Constants</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/Constants.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">Constants</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ContainerMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ContainerMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ChainedMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ChainedMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/CompositeMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">CompositeMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FirstMatchMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">FirstMatchMapper</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Contains.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.mappers.<a href="org/apache/tools/ant/types/mappers/CutDirsMapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.mappers">CutDirsMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/CVSEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">CVSEntry</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/CvsTagEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagEntry</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/CvsUser.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsUser</a></li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Date.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Date</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/DateUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">DateUtils</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</a> (implements org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a>, org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapterExtension.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Gcj.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Gcj</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Javac12.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Javac12</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Javac13.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Javac13</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/JavacExternal.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">JavacExternal</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Jikes.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Jikes</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Jvc.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Jvc</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Kjc.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Kjc</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/Sj.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.compilers">Sj</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DefaultDefinitions.html" class="type-name-link" title="class in org.apache.tools.ant">DefaultDefinitions</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/DefaultExecutor.html" class="type-name-link" title="class in org.apache.tools.ant.helper">DefaultExecutor</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</a>)</li>
<li class="circle">org.xml.sax.helpers.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" class="type-name-link external-link" title="class or interface in org.xml.sax.helpers">DefaultHandler</a> (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a>, org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/DTDHandler.html" title="class or interface in org.xml.sax" class="external-link">DTDHandler</a>, org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>, org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/ErrorHandler.html" title="class or interface in org.xml.sax" class="external-link">ErrorHandler</a>)
<ul>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.RootHandler.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2.RootHandler</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/DefaultInputHandler.html" class="type-name-link" title="class in org.apache.tools.ant.input">DefaultInputHandler</a> (implements org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a>)
<ul>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/GreedyInputHandler.html" class="type-name-link" title="class in org.apache.tools.ant.input">GreedyInputHandler</a></li>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/SecureInputHandler.html" class="type-name-link" title="class in org.apache.tools.ant.input">SecureInputHandler</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.compilers.<a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/DefaultJspCompilerAdapter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">DefaultJspCompilerAdapter</a> (implements org.apache.tools.ant.taskdefs.optional.jsp.compilers.<a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.compilers.<a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JasperC.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JasperC</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DefaultLogger.html" class="type-name-link" title="class in org.apache.tools.ant">DefaultLogger</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a>)
<ul>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/AnsiColorLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">AnsiColorLogger</a></li>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/MailLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">MailLogger</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/NoBannerLogger.html" class="type-name-link" title="class in org.apache.tools.ant">NoBannerLogger</a>
<ul>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/SimpleBigProjectLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a>
<ul>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/BigProjectLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">BigProjectLogger</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/ProfileLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">ProfileLogger</a></li>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/SilentLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">SilentLogger</a></li>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/TimestampedLogger.html" class="type-name-link" title="class in org.apache.tools.ant.listener">TimestampedLogger</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/DefaultNative2Ascii.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">DefaultNative2Ascii</a> (implements org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/Native2AsciiAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/KaffeNative2Ascii.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">KaffeNative2Ascii</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/SunNative2Ascii.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">SunNative2Ascii</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a> (implements org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/ForkingSunRmic.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">ForkingSunRmic</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/XNewRmic.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">XNewRmic</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/KaffeRmic.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">KaffeRmic</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/SunRmic.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">SunRmic</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/WLRmic.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">WLRmic</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/DeweyDecimal.html" class="type-name-link" title="class in org.apache.tools.ant.util">DeweyDecimal</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/DeweyDecimal.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">DeweyDecimal</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Diagnostics.html" class="type-name-link" title="class in org.apache.tools.ant">Diagnostics</a></li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Dictionary.html" class="type-name-link external-link" title="class or interface in java.util">Dictionary</a>&lt;K,<wbr>V&gt;
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" class="type-name-link external-link" title="class or interface in java.util">Hashtable</a>&lt;K,<wbr>V&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LazyHashtable.html" class="type-name-link" title="class in org.apache.tools.ant.util">LazyHashtable</a>&lt;K,<wbr>V&gt;</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LinkedHashtable.html" class="type-name-link" title="class in org.apache.tools.ant.util">LinkedHashtable</a>&lt;K,<wbr>V&gt;</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Properties.html" class="type-name-link external-link" title="class or interface in java.util">Properties</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LayoutPreservingProperties.html" class="type-name-link" title="class in org.apache.tools.ant.util">LayoutPreservingProperties</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/DigestAlgorithm.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">DigestAlgorithm</a> (implements org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/Directory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">Directory</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/DirectoryIterator.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend">DirectoryIterator</a> (implements org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFileIterator.html" title="interface in org.apache.tools.ant.taskdefs.optional.depend">ClassFileIterator</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant">DirectoryScanner</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a>, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>, org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ArchiveScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types">ArchiveScanner</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/TarScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types">TarScanner</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ZipScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types">ZipScanner</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.optional.depend.<a href="org/apache/tools/ant/types/optional/depend/DependScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPDirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.FTPDirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.dispatch.<a href="org/apache/tools/ant/dispatch/DispatchUtils.html" class="type-name-link" title="class in org.apache.tools.ant.dispatch">DispatchUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/DOMElementWriter.html" class="type-name-link" title="class in org.apache.tools.ant.util">DOMElementWriter</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/DOMElementWriter.XmlNamespacePolicy.html" class="type-name-link" title="class in org.apache.tools.ant.util">DOMElementWriter.XmlNamespacePolicy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/DOMUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">DOMUtils</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/EmailAddress.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a></li>
<li class="circle">org.apache.bcel.classfile.EmptyVisitor (implements org.apache.bcel.classfile.Visitor)
<ul>
<li class="circle">org.apache.tools.ant.util.depend.bcel.<a href="org/apache/tools/ant/util/depend/bcel/DependencyVisitor.html" class="type-name-link" title="class in org.apache.tools.ant.util.depend.bcel">DependencyVisitor</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.Format.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer.Format</a></li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Arc.ArcType.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Arc.ArcType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Available.FileDir.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Available.FileDir</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/CharSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">CharSet</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Checksum.FormatElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Checksum.FormatElement</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Comparison.html" class="type-name-link" title="class in org.apache.tools.ant.types">Comparison</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Length.When.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Length.When</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SizeSelector.SizeComparisons.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SizeSelector.SizeComparisons</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Definer.Format.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Definer.Format</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Definer.OnError.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Definer.OnError</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/EchoProperties.FormatAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/EchoXML.NamespacePolicy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">EchoXML.NamespacePolicy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.CMPVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/EmailTask.Encoding.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">EmailTask.Encoding</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FilterSet.OnMissing.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet.OnMissing</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/FixCRLF.AddAsisRemove.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/FixCRLF.CrLf.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/FixCrLfFilter.AddAsisRemove.html" class="type-name-link" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/FixCrLfFilter.CrLf.html" class="type-name-link" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ForkDefinition.ForkMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ForkDefinition.ForkMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.TypeAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement.TypeAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPSystemType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.Granularity.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.LanguageCode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.Action.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.Action</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.FTPSystemType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.FTPSystemType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.Granularity.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.Granularity</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.image.<a href="org/apache/tools/ant/taskdefs/optional/image/ImageIOTask.ImageFormat.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.image">ImageIOTask.ImageFormat</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Input.HandlerType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Input.HandlerType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsLastModified.CompareMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified.CompareMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Jar.FilesetManifestConfig.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Jar.StrictMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jdepend.<a href="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.FormatAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jdepend">JDependTask.FormatAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Jmod.ResolutionWarningReason.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningReason</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.SummaryAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Length.FileMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Length.FileMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.CompressionLevel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.Endianness.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.VMType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ListenerDefinition.ListenerType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ListenerDefinition.ListenerType</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/LogLevel.html" class="type-name-link" title="class in org.apache.tools.ant.types">LogLevel</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Echo.EchoLevel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Echo.EchoLevel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Recorder.VerbosityLevelChoices.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Recorder.VerbosityLevelChoices</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ManifestTask.Mode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ManifestTask.Mode</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Mapper.MapperType.html" class="type-name-link" title="class in org.apache.tools.ant.types">Mapper.MapperType</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.AlgorithmName.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.AlgorithmName</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.CacheName.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.CacheName</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.ComparatorName.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.ComparatorName</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSS.CurrentModUpdated.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSS.WritableFiles.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSHISTORY.BriefCodediffNofile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSHISTORY.BriefCodediffNofile</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/NetRexxC.VerboseAttr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PathConvert.TargetOs.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PathConvert.TargetOs</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/PresentSelector.FilePresence.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Operation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Operation</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Type.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Type</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Unit.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Unit</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/PropertySet.BuiltinPropertySetName.html" class="type-name-link" title="class in org.apache.tools.ant.types">PropertySet.BuiltinPropertySetName</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Quantifier.html" class="type-name-link" title="class in org.apache.tools.ant.types">Quantifier</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Recorder.ActionChoices.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Recorder.ActionChoices</a></li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Scale.ProportionsAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Scale.ProportionsAttribute</a></li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Scale.ProportionsAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Scale.ProportionsAttribute</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SizeSelector.ByteUnits.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SizeSelector.ByteUnits</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SQLExec.OnError.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tar.TarCompressionMethod.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tar.TarCompressionMethod</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tar.TarLongFileMode</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/TimeComparison.html" class="type-name-link" title="class in org.apache.tools.ant.types">TimeComparison</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/DateSelector.TimeComparisons.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DateSelector.TimeComparisons</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tstamp.Unit.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tstamp.Unit</a></li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Type.FileDir.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Type.FileDir</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/TypeSelector.FileType.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TypeSelector.FileType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Untar.UntarCompressionMethod.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Untar.UntarCompressionMethod</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/WaitFor.Unit.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Zip.Duplicate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Zip.UnicodeExtraField.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Zip.Zip64ModeAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/Enumerations.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">Enumerations</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Environment.html" class="type-name-link" title="class in org.apache.tools.ant.types">Environment</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/CommandlineJava.SysProperties.html" class="type-name-link" title="class in org.apache.tools.ant.types">CommandlineJava.SysProperties</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Environment.Variable.html" class="type-name-link" title="class in org.apache.tools.ant.types">Environment.Variable</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/EqualComparator.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">EqualComparator</a> (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;T&gt;)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Equals.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html" class="type-name-link external-link" title="class or interface in java.util">EventObject</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/BuildEvent.html" class="type-name-link" title="class in org.apache.tools.ant">BuildEvent</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ExecutableSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Execute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Execute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteJava.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ExecuteJava</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>, org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Exists.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Exists</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/Extension.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionUtil.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionUtil</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtraAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtraAttribute</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ExtraFieldUtils.html" class="type-name-link" title="class in org.apache.tools.zip">ExtraFieldUtils</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ExtraFieldUtils.UnparseableExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a></li>
<li class="circle">org.apache.tools.ant.util.facade.<a href="org/apache/tools/ant/util/facade/FacadeTaskHelper.html" class="type-name-link" title="class in org.apache.tools.ant.util.facade">FacadeTaskHelper</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/FailureRecorder.TestInfos.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder.TestInfos</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;)</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" class="type-name-link external-link" title="class or interface in java.io">File</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPFileProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPFileProxy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.FTPFileProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPFileProxy</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FileList.FileName.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileList.FileName</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/FileResourceIterator.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">FileResourceIterator</a> (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;E&gt;)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/FilesMatch.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">FileUtils</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FilterSet.Filter.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet.Filter</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FilterSet.FiltersFile.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet.FiltersFile</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FilterSetCollection.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSetCollection</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/FixCRLF.OneLiner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF.OneLiner</a> (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;E&gt;)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FlatFileNameMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">FlatFileNameMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FlexInteger.html" class="type-name-link" title="class in org.apache.tools.ant.types">FlexInteger</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ForkDefinition.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ForkDefinition</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/ForkingJavah.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javah">ForkingJavah</a> (implements org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPDirectoryScanner.AntFTPFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner.AntFTPFile</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPDirectoryScanner.AntFTPRootFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner.AntFTPRootFile</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl</a> (implements org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPRootFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPRootFile</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/Gcjh.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javah">Gcjh</a> (implements org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapter</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/GeneralPurposeBit.html" class="type-name-link" title="class in org.apache.tools.zip">GeneralPurposeBit</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/GenerateKey.DistinguishedName.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DistinguishedName</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/GenerateKey.DnameParam.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DnameParam</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a> (implements org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetDeploymentTool</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/JbossDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/OrionDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">OrionDeploymentTool</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicTOPLinkDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.NullProgress.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Get.NullProgress</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.VerboseProgress.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Get.VerboseProgress</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/GlobPatternMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">GlobPatternMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/PackageNameMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">PackageNameMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/UnPackageNameMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">UnPackageNameMapper</a></li>
</ul>
</li>
<li class="circle">org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/HandlerBase.html" class="type-name-link external-link" title="class or interface in org.xml.sax">HandlerBase</a> (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/DocumentHandler.html" title="class or interface in org.xml.sax" class="external-link">DocumentHandler</a>, org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/DTDHandler.html" title="class or interface in org.xml.sax" class="external-link">DTDHandler</a>, org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>, org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/ErrorHandler.html" title="class or interface in org.xml.sax" class="external-link">ErrorHandler</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/DescriptorHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/HasFreeSpace.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">HasFreeSpace</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/HashvalueAlgorithm.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">HashvalueAlgorithm</a> (implements org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/Header.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">Header</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/IdentityMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">IdentityMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/IgnoreDependenciesExecutor.html" class="type-name-link" title="class in org.apache.tools.ant.helper">IgnoreDependenciesExecutor</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/InnerClassFilenameFilter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">InnerClassFilenameFilter</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilenameFilter.html" title="class or interface in java.io" class="external-link">FilenameFilter</a>)</li>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/InputRequest.html" class="type-name-link" title="class in org.apache.tools.ant.input">InputRequest</a>
<ul>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/MultipleChoiceInputRequest.html" class="type-name-link" title="class in org.apache.tools.ant.input">MultipleChoiceInputRequest</a></li>
</ul>
</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" class="type-name-link external-link" title="class or interface in java.io">InputStream</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>)
<ul>
<li class="circle">org.apache.tools.bzip2.<a href="org/apache/tools/bzip2/CBZip2InputStream.html" class="type-name-link" title="class in org.apache.tools.bzip2">CBZip2InputStream</a> (implements org.apache.tools.bzip2.<a href="org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ConcatFileInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">ConcatFileInputStream</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ConcatResourceInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">ConcatResourceInputStream</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DemuxInputStream.html" class="type-name-link" title="class in org.apache.tools.ant">DemuxInputStream</a></li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" class="type-name-link external-link" title="class or interface in java.io">FilterInputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/KeepAliveInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">KeepAliveInputStream</a></li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarInputStream.html" class="type-name-link" title="class in org.apache.tools.tar">TarInputStream</a></li>
</ul>
</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/PipedInputStream.html" class="type-name-link external-link" title="class or interface in java.io">PipedInputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LeadPipeInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LeadPipeInputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ReaderInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">ReaderInputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/StringInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.filters">StringInputStream</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/InstanceOf.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">InstanceOf</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/IntrospectionHelper.html" class="type-name-link" title="class in org.apache.tools.ant">IntrospectionHelper</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/IntrospectionHelper.Creator.html" class="type-name-link" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/InVMExecution.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher">InVMExecution</a> (implements org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestExecutionContext</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbc</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsFailure.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsFailure</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/JakartaOroMatcher.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">JakartaOroMatcher</a> (implements org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/JakartaOroRegexp.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">JakartaOroRegexp</a> (implements org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/Regexp.html" title="interface in org.apache.tools.ant.util.regexp">Regexp</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/JakartaRegexpMatcher.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">JakartaRegexpMatcher</a> (implements org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/JakartaRegexpRegexp.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">JakartaRegexpRegexp</a> (implements org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/Regexp.html" title="interface in org.apache.tools.ant.util.regexp">Regexp</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/JarFileIterator.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend">JarFileIterator</a> (implements org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFileIterator.html" title="interface in org.apache.tools.ant.taskdefs.optional.depend">ClassFileIterator</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/JarMarker.html" class="type-name-link" title="class in org.apache.tools.zip">JarMarker</a> (implements org.apache.tools.zip.<a href="org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/Jasper41Mangler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp">Jasper41Mangler</a> (implements org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</a>)</li>
<li class="circle">org.apache.tools.ant.filters.util.<a href="org/apache/tools/ant/filters/util/JavaClassHelper.html" class="type-name-link" title="class in org.apache.tools.ant.filters.util">JavaClassHelper</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.DocletParam.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.DocletParam</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.GroupArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.GroupArgument</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.Html.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.LinkArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.LinkArgument</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.ResourceCollectionContainer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.ResourceCollectionContainer</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;T&gt;)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/JavaEnvUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">JavaEnvUtils</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Javah.ClassArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Javah.ClassArgument</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapterFactory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapterFactory</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/JavaVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">JavaVersion</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/JAXPUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">JAXPUtils</a></li>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/Jdk14RegexpMatcher.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">Jdk14RegexpMatcher</a> (implements org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/Jdk14RegexpRegexp.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">Jdk14RegexpRegexp</a> (implements org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/Regexp.html" title="interface in org.apache.tools.ant.util.regexp">Regexp</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Jikes.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Jikes</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/JikesOutputParser.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">JikesOutputParser</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jlink.<a href="org/apache/tools/ant/taskdefs/optional/jlink/jlink.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jlink">jlink</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Jmod.ResolutionWarningSpec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningSpec</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.compilers.<a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JspCompilerAdapterFactory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapterFactory</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/JspNameMangler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspNameMangler</a> (implements org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnit4TestMethodAdapter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnit4TestMethodAdapter</a> (implements junit.framework.Test)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.TestResultHolder.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirrorImpl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirrorImpl</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTestRunner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTestRunner</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a>, junit.framework.TestListener)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitVersionHelper.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitVersionHelper</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/Kaffeh.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javah">Kaffeh</a> (implements org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapter</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/LastModifiedAlgorithm.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">LastModifiedAlgorithm</a> (implements org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a>)</li>
<li class="circle">org.apache.tools.ant.launch.<a href="org/apache/tools/ant/launch/Launcher.html" class="type-name-link" title="class in org.apache.tools.ant.launch">Launcher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/LauncherSupport.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher">LauncherSupport</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/LineContains.Contains.html" class="type-name-link" title="class in org.apache.tools.ant.filters">LineContains.Contains</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.Compression.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.Compression</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.Launcher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.Launcher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.LocaleSpec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.LocaleSpec</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.ModuleSpec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ModuleSpec</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.PatternListEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.ReleaseInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.ReleaseInfoEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfoEntry</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.ReleaseInfoKey.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfoKey</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ListenerDefinition.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ListenerDefinition</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LoaderUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">LoaderUtils</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Local.Name.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Local.Name</a> (implements java.util.function.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/function/Consumer.html" title="class or interface in java.util.function" class="external-link">Consumer</a>&lt;T&gt;)</li>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/LocalPropertyStack.html" class="type-name-link" title="class in org.apache.tools.ant.property">LocalPropertyStack</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Location.html" class="type-name-link" title="class in org.apache.tools.ant">Location</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.resolvers.<a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/LocationResolver.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension.resolvers">LocationResolver</a> (implements org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionResolver.html" title="interface in org.apache.tools.ant.taskdefs.optional.extension">ExtensionResolver</a>)</li>
<li class="circle">org.apache.tools.ant.launch.<a href="org/apache/tools/ant/launch/Locator.html" class="type-name-link" title="class in org.apache.tools.ant.launch">Locator</a></li>
<li class="circle">org.apache.tools.ant.listener.<a href="org/apache/tools/ant/listener/Log4jListener.html" class="type-name-link" title="class in org.apache.tools.ant.listener">Log4jListener</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroDef.Text.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroInstance.Element.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroInstance.Element</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/MagicNames.html" class="type-name-link" title="class in org.apache.tools.ant">MagicNames</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/Mailer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">Mailer</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/JakartaMimeMailer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">JakartaMimeMailer</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/MimeMailer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">MimeMailer</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.mail.<a href="org/apache/tools/mail/MailMessage.html" class="type-name-link" title="class in org.apache.tools.mail">MailMessage</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Main.html" class="type-name-link" title="class in org.apache.tools.ant">Main</a> (implements org.apache.tools.ant.launch.<a href="org/apache/tools/ant/launch/AntMain.html" title="interface in org.apache.tools.ant.launch">AntMain</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Manifest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Manifest</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Manifest.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Manifest.Section.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/MergingMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">MergingMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ModuleVersion.html" class="type-name-link" title="class in org.apache.tools.ant.types">ModuleVersion</a></li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Name.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Name</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/Native2AsciiAdapterFactory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapterFactory</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Native2AsciiUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">Native2AsciiUtils</a></li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Not.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Not</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/NullReturn.html" class="type-name-link" title="class in org.apache.tools.ant.property">NullReturn</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Os.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Os</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" class="type-name-link external-link" title="class or interface in java.io">OutputStream</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a>)
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/ByteArrayOutputStream.html" class="type-name-link external-link" title="class or interface in java.io">ByteArrayOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/PropertyOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">PropertyOutputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.bzip2.<a href="org/apache/tools/bzip2/CBZip2OutputStream.html" class="type-name-link" title="class in org.apache.tools.bzip2">CBZip2OutputStream</a> (implements org.apache.tools.bzip2.<a href="org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DemuxOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant">DemuxOutputStream</a></li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" class="type-name-link external-link" title="class or interface in java.io">FilterOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/KeepAliveOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">KeepAliveOutputStream</a></li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarOutputStream.html" class="type-name-link" title="class in org.apache.tools.tar">TarOutputStream</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipOutputStream.html" class="type-name-link" title="class in org.apache.tools.zip">ZipOutputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LazyFileOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LazyFileOutputStream</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LineOrientedOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LineOrientedOutputStreamRedirector.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineOrientedOutputStreamRedirector</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/LogOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">LogOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/NullOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">NullOutputStream</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/TaskOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">TaskOutputStream</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/TeeOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">TeeOutputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/OutputStreamFunneler.html" class="type-name-link" title="class in org.apache.tools.ant.util">OutputStreamFunneler</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/OwnedBySelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Parallel.TaskList.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Parallel.TaskList</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Parameter.html" class="type-name-link" title="class in org.apache.tools.ant.types">Parameter</a></li>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/ParseProperties.html" class="type-name-link" title="class in org.apache.tools.ant.property">ParseProperties</a> (implements org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/ParseNextProperty.html" title="interface in org.apache.tools.ant.property">ParseNextProperty</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Path.PathElement.html" class="type-name-link" title="class in org.apache.tools.ant.types">Path.PathElement</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PathConvert.MapEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PathConvert.MapEntry</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/PathTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant">PathTokenizer</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/PatternSet.NameEntry.html" class="type-name-link" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/PatternSet.PatternFileNameEntry.html" class="type-name-link" title="class in org.apache.tools.ant.types">PatternSet.PatternFileNameEntry</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Permissions.Permission.html" class="type-name-link" title="class in org.apache.tools.ant.types">Permissions.Permission</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/PermissionUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">PermissionUtils</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/PlainJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">PlainJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/PosixGroupSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/PosixPermissionsSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ProcessUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">ProcessUtil</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Project.html" class="type-name-link" title="class in org.apache.tools.ant">Project</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.optional.<a href="org/apache/tools/ant/types/optional/AbstractScriptComponent.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional">AbstractScriptComponent</a>
<ul>
<li class="circle">org.apache.tools.ant.types.optional.<a href="org/apache/tools/ant/types/optional/ScriptCondition.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional">ScriptCondition</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.<a href="org/apache/tools/ant/types/optional/ScriptMapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional">ScriptMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/BaseIfAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">BaseIfAttribute</a> (implements org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/EnableAttribute.html" title="interface in org.apache.tools.ant.attribute">EnableAttribute</a>)
<ul>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/IfBlankAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">IfBlankAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/IfBlankAttribute.Unless.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">IfBlankAttribute.Unless</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/IfSetAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">IfSetAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/IfSetAttribute.Unless.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">IfSetAttribute.Unless</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/IfTrueAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">IfTrueAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/IfTrueAttribute.Unless.html" class="type-name-link" title="class in org.apache.tools.ant.attribute">IfTrueAttribute.Unless</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Commandline.Argument.html" class="type-name-link" title="class in org.apache.tools.ant.types">Commandline.Argument</a>
<ul>
<li class="circle">org.apache.tools.ant.util.facade.<a href="org/apache/tools/ant/util/facade/ImplementationSpecificArgument.html" class="type-name-link" title="class in org.apache.tools.ant.util.facade">ImplementationSpecificArgument</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javac.ImplementationSpecificArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javac.ImplementationSpecificArgument</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Concat.TextElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Concat.TextElement</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/ConditionBase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/And.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">And</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ConditionTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ConditionTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Not.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Not</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Or.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Or</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/WaitFor.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">WaitFor</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.testing.<a href="org/apache/tools/ant/taskdefs/optional/testing/BlockFor.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.testing">BlockFor</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Xor.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Xor</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/DataType.html" class="type-name-link" title="class in org.apache.tools.ant.types">DataType</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/AbstractFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">AbstractFileSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/DirSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">DirSet</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileSet</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ArchiveFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/TarFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">TarFileSet</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tar.TarFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tar.TarFileSet</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ZipFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">ZipFileSet</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/BCFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BCFileSet</a></li>
<li class="circle">org.apache.tools.ant.types.optional.depend.<a href="org/apache/tools/ant/types/optional/depend/ClassfileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.TagArgument</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/LibFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">LibFileSet</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/MultiRootFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">MultiRootFileSet</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Sync.SyncTarget.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Sync.SyncTarget</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AbstractResourceCollectionWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AbstractResourceCollectionWrapper</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/BaseResourceCollectionWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionWrapper</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/SizeLimitCollection.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">SizeLimitCollection</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AllButFirst.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AllButFirst</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AllButLast.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AllButLast</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/First.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">First</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Last.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Last</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Sort.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Sort</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Tokens.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Tokens</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/LazyResourceCollectionWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">LazyResourceCollectionWrapper</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/AbstractSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Files.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Files</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsFileSelected.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/AntFilterReader.html" class="type-name-link" title="class in org.apache.tools.ant.types">AntFilterReader</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Archives.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Archives</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Assertions.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/BaseResourceCollectionContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionContainer</a> (implements org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AppendableResourceCollection.html" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a>, java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Difference.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Difference</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Intersect.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Intersect</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Union.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Union</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/BaseSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/BaseExtendSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ContainsRegexpSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ContainsSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/DateSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DateSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/DepthSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FilenameSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>, org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SizeSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/TypeSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/BaseSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/AndSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/MajoritySelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/NoneSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/NotSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">NotSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/OrSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ExtendSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/MappingSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">MappingSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/DependSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DependSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/DifferentSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/PresentSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a></li>
<li class="circle">org.apache.tools.ant.types.optional.<a href="org/apache/tools/ant/types/optional/ScriptSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional">ScriptSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Compare.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Compare</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Description.html" class="type-name-link" title="class in org.apache.tools.ant.types">Description</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionAdapter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionAdapter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionSet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionSet</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FileList.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileList</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FilterChain.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterChain</a>
<ul>
<li class="circle">org.apache.tools.ant.types.mappers.<a href="org/apache/tools/ant/types/mappers/FilterMapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.mappers">FilterMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/FilterSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/ImageOperation.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">ImageOperation</a>
<ul>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/BasicShape.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">BasicShape</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Arc.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Arc</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Ellipse.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Ellipse</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Rectangle.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Rectangle</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Text.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Text</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/TransformOperation.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">TransformOperation</a>
<ul>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Draw.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Draw</a></li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Rotate.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Rotate</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/Scale.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.image">Scale</a> (implements org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/ImageOperation.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">ImageOperation</a>
<ul>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/BasicShape.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">BasicShape</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Arc.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Arc</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Ellipse.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Ellipse</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Rectangle.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Rectangle</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Text.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Text</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/TransformOperation.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">TransformOperation</a>
<ul>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Draw.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Draw</a></li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Rotate.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Rotate</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)</li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/Scale.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.imageio">Scale</a> (implements org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsSigned.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsSigned</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/MappedResourceCollection.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">MappedResourceCollection</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Mapper.html" class="type-name-link" title="class in org.apache.tools.ant.types">Mapper</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Path.html" class="type-name-link" title="class in org.apache.tools.ant.types">Path</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/PatternSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">PatternSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/PropertySet.html" class="type-name-link" title="class in org.apache.tools.ant.types">PropertySet</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/RedirectorElement.html" class="type-name-link" title="class in org.apache.tools.ant.types">RedirectorElement</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/RegularExpression.html" class="type-name-link" title="class in org.apache.tools.ant.types">RegularExpression</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Resource.html" class="type-name-link" title="class in org.apache.tools.ant.types">Resource</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AbstractClasspathResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/JavaConstantResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">JavaConstantResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/JavaResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">JavaResource</a> (implements org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/URLProvider.html" title="interface in org.apache.tools.ant.types.resources">URLProvider</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/ArchiveResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/TarResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">TarResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/ZipResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ZipResource</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/FileResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">FileResource</a> (implements org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Appendable.html" title="interface in org.apache.tools.ant.types.resources">Appendable</a>, org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/FileProvider.html" title="interface in org.apache.tools.ant.types.resources">FileProvider</a>, org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>, org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Touchable.html" title="interface in org.apache.tools.ant.types.resources">Touchable</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/LogOutputResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">LogOutputResource</a> (implements org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Appendable.html" title="interface in org.apache.tools.ant.types.resources">Appendable</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/PropertyResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">PropertyResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/ResourceDecorator.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/ContentTransformingResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ContentTransformingResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/CompressedResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">CompressedResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/BZip2Resource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BZip2Resource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/GZipResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">GZipResource</a></li>
<li class="circle">org.apache.tools.ant.types.optional.xz.<a href="org/apache/tools/ant/types/optional/xz/XzResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.xz">XzResource</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/MappedResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">MappedResource</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/StringResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">StringResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/URLResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">URLResource</a> (implements org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/URLProvider.html" title="interface in org.apache.tools.ant.types.resources">URLProvider</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/ResourceComparator.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">ResourceComparator</a> (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;T&gt;)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Content.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Content</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Date.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Date</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/DelegatedResourceComparator.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">DelegatedResourceComparator</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Exists.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Exists</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/FileSystem.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">FileSystem</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Name.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Name</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Reverse.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Reverse</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Size.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Size</a></li>
<li class="circle">org.apache.tools.ant.types.resources.comparators.<a href="org/apache/tools/ant/types/resources/comparators/Type.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.comparators">Type</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/ResourceList.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ResourceList</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Resources.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Resources</a> (implements org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AppendableResourceCollection.html" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">ResourceSelectorContainer</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/And.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">And</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Majority.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Majority</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/None.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">None</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Or.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Or</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Restrict.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Restrict</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SignedSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Substitution.html" class="type-name-link" title="class in org.apache.tools.ant.types">Substitution</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/XMLCatalog.html" class="type-name-link" title="class in org.apache.tools.ant.types">XMLCatalog</a> (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>, javax.xml.transform.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/transform/URIResolver.html" title="class or interface in javax.xml.transform" class="external-link">URIResolver</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/FailureRecorder.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.util">FileTokenizer</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>)
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.FileTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.FileTokenizer</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/HasMethod.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Http.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Http</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsFalse.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsLastModified.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsReachable.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsReference.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsSet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/IsTrue.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.DocletInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc.DocletInfo</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/LineTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineTokenizer</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Matches.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Matches</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/Message.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">Message</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/ParserSupports.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Permissions.html" class="type-name-link" title="class in org.apache.tools.ant.types">Permissions</a></li>
<li class="circle">org.apache.tools.ant.types.spi.<a href="org/apache/tools/ant/types/spi/Provider.html" class="type-name-link" title="class in org.apache.tools.ant.types.spi">Provider</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/ResourceExists.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ResourceExists</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.spi.<a href="org/apache/tools/ant/types/spi/Service.html" class="type-name-link" title="class in org.apache.tools.ant.types.spi">Service</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Socket.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/StringTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.util">StringTokenizer</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>)
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.StringTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.StringTokenizer</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Task.html" class="type-name-link" title="class in org.apache.tools.ant">Task</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AbstractCvsTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/ChangeLogTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Cvs.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Cvs</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/CvsTagDiff.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagDiff</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.cvslib.<a href="org/apache/tools/ant/taskdefs/cvslib/CvsVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsVersion</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SignJar.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SignJar</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/VerifyJar.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">VerifyJar</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Ant.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Ant</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Antlib.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Antlib</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AntlibDefinition.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AttributeNamespaceDef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AttributeNamespaceDef</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/DefBase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">DefBase</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Definer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Definer</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Componentdef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Componentdef</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Typedef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Typedef</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Taskdef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Taskdef</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Input.Handler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Input.Handler</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.script.<a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDef</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroDef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroDef</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PreSetDef.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PreSetDef</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/ANTLR.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">ANTLR</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AntStructure.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AntStructure</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/AntVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">AntVersion</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AugmentReference.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">AugmentReference</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant">TypeAdapter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Available.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Available</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Basename.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Basename</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/BindTargets.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">BindTargets</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/BorlandGenerateClient.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandGenerateClient</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/BuildNumber.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">BuildNumber</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/CallTarget.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">CallTarget</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Classloader.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Classloader</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCCheckin</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCCheckout</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCLock</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkattr</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkbl</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkdir</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkelem</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMklabel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMklbtype</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCRmtype</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnCheckout.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUnCheckout</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnlock.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUnlock</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.clearcase.<a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUpdate</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/CloseResources.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">CloseResources</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/CommandLauncherTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">CommandLauncherTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Concat.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Concat</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">Continuus</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheck.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheck</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckin.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckin</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckinDefault.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckinDefault</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckout.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckout</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCreateTask</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ccm.<a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMReconfigure.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMReconfigure</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Copy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Copy</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Move.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Move</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Sync.MyCopy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Sync.MyCopy</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Copyfile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Copyfile</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/CopyPath.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">CopyPath</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/CVSPass.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">CVSPass</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/DefaultExcludes.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">DefaultExcludes</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Deltree.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Deltree</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/DiagnosticsTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">DiagnosticsTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Dirname.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Dirname</a></li>
<li class="circle">org.apache.tools.ant.dispatch.<a href="org/apache/tools/ant/dispatch/DispatchTask.html" class="type-name-link" title="class in org.apache.tools.ant.dispatch">DispatchTask</a> (implements org.apache.tools.ant.dispatch.<a href="org/apache/tools/ant/dispatch/Dispatchable.html" title="interface in org.apache.tools.ant.dispatch">Dispatchable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.unix.<a href="org/apache/tools/ant/taskdefs/optional/unix/Symlink.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.unix">Symlink</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Echo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Echo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/EchoProperties.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="org/apache/tools/ant/taskdefs/email/EmailTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">EmailTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/MimeMail.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">MimeMail</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SendEmail.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SendEmail</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Exec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Exec</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ExecTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteOn.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.unix.<a href="org/apache/tools/ant/taskdefs/optional/unix/AbstractAccessTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.unix">AbstractAccessTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.unix.<a href="org/apache/tools/ant/taskdefs/optional/unix/Chgrp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.unix">Chgrp</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.unix.<a href="org/apache/tools/ant/taskdefs/optional/unix/Chown.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.unix">Chown</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.windows.<a href="org/apache/tools/ant/taskdefs/optional/windows/Attrib.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.windows">Attrib</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Chmod.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Chmod</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Transform.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Transform</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Exit.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Exit</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Expand.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Expand</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Untar.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Untar</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Filter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Filter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.testing.<a href="org/apache/tools/ant/taskdefs/optional/testing/Funtest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.testing">Funtest</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/GenerateKey.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">GenerateKey</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Get</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/HostInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">HostInfo</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ImportTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ImportTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Input.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Input</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbcTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbcTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibAvailableTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibAvailableTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibDisplayTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibDisplayTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibManifestTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibManifestTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibResolveTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibResolveTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Java.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Java</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javacc.<a href="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JavaCC</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javadoc.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javadoc</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Javah.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Javah</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/JDBCTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SQLExec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jdepend.<a href="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jdepend">JDependTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javacc.<a href="org/apache/tools/ant/taskdefs/optional/javacc/JJDoc.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJDoc</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javacc.<a href="org/apache/tools/ant/taskdefs/optional/javacc/JJTree.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJTree</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Jmod.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Jmod</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">JUnitLauncherTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/KeySubst.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">KeySubst</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Length.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Length</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.modules.<a href="org/apache/tools/ant/taskdefs/modules/Link.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/LoadProperties.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">LoadProperties</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/LoadResource.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">LoadResource</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/LoadFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">LoadFile</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Local.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Local</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MacroInstance.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MacroInstance</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</a>, org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MakeUrl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MakeUrl</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ManifestClassPath.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ManifestClassPath</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ManifestTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ManifestTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/MatchingTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Cab.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Cab</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Checksum.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Checksum</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Copydir.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Copydir</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Delete.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Delete</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/Depend.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.depend">Depend</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/DependSet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">DependSet</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/FixCRLF.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.image.<a href="org/apache/tools/ant/taskdefs/optional/image/Image.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.image">Image</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.image.<a href="org/apache/tools/ant/taskdefs/optional/image/ImageIOTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.image">ImageIOTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Javac.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javac</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jlink.<a href="org/apache/tools/ant/taskdefs/optional/jlink/JlinkTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jlink">JlinkTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/JspC.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Native2Ascii.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/NetRexxC.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/RenameExtensions.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">RenameExtensions</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Replace.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Replace</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Rmic.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Rmic</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tar.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tar</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.i18n.<a href="org/apache/tools/ant/taskdefs/optional/i18n/Translate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.i18n">Translate</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/WLJspc.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.jsp">WLJspc</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Zip.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Jar.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Jar</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Ear.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Ear</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/War.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">War</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Mkdir.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Mkdir</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSS.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</a> (implements org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSADD.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSADD</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCHECKIN.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCHECKIN</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCHECKOUT.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCHECKOUT</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCP.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCP</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCREATE.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCREATE</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSGET.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSGET</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSHISTORY.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSHISTORY</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSLABEL.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSLABEL</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Nice.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Nice</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Pack.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Pack</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/BZip2.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">BZip2</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/GZip.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">GZip</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.xz.<a href="org/apache/tools/ant/taskdefs/optional/xz/Xz.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.xz">Xz</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Parallel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Parallel</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Patch.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Patch</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PathConvert.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PathConvert</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ProjectHelperTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ProjectHelperTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Property.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Property</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PropertyHelperTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PropertyHelperTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.pvcs.<a href="org/apache/tools/ant/taskdefs/optional/pvcs/Pvcs.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.pvcs">Pvcs</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Recorder.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Recorder</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Rename.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Rename</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/ReplaceRegExp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">ReplaceRegExp</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ResourceCount.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ResourceCount</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Retry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Retry</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/RExecTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Rpm.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Rpm</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Script.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Script</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.script.<a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDefBase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDefBase</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant">DynamicConfigurator</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Sequential.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Sequential</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/ServerDeploy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">ServerDeploy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SetPermissions.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SetPermissions</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/SetProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">SetProxy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Sleep.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Sleep</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOS.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a> (implements org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCheckin.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSCheckin</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCheckout.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSCheckout</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOSGet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSGet</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSLabel</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sound.<a href="org/apache/tools/ant/taskdefs/optional/sound/SoundTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sound">SoundTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.splash.<a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.splash">SplashTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHBase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHBase</a> (implements org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/LogListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/Scp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">Scp</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHExec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHExec</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHSession.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SubAnt.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SubAnt</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Sync.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Sync</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/TaskAdapter.html" class="type-name-link" title="class in org.apache.tools.ant">TaskAdapter</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant">TypeAdapter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/TempFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">TempFile</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Touch.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Touch</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Truncate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Truncate</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tstamp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tstamp</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/UnknownElement.html" class="type-name-link" title="class in org.apache.tools.ant">UnknownElement</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Unpack.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Unpack</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/BUnzip2.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">BUnzip2</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/GUnzip.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">GUnzip</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.xz.<a href="org/apache/tools/ant/taskdefs/optional/xz/Unxz.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.xz">Unxz</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/UpToDate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">UpToDate</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/WhichResource.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">WhichResource</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XmlProperty.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XmlProperty</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/XMLResultAggregator.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.ChainableReaderFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>, org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a>)
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/Native2AsciiFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">Native2AsciiFilter</a></li>
<li class="circle">org.apache.tools.ant.types.optional.<a href="org/apache/tools/ant/types/optional/ScriptFilter.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional">ScriptFilter</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.ContainsRegex.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.IgnoreBlank.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.ReplaceRegex.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.ReplaceString.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.Trim.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/UniqFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">UniqFilter</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.ContainsString.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.DeleteCharacters.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>, org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/TypeFound.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/XMLFragment.html" class="type-name-link" title="class in org.apache.tools.ant.util">XMLFragment</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/EchoXML.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">EchoXML</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.Factory.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Attribute</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant">DynamicConfigurator</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ProjectHelper.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectHelper</a>
<ul>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelperImpl.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelperImpl</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ProjectHelper.OnMissingExtensionPoint.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectHelper.OnMissingExtensionPoint</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a>
<ul>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.ElementHandler.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2.ElementHandler</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.MainHandler.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2.MainHandler</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.ProjectHandler.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2.ProjectHandler</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html" class="type-name-link" title="class in org.apache.tools.ant.helper">ProjectHelper2.TargetHandler</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ProjectHelperRepository.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectHelperRepository</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/PropertiesfileCache.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">PropertiesfileCache</a> (implements org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Cache.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Cache</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry</a></li>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/PropertyFileInputHandler.html" class="type-name-link" title="class in org.apache.tools.ant.input">PropertyFileInputHandler</a> (implements org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.html" class="type-name-link" title="class in org.apache.tools.ant">PropertyHelper</a> (implements org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PropertyHelperTask.DelegateElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PropertyHelperTask.DelegateElement</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/PropertySet.PropertyRef.html" class="type-name-link" title="class in org.apache.tools.ant.types">PropertySet.PropertyRef</a></li>
<li class="circle">org.apache.tools.ant.util.java15.<a href="org/apache/tools/ant/util/java15/ProxyDiagnostics.html" class="type-name-link" title="class in org.apache.tools.ant.util.java15">ProxyDiagnostics</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ProxySetup.html" class="type-name-link" title="class in org.apache.tools.ant.util">ProxySetup</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PumpStreamHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler</a> (implements org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogStreamHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/LogStreamHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">LogStreamHandler</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.pvcs.<a href="org/apache/tools/ant/taskdefs/optional/pvcs/PvcsProject.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.pvcs">PvcsProject</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ReadableSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" class="type-name-link external-link" title="class or interface in java.io">Reader</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Readable.html" title="class or interface in java.lang" class="external-link">Readable</a>)
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html" class="type-name-link external-link" title="class or interface in java.io">FilterReader</a>
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/BaseFilterReader.html" class="type-name-link" title="class in org.apache.tools.ant.filters">BaseFilterReader</a>
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/BaseParamFilterReader.html" class="type-name-link" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a>)
<ul>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ConcatFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">ConcatFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/EscapeUnicode.html" class="type-name-link" title="class in org.apache.tools.ant.filters">EscapeUnicode</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/FixCrLfFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">FixCrLfFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/HeadFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">HeadFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/LineContains.html" class="type-name-link" title="class in org.apache.tools.ant.filters">LineContains</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/LineContainsRegExp.html" class="type-name-link" title="class in org.apache.tools.ant.filters">LineContainsRegExp</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/PrefixLines.html" class="type-name-link" title="class in org.apache.tools.ant.filters">PrefixLines</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ReplaceTokens.html" class="type-name-link" title="class in org.apache.tools.ant.filters">ReplaceTokens</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/SortFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">SortFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/StripLineBreaks.html" class="type-name-link" title="class in org.apache.tools.ant.filters">StripLineBreaks</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/StripLineComments.html" class="type-name-link" title="class in org.apache.tools.ant.filters">StripLineComments</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/SuffixLines.html" class="type-name-link" title="class in org.apache.tools.ant.filters">SuffixLines</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TabsToSpaces.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TabsToSpaces</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TailFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TailFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ClassConstants.html" class="type-name-link" title="class in org.apache.tools.ant.filters">ClassConstants</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ExpandProperties.html" class="type-name-link" title="class in org.apache.tools.ant.filters">ExpandProperties</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/StripJavaComments.html" class="type-name-link" title="class in org.apache.tools.ant.filters">StripJavaComments</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter</a> (implements org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.filters.util.<a href="org/apache/tools/ant/filters/util/ChainReaderHelper.ChainReader.html" class="type-name-link" title="class in org.apache.tools.ant.filters.util">ChainReaderHelper.ChainReader</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/RecorderEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a>, org.apache.tools.ant.<a href="org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Redirector.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Redirector</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Reference.html" class="type-name-link" title="class in org.apache.tools.ant.types">Reference</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Ant.Reference.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Ant.Reference</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ReflectUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">ReflectUtil</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ReflectWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ReflectWrapper</a></li>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpMatcherFactory.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">RegexpMatcherFactory</a>
<ul>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpFactory.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">RegexpFactory</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/RegexpPatternMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">RegexpPatternMapper</a> (implements org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util.regexp">RegexpUtil</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Replace.NestedString.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Replace.NestedString</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Replace.Replacefilter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Replace.Replacefilter</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ReplaceTokens.Token.html" class="type-name-link" title="class in org.apache.tools.ant.filters">ReplaceTokens.Token</a></li>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/ResolvePropertyMap.html" class="type-name-link" title="class in org.apache.tools.ant.property">ResolvePropertyMap</a> (implements org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/ResourceContains.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ResourceContains</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceLocation.html" class="type-name-link" title="class in org.apache.tools.ant.types">ResourceLocation</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/DTDLocation.html" class="type-name-link" title="class in org.apache.tools.ant.types">DTDLocation</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.DTDLocation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.DTDLocation</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/ResourcesMatch.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ResourcesMatch</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ResourceUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">ResourceUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/RetryHandler.html" class="type-name-link" title="class in org.apache.tools.ant.util">RetryHandler</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/RExecTask.RExecSubTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.RExecSubTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/RExecTask.RExecRead.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.RExecRead</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/RExecTask.RExecWrite.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.RExecWrite</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/RmicAdapterFactory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.rmic">RmicAdapterFactory</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/RuntimeConfigurable.html" class="type-name-link" title="class in org.apache.tools.ant">RuntimeConfigurable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.script.<a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDef.Attribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.script.<a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.NestedElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDef.NestedElement</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ScriptFixBSFPath.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptFixBSFPath</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ScriptRunnerBase.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunnerBase</a>
<ul>
<li class="circle">org.apache.tools.ant.util.optional.<a href="org/apache/tools/ant/util/optional/JavaxScriptRunner.html" class="type-name-link" title="class in org.apache.tools.ant.util.optional">JavaxScriptRunner</a></li>
<li class="circle">org.apache.tools.ant.util.optional.<a href="org/apache/tools/ant/util/optional/ScriptRunner.html" class="type-name-link" title="class in org.apache.tools.ant.util.optional">ScriptRunner</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ScriptRunner.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunner</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ScriptRunnerCreator.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunnerCreator</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ScriptRunnerHelper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunnerHelper</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/SecurityManager.html" class="type-name-link external-link" title="class or interface in java.lang">SecurityManager</a>
<ul>
<li class="circle">org.apache.tools.ant.util.optional.<a href="org/apache/tools/ant/util/optional/NoExitSecurityManager.html" class="type-name-link" title="class in org.apache.tools.ant.util.optional">NoExitSecurityManager</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/SecurityManagerUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">SecurityManagerUtil</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorUtils.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SelectorUtils</a></li>
<li class="circle">org.apache.tools.ant.helper.<a href="org/apache/tools/ant/helper/SingleCheckExecutor.html" class="type-name-link" title="class in org.apache.tools.ant.helper">SingleCheckExecutor</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Size.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Size</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.mail.<a href="org/apache/tools/mail/SmtpResponseReader.html" class="type-name-link" title="class in org.apache.tools.mail">SmtpResponseReader</a></li>
<li class="circle">org.apache.commons.net.SocketClient
<ul>
<li class="circle">org.apache.commons.net.bsd.RExecClient
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/RExecTask.AntRExecClient.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.AntRExecClient</a></li>
</ul>
</li>
<li class="circle">org.apache.commons.net.telnet.TelnetClient
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.AntTelnetClient.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.AntTelnetClient</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sound.<a href="org/apache/tools/ant/taskdefs/optional/sound/SoundTask.BuildAlert.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.sound">SoundTask.BuildAlert</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/SourceFileScanner.html" class="type-name-link" title="class in org.apache.tools.ant.util">SourceFileScanner</a> (implements org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/Specification.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension">Specification</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SQLExec.Transaction.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHSession.LocalTunnel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.LocalTunnel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHSession.NestedSequential.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.NestedSequential</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHSession.RemoteTunnel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.RemoteTunnel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHUserInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHUserInfo</a> (implements com.jcraft.jsch.UIKeyboardInteractive, com.jcraft.jsch.UserInfo)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/StandaloneLauncher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher">StandaloneLauncher</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/StreamPumper.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/StreamUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">StreamUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/StringUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">StringUtils</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/StripLineComments.Comment.html" class="type-name-link" title="class in org.apache.tools.ant.filters">StripLineComments.Comment</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/SummaryJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">SummaryJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/OutErrSummaryJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">OutErrSummaryJUnitResultFormatter</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/SunJavah.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.javah">SunJavah</a> (implements org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapter</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/SymbolicLinkUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">SymbolicLinkUtils</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SymlinkSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarArchiveSparseEntry.html" class="type-name-link" title="class in org.apache.tools.tar">TarArchiveSparseEntry</a> (implements org.apache.tools.tar.<a href="org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a>)</li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarBuffer.html" class="type-name-link" title="class in org.apache.tools.tar">TarBuffer</a></li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarEntry.html" class="type-name-link" title="class in org.apache.tools.tar">TarEntry</a> (implements org.apache.tools.tar.<a href="org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Target.html" class="type-name-link" title="class in org.apache.tools.ant">Target</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ExtensionPoint.html" class="type-name-link" title="class in org.apache.tools.ant">ExtensionPoint</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarUtils.html" class="type-name-link" title="class in org.apache.tools.tar">TarUtils</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/TaskConfigurationChecker.html" class="type-name-link" title="class in org.apache.tools.ant">TaskConfigurationChecker</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/TaskLogger.html" class="type-name-link" title="class in org.apache.tools.ant.util">TaskLogger</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/TearDownOnVmCrash.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">TearDownOnVmCrash</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.TelnetSubTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.TelnetSubTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.TelnetRead.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.TelnetRead</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.TelnetWrite.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.TelnetWrite</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/TestDefinition.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/SingleTestClass.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">SingleTestClass</a> (implements org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/NamedTest.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">NamedTest</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/TestClasses.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestClasses</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/TestDefinition.ForkedRepresentation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition.ForkedRepresentation</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/TestIgnored.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestIgnored</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/TestListenerWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestListenerWrapper</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, junit.framework.TestListener)</li>
<li class="circle">junit.framework.TestResult
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestResult.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestResult</a></li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Thread.html" class="type-name-link external-link" title="class or interface in java.lang">Thread</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/PumpStreamHandler.ThreadWithPumper.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler.ThreadWithPumper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/WorkerAnt.html" class="type-name-link" title="class in org.apache.tools.ant.util">WorkerAnt</a></li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ThreadLocal.html" class="type-name-link external-link" title="class or interface in java.lang">ThreadLocal</a>&lt;T&gt;
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/InheritableThreadLocal.html" class="type-name-link external-link" title="class or interface in java.lang">InheritableThreadLocal</a>&lt;T&gt;
<ul>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/LocalProperties.html" class="type-name-link" title="class in org.apache.tools.ant.property">LocalProperties</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.PropertyEnumerator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a>, org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a>, org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" class="type-name-link external-link" title="class or interface in java.io">IOException</a>
<ul>
<li class="circle">org.apache.tools.mail.<a href="org/apache/tools/mail/ErrorInQuitException.html" class="type-name-link" title="class in org.apache.tools.mail">ErrorInQuitException</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/ImmutableResourceException.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ImmutableResourceException</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ResourceUtils.ReadOnlyTargetFileException.html" class="type-name-link" title="class in org.apache.tools.ant.util">ResourceUtils.ReadOnlyTargetFileException</a></li>
<li class="circle">java.util.zip.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" class="type-name-link external-link" title="class or interface in java.util.zip">ZipException</a>
<ul>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnsupportedZipFeatureException.html" class="type-name-link" title="class in org.apache.tools.zip">UnsupportedZipFeatureException</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/Zip64RequiredException.html" class="type-name-link" title="class in org.apache.tools.zip">Zip64RequiredException</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.EjbcException.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbc.EjbcException</a></li>
<li class="circle">org.apache.tools.ant.launch.<a href="org/apache/tools/ant/launch/LaunchException.html" class="type-name-link" title="class in org.apache.tools.ant.launch">LaunchException</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ManifestException.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="class or interface in java.lang">RuntimeException</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/BuildException.html" class="type-name-link" title="class in org.apache.tools.ant">BuildException</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.testing.<a href="org/apache/tools/ant/taskdefs/optional/testing/BuildTimeoutException.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.testing">BuildTimeoutException</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ExitStatusException.html" class="type-name-link" title="class in org.apache.tools.ant">ExitStatusException</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/UnsupportedAttributeException.html" class="type-name-link" title="class in org.apache.tools.ant">UnsupportedAttributeException</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/UnsupportedElementException.html" class="type-name-link" title="class in org.apache.tools.ant">UnsupportedElementException</a></li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/SecurityException.html" class="type-name-link external-link" title="class or interface in java.lang">SecurityException</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ExitException.html" class="type-name-link" title="class in org.apache.tools.ant">ExitException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/TokenizedPath.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TokenizedPath</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/TokenizedPattern.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TokenizedPattern</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/TraXLiaison.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">TraXLiaison</a> (implements javax.xml.transform.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a>, org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a>, org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Tstamp.CustomFormat.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Tstamp.CustomFormat</a></li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/Type.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">Type</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/UnicodeUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">UnicodeUtil</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnparseableExtraFieldData.html" class="type-name-link" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a> (implements org.apache.tools.zip.<a href="org/apache/tools/zip/CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnrecognizedExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">UnrecognizedExtraField</a> (implements org.apache.tools.zip.<a href="org/apache/tools/zip/CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnsupportedZipFeatureException.Feature.html" class="type-name-link" title="class in org.apache.tools.zip">UnsupportedZipFeatureException.Feature</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.resolvers.<a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/URLResolver.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.extension.resolvers">URLResolver</a> (implements org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionResolver.html" title="interface in org.apache.tools.ant.taskdefs.optional.extension">ExtensionResolver</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/UUEncoder.html" class="type-name-link" title="class in org.apache.tools.ant.util">UUEncoder</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Watchdog.html" class="type-name-link" title="class in org.apache.tools.ant.util">Watchdog</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/WeakishReference.html" class="type-name-link" title="class in org.apache.tools.ant.util">WeakishReference</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/WeakishReference.HardReference.html" class="type-name-link" title="class in org.apache.tools.ant.util">WeakishReference.HardReference</a>
<ul>
<li class="circle">org.apache.tools.ant.util.optional.<a href="org/apache/tools/ant/util/optional/WeakishReference12.html" class="type-name-link" title="class in org.apache.tools.ant.util.optional">WeakishReference12</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/WritableSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/Xalan2TraceSupport.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Xalan2TraceSupport</a> (implements org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/XSLTTraceSupport.html" title="interface in org.apache.tools.ant.taskdefs.optional">XSLTTraceSupport</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/XmlConstants.html" class="type-name-link" title="class in org.apache.tools.ant.util">XmlConstants</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/XMLFragment.Child.html" class="type-name-link" title="class in org.apache.tools.ant.util">XMLFragment.Child</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/XMLJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/XmlLogger.html" class="type-name-link" title="class in org.apache.tools.ant">XmlLogger</a> (implements org.apache.tools.ant.<a href="org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Property.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.ValidatorErrorHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</a> (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/ErrorHandler.html" title="class or interface in org.xml.sax" class="external-link">ErrorHandler</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.Factory.Feature.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Feature</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.TraceConfiguration.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.TraceConfiguration</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/Zip64ExtendedInformationExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">Zip64ExtendedInformationExtraField</a> (implements org.apache.tools.zip.<a href="org/apache/tools/zip/CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipEightByteInteger.html" class="type-name-link" title="class in org.apache.tools.zip">ZipEightByteInteger</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipEncodingHelper.html" class="type-name-link" title="class in org.apache.tools.zip">ZipEncodingHelper</a></li>
<li class="circle">java.util.zip.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" class="type-name-link external-link" title="class or interface in java.util.zip">ZipEntry</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipEntry.html" class="type-name-link" title="class in org.apache.tools.zip">ZipEntry</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipFile.html" class="type-name-link" title="class in org.apache.tools.zip">ZipFile</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipLong.html" class="type-name-link" title="class in org.apache.tools.zip">ZipLong</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipOutputStream.UnicodeExtraFieldPolicy.html" class="type-name-link" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipShort.html" class="type-name-link" title="class in org.apache.tools.zip">ZipShort</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipUtil.html" class="type-name-link" title="class in org.apache.tools.zip">ZipUtil</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Algorithm.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a></li>
<li class="circle">org.apache.tools.ant.launch.<a href="org/apache/tools/ant/launch/AntMain.html" class="type-name-link" title="interface in org.apache.tools.ant.launch">AntMain</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/AntStructure.StructurePrinter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">AntStructure.StructurePrinter</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Appendable.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">Appendable</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/ArgumentProcessor.html" class="type-name-link" title="interface in org.apache.tools.ant">ArgumentProcessor</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" class="type-name-link external-link" title="class or interface in java.lang">AutoCloseable</a>
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" class="type-name-link external-link" title="class or interface in java.io">Closeable</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestResultFormatter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestResultFormatter</a> (also extends org.junit.platform.launcher.TestExecutionListener)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.bzip2.<a href="org/apache/tools/bzip2/BZip2Constants.html" class="type-name-link" title="interface in org.apache.tools.bzip2">BZip2Constants</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.modifiedselector.<a href="org/apache/tools/ant/types/selectors/modifiedselector/Cache.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Cache</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/ChainableReader.html" class="type-name-link" title="interface in org.apache.tools.ant.filters">ChainableReader</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.compilers.<a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapterExtension.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="org/apache/tools/ant/taskdefs/condition/Condition.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></li>
<li class="circle">org.apache.tools.ant.util.depend.<a href="org/apache/tools/ant/util/depend/DependencyAnalyzer.html" class="type-name-link" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</a></li>
<li class="circle">org.apache.tools.ant.dispatch.<a href="org/apache/tools/ant/dispatch/Dispatchable.html" class="type-name-link" title="interface in org.apache.tools.ant.dispatch">Dispatchable</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.NodeFilter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeFilter</a></li>
<li class="circle">org.apache.tools.ant.types.optional.image.<a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" class="type-name-link" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a></li>
<li class="circle">org.apache.tools.ant.types.optional.imageio.<a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" class="type-name-link" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicAttribute.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfigurator.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicConfigurator</a> (also extends org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicAttributeNS.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicAttributeNS</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfiguratorNS.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</a> (also extends org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicElement.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicElement</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfigurator.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicConfigurator</a> (also extends org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicElementNS.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicElementNS</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicConfiguratorNS.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</a> (also extends org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicAttributeNS.html" title="interface in org.apache.tools.ant">DynamicAttributeNS</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/DynamicObjectAttribute.html" class="type-name-link" title="interface in org.apache.tools.ant">DynamicObjectAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ejb.<a href="org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></li>
<li class="circle">org.apache.tools.ant.attribute.<a href="org/apache/tools/ant/attribute/EnableAttribute.html" class="type-name-link" title="interface in org.apache.tools.ant.attribute">EnableAttribute</a></li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventListener.html" class="type-name-link external-link" title="class or interface in java.util">EventListener</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/BuildListener.html" class="type-name-link" title="interface in org.apache.tools.ant">BuildListener</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/BuildLogger.html" class="type-name-link" title="interface in org.apache.tools.ant">BuildLogger</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/SubBuildListener.html" class="type-name-link" title="interface in org.apache.tools.ant">SubBuildListener</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Executor.html" class="type-name-link" title="interface in org.apache.tools.ant">Executor</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.extension.<a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionResolver.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.extension">ExtensionResolver</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/FileNameMapper.html" class="type-name-link" title="interface in org.apache.tools.ant.util">FileNameMapper</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/FileProvider.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">FileProvider</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/FileScanner.html" class="type-name-link" title="interface in org.apache.tools.ant">FileScanner</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</a></li>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/GetProperty.html" class="type-name-link" title="interface in org.apache.tools.ant.property">GetProperty</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.j2ee.<a href="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></li>
<li class="circle">org.apache.tools.ant.input.<a href="org/apache/tools/ant/input/InputHandler.html" class="type-name-link" title="interface in org.apache.tools.ant.input">InputHandler</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" class="type-name-link external-link" title="class or interface in java.lang">Iterable</a>&lt;T&gt;
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.<a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFileIterator.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.depend">ClassFileIterator</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceCollection.html" class="type-name-link" title="interface in org.apache.tools.ant.types">ResourceCollection</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/AppendableResourceCollection.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.javah.<a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.compilers.<a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JspCompilerAdapter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.jsp.<a href="org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitResultFormatterMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a> (also extends junit.framework.TestListener)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/LaunchDefinition.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">LaunchDefinition</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="org/apache/tools/ant/taskdefs/optional/ssh/LogListener.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.vss.<a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/NamedTest.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">NamedTest</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.native2ascii.<a href="org/apache/tools/ant/taskdefs/optional/native2ascii/Native2AsciiAdapter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Parameterizable.html" class="type-name-link" title="interface in org.apache.tools.ant.types">Parameterizable</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ExtendFileSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a> (also extends org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/ParseNextProperty.html" class="type-name-link" title="interface in org.apache.tools.ant.property">ParseNextProperty</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.Delegate.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>
<ul>
<li class="circle">org.apache.tools.ant.property.<a href="org/apache/tools/ant/property/PropertyExpander.html" class="type-name-link" title="interface in org.apache.tools.ant.property">PropertyExpander</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.PropertyEnumerator.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.PropertyEvaluator.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/PropertyHelper.PropertySetter.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/RegexpMatcher.html" class="type-name-link" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</a>
<ul>
<li class="circle">org.apache.tools.ant.util.regexp.<a href="org/apache/tools/ant/util/regexp/Regexp.html" class="type-name-link" title="interface in org.apache.tools.ant.util.regexp">Regexp</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/ResourceFactory.html" class="type-name-link" title="interface in org.apache.tools.ant.types">ResourceFactory</a></li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/FileSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/ExtendFileSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a> (also extends org.apache.tools.ant.types.<a href="org/apache/tools/ant/types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ResourceUtils.ResourceSelectorProvider.html" class="type-name-link" title="interface in org.apache.tools.ant.util">ResourceUtils.ResourceSelectorProvider</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Retryable.html" class="type-name-link" title="interface in org.apache.tools.ant.util">Retryable</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.rmic.<a href="org/apache/tools/ant/taskdefs/rmic/RmicAdapter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorContainer.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="org/apache/tools/ant/types/selectors/SelectorScanner.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.sos.<a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</a></li>
<li class="circle">java.util.function.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/function/Supplier.html" class="type-name-link external-link" title="class or interface in java.util.function">Supplier</a>&lt;T&gt;
<ul>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/Evaluable.html" class="type-name-link" title="interface in org.apache.tools.ant">Evaluable</a>&lt;T&gt;</li>
</ul>
</li>
<li class="circle">org.apache.tools.tar.<a href="org/apache/tools/tar/TarConstants.html" class="type-name-link" title="interface in org.apache.tools.tar">TarConstants</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/TaskContainer.html" class="type-name-link" title="interface in org.apache.tools.ant">TaskContainer</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestExecutionContext.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestExecutionContext</a></li>
<li class="circle">org.junit.platform.launcher.TestExecutionListener
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junitlauncher.<a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestResultFormatter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestResultFormatter</a> (also extends java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>)</li>
</ul>
</li>
<li class="circle">junit.framework.TestListener
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestListener.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a> (also extends org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/TimeoutObserver.html" class="type-name-link" title="interface in org.apache.tools.ant.util">TimeoutObserver</a></li>
<li class="circle">org.apache.tools.ant.filters.<a href="org/apache/tools/ant/filters/TokenFilter.Filter.html" class="type-name-link" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/Tokenizer.html" class="type-name-link" title="interface in org.apache.tools.ant.util">Tokenizer</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/Touch.DateFormatFactory.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">Touch.DateFormatFactory</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/Touchable.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">Touchable</a></li>
<li class="circle">org.apache.tools.ant.<a href="org/apache/tools/ant/TypeAdapter.html" class="type-name-link" title="interface in org.apache.tools.ant">TypeAdapter</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/UnixStat.html" class="type-name-link" title="interface in org.apache.tools.zip">UnixStat</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/URLProvider.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">URLProvider</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLiaison.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLiaison2.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLiaison3.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLiaison4.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLogger.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="org/apache/tools/ant/taskdefs/optional/XSLTTraceSupport.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional">XSLTTraceSupport</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipEncoding.html" class="type-name-link" title="interface in org.apache.tools.zip">ZipEncoding</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/ZipExtraField.html" class="type-name-link" title="interface in org.apache.tools.zip">ZipExtraField</a>
<ul>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/CentralDirectoryParsingZipExtraField.html" class="type-name-link" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodHandleCPInfo.ReferenceKind.html" class="type-name-link" title="enum class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodHandleCPInfo.ReferenceKind</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="org/apache/tools/ant/types/resources/MultiRootFileSet.SetType.html" class="type-name-link" title="enum class in org.apache.tools.ant.types.resources">MultiRootFileSet.SetType</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/PermissionUtils.FileType.html" class="type-name-link" title="enum class in org.apache.tools.ant.util">PermissionUtils.FileType</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="org/apache/tools/ant/util/ScriptManager.html" class="type-name-link" title="enum class in org.apache.tools.ant.util">ScriptManager</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/SetPermissions.NonPosixMode.html" class="type-name-link" title="enum class in org.apache.tools.ant.taskdefs">SetPermissions.NonPosixMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="org/apache/tools/ant/taskdefs/XSLTProcess.ParamType.html" class="type-name-link" title="enum class in org.apache.tools.ant.taskdefs">XSLTProcess.ParamType</a></li>
<li class="circle">org.apache.tools.zip.<a href="org/apache/tools/zip/Zip64Mode.html" class="type-name-link" title="enum class in org.apache.tools.zip">Zip64Mode</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</body>
</html>
