<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ExtraFieldUtils (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip, class: ExtraFieldUtils">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Class ExtraFieldUtils" class="title">Class ExtraFieldUtils</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.zip.ExtraFieldUtils</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ExtraFieldUtils</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">ZipExtraField related methods</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ExtraFieldUtils.UnparseableExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a></code></div>
<div class="col-last even-row-color">
<div class="block">"enum" for the possible actions to take if the extra field
 cannot be parsed.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ExtraFieldUtils</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createExtraField(org.apache.tools.zip.ZipShort)" class="member-name-link">createExtraField</a><wbr>(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;headerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create an instance of the appropriate ExtraField, falls back to
 <a href="UnrecognizedExtraField.html" title="class in org.apache.tools.zip"><code>UnrecognizedExtraField</code></a>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#mergeCentralDirectoryData(org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">mergeCentralDirectoryData</a><wbr>(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Merges the central directory fields of the given ZipExtraFields.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#mergeLocalFileDataData(org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">mergeLocalFileDataData</a><wbr>(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Merges the local file data fields of the given ZipExtraFields.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parse(byte%5B%5D)" class="member-name-link">parse</a><wbr>(byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Split the array into ExtraFields and populate them with the
 given data as local file data, throwing an exception if the
 data cannot be parsed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parse(byte%5B%5D,boolean)" class="member-name-link">parse</a><wbr>(byte[]&nbsp;data,
 boolean&nbsp;local)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Split the array into ExtraFields and populate them with the
 given data, throwing an exception if the data cannot be parsed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parse(byte%5B%5D,boolean,org.apache.tools.zip.ExtraFieldUtils.UnparseableExtraField)" class="member-name-link">parse</a><wbr>(byte[]&nbsp;data,
 boolean&nbsp;local,
 <a href="ExtraFieldUtils.UnparseableExtraField.html" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a>&nbsp;onUnparseableData)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Split the array into ExtraFields and populate them with the
 given data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#register(java.lang.Class)" class="member-name-link">register</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;c)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Register a ZipExtraField implementation.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ExtraFieldUtils</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ExtraFieldUtils</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="register(java.lang.Class)">
<h3>register</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">register</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;c)</span></div>
<div class="block">Register a ZipExtraField implementation.

 <p>The given class must have a no-arg constructor and implement
 the <a href="ZipExtraField.html" title="interface in org.apache.tools.zip"><code>ZipExtraField interface</code></a>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c</code> - the class to register</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createExtraField(org.apache.tools.zip.ZipShort)">
<h3>createExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></span>&nbsp;<span class="element-name">createExtraField</span><wbr><span class="parameters">(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;headerId)</span>
                                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/InstantiationException.html" title="class or interface in java.lang" class="external-link">InstantiationException</a>,
<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalAccessException.html" title="class or interface in java.lang" class="external-link">IllegalAccessException</a></span></div>
<div class="block">Create an instance of the appropriate ExtraField, falls back to
 <a href="UnrecognizedExtraField.html" title="class in org.apache.tools.zip"><code>UnrecognizedExtraField</code></a>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerId</code> - the header identifier</dd>
<dt>Returns:</dt>
<dd>an instance of the appropriate ExtraField</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/InstantiationException.html" title="class or interface in java.lang" class="external-link">InstantiationException</a></code> - if unable to instantiate the class</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalAccessException.html" title="class or interface in java.lang" class="external-link">IllegalAccessException</a></code> - if not allowed to instantiate the class</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parse(byte[])">
<h3>parse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</span>&nbsp;<span class="element-name">parse</span><wbr><span class="parameters">(byte[]&nbsp;data)</span>
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Split the array into ExtraFields and populate them with the
 given data as local file data, throwing an exception if the
 data cannot be parsed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - an array of bytes as it appears in local file data</dd>
<dt>Returns:</dt>
<dd>an array of ExtraFields</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parse(byte[],boolean)">
<h3>parse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</span>&nbsp;<span class="element-name">parse</span><wbr><span class="parameters">(byte[]&nbsp;data,
 boolean&nbsp;local)</span>
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Split the array into ExtraFields and populate them with the
 given data, throwing an exception if the data cannot be parsed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - an array of bytes</dd>
<dd><code>local</code> - whether data originates from the local file data
 or the central directory</dd>
<dt>Returns:</dt>
<dd>an array of ExtraFields</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parse(byte[],boolean,org.apache.tools.zip.ExtraFieldUtils.UnparseableExtraField)">
<h3>parse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</span>&nbsp;<span class="element-name">parse</span><wbr><span class="parameters">(byte[]&nbsp;data,
 boolean&nbsp;local,
 <a href="ExtraFieldUtils.UnparseableExtraField.html" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a>&nbsp;onUnparseableData)</span>
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Split the array into ExtraFields and populate them with the
 given data.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - an array of bytes</dd>
<dd><code>local</code> - whether data originates from the local file data
 or the central directory</dd>
<dd><code>onUnparseableData</code> - what to do if the extra field data
 cannot be parsed.</dd>
<dt>Returns:</dt>
<dd>an array of ExtraFields</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.8.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="mergeLocalFileDataData(org.apache.tools.zip.ZipExtraField[])">
<h3>mergeLocalFileDataData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">mergeLocalFileDataData</span><wbr><span class="parameters">(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;data)</span></div>
<div class="block">Merges the local file data fields of the given ZipExtraFields.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - an array of ExtraFiles</dd>
<dt>Returns:</dt>
<dd>an array of bytes</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="mergeCentralDirectoryData(org.apache.tools.zip.ZipExtraField[])">
<h3>mergeCentralDirectoryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">mergeCentralDirectoryData</span><wbr><span class="parameters">(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;data)</span></div>
<div class="block">Merges the central directory fields of the given ZipExtraFields.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - an array of ExtraFields</dd>
<dt>Returns:</dt>
<dd>an array of bytes</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
