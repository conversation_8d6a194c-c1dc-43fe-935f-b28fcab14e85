<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ZipExtraField (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip, interface: ZipExtraField">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Interface ZipExtraField" class="title">Interface ZipExtraField</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="AbstractUnicodeExtraField.html" title="class in org.apache.tools.zip">AbstractUnicodeExtraField</a></code>, <code><a href="AsiExtraField.html" title="class in org.apache.tools.zip">AsiExtraField</a></code>, <code><a href="JarMarker.html" title="class in org.apache.tools.zip">JarMarker</a></code>, <code><a href="UnicodeCommentExtraField.html" title="class in org.apache.tools.zip">UnicodeCommentExtraField</a></code>, <code><a href="UnicodePathExtraField.html" title="class in org.apache.tools.zip">UnicodePathExtraField</a></code>, <code><a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a></code>, <code><a href="UnrecognizedExtraField.html" title="class in org.apache.tools.zip">UnrecognizedExtraField</a></code>, <code><a href="Zip64ExtendedInformationExtraField.html" title="class in org.apache.tools.zip">Zip64ExtendedInformationExtraField</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">ZipExtraField</span></div>
<div class="block">General format of extra field data.

 <p>Extra fields usually appear twice per file, once in the local
 file data and once in the central directory.  Usually they are the
 same, but they don't have to be.  <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>java.util.zip.ZipOutputStream</code></a> will
 only use the local file data in both places.</p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getCentralDirectoryData()" class="member-name-link">getCentralDirectoryData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">The actual data to put into central directory - without Header-ID or
 length specifier.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getCentralDirectoryLength()" class="member-name-link">getCentralDirectoryLength</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Length of the extra field in the central directory - without
 Header-ID or length specifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getHeaderId()" class="member-name-link">getHeaderId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">The Header-ID.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getLocalFileDataData()" class="member-name-link">getLocalFileDataData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">The actual data to put into local file data - without Header-ID
 or length specifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getLocalFileDataLength()" class="member-name-link">getLocalFileDataLength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Length of the extra field in the local file data - without
 Header-ID or length specifier.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#parseFromLocalFileData(byte%5B%5D,int,int)" class="member-name-link">parseFromLocalFileData</a><wbr>(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Populate data from this array as if it was in local file data.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getHeaderId()">
<h3>getHeaderId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getHeaderId</span>()</div>
<div class="block">The Header-ID.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the header id</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataLength()">
<h3>getLocalFileDataLength</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getLocalFileDataLength</span>()</div>
<div class="block">Length of the extra field in the local file data - without
 Header-ID or length specifier.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the length of the field in the local file data</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryLength()">
<h3>getCentralDirectoryLength</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getCentralDirectoryLength</span>()</div>
<div class="block">Length of the extra field in the central directory - without
 Header-ID or length specifier.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the length of the field in the central directory</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataData()">
<h3>getLocalFileDataData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">byte[]</span>&nbsp;<span class="element-name">getLocalFileDataData</span>()</div>
<div class="block">The actual data to put into local file data - without Header-ID
 or length specifier.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the data</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryData()">
<h3>getCentralDirectoryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">byte[]</span>&nbsp;<span class="element-name">getCentralDirectoryData</span>()</div>
<div class="block">The actual data to put into central directory - without Header-ID or
 length specifier.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the data</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseFromLocalFileData(byte[],int,int)">
<h3>parseFromLocalFileData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">parseFromLocalFileData</span><wbr><span class="parameters">(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span>
                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Populate data from this array as if it was in local file data.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - an array of bytes</dd>
<dd><code>offset</code> - the start offset</dd>
<dd><code>length</code> - the number of bytes in the array from offset</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
