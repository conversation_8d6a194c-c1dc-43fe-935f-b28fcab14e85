<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ZipOutputStream (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip, class: ZipOutputStream">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Class ZipOutputStream" class="title">Class ZipOutputStream</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">java.io.OutputStream</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">java.io.FilterOutputStream</a>
<div class="inheritance">org.apache.tools.zip.ZipOutputStream</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ZipOutputStream</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></span></div>
<div class="block">Reimplementation of <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>java.util.zip.ZipOutputStream</code></a> that does handle the extended
 functionality of this package, especially internal/external file
 attributes and extra fields with different layouts for local file
 data and central directory entries.

 <p>This class will try to use <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/RandomAccessFile.html" title="class or interface in java.io" class="external-link"><code>RandomAccessFile</code></a> when you know that the output is going to go to a
 file.</p>

 <p>If RandomAccessFile cannot be used, this implementation will use
 a Data Descriptor to store size and CRC information for <a href="#DEFLATED"><code>DEFLATED</code></a> entries, this means, you don't need to
 calculate them yourself.  Unfortunately this is not possible for
 the <a href="#STORED"><code>STORED</code></a> method, here setting the CRC and
 uncompressed size information is required before <a href="#putNextEntry(org.apache.tools.zip.ZipEntry)"><code>putNextEntry</code></a> can be called.</p>

 <p>As of Apache Ant 1.9.0 it transparently supports Zip64
 extensions and thus individual entries and archives larger than 4
 GB or with more than 65536 entries in most cases but explicit
 control is provided via <a href="#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>setUseZip64(org.apache.tools.zip.Zip64Mode)</code></a>.  If the stream can not
 user RandomAccessFile and you try to write a ZipEntry of
 unknown size then Zip64 extensions will be disabled by default.</p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ZipOutputStream.UnicodeExtraFieldPolicy.html" class="type-name-link" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a></code></div>
<div class="col-last even-row-color">
<div class="block">enum that represents the possible policies for creating Unicode
 extra fields.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected byte[]</code></div>
<div class="col-second even-row-color"><code><a href="#buf" class="member-name-link">buf</a></code></div>
<div class="col-last even-row-color">
<div class="block">This buffer serves as a Deflater.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final byte[]</code></div>
<div class="col-second odd-row-color"><code><a href="#CFH_SIG" class="member-name-link">CFH_SIG</a></code></div>
<div class="col-last odd-row-color">
<div class="block">central file header signature</div>
</div>
<div class="col-first even-row-color"><code>protected static final byte[]</code></div>
<div class="col-second even-row-color"><code><a href="#DD_SIG" class="member-name-link">DD_SIG</a></code></div>
<div class="col-last even-row-color">
<div class="block">data descriptor signature</div>
</div>
<div class="col-first odd-row-color"><code>protected final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/Deflater.html" title="class or interface in java.util.zip" class="external-link">Deflater</a></code></div>
<div class="col-second odd-row-color"><code><a href="#def" class="member-name-link">def</a></code></div>
<div class="col-last odd-row-color">
<div class="block">This Deflater object is used for output.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_COMPRESSION" class="member-name-link">DEFAULT_COMPRESSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">Default compression level for deflated entries.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEFLATED" class="member-name-link">DEFLATED</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Compression method for deflated entries.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#EFS_FLAG" class="member-name-link">EFS_FLAG</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="GeneralPurposeBit.html#UFT8_NAMES_FLAG"><code>GeneralPurposeBit.UFT8_NAMES_FLAG</code></a> instead</div>
</div>
</div>
<div class="col-first odd-row-color"><code>protected static final byte[]</code></div>
<div class="col-second odd-row-color"><code><a href="#EOCD_SIG" class="member-name-link">EOCD_SIG</a></code></div>
<div class="col-last odd-row-color">
<div class="block">end of central dir signature</div>
</div>
<div class="col-first even-row-color"><code>protected static final byte[]</code></div>
<div class="col-second even-row-color"><code><a href="#LFH_SIG" class="member-name-link">LFH_SIG</a></code></div>
<div class="col-last even-row-color">
<div class="block">local file header signature</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#STORED" class="member-name-link">STORED</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Compression method for stored entries.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.FilterOutputStream">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html#out" title="class or interface in java.io" class="external-link">out</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.File)" class="member-name-link">ZipOutputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new ZIP OutputStream writing to a File.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.OutputStream)" class="member-name-link">ZipOutputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new ZIP OutputStream filtering the underlying stream.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>protected static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#adjustToLong(int)" class="member-name-link">adjustToLong</a><wbr>(int&nbsp;i)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use ZipUtil#adjustToLong</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#canWriteEntryData(org.apache.tools.zip.ZipEntry)" class="member-name-link">canWriteEntryData</a><wbr>(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ae)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether this stream is able to write the given entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes this output stream and releases any system resources
 associated with the stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#closeEntry()" class="member-name-link">closeEntry</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes all necessary data for this entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deflate()" class="member-name-link">deflate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes next block of compressed data to the output stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#finish()" class="member-name-link">finish</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finish writing the archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#flush()" class="member-name-link">flush</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flushes this output stream and forces any buffered output bytes
 to be written out to the stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBytes(java.lang.String)" class="member-name-link">getBytes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieve the bytes for the given String in the encoding set for
 this Stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEncoding()" class="member-name-link">getEncoding</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The encoding to use for filenames and the file comment.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSeekable()" class="member-name-link">isSeekable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method indicates whether this archive is writing to a
 seekable stream (i.e., to a random access file).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#putNextEntry(org.apache.tools.zip.ZipEntry)" class="member-name-link">putNextEntry</a><wbr>(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;archiveEntry)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Put the specified entry into the archive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComment(java.lang.String)" class="member-name-link">setComment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the file comment.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCreateUnicodeExtraFields(org.apache.tools.zip.ZipOutputStream.UnicodeExtraFieldPolicy)" class="member-name-link">setCreateUnicodeExtraFields</a><wbr>(<a href="ZipOutputStream.UnicodeExtraFieldPolicy.html" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a>&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to create Unicode Extra Fields.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The encoding to use for filenames and the file comment.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFallbackToUTF8(boolean)" class="member-name-link">setFallbackToUTF8</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to fall back to UTF and the language encoding flag if
 the file name cannot be encoded using the specified encoding.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLevel(int)" class="member-name-link">setLevel</a><wbr>(int&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the compression level for subsequent entries.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMethod(int)" class="member-name-link">setMethod</a><wbr>(int&nbsp;method)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the default compression method for subsequent entries.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUseLanguageEncodingFlag(boolean)" class="member-name-link">setUseLanguageEncodingFlag</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to set the language encoding flag if the file name
 encoding is UTF-8.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUseZip64(org.apache.tools.zip.Zip64Mode)" class="member-name-link">setUseZip64</a><wbr>(<a href="Zip64Mode.html" title="enum class in org.apache.tools.zip">Zip64Mode</a>&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether Zip64 extensions will be used.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>protected static byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#toDosTime(long)" class="member-name-link">toDosTime</a><wbr>(long&nbsp;t)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use ZipUtil#toDosTime</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>protected static <a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#toDosTime(java.util.Date)" class="member-name-link">toDosTime</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;time)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use ZipUtil#toDosTime</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(byte%5B%5D,int,int)" class="member-name-link">write</a><wbr>(byte[]&nbsp;b,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes bytes to ZIP entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(int)" class="member-name-link">write</a><wbr>(int&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes a byte to ZIP entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeCentralDirectoryEnd()" class="member-name-link">writeCentralDirectoryEnd</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the &quot;End of central dir record&quot;.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeCentralFileHeader(org.apache.tools.zip.ZipEntry)" class="member-name-link">writeCentralFileHeader</a><wbr>(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ze)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the central file header entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeDataDescriptor(org.apache.tools.zip.ZipEntry)" class="member-name-link">writeDataDescriptor</a><wbr>(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ze)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the data descriptor entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeLocalFileHeader(org.apache.tools.zip.ZipEntry)" class="member-name-link">writeLocalFileHeader</a><wbr>(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ze)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the local file header entry</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeOut(byte%5B%5D)" class="member-name-link">writeOut</a><wbr>(byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write bytes to output or random access file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeOut(byte%5B%5D,int,int)" class="member-name-link">writeOut</a><wbr>(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write bytes to output or random access file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeZip64CentralDirectory()" class="member-name-link">writeZip64CentralDirectory</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the &quot;ZIP64 End of central dir record&quot; and
 &quot;ZIP64 End of central dir locator&quot;.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.FilterOutputStream">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html#write(byte%5B%5D)" title="class or interface in java.io" class="external-link">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.OutputStream">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#nullOutputStream()" title="class or interface in java.io" class="external-link">nullOutputStream</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFLATED">
<h3>DEFLATED</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFLATED</span></div>
<div class="block">Compression method for deflated entries.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.DEFLATED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_COMPRESSION">
<h3>DEFAULT_COMPRESSION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_COMPRESSION</span></div>
<div class="block">Default compression level for deflated entries.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.DEFAULT_COMPRESSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="STORED">
<h3>STORED</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">STORED</span></div>
<div class="block">Compression method for stored entries.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.STORED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EFS_FLAG">
<h3>EFS_FLAG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">EFS_FLAG</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="GeneralPurposeBit.html#UFT8_NAMES_FLAG"><code>GeneralPurposeBit.UFT8_NAMES_FLAG</code></a> instead</div>
</div>
<div class="block">General purpose flag, which indicates that filenames are
 written in utf-8.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.EFS_FLAG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="def">
<h3>def</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/Deflater.html" title="class or interface in java.util.zip" class="external-link">Deflater</a></span>&nbsp;<span class="element-name">def</span></div>
<div class="block">This Deflater object is used for output.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="buf">
<h3>buf</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">buf</span></div>
<div class="block">This buffer serves as a Deflater.

 <p>This attribute is only protected to provide a level of API
 backwards compatibility.  This class used to extend <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/DeflaterOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>DeflaterOutputStream</code></a> up to
 Revision 1.13.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.14</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LFH_SIG">
<h3>LFH_SIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">LFH_SIG</span></div>
<div class="block">local file header signature</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DD_SIG">
<h3>DD_SIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">DD_SIG</span></div>
<div class="block">data descriptor signature</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CFH_SIG">
<h3>CFH_SIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">CFH_SIG</span></div>
<div class="block">central file header signature</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EOCD_SIG">
<h3>EOCD_SIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">EOCD_SIG</span></div>
<div class="block">end of central dir signature</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.io.OutputStream)">
<h3>ZipOutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ZipOutputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</span></div>
<div class="block">Creates a new ZIP OutputStream filtering the underlying stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - the outputstream to zip</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.File)">
<h3>ZipOutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ZipOutputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Creates a new ZIP OutputStream writing to a File.  Will use
 random access if possible.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to zip to</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.14</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isSeekable()">
<h3>isSeekable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSeekable</span>()</div>
<div class="block">This method indicates whether this archive is writing to a
 seekable stream (i.e., to a random access file).

 <p>For seekable streams, you don't need to calculate the CRC or
 uncompressed size for <a href="#STORED"><code>STORED</code></a> entries before
 invoking <a href="#putNextEntry(org.apache.tools.zip.ZipEntry)"><code>putNextEntry(org.apache.tools.zip.ZipEntry)</code></a>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if seekable</dd>
<dt>Since:</dt>
<dd>1.17</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">The encoding to use for filenames and the file comment.

 <p>For a list of possible values see <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html">
 https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html</a>.
 Defaults to the platform's default character encoding.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - the encoding value</dd>
<dt>Since:</dt>
<dd>1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getEncoding()">
<h3>getEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getEncoding</span>()</div>
<div class="block">The encoding to use for filenames and the file comment.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>null if using the platform's default character encoding.</dd>
<dt>Since:</dt>
<dd>1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUseLanguageEncodingFlag(boolean)">
<h3>setUseLanguageEncodingFlag</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUseLanguageEncodingFlag</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to set the language encoding flag if the file name
 encoding is UTF-8.

 <p>Defaults to true.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCreateUnicodeExtraFields(org.apache.tools.zip.ZipOutputStream.UnicodeExtraFieldPolicy)">
<h3>setCreateUnicodeExtraFields</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCreateUnicodeExtraFields</span><wbr><span class="parameters">(<a href="ZipOutputStream.UnicodeExtraFieldPolicy.html" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a>&nbsp;b)</span></div>
<div class="block">Whether to create Unicode Extra Fields.

 <p>Defaults to NEVER.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFallbackToUTF8(boolean)">
<h3>setFallbackToUTF8</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFallbackToUTF8</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to fall back to UTF and the language encoding flag if
 the file name cannot be encoded using the specified encoding.

 <p>Defaults to false.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUseZip64(org.apache.tools.zip.Zip64Mode)">
<h3>setUseZip64</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUseZip64</span><wbr><span class="parameters">(<a href="Zip64Mode.html" title="enum class in org.apache.tools.zip">Zip64Mode</a>&nbsp;mode)</span></div>
<div class="block">Whether Zip64 extensions will be used.

 <p>When setting the mode to <a href="Zip64Mode.html#Never"><code>Never</code></a>,
 <a href="#putNextEntry(org.apache.tools.zip.ZipEntry)"><code>putNextEntry(org.apache.tools.zip.ZipEntry)</code></a>, <a href="#closeEntry()"><code>closeEntry()</code></a>, <a href="#finish()"><code>finish()</code></a> or <a href="#close()"><code>close()</code></a> may throw a <a href="Zip64RequiredException.html" title="class in org.apache.tools.zip"><code>Zip64RequiredException</code></a> if the entry's size or the total size
 of the archive exceeds 4GB or there are more than 65536 entries
 inside the archive.  Any archive created in this mode will be
 readable by implementations that don't support Zip64.</p>

 <p>When setting the mode to <a href="Zip64Mode.html#Always"><code>Always</code></a>,
 Zip64 extensions will be used for all entries.  Any archive
 created in this mode may be unreadable by implementations that
 don't support Zip64 even if all its contents would be.</p>

 <p>When setting the mode to <a href="Zip64Mode.html#AsNeeded"><code>AsNeeded</code></a>, Zip64 extensions will transparently be used for
 those entries that require them.  This mode can only be used if
 the uncompressed size of the <a href="ZipEntry.html" title="class in org.apache.tools.zip"><code>ZipEntry</code></a> is known
 when calling <a href="#putNextEntry(org.apache.tools.zip.ZipEntry)"><code>putNextEntry(org.apache.tools.zip.ZipEntry)</code></a> or the archive is written
 to a seekable output (i.e. you have used the <a href="#%3Cinit%3E(java.io.File)"><code>File-arg constructor</code></a>) -
 this mode is not valid when the output stream is not seekable
 and the uncompressed size is unknown when <a href="#putNextEntry(org.apache.tools.zip.ZipEntry)"><code>putNextEntry(org.apache.tools.zip.ZipEntry)</code></a> is called.</p>

 <p>If no entry inside the resulting archive requires Zip64
 extensions then <a href="Zip64Mode.html#Never"><code>Never</code></a> will create the
 smallest archive.  <a href="Zip64Mode.html#AsNeeded"><code>AsNeeded</code></a> will
 create a slightly bigger archive if the uncompressed size of
 any entry has initially been unknown and create an archive
 identical to <a href="Zip64Mode.html#Never"><code>Never</code></a> otherwise.  <a href="Zip64Mode.html#Always"><code>Always</code></a> will create an archive that is at
 least 24 bytes per entry bigger than the one <a href="Zip64Mode.html#Never"><code>Never</code></a> would create.</p>

 <p>Defaults to <a href="Zip64Mode.html#AsNeeded"><code>AsNeeded</code></a> unless
 <a href="#putNextEntry(org.apache.tools.zip.ZipEntry)"><code>putNextEntry(org.apache.tools.zip.ZipEntry)</code></a> is called with an entry of unknown
 size and data is written to a non-seekable stream - in this
 case the default is <a href="Zip64Mode.html#Never"><code>Never</code></a>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - Zip64Mode</dd>
<dt>Since:</dt>
<dd>1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="finish()">
<h3>finish</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">finish</span>()
            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Finish writing the archive.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></code> - if the archive's size exceeds 4
 GByte or there are more than 65535 entries inside the archive
 and <a href="#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>setUseZip64(org.apache.tools.zip.Zip64Mode)</code></a> is <a href="Zip64Mode.html#Never"><code>Zip64Mode.Never</code></a>.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="closeEntry()">
<h3>closeEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">closeEntry</span>()
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes all necessary data for this entry.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dd><code><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></code> - if the entry's uncompressed or
 compressed size exceeds 4 GByte and <a href="#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>setUseZip64(org.apache.tools.zip.Zip64Mode)</code></a>
 is <a href="Zip64Mode.html#Never"><code>Zip64Mode.Never</code></a>.</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="putNextEntry(org.apache.tools.zip.ZipEntry)">
<h3>putNextEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">putNextEntry</span><wbr><span class="parameters">(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;archiveEntry)</span>
                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Put the specified entry into the archive.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></code> - if the entry's uncompressed or
 compressed size is known to exceed 4 GByte and <a href="#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>setUseZip64(org.apache.tools.zip.Zip64Mode)</code></a>
 is <a href="Zip64Mode.html#Never"><code>Zip64Mode.Never</code></a>.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setComment(java.lang.String)">
<h3>setComment</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</span></div>
<div class="block">Set the file comment.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comment</code> - the comment</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLevel(int)">
<h3>setLevel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLevel</span><wbr><span class="parameters">(int&nbsp;level)</span></div>
<div class="block">Sets the compression level for subsequent entries.

 <p>Default is Deflater.DEFAULT_COMPRESSION.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>level</code> - the compression level.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if an invalid compression
 level is specified.</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMethod(int)">
<h3>setMethod</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMethod</span><wbr><span class="parameters">(int&nbsp;method)</span></div>
<div class="block">Sets the default compression method for subsequent entries.

 <p>Default is DEFLATED.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>method</code> - an <code>int</code> from java.util.zip.ZipEntry</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="canWriteEntryData(org.apache.tools.zip.ZipEntry)">
<h3>canWriteEntryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">canWriteEntryData</span><wbr><span class="parameters">(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ae)</span></div>
<div class="block">Whether this stream is able to write the given entry.

 <p>May return false if it is set up to use encryption or a
 compression method that hasn't been implemented yet.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ae</code> - ZipEntry</dd>
<dt>Returns:</dt>
<dd>boolean</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="write(int)">
<h3>write</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(int&nbsp;b)</span>
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes a byte to ZIP entry.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html#write(int)" title="class or interface in java.io" class="external-link">write</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></code></dd>
<dt>Parameters:</dt>
<dd><code>b</code> - the byte to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.10.10</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="write(byte[],int,int)">
<h3>write</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(byte[]&nbsp;b,
 int&nbsp;offset,
 int&nbsp;length)</span>
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes bytes to ZIP entry.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html#write(byte%5B%5D,int,int)" title="class or interface in java.io" class="external-link">write</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></code></dd>
<dt>Parameters:</dt>
<dd><code>b</code> - the byte array to write</dd>
<dd><code>offset</code> - the start position to write from</dd>
<dd><code>length</code> - the number of bytes to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Closes this output stream and releases any system resources
 associated with the stream.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html#close()" title="class or interface in java.lang" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an I/O error occurs.</dd>
<dd><code><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></code> - if the archive's size exceeds 4
 GByte or there are more than 65535 entries inside the archive
 and <a href="#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>setUseZip64(org.apache.tools.zip.Zip64Mode)</code></a> is <a href="Zip64Mode.html#Never"><code>Zip64Mode.Never</code></a>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="flush()">
<h3>flush</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">flush</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Flushes this output stream and forces any buffered output bytes
 to be written out to the stream.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html#flush()" title="class or interface in java.io" class="external-link">flush</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html#flush()" title="class or interface in java.io" class="external-link">flush</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" title="class or interface in java.io" class="external-link">FilterOutputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an I/O error occurs.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="deflate()">
<h3>deflate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deflate</span>()
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes next block of compressed data to the output stream.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.14</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeLocalFileHeader(org.apache.tools.zip.ZipEntry)">
<h3>writeLocalFileHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeLocalFileHeader</span><wbr><span class="parameters">(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ze)</span>
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes the local file header entry</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ze</code> - the entry to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeDataDescriptor(org.apache.tools.zip.ZipEntry)">
<h3>writeDataDescriptor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeDataDescriptor</span><wbr><span class="parameters">(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ze)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes the data descriptor entry.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ze</code> - the entry to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeCentralFileHeader(org.apache.tools.zip.ZipEntry)">
<h3>writeCentralFileHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeCentralFileHeader</span><wbr><span class="parameters">(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;ze)</span>
                               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes the central file header entry.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ze</code> - the entry to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dd><code><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></code> - if the archive's size exceeds 4
 GByte and <a href="Zip64Mode.html" title="enum class in org.apache.tools.zip"><code>#setUseZip64</code></a> is <a href="Zip64Mode.html#Never"><code>Zip64Mode.Never</code></a>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeCentralDirectoryEnd()">
<h3>writeCentralDirectoryEnd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeCentralDirectoryEnd</span>()
                                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes the &quot;End of central dir record&quot;.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dd><code><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></code> - if the archive's size exceeds 4
 GByte or there are more than 65535 entries inside the archive
 and <a href="Zip64Mode.html" title="enum class in org.apache.tools.zip"><code>#setUseZip64</code></a> is <a href="Zip64Mode.html#Never"><code>Zip64Mode.Never</code></a>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toDosTime(java.util.Date)">
<h3>toDosTime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a></span>&nbsp;<span class="element-name">toDosTime</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;time)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use ZipUtil#toDosTime</div>
</div>
<div class="block">Convert a Date object to a DOS date/time field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - the <code>Date</code> to convert</dd>
<dt>Returns:</dt>
<dd>the date as a <code>ZipLong</code></dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toDosTime(long)">
<h3>toDosTime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">toDosTime</span><wbr><span class="parameters">(long&nbsp;t)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use ZipUtil#toDosTime</div>
</div>
<div class="block">Convert a Date object to a DOS date/time field.

 <p>Stolen from InfoZip's <code>fileio.c</code></p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t</code> - number of milliseconds since the epoch</dd>
<dt>Returns:</dt>
<dd>the date as a byte array</dd>
<dt>Since:</dt>
<dd>1.26</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getBytes(java.lang.String)">
<h3>getBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getBytes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Retrieve the bytes for the given String in the encoding set for
 this Stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the string to get bytes from</dd>
<dt>Returns:</dt>
<dd>the bytes as a byte array</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeZip64CentralDirectory()">
<h3>writeZip64CentralDirectory</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeZip64CentralDirectory</span>()
                                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Writes the &quot;ZIP64 End of central dir record&quot; and
 &quot;ZIP64 End of central dir locator&quot;.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeOut(byte[])">
<h3>writeOut</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeOut</span><wbr><span class="parameters">(byte[]&nbsp;data)</span>
                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Write bytes to output or random access file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - the byte array to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.14</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeOut(byte[],int,int)">
<h3>writeOut</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeOut</span><wbr><span class="parameters">(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span>
                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Write bytes to output or random access file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - the byte array to write</dd>
<dd><code>offset</code> - the start position to write from</dd>
<dd><code>length</code> - the number of bytes to write</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.14</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="adjustToLong(int)">
<h3>adjustToLong</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">adjustToLong</span><wbr><span class="parameters">(int&nbsp;i)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use ZipUtil#adjustToLong</div>
</div>
<div class="block">Assumes a negative integer really is a positive integer that
 has wrapped around and re-creates the original value.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>i</code> - the value to treat as unsigned int.</dd>
<dt>Returns:</dt>
<dd>the unsigned int as a long.</dd>
<dt>Since:</dt>
<dd>1.34</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
