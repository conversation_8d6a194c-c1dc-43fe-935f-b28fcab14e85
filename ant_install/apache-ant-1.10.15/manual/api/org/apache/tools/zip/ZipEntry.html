<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ZipEntry (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip, class: ZipEntry">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Class ZipEntry" class="title">Class ZipEntry</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">java.util.zip.ZipEntry</a>
<div class="inheritance">org.apache.tools.zip.ZipEntry</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ZipEntry</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a>
implements <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></span></div>
<div class="block">Extension that adds better handling of extra fields and provides
 access to the internal and external file attributes.

 <p>The extra data is expected to follow the recommendation of
 <a href="https://www.pkware.com/documents/casestudies/APPNOTE.TXT">APPNOTE.txt</a>:</p>
 <ul>
   <li>the extra byte array consists of a sequence of extra fields</li>
   <li>each extra fields starts by a two byte header id followed by
   a two byte sequence holding the length of the remainder of
   data.</li>
 </ul>

 <p>Any extra data that cannot be parsed by the rules above will be
 consumed as "unparseable" extra data and treated differently by the
 methods of this class.  Older versions would have thrown an
 exception if any attempt was made to read or write extra data not
 conforming to the recommendation.</p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENATT" class="member-name-link">CENATT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENATX" class="member-name-link">CENATX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENCOM" class="member-name-link">CENCOM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENCRC" class="member-name-link">CENCRC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENDSK" class="member-name-link">CENDSK</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENEXT" class="member-name-link">CENEXT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENFLG" class="member-name-link">CENFLG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENHDR" class="member-name-link">CENHDR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENHOW" class="member-name-link">CENHOW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENLEN" class="member-name-link">CENLEN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENNAM" class="member-name-link">CENNAM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENOFF" class="member-name-link">CENOFF</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#CENSIG" class="member-name-link">CENSIG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENSIZ" class="member-name-link">CENSIZ</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENTIM" class="member-name-link">CENTIM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CENVEM" class="member-name-link">CENVEM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CENVER" class="member-name-link">CENVER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CRC_UNKNOWN" class="member-name-link">CRC_UNKNOWN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ENDCOM" class="member-name-link">ENDCOM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#ENDHDR" class="member-name-link">ENDHDR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ENDOFF" class="member-name-link">ENDOFF</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#ENDSIG" class="member-name-link">ENDSIG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ENDSIZ" class="member-name-link">ENDSIZ</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#ENDSUB" class="member-name-link">ENDSUB</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ENDTOT" class="member-name-link">ENDTOT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#EXTCRC" class="member-name-link">EXTCRC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#EXTHDR" class="member-name-link">EXTHDR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#EXTLEN" class="member-name-link">EXTLEN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#EXTSIG" class="member-name-link">EXTSIG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#EXTSIZ" class="member-name-link">EXTSIZ</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LOCCRC" class="member-name-link">LOCCRC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LOCEXT" class="member-name-link">LOCEXT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LOCFLG" class="member-name-link">LOCFLG</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LOCHDR" class="member-name-link">LOCHDR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LOCHOW" class="member-name-link">LOCHOW</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LOCLEN" class="member-name-link">LOCLEN</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LOCNAM" class="member-name-link">LOCNAM</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#LOCSIG" class="member-name-link">LOCSIG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LOCSIZ" class="member-name-link">LOCSIZ</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LOCTIM" class="member-name-link">LOCTIM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LOCVER" class="member-name-link">LOCVER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PLATFORM_FAT" class="member-name-link">PLATFORM_FAT</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PLATFORM_UNIX" class="member-name-link">PLATFORM_UNIX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.zip.ZipEntry">Fields inherited from class&nbsp;java.util.zip.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#DEFLATED" title="class or interface in java.util.zip" class="external-link">DEFLATED</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#STORED" title="class or interface in java.util.zip" class="external-link">STORED</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ZipEntry</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.File,java.lang.String)" class="member-name-link">ZipEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inputFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;entryName)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new zip entry taking some information from the given
 file and using the provided name.</div>
</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">ZipEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new zip entry with the specified name.</div>
</div>
<div class="col-first odd-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.util.zip.ZipEntry)" class="member-name-link">ZipEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a>&nbsp;entry)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new zip entry with fields taken from the specified zip entry.</div>
</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.zip.ZipEntry)" class="member-name-link">ZipEntry</a><wbr>(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;entry)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new zip entry with fields taken from the specified zip entry.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addAsFirstExtraField(org.apache.tools.zip.ZipExtraField)" class="member-name-link">addAsFirstExtraField</a><wbr>(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>&nbsp;ze)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an extra field - replacing an already present extra field
 of the same type.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addExtraField(org.apache.tools.zip.ZipExtraField)" class="member-name-link">addExtraField</a><wbr>(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>&nbsp;ze)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an extra field - replacing an already present extra field
 of the same type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override clone.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#equals(java.lang.Object)" class="member-name-link">equals</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"></div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryExtra()" class="member-name-link">getCentralDirectoryExtra</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves the extra data for the central directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExternalAttributes()" class="member-name-link">getExternalAttributes</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves the external file attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtraField(org.apache.tools.zip.ZipShort)" class="member-name-link">getExtraField</a><wbr>(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Looks up an extra field by its header id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtraFields()" class="member-name-link">getExtraFields</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves all extra fields that have been parsed successfully.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtraFields(boolean)" class="member-name-link">getExtraFields</a><wbr>(boolean&nbsp;includeUnparseable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves extra fields.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="GeneralPurposeBit.html" title="class in org.apache.tools.zip">GeneralPurposeBit</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGeneralPurposeBit()" class="member-name-link">getGeneralPurposeBit</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The "general purpose bit" field.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInternalAttributes()" class="member-name-link">getInternalAttributes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves the internal file attributes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLastModifiedDate()" class="member-name-link">getLastModifiedDate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get last modified time as <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link"><code>Date</code></a>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataExtra()" class="member-name-link">getLocalFileDataExtra</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves the extra data for the local file data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMethod()" class="member-name-link">getMethod</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the compression method of this entry, or -1 if the
 compression method has not been specified.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the name of the entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlatform()" class="member-name-link">getPlatform</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Platform specification to put into the &quot;version made
 by&quot; part of the central file header.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRawName()" class="member-name-link">getRawName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the raw bytes that made up the name before it has been
 converted using the configured or guessed encoding.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSize()" class="member-name-link">getSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the uncompressed size of the entry data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUnixMode()" class="member-name-link">getUnixMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Unix permission.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUnparseableExtraFieldData()" class="member-name-link">getUnparseableExtraFieldData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Looks up extra field data that couldn't be parsed correctly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hashCode()" class="member-name-link">hashCode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the hashCode of the entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDirectory()" class="member-name-link">isDirectory</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is this entry a directory?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeExtraField(org.apache.tools.zip.ZipShort)" class="member-name-link">removeExtraField</a><wbr>(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Remove an extra field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeUnparseableExtraFieldData()" class="member-name-link">removeUnparseableExtraFieldData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Removes unparseable extra field data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCentralDirectoryExtra(byte%5B%5D)" class="member-name-link">setCentralDirectoryExtra</a><wbr>(byte[]&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the central directory part of extra fields.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setComprSize(long)" class="member-name-link">setComprSize</a><wbr>(long&nbsp;size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExternalAttributes(long)" class="member-name-link">setExternalAttributes</a><wbr>(long&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the external file attributes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtra()" class="member-name-link">setExtra</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Unfortunately <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>java.util.zip.ZipOutputStream</code></a> seems to access the extra data
 directly, so overriding getExtra doesn't help - we need to
 modify super's data directly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtra(byte%5B%5D)" class="member-name-link">setExtra</a><wbr>(byte[]&nbsp;extra)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the given bytes as extra field data and consumes any
 unparseable data as an <a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip"><code>UnparseableExtraFieldData</code></a>
 instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtraFields(org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">setExtraFields</a><wbr>(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;fields)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replaces all currently attached extra fields with the new array.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGeneralPurposeBit(org.apache.tools.zip.GeneralPurposeBit)" class="member-name-link">setGeneralPurposeBit</a><wbr>(<a href="GeneralPurposeBit.html" title="class in org.apache.tools.zip">GeneralPurposeBit</a>&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The "general purpose bit" field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalAttributes(int)" class="member-name-link">setInternalAttributes</a><wbr>(int&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the internal file attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMethod(int)" class="member-name-link">setMethod</a><wbr>(int&nbsp;method)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the compression method of this entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String,byte%5B%5D)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;rawName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name using the raw bytes and the string created from
 it by guessing or using the configured encoding.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPlatform(int)" class="member-name-link">setPlatform</a><wbr>(int&nbsp;platform)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the platform (UNIX or FAT).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(long)" class="member-name-link">setSize</a><wbr>(long&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the uncompressed size of the entry data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUnixMode(int)" class="member-name-link">setUnixMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets Unix permissions in a way that is understood by Info-Zip's
 unzip command.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.zip.ZipEntry">Methods inherited from class&nbsp;java.util.zip.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getComment()" title="class or interface in java.util.zip" class="external-link">getComment</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getCompressedSize()" title="class or interface in java.util.zip" class="external-link">getCompressedSize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getCrc()" title="class or interface in java.util.zip" class="external-link">getCrc</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getCreationTime()" title="class or interface in java.util.zip" class="external-link">getCreationTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getExtra()" title="class or interface in java.util.zip" class="external-link">getExtra</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getLastAccessTime()" title="class or interface in java.util.zip" class="external-link">getLastAccessTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getLastModifiedTime()" title="class or interface in java.util.zip" class="external-link">getLastModifiedTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getTime()" title="class or interface in java.util.zip" class="external-link">getTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getTimeLocal()" title="class or interface in java.util.zip" class="external-link">getTimeLocal</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setComment(java.lang.String)" title="class or interface in java.util.zip" class="external-link">setComment</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setCompressedSize(long)" title="class or interface in java.util.zip" class="external-link">setCompressedSize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setCrc(long)" title="class or interface in java.util.zip" class="external-link">setCrc</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setCreationTime(java.nio.file.attribute.FileTime)" title="class or interface in java.util.zip" class="external-link">setCreationTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setLastAccessTime(java.nio.file.attribute.FileTime)" title="class or interface in java.util.zip" class="external-link">setLastAccessTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setLastModifiedTime(java.nio.file.attribute.FileTime)" title="class or interface in java.util.zip" class="external-link">setLastModifiedTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setTime(long)" title="class or interface in java.util.zip" class="external-link">setTime</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setTimeLocal(java.time.LocalDateTime)" title="class or interface in java.util.zip" class="external-link">setTimeLocal</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#toString()" title="class or interface in java.util.zip" class="external-link">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PLATFORM_UNIX">
<h3>PLATFORM_UNIX</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PLATFORM_UNIX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.PLATFORM_UNIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PLATFORM_FAT">
<h3>PLATFORM_FAT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PLATFORM_FAT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.PLATFORM_FAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CRC_UNKNOWN">
<h3>CRC_UNKNOWN</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CRC_UNKNOWN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CRC_UNKNOWN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCSIG">
<h3>LOCSIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">LOCSIG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCSIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EXTSIG">
<h3>EXTSIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">EXTSIG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTSIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENSIG">
<h3>CENSIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">CENSIG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENSIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDSIG">
<h3>ENDSIG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ENDSIG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDSIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCHDR">
<h3>LOCHDR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCHDR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCHDR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EXTHDR">
<h3>EXTHDR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">EXTHDR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTHDR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENHDR">
<h3>CENHDR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENHDR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENHDR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDHDR">
<h3>ENDHDR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ENDHDR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDHDR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCVER">
<h3>LOCVER</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCVER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCVER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCFLG">
<h3>LOCFLG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCFLG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCFLG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCHOW">
<h3>LOCHOW</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCHOW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCHOW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCTIM">
<h3>LOCTIM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCTIM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCTIM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCCRC">
<h3>LOCCRC</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCCRC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCCRC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCSIZ">
<h3>LOCSIZ</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCSIZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCSIZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCLEN">
<h3>LOCLEN</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCLEN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCNAM">
<h3>LOCNAM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCNAM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCNAM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LOCEXT">
<h3>LOCEXT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LOCEXT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCEXT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EXTCRC">
<h3>EXTCRC</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">EXTCRC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTCRC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EXTSIZ">
<h3>EXTSIZ</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">EXTSIZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTSIZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="EXTLEN">
<h3>EXTLEN</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">EXTLEN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENVEM">
<h3>CENVEM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENVEM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENVEM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENVER">
<h3>CENVER</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENVER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENVER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENFLG">
<h3>CENFLG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENFLG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENFLG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENHOW">
<h3>CENHOW</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENHOW</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENHOW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENTIM">
<h3>CENTIM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENTIM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENTIM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENCRC">
<h3>CENCRC</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENCRC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENCRC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENSIZ">
<h3>CENSIZ</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENSIZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENSIZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENLEN">
<h3>CENLEN</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENLEN</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENNAM">
<h3>CENNAM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENNAM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENNAM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENEXT">
<h3>CENEXT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENEXT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENEXT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENCOM">
<h3>CENCOM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENCOM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENCOM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENDSK">
<h3>CENDSK</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENDSK</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENDSK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENATT">
<h3>CENATT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENATT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENATT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENATX">
<h3>CENATX</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENATX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENATX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CENOFF">
<h3>CENOFF</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CENOFF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENOFF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDSUB">
<h3>ENDSUB</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ENDSUB</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDSUB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDTOT">
<h3>ENDTOT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ENDTOT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDTOT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDSIZ">
<h3>ENDSIZ</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ENDSIZ</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDSIZ">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDOFF">
<h3>ENDOFF</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ENDOFF</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDOFF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENDCOM">
<h3>ENDCOM</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ENDCOM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDCOM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>ZipEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ZipEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Creates a new zip entry with the specified name.

 <p>Assumes the entry represents a directory if and only if the
 name ends with a forward slash "/".</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the entry</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.util.zip.ZipEntry)">
<h3>ZipEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ZipEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a>&nbsp;entry)</span>
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Creates a new zip entry with fields taken from the specified zip entry.

 <p>Assumes the entry represents a directory if and only if the
 name ends with a forward slash "/".</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>entry</code> - the entry to get fields from</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.zip.ZipEntry)">
<h3>ZipEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ZipEntry</span><wbr><span class="parameters">(<a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a>&nbsp;entry)</span>
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Creates a new zip entry with fields taken from the specified zip entry.

 <p>Assumes the entry represents a directory if and only if the
 name ends with a forward slash "/".</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>entry</code> - the entry to get fields from</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ZipEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ZipEntry</span>()</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.9</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.File,java.lang.String)">
<h3>ZipEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ZipEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inputFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;entryName)</span></div>
<div class="block">Creates a new zip entry taking some information from the given
 file and using the provided name.

 <p>The name will be adjusted to end with a forward slash "/" if
 the file is a directory.  If the file is not a directory a
 potential trailing forward slash will be stripped from the
 entry name.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputFile</code> - File</dd>
<dd><code>entryName</code> - String</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<div class="block">Override clone.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#clone()" title="class or interface in java.util.zip" class="external-link">clone</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Returns:</dt>
<dd>a cloned copy of this ZipEntry</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMethod()">
<h3>getMethod</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMethod</span>()</div>
<div class="block">Returns the compression method of this entry, or -1 if the
 compression method has not been specified.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getMethod()" title="class or interface in java.util.zip" class="external-link">getMethod</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Returns:</dt>
<dd>compression method</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMethod(int)">
<h3>setMethod</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMethod</span><wbr><span class="parameters">(int&nbsp;method)</span></div>
<div class="block">Sets the compression method of this entry.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setMethod(int)" title="class or interface in java.util.zip" class="external-link">setMethod</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Parameters:</dt>
<dd><code>method</code> - compression method</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getInternalAttributes()">
<h3>getInternalAttributes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getInternalAttributes</span>()</div>
<div class="block">Retrieves the internal file attributes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the internal file attributes</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setInternalAttributes(int)">
<h3>setInternalAttributes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalAttributes</span><wbr><span class="parameters">(int&nbsp;value)</span></div>
<div class="block">Sets the internal file attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - an <code>int</code> value</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExternalAttributes()">
<h3>getExternalAttributes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getExternalAttributes</span>()</div>
<div class="block">Retrieves the external file attributes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the external file attributes</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExternalAttributes(long)">
<h3>setExternalAttributes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExternalAttributes</span><wbr><span class="parameters">(long&nbsp;value)</span></div>
<div class="block">Sets the external file attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - an <code>long</code> value</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUnixMode(int)">
<h3>setUnixMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUnixMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
<div class="block">Sets Unix permissions in a way that is understood by Info-Zip's
 unzip command.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - an <code>int</code> value</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUnixMode()">
<h3>getUnixMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUnixMode</span>()</div>
<div class="block">Unix permission.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the unix permissions</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPlatform()">
<h3>getPlatform</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPlatform</span>()</div>
<div class="block">Platform specification to put into the &quot;version made
 by&quot; part of the central file header.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>PLATFORM_FAT unless <a href="#setUnixMode(int)"><code>setUnixMode</code></a>
 has been called, in which case PLATFORM_UNIX will be returned.</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setPlatform(int)">
<h3>setPlatform</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPlatform</span><wbr><span class="parameters">(int&nbsp;platform)</span></div>
<div class="block">Set the platform (UNIX or FAT).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>platform</code> - an <code>int</code> value - 0 is FAT, 3 is UNIX</dd>
<dt>Since:</dt>
<dd>1.9</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExtraFields(org.apache.tools.zip.ZipExtraField[])">
<h3>setExtraFields</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtraFields</span><wbr><span class="parameters">(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;fields)</span></div>
<div class="block">Replaces all currently attached extra fields with the new array.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fields</code> - an array of extra fields</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExtraFields()">
<h3>getExtraFields</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</span>&nbsp;<span class="element-name">getExtraFields</span>()</div>
<div class="block">Retrieves all extra fields that have been parsed successfully.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an array of the extra fields</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExtraFields(boolean)">
<h3>getExtraFields</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</span>&nbsp;<span class="element-name">getExtraFields</span><wbr><span class="parameters">(boolean&nbsp;includeUnparseable)</span></div>
<div class="block">Retrieves extra fields.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includeUnparseable</code> - whether to also return unparseable
 extra fields as <a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip"><code>UnparseableExtraFieldData</code></a> if such data
 exists.</dd>
<dt>Returns:</dt>
<dd>an array of the extra fields</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addExtraField(org.apache.tools.zip.ZipExtraField)">
<h3>addExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExtraField</span><wbr><span class="parameters">(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>&nbsp;ze)</span></div>
<div class="block">Adds an extra field - replacing an already present extra field
 of the same type.

 <p>If no extra field of the same type exists, the field will be
 added as last field.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ze</code> - an extra field</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addAsFirstExtraField(org.apache.tools.zip.ZipExtraField)">
<h3>addAsFirstExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addAsFirstExtraField</span><wbr><span class="parameters">(<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>&nbsp;ze)</span></div>
<div class="block">Adds an extra field - replacing an already present extra field
 of the same type.

 <p>The new extra field will be the first one.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ze</code> - an extra field</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="removeExtraField(org.apache.tools.zip.ZipShort)">
<h3>removeExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">removeExtraField</span><wbr><span class="parameters">(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;type)</span></div>
<div class="block">Remove an extra field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - the type of extra field to remove</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="removeUnparseableExtraFieldData()">
<h3>removeUnparseableExtraFieldData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">removeUnparseableExtraFieldData</span>()</div>
<div class="block">Removes unparseable extra field data.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="getExtraField(org.apache.tools.zip.ZipShort)">
<h3>getExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></span>&nbsp;<span class="element-name">getExtraField</span><wbr><span class="parameters">(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;type)</span></div>
<div class="block">Looks up an extra field by its header id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - ZipShort</dd>
<dt>Returns:</dt>
<dd><code>null</code> if no such field exists.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUnparseableExtraFieldData()">
<h3>getUnparseableExtraFieldData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a></span>&nbsp;<span class="element-name">getUnparseableExtraFieldData</span>()</div>
<div class="block">Looks up extra field data that couldn't be parsed correctly.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>null</code> if no such field exists.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExtra(byte[])">
<h3>setExtra</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtra</span><wbr><span class="parameters">(byte[]&nbsp;extra)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a></span></div>
<div class="block">Parses the given bytes as extra field data and consumes any
 unparseable data as an <a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip"><code>UnparseableExtraFieldData</code></a>
 instance.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setExtra(byte%5B%5D)" title="class or interface in java.util.zip" class="external-link">setExtra</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Parameters:</dt>
<dd><code>extra</code> - an array of bytes to be parsed into extra fields</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a></code> - if the bytes cannot be parsed</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExtra()">
<h3>setExtra</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtra</span>()</div>
<div class="block">Unfortunately <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>java.util.zip.ZipOutputStream</code></a> seems to access the extra data
 directly, so overriding getExtra doesn't help - we need to
 modify super's data directly.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCentralDirectoryExtra(byte[])">
<h3>setCentralDirectoryExtra</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCentralDirectoryExtra</span><wbr><span class="parameters">(byte[]&nbsp;b)</span></div>
<div class="block">Sets the central directory part of extra fields.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - <code>boolean</code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataExtra()">
<h3>getLocalFileDataExtra</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getLocalFileDataExtra</span>()</div>
<div class="block">Retrieves the extra data for the local file data.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the extra data for local file</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryExtra()">
<h3>getCentralDirectoryExtra</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getCentralDirectoryExtra</span>()</div>
<div class="block">Retrieves the extra data for the central directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the central directory extra data</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setComprSize(long)">
<h3>setComprSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComprSize</span><wbr><span class="parameters">(long&nbsp;size)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.
             Use <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setCompressedSize(long)" title="class or interface in java.util.zip" class="external-link"><code>ZipEntry.setCompressedSize(long)</code></a> directly.</div>
</div>
<div class="block">Make this class work in JDK 1.1 like a 1.2 class.

 <p>This either stores the size for later usage or invokes
 setCompressedSize via reflection.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - the size to use</dd>
<dt>Since:</dt>
<dd>1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<div class="block">Get the name of the entry.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getName()" title="class or interface in java.util.zip" class="external-link">getName</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Returns:</dt>
<dd>the entry name</dd>
<dt>Since:</dt>
<dd>1.9</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isDirectory()">
<h3>isDirectory</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDirectory</span>()</div>
<div class="block">Is this entry a directory?</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#isDirectory()" title="class or interface in java.util.zip" class="external-link">isDirectory</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Returns:</dt>
<dd><code>true</code> if the entry is a directory</dd>
<dt>Since:</dt>
<dd>1.10</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setName(java.lang.String)">
<h3>setName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set the name of the entry.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSize()">
<h3>getSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getSize</span>()</div>
<div class="block">Gets the uncompressed size of the entry data.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#getSize()" title="class or interface in java.util.zip" class="external-link">getSize</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Returns:</dt>
<dd>the entry size</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSize(long)">
<h3>setSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(long&nbsp;size)</span></div>
<div class="block">Sets the uncompressed size of the entry data.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#setSize(long)" title="class or interface in java.util.zip" class="external-link">setSize</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Parameters:</dt>
<dd><code>size</code> - the uncompressed size in bytes</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the specified size is less
            than 0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setName(java.lang.String,byte[])">
<h3>setName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;rawName)</span></div>
<div class="block">Sets the name using the raw bytes and the string created from
 it by guessing or using the configured encoding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name to use created from the raw bytes using
 the guessed or configured encoding</dd>
<dd><code>rawName</code> - the bytes originally read as name from the
 archive</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRawName()">
<h3>getRawName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getRawName</span>()</div>
<div class="block">Returns the raw bytes that made up the name before it has been
 converted using the configured or guessed encoding.

 <p>This method will return null if this instance has not been
 read from an archive.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>byte[]</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hashCode()">
<h3>hashCode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hashCode</span>()</div>
<div class="block">Get the hashCode of the entry.
 This uses the name as the hashcode.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html#hashCode()" title="class or interface in java.util.zip" class="external-link">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" title="class or interface in java.util.zip" class="external-link">ZipEntry</a></code></dd>
<dt>Returns:</dt>
<dd>a hashcode.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGeneralPurposeBit()">
<h3>getGeneralPurposeBit</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="GeneralPurposeBit.html" title="class in org.apache.tools.zip">GeneralPurposeBit</a></span>&nbsp;<span class="element-name">getGeneralPurposeBit</span>()</div>
<div class="block">The "general purpose bit" field.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>GeneralPurposeBit</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGeneralPurposeBit(org.apache.tools.zip.GeneralPurposeBit)">
<h3>setGeneralPurposeBit</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGeneralPurposeBit</span><wbr><span class="parameters">(<a href="GeneralPurposeBit.html" title="class in org.apache.tools.zip">GeneralPurposeBit</a>&nbsp;b)</span></div>
<div class="block">The "general purpose bit" field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - GeneralPurposeBit</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLastModifiedDate()">
<h3>getLastModifiedDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">getLastModifiedDate</span>()</div>
<div class="block">Get last modified time as <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link"><code>Date</code></a>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link"><code>Date</code></a></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="equals(java.lang.Object)">
<h3>equals</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">equals</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
