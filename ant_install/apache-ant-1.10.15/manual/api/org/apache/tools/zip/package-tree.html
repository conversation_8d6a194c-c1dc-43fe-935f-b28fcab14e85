<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.zip Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="tree: package: org.apache.tools.zip">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.zip</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.zip.<a href="AbstractUnicodeExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">AbstractUnicodeExtraField</a> (implements org.apache.tools.zip.<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>)
<ul>
<li class="circle">org.apache.tools.zip.<a href="UnicodeCommentExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">UnicodeCommentExtraField</a></li>
<li class="circle">org.apache.tools.zip.<a href="UnicodePathExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">UnicodePathExtraField</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.zip.<a href="AsiExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">AsiExtraField</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.zip.<a href="UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a>, org.apache.tools.zip.<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="ExtraFieldUtils.html" class="type-name-link" title="class in org.apache.tools.zip">ExtraFieldUtils</a></li>
<li class="circle">org.apache.tools.zip.<a href="ExtraFieldUtils.UnparseableExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a></li>
<li class="circle">org.apache.tools.zip.<a href="GeneralPurposeBit.html" class="type-name-link" title="class in org.apache.tools.zip">GeneralPurposeBit</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="JarMarker.html" class="type-name-link" title="class in org.apache.tools.zip">JarMarker</a> (implements org.apache.tools.zip.<a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>)</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" class="type-name-link external-link" title="class or interface in java.io">OutputStream</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a>)
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" class="type-name-link external-link" title="class or interface in java.io">FilterOutputStream</a>
<ul>
<li class="circle">org.apache.tools.zip.<a href="ZipOutputStream.html" class="type-name-link" title="class in org.apache.tools.zip">ZipOutputStream</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" class="type-name-link external-link" title="class or interface in java.io">IOException</a>
<ul>
<li class="circle">java.util.zip.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" class="type-name-link external-link" title="class or interface in java.util.zip">ZipException</a>
<ul>
<li class="circle">org.apache.tools.zip.<a href="UnsupportedZipFeatureException.html" class="type-name-link" title="class in org.apache.tools.zip">UnsupportedZipFeatureException</a></li>
<li class="circle">org.apache.tools.zip.<a href="Zip64RequiredException.html" class="type-name-link" title="class in org.apache.tools.zip">Zip64RequiredException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.zip.<a href="UnparseableExtraFieldData.html" class="type-name-link" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a> (implements org.apache.tools.zip.<a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="UnrecognizedExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">UnrecognizedExtraField</a> (implements org.apache.tools.zip.<a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="UnsupportedZipFeatureException.Feature.html" class="type-name-link" title="class in org.apache.tools.zip">UnsupportedZipFeatureException.Feature</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="Zip64ExtendedInformationExtraField.html" class="type-name-link" title="class in org.apache.tools.zip">Zip64ExtendedInformationExtraField</a> (implements org.apache.tools.zip.<a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="ZipEightByteInteger.html" class="type-name-link" title="class in org.apache.tools.zip">ZipEightByteInteger</a></li>
<li class="circle">org.apache.tools.zip.<a href="ZipEncodingHelper.html" class="type-name-link" title="class in org.apache.tools.zip">ZipEncodingHelper</a></li>
<li class="circle">java.util.zip.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipEntry.html" class="type-name-link external-link" title="class or interface in java.util.zip">ZipEntry</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.zip.<a href="ZipEntry.html" class="type-name-link" title="class in org.apache.tools.zip">ZipEntry</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.zip.<a href="ZipFile.html" class="type-name-link" title="class in org.apache.tools.zip">ZipFile</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="ZipLong.html" class="type-name-link" title="class in org.apache.tools.zip">ZipLong</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="ZipOutputStream.UnicodeExtraFieldPolicy.html" class="type-name-link" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a></li>
<li class="circle">org.apache.tools.zip.<a href="ZipShort.html" class="type-name-link" title="class in org.apache.tools.zip">ZipShort</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.zip.<a href="ZipUtil.html" class="type-name-link" title="class in org.apache.tools.zip">ZipUtil</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.zip.<a href="UnixStat.html" class="type-name-link" title="interface in org.apache.tools.zip">UnixStat</a></li>
<li class="circle">org.apache.tools.zip.<a href="ZipEncoding.html" class="type-name-link" title="interface in org.apache.tools.zip">ZipEncoding</a></li>
<li class="circle">org.apache.tools.zip.<a href="ZipExtraField.html" class="type-name-link" title="interface in org.apache.tools.zip">ZipExtraField</a>
<ul>
<li class="circle">org.apache.tools.zip.<a href="CentralDirectoryParsingZipExtraField.html" class="type-name-link" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.zip.<a href="Zip64Mode.html" class="type-name-link" title="enum class in org.apache.tools.zip">Zip64Mode</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</body>
</html>
