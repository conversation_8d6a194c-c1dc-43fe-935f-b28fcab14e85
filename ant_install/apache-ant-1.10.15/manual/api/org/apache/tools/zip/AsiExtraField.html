<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>AsiExtraField (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip, class: AsiExtraField">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Class AsiExtraField" class="title">Class AsiExtraField</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.zip.AsiExtraField</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a></code>, <code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">AsiExtraField</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>, <a href="UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></span></div>
<div class="block">Adds Unix file permission and UID/GID fields as well as symbolic
 link handling.

 <p>This class uses the ASi extra field in the format:</p>
 <pre>
         Value         Size            Description
         -----         ----            -----------
 (Unix3) 0x756e        Short           tag for this extra block type
         TSize         Short           total data size for this block
         CRC           Long            CRC-32 of the remaining data
         Mode          Short           file permissions
         SizDev        Long            symlink'd size OR major/minor dev num
         UID           Short           user ID
         GID           Short           group ID
         (var.)        variable        symbolic link filename
 </pre>
 taken from appnote.iz (Info-ZIP note, 981119) found at <a href="ftp://ftp.uu.net/pub/archiving/zip/doc/">ftp://ftp.uu.net/pub/archiving/zip/doc/</a>


 <p>Short is two bytes and Long is four bytes in big endian byte and
 word order, device numbers are currently not supported.</p>

 <p>Since the documentation this class is based upon doesn't mention
 the character encoding of the file name at all, it is assumed that
 it uses the current platform's default encoding.</p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.zip.UnixStat">Fields inherited from interface&nbsp;org.apache.tools.zip.<a href="UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a></h3>
<code><a href="UnixStat.html#DEFAULT_DIR_PERM">DEFAULT_DIR_PERM</a>, <a href="UnixStat.html#DEFAULT_FILE_PERM">DEFAULT_FILE_PERM</a>, <a href="UnixStat.html#DEFAULT_LINK_PERM">DEFAULT_LINK_PERM</a>, <a href="UnixStat.html#DIR_FLAG">DIR_FLAG</a>, <a href="UnixStat.html#FILE_FLAG">FILE_FLAG</a>, <a href="UnixStat.html#LINK_FLAG">LINK_FLAG</a>, <a href="UnixStat.html#PERM_MASK">PERM_MASK</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">AsiExtraField</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for AsiExtraField.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryData()" class="member-name-link">getCentralDirectoryData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delegate to local file data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryLength()" class="member-name-link">getCentralDirectoryLength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delegate to local file data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGroupId()" class="member-name-link">getGroupId</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the group id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeaderId()" class="member-name-link">getHeaderId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The Header-ID.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLinkedFile()" class="member-name-link">getLinkedFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Name of linked file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataData()" class="member-name-link">getLocalFileDataData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The actual data to put into local file data - without Header-ID
 or length specifier.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataLength()" class="member-name-link">getLocalFileDataLength</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Length of the extra field in the local file data - without
 Header-ID or length specifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMode()" class="member-name-link">getMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">File mode of this file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMode(int)" class="member-name-link">getMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the file mode for given permissions with the correct file type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserId()" class="member-name-link">getUserId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the user id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDirectory()" class="member-name-link">isDirectory</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is this entry a directory?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLink()" class="member-name-link">isLink</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is this entry a symbolic link?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseFromLocalFileData(byte%5B%5D,int,int)" class="member-name-link">parseFromLocalFileData</a><wbr>(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Populate data from this array as if it was in local file data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDirectory(boolean)" class="member-name-link">setDirectory</a><wbr>(boolean&nbsp;dirFlag)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether this entry is a directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGroupId(int)" class="member-name-link">setGroupId</a><wbr>(int&nbsp;gid)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the group id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLinkedFile(java.lang.String)" class="member-name-link">setLinkedFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate that this entry is a symbolic link to the given filename.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMode(int)" class="member-name-link">setMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">File mode of this file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserId(int)" class="member-name-link">setUserId</a><wbr>(int&nbsp;uid)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the user id.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>AsiExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AsiExtraField</span>()</div>
<div class="block">Constructor for AsiExtraField.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getHeaderId()">
<h3>getHeaderId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getHeaderId</span>()</div>
<div class="block">The Header-ID.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getHeaderId()">getHeaderId</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the value for the header id for this extrafield</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataLength()">
<h3>getLocalFileDataLength</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getLocalFileDataLength</span>()</div>
<div class="block">Length of the extra field in the local file data - without
 Header-ID or length specifier.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getLocalFileDataLength()">getLocalFileDataLength</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>a <code>ZipShort</code> for the length of the data of this extra field</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryLength()">
<h3>getCentralDirectoryLength</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getCentralDirectoryLength</span>()</div>
<div class="block">Delegate to local file data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getCentralDirectoryLength()">getCentralDirectoryLength</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the centralDirectory length</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataData()">
<h3>getLocalFileDataData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getLocalFileDataData</span>()</div>
<div class="block">The actual data to put into local file data - without Header-ID
 or length specifier.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getLocalFileDataData()">getLocalFileDataData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>get the data</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryData()">
<h3>getCentralDirectoryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getCentralDirectoryData</span>()</div>
<div class="block">Delegate to local file data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getCentralDirectoryData()">getCentralDirectoryData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the local file data</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUserId(int)">
<h3>setUserId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserId</span><wbr><span class="parameters">(int&nbsp;uid)</span></div>
<div class="block">Set the user id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uid</code> - the user id</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUserId()">
<h3>getUserId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUserId</span>()</div>
<div class="block">Get the user id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the user id</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGroupId(int)">
<h3>setGroupId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGroupId</span><wbr><span class="parameters">(int&nbsp;gid)</span></div>
<div class="block">Set the group id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gid</code> - the group id</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGroupId()">
<h3>getGroupId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getGroupId</span>()</div>
<div class="block">Get the group id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the group id</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLinkedFile(java.lang.String)">
<h3>setLinkedFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLinkedFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Indicate that this entry is a symbolic link to the given filename.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - Name of the file this entry links to, empty String
             if it is not a symbolic link.</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLinkedFile()">
<h3>getLinkedFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLinkedFile</span>()</div>
<div class="block">Name of linked file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>name of the file this entry links to if it is a
         symbolic link, the empty string otherwise.</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isLink()">
<h3>isLink</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLink</span>()</div>
<div class="block">Is this entry a symbolic link?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this is a symbolic link</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMode(int)">
<h3>setMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
<div class="block">File mode of this file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - the file mode</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMode()">
<h3>getMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMode</span>()</div>
<div class="block">File mode of this file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the file mode</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDirectory(boolean)">
<h3>setDirectory</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDirectory</span><wbr><span class="parameters">(boolean&nbsp;dirFlag)</span></div>
<div class="block">Indicate whether this entry is a directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dirFlag</code> - if true, this entry is a directory</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isDirectory()">
<h3>isDirectory</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDirectory</span>()</div>
<div class="block">Is this entry a directory?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this entry is a directory</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseFromLocalFileData(byte[],int,int)">
<h3>parseFromLocalFileData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseFromLocalFileData</span><wbr><span class="parameters">(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Populate data from this array as if it was in local file data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#parseFromLocalFileData(byte%5B%5D,int,int)">parseFromLocalFileData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Parameters:</dt>
<dd><code>data</code> - an array of bytes</dd>
<dd><code>offset</code> - the start offset</dd>
<dd><code>length</code> - the number of bytes in the array from offset</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMode(int)">
<h3>getMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
<div class="block">Get the file mode for given permissions with the correct file type.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - the mode</dd>
<dt>Returns:</dt>
<dd>the type with the mode</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
