<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>TarEntry (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.tar, class: TarEntry">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.tar</a></div>
<h1 title="Class TarEntry" class="title">Class TarEntry</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.tar.TarEntry</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarEntry</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></span></div>
<div class="block">This class represents an entry in a Tar archive. It consists
 of the entry's header, as well as the entry's File. Entries
 can be instantiated in one of three ways, depending on how
 they are to be used.
 <p>
 TarEntries that are created from the header bytes read from
 an archive are instantiated with the TarEntry(byte[])
 constructor. These entries will be used when extracting from
 or listing the contents of an archive. These entries have their
 header filled in using the header bytes. They also set the File
 to null, since they reference an archive entry not a file.
 </p>
 <p>
 TarEntries that are created from Files that are to be written
 into an archive are instantiated with the TarEntry(File)
 constructor. These entries have their header filled in using
 the File's information. They also keep a reference to the File
 for convenience when writing entries.
 </p>
 <p>
 Finally, TarEntries can be constructed from nothing but a name.
 This allows the programmer to construct the entry by hand, for
 instance when only an InputStream is available for writing to
 the archive, and the header information is constructed from
 other information. In this case the header fields are set to
 defaults and the File is set to null.
 </p>
 The C structure for a Tar Entry's header is:
 <pre>
 struct header {
 char name[NAMSIZ];
 char mode[8];
 char uid[8];
 char gid[8];
 char size[12];
 char mtime[12];
 char chksum[8];
 char linkflag;
 char linkname[NAMSIZ];
 char magic[8];
 char uname[TUNMLEN];
 char gname[TGNMLEN];
 char devmajor[8];
 char devminor[8];
 } header;
 All unused bytes are set to null.
 New-style GNU tar files are slightly different from the above.
 For values of size larger than 077777777777L (11 7s)
 or uid and gid larger than 07777777L (7 7s)
 the sign bit of the first byte is set, and the rest of the
 field is the binary representation of the number.
 See TarUtils.parseOctalOrBinary.
 </pre>
 The C structure for a old GNU Tar Entry's header is:
 <pre>
 struct oldgnu_header {
 char unused_pad1[345]; // TarConstants.PAD1LEN_GNU       - offset 0
 char atime[12];        // TarConstants.ATIMELEN_GNU      - offset 345
 char ctime[12];        // TarConstants.CTIMELEN_GNU      - offset 357
 char offset[12];       // TarConstants.OFFSETLEN_GNU     - offset 369
 char longnames[4];     // TarConstants.LONGNAMESLEN_GNU  - offset 381
 char unused_pad2;      // TarConstants.PAD2LEN_GNU       - offset 385
 struct sparse sp[4];   // TarConstants.SPARSELEN_GNU     - offset 386
 char isextended;       // TarConstants.ISEXTENDEDLEN_GNU - offset 482
 char realsize[12];     // TarConstants.REALSIZELEN_GNU   - offset 483
 char unused_pad[17];   // TarConstants.PAD3LEN_GNU       - offset 495
 };
 </pre>
 Whereas, "struct sparse" is:
 <pre>
 struct sparse {
 char offset[12];   // offset 0
 char numbytes[12]; // offset 12
 };
 </pre></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_DIR_MODE" class="member-name-link">DEFAULT_DIR_MODE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Default permissions bits for directories</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_FILE_MODE" class="member-name-link">DEFAULT_FILE_MODE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Default permissions bits for files</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MAX_NAMELEN" class="member-name-link">MAX_NAMELEN</a></code></div>
<div class="col-last even-row-color">
<div class="block">Maximum length of a user's name in the tar file</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MILLIS_PER_SECOND" class="member-name-link">MILLIS_PER_SECOND</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Convert millis to seconds</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.tar.TarConstants">Fields inherited from interface&nbsp;org.apache.tools.tar.<a href="TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></h3>
<code><a href="TarConstants.html#ATIMELEN_GNU">ATIMELEN_GNU</a>, <a href="TarConstants.html#CHKSUMLEN">CHKSUMLEN</a>, <a href="TarConstants.html#CTIMELEN_GNU">CTIMELEN_GNU</a>, <a href="TarConstants.html#DEVLEN">DEVLEN</a>, <a href="TarConstants.html#FORMAT_OLDGNU">FORMAT_OLDGNU</a>, <a href="TarConstants.html#FORMAT_POSIX">FORMAT_POSIX</a>, <a href="TarConstants.html#GIDLEN">GIDLEN</a>, <a href="TarConstants.html#GNAMELEN">GNAMELEN</a>, <a href="TarConstants.html#GNU_LONGLINK">GNU_LONGLINK</a>, <a href="TarConstants.html#GNU_TMAGIC">GNU_TMAGIC</a>, <a href="TarConstants.html#ISEXTENDEDLEN_GNU">ISEXTENDEDLEN_GNU</a>, <a href="TarConstants.html#ISEXTENDEDLEN_GNU_SPARSE">ISEXTENDEDLEN_GNU_SPARSE</a>, <a href="TarConstants.html#LF_BLK">LF_BLK</a>, <a href="TarConstants.html#LF_CHR">LF_CHR</a>, <a href="TarConstants.html#LF_CONTIG">LF_CONTIG</a>, <a href="TarConstants.html#LF_DIR">LF_DIR</a>, <a href="TarConstants.html#LF_FIFO">LF_FIFO</a>, <a href="TarConstants.html#LF_GNUTYPE_LONGLINK">LF_GNUTYPE_LONGLINK</a>, <a href="TarConstants.html#LF_GNUTYPE_LONGNAME">LF_GNUTYPE_LONGNAME</a>, <a href="TarConstants.html#LF_GNUTYPE_SPARSE">LF_GNUTYPE_SPARSE</a>, <a href="TarConstants.html#LF_LINK">LF_LINK</a>, <a href="TarConstants.html#LF_NORMAL">LF_NORMAL</a>, <a href="TarConstants.html#LF_OLDNORM">LF_OLDNORM</a>, <a href="TarConstants.html#LF_PAX_EXTENDED_HEADER_LC">LF_PAX_EXTENDED_HEADER_LC</a>, <a href="TarConstants.html#LF_PAX_EXTENDED_HEADER_UC">LF_PAX_EXTENDED_HEADER_UC</a>, <a href="TarConstants.html#LF_PAX_GLOBAL_EXTENDED_HEADER">LF_PAX_GLOBAL_EXTENDED_HEADER</a>, <a href="TarConstants.html#LF_SYMLINK">LF_SYMLINK</a>, <a href="TarConstants.html#LONGNAMESLEN_GNU">LONGNAMESLEN_GNU</a>, <a href="TarConstants.html#MAGIC_OFFSET">MAGIC_OFFSET</a>, <a href="TarConstants.html#MAGIC_POSIX">MAGIC_POSIX</a>, <a href="TarConstants.html#MAGICLEN">MAGICLEN</a>, <a href="TarConstants.html#MAXID">MAXID</a>, <a href="TarConstants.html#MAXSIZE">MAXSIZE</a>, <a href="TarConstants.html#MODELEN">MODELEN</a>, <a href="TarConstants.html#MODTIMELEN">MODTIMELEN</a>, <a href="TarConstants.html#NAMELEN">NAMELEN</a>, <a href="TarConstants.html#OFFSETLEN_GNU">OFFSETLEN_GNU</a>, <a href="TarConstants.html#PAD2LEN_GNU">PAD2LEN_GNU</a>, <a href="TarConstants.html#PREFIXLEN">PREFIXLEN</a>, <a href="TarConstants.html#PURE_MAGICLEN">PURE_MAGICLEN</a>, <a href="TarConstants.html#REALSIZELEN_GNU">REALSIZELEN_GNU</a>, <a href="TarConstants.html#SIZELEN">SIZELEN</a>, <a href="TarConstants.html#SPARSELEN_GNU">SPARSELEN_GNU</a>, <a href="TarConstants.html#SPARSELEN_GNU_SPARSE">SPARSELEN_GNU_SPARSE</a>, <a href="TarConstants.html#TMAGIC">TMAGIC</a>, <a href="TarConstants.html#UIDLEN">UIDLEN</a>, <a href="TarConstants.html#UNAMELEN">UNAMELEN</a>, <a href="TarConstants.html#VERSION_GNU_SPACE">VERSION_GNU_SPACE</a>, <a href="TarConstants.html#VERSION_GNU_ZERO">VERSION_GNU_ZERO</a>, <a href="TarConstants.html#VERSION_OFFSET">VERSION_OFFSET</a>, <a href="TarConstants.html#VERSION_POSIX">VERSION_POSIX</a>, <a href="TarConstants.html#VERSIONLEN">VERSIONLEN</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(byte%5B%5D)" class="member-name-link">TarEntry</a><wbr>(byte[]&nbsp;headerBuf)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct an entry from an archive's header bytes.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(byte%5B%5D,org.apache.tools.zip.ZipEncoding)" class="member-name-link">TarEntry</a><wbr>(byte[]&nbsp;headerBuf,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct an entry from an archive's header bytes.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.File)" class="member-name-link">TarEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct an entry for a file.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.File,java.lang.String)" class="member-name-link">TarEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct an entry for a file.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">TarEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct an entry with only a name.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,boolean)" class="member-name-link">TarEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;preserveLeadingSlashes)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct an entry with only a name.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,byte)" class="member-name-link">TarEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte&nbsp;linkFlag)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct an entry with a name and a link flag.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#equals(java.lang.Object)" class="member-name-link">equals</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;it)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Determine if the two entries are equal.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#equals(org.apache.tools.tar.TarEntry)" class="member-name-link">equals</a><wbr>(<a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;it)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Determine if the two entries are equal.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDevMajor()" class="member-name-link">getDevMajor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's major device number.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDevMinor()" class="member-name-link">getDevMinor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's minor device number.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDirectoryEntries()" class="member-name-link">getDirectoryEntries</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If this entry represents a file, and the file is a directory, return
 an array of TarEntries for this entry's children.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFile()" class="member-name-link">getFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getGroupId()" class="member-name-link">getGroupId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use #getLongGroupId instead as group ids can be
 bigger than <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html#MAX_VALUE" title="class or interface in java.lang" class="external-link"><code>Integer.MAX_VALUE</code></a></div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGroupName()" class="member-name-link">getGroupName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's group name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLinkFlag()" class="member-name-link">getLinkFlag</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's link flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLinkName()" class="member-name-link">getLinkName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's link name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLongGroupId()" class="member-name-link">getLongGroupId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's group id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLongUserId()" class="member-name-link">getLongUserId</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's user id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMode()" class="member-name-link">getMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's mode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModTime()" class="member-name-link">getModTime</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's modification time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRealSize()" class="member-name-link">getRealSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's real file size in case of a sparse file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSize()" class="member-name-link">getSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's file size.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getUserId()" class="member-name-link">getUserId</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use #getLongUserId instead as user ids can be
 bigger than <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html#MAX_VALUE" title="class or interface in java.lang" class="external-link"><code>Integer.MAX_VALUE</code></a></div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserName()" class="member-name-link">getUserName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this entry's user name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hashCode()" class="member-name-link">hashCode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Hashcodes are based on entry names.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isBlockDevice()" class="member-name-link">isBlockDevice</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCharacterDevice()" class="member-name-link">isCharacterDevice</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this is a character device entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDescendent(org.apache.tools.tar.TarEntry)" class="member-name-link">isDescendent</a><wbr>(<a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;desc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Determine if the given entry is a descendant of this entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDirectory()" class="member-name-link">isDirectory</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return whether or not this entry represents a directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isExtended()" class="member-name-link">isExtended</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates in case of a sparse file if an extension sparse header
 follows.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFIFO()" class="member-name-link">isFIFO</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFile()" class="member-name-link">isFile</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this is a "normal file".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isGlobalPaxHeader()" class="member-name-link">isGlobalPaxHeader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this is a Pax header.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isGNULongLinkEntry()" class="member-name-link">isGNULongLinkEntry</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if this entry is a GNU long linkname block</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isGNULongNameEntry()" class="member-name-link">isGNULongNameEntry</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if this entry is a GNU long name block</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isGNUSparse()" class="member-name-link">isGNUSparse</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if this entry is a GNU sparse block.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLink()" class="member-name-link">isLink</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this is a link entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isPaxHeader()" class="member-name-link">isPaxHeader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this is a Pax header.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSymbolicLink()" class="member-name-link">isSymbolicLink</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this is a symbolic link entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseTarHeader(byte%5B%5D)" class="member-name-link">parseTarHeader</a><wbr>(byte[]&nbsp;header)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parse an entry's header information from a header buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseTarHeader(byte%5B%5D,org.apache.tools.zip.ZipEncoding)" class="member-name-link">parseTarHeader</a><wbr>(byte[]&nbsp;header,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parse an entry's header information from a header buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDevMajor(int)" class="member-name-link">setDevMajor</a><wbr>(int&nbsp;devNo)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's major device number.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDevMinor(int)" class="member-name-link">setDevMinor</a><wbr>(int&nbsp;devNo)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's minor device number.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGroupId(int)" class="member-name-link">setGroupId</a><wbr>(int&nbsp;groupId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's group id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGroupId(long)" class="member-name-link">setGroupId</a><wbr>(long&nbsp;groupId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's group id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGroupName(java.lang.String)" class="member-name-link">setGroupName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's group name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIds(int,int)" class="member-name-link">setIds</a><wbr>(int&nbsp;userId,
 int&nbsp;groupId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience method to set this entry's group and user ids.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLinkFlag(byte)" class="member-name-link">setLinkFlag</a><wbr>(byte&nbsp;linkFlag)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's link flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLinkName(java.lang.String)" class="member-name-link">setLinkName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;link)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's link name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMode(int)" class="member-name-link">setMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the mode for this entry</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModTime(long)" class="member-name-link">setModTime</a><wbr>(long&nbsp;time)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's modification time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModTime(java.util.Date)" class="member-name-link">setModTime</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;time)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's modification time.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNames(java.lang.String,java.lang.String)" class="member-name-link">setNames</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience method to set this entry's group and user names.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(long)" class="member-name-link">setSize</a><wbr>(long&nbsp;size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's file size.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserId(int)" class="member-name-link">setUserId</a><wbr>(int&nbsp;userId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's user id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserId(long)" class="member-name-link">setUserId</a><wbr>(long&nbsp;userId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's user id.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserName(java.lang.String)" class="member-name-link">setUserName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this entry's user name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeEntryHeader(byte%5B%5D)" class="member-name-link">writeEntryHeader</a><wbr>(byte[]&nbsp;outbuf)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write an entry's header information to a header buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeEntryHeader(byte%5B%5D,org.apache.tools.zip.ZipEncoding,boolean)" class="member-name-link">writeEntryHeader</a><wbr>(byte[]&nbsp;outbuf,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding,
 boolean&nbsp;starMode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write an entry's header information to a header buffer.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="MAX_NAMELEN">
<h3>MAX_NAMELEN</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAX_NAMELEN</span></div>
<div class="block">Maximum length of a user's name in the tar file</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarEntry.MAX_NAMELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_DIR_MODE">
<h3>DEFAULT_DIR_MODE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_DIR_MODE</span></div>
<div class="block">Default permissions bits for directories</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarEntry.DEFAULT_DIR_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_FILE_MODE">
<h3>DEFAULT_FILE_MODE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_FILE_MODE</span></div>
<div class="block">Default permissions bits for files</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarEntry.DEFAULT_FILE_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MILLIS_PER_SECOND">
<h3>MILLIS_PER_SECOND</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MILLIS_PER_SECOND</span></div>
<div class="block">Convert millis to seconds</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarEntry.MILLIS_PER_SECOND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Construct an entry with only a name. This allows the programmer
 to construct the entry's header "by hand". File is set to null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the entry name</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,boolean)">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;preserveLeadingSlashes)</span></div>
<div class="block">Construct an entry with only a name. This allows the programmer
 to construct the entry's header "by hand". File is set to null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the entry name</dd>
<dd><code>preserveLeadingSlashes</code> - whether to allow leading slashes
 in the name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,byte)">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte&nbsp;linkFlag)</span></div>
<div class="block">Construct an entry with a name and a link flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the entry name</dd>
<dd><code>linkFlag</code> - the entry link flag.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.File)">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Construct an entry for a file. File is set to file, and the
 header is constructed from information from the file.
 The name is set from the normalized file path.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - The file that the entry represents.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.File,java.lang.String)">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span></div>
<div class="block">Construct an entry for a file. File is set to file, and the
 header is constructed from information from the file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - The file that the entry represents.</dd>
<dd><code>fileName</code> - the name to be used for the entry.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(byte[])">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(byte[]&nbsp;headerBuf)</span></div>
<div class="block">Construct an entry from an archive's header bytes. File is set
 to null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerBuf</code> - The header bytes from a tar archive entry.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if any of the numeric fields have an invalid format</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(byte[],org.apache.tools.zip.ZipEncoding)">
<h3>TarEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarEntry</span><wbr><span class="parameters">(byte[]&nbsp;headerBuf,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</span>
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Construct an entry from an archive's header bytes. File is set
 to null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerBuf</code> - The header bytes from a tar archive entry.</dd>
<dd><code>encoding</code> - encoding to use for file names</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if any of the numeric fields have an invalid format</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an error occurs during reading the archive</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="equals(org.apache.tools.tar.TarEntry)">
<h3>equals</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">equals</span><wbr><span class="parameters">(<a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;it)</span></div>
<div class="block">Determine if the two entries are equal. Equality is determined
 by the header names being equal.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>it</code> - Entry to be checked for equality.</dd>
<dt>Returns:</dt>
<dd>True if the entries are equal.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="equals(java.lang.Object)">
<h3>equals</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">equals</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;it)</span></div>
<div class="block">Determine if the two entries are equal. Equality is determined
 by the header names being equal.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Parameters:</dt>
<dd><code>it</code> - Entry to be checked for equality.</dd>
<dt>Returns:</dt>
<dd>True if the entries are equal.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hashCode()">
<h3>hashCode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hashCode</span>()</div>
<div class="block">Hashcodes are based on entry names.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>the entry hashcode</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isDescendent(org.apache.tools.tar.TarEntry)">
<h3>isDescendent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDescendent</span><wbr><span class="parameters">(<a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;desc)</span></div>
<div class="block">Determine if the given entry is a descendant of this entry.
 Descendancy is determined by the name of the descendant
 starting with this entry's name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>desc</code> - Entry to be checked as a descendant of this.</dd>
<dt>Returns:</dt>
<dd>True if entry is a descendant of this.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<div class="block">Get this entry's name.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setName(java.lang.String)">
<h3>setName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set this entry's name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - This entry's new name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMode(int)">
<h3>setMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
<div class="block">Set the mode for this entry</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - the mode for this entry</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLinkFlag()">
<h3>getLinkFlag</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">getLinkFlag</span>()</div>
<div class="block">Get this entry's link flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's link flag.</dd>
<dt>Since:</dt>
<dd>1.10.12</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLinkFlag(byte)">
<h3>setLinkFlag</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLinkFlag</span><wbr><span class="parameters">(byte&nbsp;linkFlag)</span></div>
<div class="block">Set this entry's link flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>link</code> - the link flag to use.</dd>
<dt>Since:</dt>
<dd>1.10.12</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLinkName()">
<h3>getLinkName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLinkName</span>()</div>
<div class="block">Get this entry's link name.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's link name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLinkName(java.lang.String)">
<h3>setLinkName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLinkName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;link)</span></div>
<div class="block">Set this entry's link name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>link</code> - the link name to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUserId()">
<h3>getUserId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUserId</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use #getLongUserId instead as user ids can be
 bigger than <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html#MAX_VALUE" title="class or interface in java.lang" class="external-link"><code>Integer.MAX_VALUE</code></a></div>
</div>
<div class="block">Get this entry's user id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's user id.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUserId(int)">
<h3>setUserId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserId</span><wbr><span class="parameters">(int&nbsp;userId)</span></div>
<div class="block">Set this entry's user id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userId</code> - This entry's new user id.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLongUserId()">
<h3>getLongUserId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLongUserId</span>()</div>
<div class="block">Get this entry's user id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's user id.</dd>
<dt>Since:</dt>
<dd>1.9.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUserId(long)">
<h3>setUserId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserId</span><wbr><span class="parameters">(long&nbsp;userId)</span></div>
<div class="block">Set this entry's user id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userId</code> - This entry's new user id.</dd>
<dt>Since:</dt>
<dd>1.9.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGroupId()">
<h3>getGroupId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getGroupId</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use #getLongGroupId instead as group ids can be
 bigger than <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html#MAX_VALUE" title="class or interface in java.lang" class="external-link"><code>Integer.MAX_VALUE</code></a></div>
</div>
<div class="block">Get this entry's group id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's group id.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGroupId(int)">
<h3>setGroupId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGroupId</span><wbr><span class="parameters">(int&nbsp;groupId)</span></div>
<div class="block">Set this entry's group id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>groupId</code> - This entry's new group id.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLongGroupId()">
<h3>getLongGroupId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLongGroupId</span>()</div>
<div class="block">Get this entry's group id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's group id.</dd>
<dt>Since:</dt>
<dd>1.9.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGroupId(long)">
<h3>setGroupId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGroupId</span><wbr><span class="parameters">(long&nbsp;groupId)</span></div>
<div class="block">Set this entry's group id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>groupId</code> - This entry's new group id.</dd>
<dt>Since:</dt>
<dd>1.9.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUserName()">
<h3>getUserName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getUserName</span>()</div>
<div class="block">Get this entry's user name.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's user name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUserName(java.lang.String)">
<h3>setUserName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userName)</span></div>
<div class="block">Set this entry's user name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userName</code> - This entry's new user name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGroupName()">
<h3>getGroupName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getGroupName</span>()</div>
<div class="block">Get this entry's group name.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's group name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGroupName(java.lang.String)">
<h3>setGroupName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGroupName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupName)</span></div>
<div class="block">Set this entry's group name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>groupName</code> - This entry's new group name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIds(int,int)">
<h3>setIds</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIds</span><wbr><span class="parameters">(int&nbsp;userId,
 int&nbsp;groupId)</span></div>
<div class="block">Convenience method to set this entry's group and user ids.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userId</code> - This entry's new user id.</dd>
<dd><code>groupId</code> - This entry's new group id.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNames(java.lang.String,java.lang.String)">
<h3>setNames</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNames</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupName)</span></div>
<div class="block">Convenience method to set this entry's group and user names.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userName</code> - This entry's new user name.</dd>
<dd><code>groupName</code> - This entry's new group name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModTime(long)">
<h3>setModTime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModTime</span><wbr><span class="parameters">(long&nbsp;time)</span></div>
<div class="block">Set this entry's modification time. The parameter passed
 to this method is in "Java time".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - This entry's new modification time.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModTime(java.util.Date)">
<h3>setModTime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModTime</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;time)</span></div>
<div class="block">Set this entry's modification time.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - This entry's new modification time.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getModTime()">
<h3>getModTime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">getModTime</span>()</div>
<div class="block">Set this entry's modification time.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>time This entry's new modification time.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFile()">
<h3>getFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getFile</span>()</div>
<div class="block">Get this entry's file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's file.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMode()">
<h3>getMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMode</span>()</div>
<div class="block">Get this entry's mode.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's mode.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSize()">
<h3>getSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getSize</span>()</div>
<div class="block">Get this entry's file size.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's file size.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSize(long)">
<h3>setSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(long&nbsp;size)</span></div>
<div class="block">Set this entry's file size.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - This entry's new file size.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the size is &lt; 0.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDevMajor()">
<h3>getDevMajor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDevMajor</span>()</div>
<div class="block">Get this entry's major device number.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's major device number.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDevMajor(int)">
<h3>setDevMajor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDevMajor</span><wbr><span class="parameters">(int&nbsp;devNo)</span></div>
<div class="block">Set this entry's major device number.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>devNo</code> - This entry's major device number.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the devNo is &lt; 0.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDevMinor()">
<h3>getDevMinor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDevMinor</span>()</div>
<div class="block">Get this entry's minor device number.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's minor device number.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDevMinor(int)">
<h3>setDevMinor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDevMinor</span><wbr><span class="parameters">(int&nbsp;devNo)</span></div>
<div class="block">Set this entry's minor device number.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>devNo</code> - This entry's minor device number.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the devNo is &lt; 0.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isExtended()">
<h3>isExtended</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isExtended</span>()</div>
<div class="block">Indicates in case of a sparse file if an extension sparse header
 follows.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if an extension sparse header follows.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRealSize()">
<h3>getRealSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getRealSize</span>()</div>
<div class="block">Get this entry's real file size in case of a sparse file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This entry's real file size.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isGNUSparse()">
<h3>isGNUSparse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isGNUSparse</span>()</div>
<div class="block">Indicate if this entry is a GNU sparse block.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this is a sparse extension provided by GNU tar</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isGNULongLinkEntry()">
<h3>isGNULongLinkEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isGNULongLinkEntry</span>()</div>
<div class="block">Indicate if this entry is a GNU long linkname block</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this is a long name extension provided by GNU tar</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isGNULongNameEntry()">
<h3>isGNULongNameEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isGNULongNameEntry</span>()</div>
<div class="block">Indicate if this entry is a GNU long name block</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this is a long name extension provided by GNU tar</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isPaxHeader()">
<h3>isPaxHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isPaxHeader</span>()</div>
<div class="block">Check if this is a Pax header.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> if this is a Pax header.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isGlobalPaxHeader()">
<h3>isGlobalPaxHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isGlobalPaxHeader</span>()</div>
<div class="block">Check if this is a Pax header.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> if this is a Pax header.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isDirectory()">
<h3>isDirectory</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDirectory</span>()</div>
<div class="block">Return whether or not this entry represents a directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>True if this entry is a directory.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isFile()">
<h3>isFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFile</span>()</div>
<div class="block">Check if this is a "normal file".</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><i>true</i> if it is a 'normal' file</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSymbolicLink()">
<h3>isSymbolicLink</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSymbolicLink</span>()</div>
<div class="block">Check if this is a symbolic link entry.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><i>true</i> if it is a symlink</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isLink()">
<h3>isLink</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLink</span>()</div>
<div class="block">Check if this is a link entry.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><i>true</i> if it is a link</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isCharacterDevice()">
<h3>isCharacterDevice</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCharacterDevice</span>()</div>
<div class="block">Check if this is a character device entry.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><i>true</i> if it is a character device entry</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isBlockDevice()">
<h3>isBlockDevice</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isBlockDevice</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><i>true</i> if this is a block device entry.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isFIFO()">
<h3>isFIFO</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFIFO</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><i>true</i> if this is a FIFO (pipe) entry.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDirectoryEntries()">
<h3>getDirectoryEntries</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>[]</span>&nbsp;<span class="element-name">getDirectoryEntries</span>()</div>
<div class="block">If this entry represents a file, and the file is a directory, return
 an array of TarEntries for this entry's children.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An array of TarEntry's for this entry's children.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeEntryHeader(byte[])">
<h3>writeEntryHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeEntryHeader</span><wbr><span class="parameters">(byte[]&nbsp;outbuf)</span></div>
<div class="block">Write an entry's header information to a header buffer.

 <p>This method does not use the star/GNU tar/BSD tar extensions.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outbuf</code> - The tar entry header buffer to fill in.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeEntryHeader(byte[],org.apache.tools.zip.ZipEncoding,boolean)">
<h3>writeEntryHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeEntryHeader</span><wbr><span class="parameters">(byte[]&nbsp;outbuf,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding,
 boolean&nbsp;starMode)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Write an entry's header information to a header buffer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outbuf</code> - The tar entry header buffer to fill in.</dd>
<dd><code>encoding</code> - encoding to use when writing the file name.</dd>
<dd><code>starMode</code> - whether to use the star/GNU tar/BSD tar
 extension for numeric fields if their value doesn't fit in the
 maximum size of standard tar archives</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an error occurs while writing the archive</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseTarHeader(byte[])">
<h3>parseTarHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseTarHeader</span><wbr><span class="parameters">(byte[]&nbsp;header)</span></div>
<div class="block">Parse an entry's header information from a header buffer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>header</code> - The tar entry header buffer to get information from.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if any of the numeric fields have an invalid format</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseTarHeader(byte[],org.apache.tools.zip.ZipEncoding)">
<h3>parseTarHeader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseTarHeader</span><wbr><span class="parameters">(byte[]&nbsp;header,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Parse an entry's header information from a header buffer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>header</code> - The tar entry header buffer to get information from.</dd>
<dd><code>encoding</code> - encoding to use for file names</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if any of the numeric fields
 have an invalid format</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an error occurs while reading the archive</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
