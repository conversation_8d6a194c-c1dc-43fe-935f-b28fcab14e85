<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>TarArchiveSparseEntry (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.tar, class: TarArchiveSparseEntry">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.tar</a></div>
<h1 title="Class TarArchiveSparseEntry" class="title">Class TarArchiveSparseEntry</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.tar.TarArchiveSparseEntry</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarArchiveSparseEntry</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></span></div>
<div class="block">This class represents a sparse entry in a Tar archive.

 <p>
 The C structure for a sparse entry is:
 <pre>
 struct posix_header {
 struct sparse sp[21]; // TarConstants.SPARSELEN_GNU_SPARSE     - offset 0
 char isextended;      // TarConstants.ISEXTENDEDLEN_GNU_SPARSE - offset 504
 };
 </pre>
 Whereas, "struct sparse" is:
 <pre>
 struct sparse {
 char offset[12];   // offset 0
 char numbytes[12]; // offset 12
 };
 </pre></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.tar.TarConstants">Fields inherited from interface&nbsp;org.apache.tools.tar.<a href="TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></h3>
<code><a href="TarConstants.html#ATIMELEN_GNU">ATIMELEN_GNU</a>, <a href="TarConstants.html#CHKSUMLEN">CHKSUMLEN</a>, <a href="TarConstants.html#CTIMELEN_GNU">CTIMELEN_GNU</a>, <a href="TarConstants.html#DEVLEN">DEVLEN</a>, <a href="TarConstants.html#FORMAT_OLDGNU">FORMAT_OLDGNU</a>, <a href="TarConstants.html#FORMAT_POSIX">FORMAT_POSIX</a>, <a href="TarConstants.html#GIDLEN">GIDLEN</a>, <a href="TarConstants.html#GNAMELEN">GNAMELEN</a>, <a href="TarConstants.html#GNU_LONGLINK">GNU_LONGLINK</a>, <a href="TarConstants.html#GNU_TMAGIC">GNU_TMAGIC</a>, <a href="TarConstants.html#ISEXTENDEDLEN_GNU">ISEXTENDEDLEN_GNU</a>, <a href="TarConstants.html#ISEXTENDEDLEN_GNU_SPARSE">ISEXTENDEDLEN_GNU_SPARSE</a>, <a href="TarConstants.html#LF_BLK">LF_BLK</a>, <a href="TarConstants.html#LF_CHR">LF_CHR</a>, <a href="TarConstants.html#LF_CONTIG">LF_CONTIG</a>, <a href="TarConstants.html#LF_DIR">LF_DIR</a>, <a href="TarConstants.html#LF_FIFO">LF_FIFO</a>, <a href="TarConstants.html#LF_GNUTYPE_LONGLINK">LF_GNUTYPE_LONGLINK</a>, <a href="TarConstants.html#LF_GNUTYPE_LONGNAME">LF_GNUTYPE_LONGNAME</a>, <a href="TarConstants.html#LF_GNUTYPE_SPARSE">LF_GNUTYPE_SPARSE</a>, <a href="TarConstants.html#LF_LINK">LF_LINK</a>, <a href="TarConstants.html#LF_NORMAL">LF_NORMAL</a>, <a href="TarConstants.html#LF_OLDNORM">LF_OLDNORM</a>, <a href="TarConstants.html#LF_PAX_EXTENDED_HEADER_LC">LF_PAX_EXTENDED_HEADER_LC</a>, <a href="TarConstants.html#LF_PAX_EXTENDED_HEADER_UC">LF_PAX_EXTENDED_HEADER_UC</a>, <a href="TarConstants.html#LF_PAX_GLOBAL_EXTENDED_HEADER">LF_PAX_GLOBAL_EXTENDED_HEADER</a>, <a href="TarConstants.html#LF_SYMLINK">LF_SYMLINK</a>, <a href="TarConstants.html#LONGNAMESLEN_GNU">LONGNAMESLEN_GNU</a>, <a href="TarConstants.html#MAGIC_OFFSET">MAGIC_OFFSET</a>, <a href="TarConstants.html#MAGIC_POSIX">MAGIC_POSIX</a>, <a href="TarConstants.html#MAGICLEN">MAGICLEN</a>, <a href="TarConstants.html#MAXID">MAXID</a>, <a href="TarConstants.html#MAXSIZE">MAXSIZE</a>, <a href="TarConstants.html#MODELEN">MODELEN</a>, <a href="TarConstants.html#MODTIMELEN">MODTIMELEN</a>, <a href="TarConstants.html#NAMELEN">NAMELEN</a>, <a href="TarConstants.html#OFFSETLEN_GNU">OFFSETLEN_GNU</a>, <a href="TarConstants.html#PAD2LEN_GNU">PAD2LEN_GNU</a>, <a href="TarConstants.html#PREFIXLEN">PREFIXLEN</a>, <a href="TarConstants.html#PURE_MAGICLEN">PURE_MAGICLEN</a>, <a href="TarConstants.html#REALSIZELEN_GNU">REALSIZELEN_GNU</a>, <a href="TarConstants.html#SIZELEN">SIZELEN</a>, <a href="TarConstants.html#SPARSELEN_GNU">SPARSELEN_GNU</a>, <a href="TarConstants.html#SPARSELEN_GNU_SPARSE">SPARSELEN_GNU_SPARSE</a>, <a href="TarConstants.html#TMAGIC">TMAGIC</a>, <a href="TarConstants.html#UIDLEN">UIDLEN</a>, <a href="TarConstants.html#UNAMELEN">UNAMELEN</a>, <a href="TarConstants.html#VERSION_GNU_SPACE">VERSION_GNU_SPACE</a>, <a href="TarConstants.html#VERSION_GNU_ZERO">VERSION_GNU_ZERO</a>, <a href="TarConstants.html#VERSION_OFFSET">VERSION_OFFSET</a>, <a href="TarConstants.html#VERSION_POSIX">VERSION_POSIX</a>, <a href="TarConstants.html#VERSIONLEN">VERSIONLEN</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(byte%5B%5D)" class="member-name-link">TarArchiveSparseEntry</a><wbr>(byte[]&nbsp;headerBuf)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct an entry from an archive's header bytes.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isExtended()" class="member-name-link">isExtended</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(byte[])">
<h3>TarArchiveSparseEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarArchiveSparseEntry</span><wbr><span class="parameters">(byte[]&nbsp;headerBuf)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Construct an entry from an archive's header bytes. File is set
 to null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerBuf</code> - The header bytes from a tar archive entry.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on unknown format</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isExtended()">
<h3>isExtended</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isExtended</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
