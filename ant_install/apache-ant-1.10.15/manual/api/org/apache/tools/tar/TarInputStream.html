<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>TarInputStream (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.tar, class: TarInputStream">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.tar</a></div>
<h1 title="Class TarInputStream" class="title">Class TarInputStream</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">java.io.InputStream</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">java.io.FilterInputStream</a>
<div class="inheritance">org.apache.tools.tar.TarInputStream</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarInputStream</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></span></div>
<div class="block">The TarInputStream reads a UNIX tar archive as an InputStream.
 methods are provided to position at each successive entry in
 the archive, and the read each entry as a normal input stream
 using read().</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</a></code></div>
<div class="col-second even-row-color"><code><a href="#buffer" class="member-name-link">buffer</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a></code></div>
<div class="col-second odd-row-color"><code><a href="#currEntry" class="member-name-link">currEntry</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#debug" class="member-name-link">debug</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected long</code></div>
<div class="col-second odd-row-color"><code><a href="#entryOffset" class="member-name-link">entryOffset</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected long</code></div>
<div class="col-second even-row-color"><code><a href="#entrySize" class="member-name-link">entrySize</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#hasHitEOF" class="member-name-link">hasHitEOF</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected byte[]</code></div>
<div class="col-second even-row-color"><code><a href="#oneBuf" class="member-name-link">oneBuf</a></code></div>
<div class="col-last even-row-color">
<div class="block">This contents of this array is not used at all in this class,
 it is only here to avoid repeated object creation during calls
 to the no-arg read method.</div>
</div>
<div class="col-first odd-row-color"><code>protected byte[]</code></div>
<div class="col-second odd-row-color"><code><a href="#readBuf" class="member-name-link">readBuf</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.FilterInputStream">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#in" title="class or interface in java.io" class="external-link">in</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream)" class="member-name-link">TarInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for TarInputStream.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,int)" class="member-name-link">TarInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor for TarInputStream.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,int,int)" class="member-name-link">TarInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize,
 int&nbsp;recordSize)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for TarInputStream.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,int,int,java.lang.String)" class="member-name-link">TarInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize,
 int&nbsp;recordSize,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor for TarInputStream.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,int,java.lang.String)" class="member-name-link">TarInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for TarInputStream.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,java.lang.String)" class="member-name-link">TarInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor for TarInputStream.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#available()" class="member-name-link">available</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the available data that can be read from the current
 entry in the archive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#canReadEntryData(org.apache.tools.tar.TarEntry)" class="member-name-link">canReadEntryData</a><wbr>(<a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;te)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether this class is able to read the given entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes this stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyEntryContents(java.io.OutputStream)" class="member-name-link">copyEntryContents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies the contents of the current tar archive entry directly into
 an output stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLongNameData()" class="member-name-link">getLongNameData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the next entry in this tar archive as longname data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNextEntry()" class="member-name-link">getNextEntry</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the next entry in this tar archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRecordSize()" class="member-name-link">getRecordSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the record size being used by this stream's TarBuffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mark(int)" class="member-name-link">mark</a><wbr>(int&nbsp;markLimit)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Since we do not support marking just yet, we do nothing.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#markSupported()" class="member-name-link">markSupported</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Since we do not support marking just yet, we return false.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read()" class="member-name-link">read</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Reads a byte from the current tar archive entry.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read(byte%5B%5D,int,int)" class="member-name-link">read</a><wbr>(byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;numToRead)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Reads bytes from the current tar archive entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reset()" class="member-name-link">reset</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Since we do not support marking just yet, we do nothing.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebug(boolean)" class="member-name-link">setDebug</a><wbr>(boolean&nbsp;debug)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the debugging flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#skip(long)" class="member-name-link">skip</a><wbr>(long&nbsp;numToSkip)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Skip bytes in the input buffer.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.FilterInputStream">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#read(byte%5B%5D)" title="class or interface in java.io" class="external-link">read</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.InputStream">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html#nullInputStream()" title="class or interface in java.io" class="external-link">nullInputStream</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html#readAllBytes()" title="class or interface in java.io" class="external-link">readAllBytes</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html#readNBytes(byte%5B%5D,int,int)" title="class or interface in java.io" class="external-link">readNBytes</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html#readNBytes(int)" title="class or interface in java.io" class="external-link">readNBytes</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html#skipNBytes(long)" title="class or interface in java.io" class="external-link">skipNBytes</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html#transferTo(java.io.OutputStream)" title="class or interface in java.io" class="external-link">transferTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="debug">
<h3>debug</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">debug</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="hasHitEOF">
<h3>hasHitEOF</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasHitEOF</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="entrySize">
<h3>entrySize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">entrySize</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="entryOffset">
<h3>entryOffset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">entryOffset</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="readBuf">
<h3>readBuf</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">readBuf</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="buffer">
<h3>buffer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</a></span>&nbsp;<span class="element-name">buffer</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="currEntry">
<h3>currEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a></span>&nbsp;<span class="element-name">currEntry</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="oneBuf">
<h3>oneBuf</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">oneBuf</span></div>
<div class="block">This contents of this array is not used at all in this class,
 it is only here to avoid repeated object creation during calls
 to the no-arg read method.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream)">
<h3>TarInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is)</span></div>
<div class="block">Constructor for TarInputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,java.lang.String)">
<h3>TarInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Constructor for TarInputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream to use</dd>
<dd><code>encoding</code> - name of the encoding to use for file names</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,int)">
<h3>TarInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize)</span></div>
<div class="block">Constructor for TarInputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream to use</dd>
<dd><code>blockSize</code> - the block size to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,int,java.lang.String)">
<h3>TarInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Constructor for TarInputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream to use</dd>
<dd><code>blockSize</code> - the block size to use</dd>
<dd><code>encoding</code> - name of the encoding to use for file names</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,int,int)">
<h3>TarInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize,
 int&nbsp;recordSize)</span></div>
<div class="block">Constructor for TarInputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream to use</dd>
<dd><code>blockSize</code> - the block size to use</dd>
<dd><code>recordSize</code> - the record size to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,int,int,java.lang.String)">
<h3>TarInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 int&nbsp;blockSize,
 int&nbsp;recordSize,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Constructor for TarInputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream to use</dd>
<dd><code>blockSize</code> - the block size to use</dd>
<dd><code>recordSize</code> - the record size to use</dd>
<dd><code>encoding</code> - name of the encoding to use for file names</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDebug(boolean)">
<h3>setDebug</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebug</span><wbr><span class="parameters">(boolean&nbsp;debug)</span></div>
<div class="block">Sets the debugging flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debug</code> - True to turn on debugging.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Closes this stream. Calls the TarBuffer's close() method.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html#close()" title="class or interface in java.lang" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRecordSize()">
<h3>getRecordSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getRecordSize</span>()</div>
<div class="block">Get the record size being used by this stream's TarBuffer.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The TarBuffer record size.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="available()">
<h3>available</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">available</span>()
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get the available data that can be read from the current
 entry in the archive. This does not indicate how much data
 is left in the entire archive, only in the current entry.
 This value is determined from the entry's size header field
 and the amount of data already read from the current entry.
 Integer.MAX_VALUE is returned in case more than Integer.MAX_VALUE
 bytes are left in the current entry in the archive.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#available()" title="class or interface in java.io" class="external-link">available</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Returns:</dt>
<dd>The number of available bytes for the current entry.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - for signature</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="skip(long)">
<h3>skip</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">skip</span><wbr><span class="parameters">(long&nbsp;numToSkip)</span>
          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Skip bytes in the input buffer. This skips bytes in the
 current entry's data, not the entire archive, and will
 stop at the end of the current entry's data if the number
 to skip extends beyond that point.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#skip(long)" title="class or interface in java.io" class="external-link">skip</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Parameters:</dt>
<dd><code>numToSkip</code> - The number of bytes to skip.</dd>
<dt>Returns:</dt>
<dd>the number actually skipped</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="markSupported()">
<h3>markSupported</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">markSupported</span>()</div>
<div class="block">Since we do not support marking just yet, we return false.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#markSupported()" title="class or interface in java.io" class="external-link">markSupported</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Returns:</dt>
<dd>False.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="mark(int)">
<h3>mark</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">mark</span><wbr><span class="parameters">(int&nbsp;markLimit)</span></div>
<div class="block">Since we do not support marking just yet, we do nothing.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#mark(int)" title="class or interface in java.io" class="external-link">mark</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Parameters:</dt>
<dd><code>markLimit</code> - The limit to mark.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="reset()">
<h3>reset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">reset</span>()</div>
<div class="block">Since we do not support marking just yet, we do nothing.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#reset()" title="class or interface in java.io" class="external-link">reset</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNextEntry()">
<h3>getNextEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a></span>&nbsp;<span class="element-name">getNextEntry</span>()
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get the next entry in this tar archive. This will skip
 over any remaining data in the current entry, if there
 is one, and place the input stream at the header of the
 next entry, and read the header and instantiate a new
 TarEntry from the header bytes and return that entry.
 If there are no more entries in the archive, null will
 be returned to indicate that the end of the archive has
 been reached.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The next TarEntry in the archive, or null.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLongNameData()">
<h3>getLongNameData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getLongNameData</span>()
                          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get the next entry in this tar archive as longname data.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The next entry in the archive as longname data, or null.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="read()">
<h3>read</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">read</span>()
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Reads a byte from the current tar archive entry.

 This method simply calls read(byte[], int, int).</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#read()" title="class or interface in java.io" class="external-link">read</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Returns:</dt>
<dd>The byte read, or -1 at EOF.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="read(byte[],int,int)">
<h3>read</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">read</span><wbr><span class="parameters">(byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;numToRead)</span>
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Reads bytes from the current tar archive entry.

 This method is aware of the boundaries of the current
 entry in the archive and will deal with them as if they
 were this stream's start and EOF.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html#read(byte%5B%5D,int,int)" title="class or interface in java.io" class="external-link">read</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" title="class or interface in java.io" class="external-link">FilterInputStream</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buf</code> - The buffer into which to place bytes read.</dd>
<dd><code>offset</code> - The offset at which to place bytes read.</dd>
<dd><code>numToRead</code> - The number of bytes to read.</dd>
<dt>Returns:</dt>
<dd>The number of bytes read, or -1 at EOF.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyEntryContents(java.io.OutputStream)">
<h3>copyEntryContents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyEntryContents</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</span>
                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies the contents of the current tar archive entry directly into
 an output stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - The OutputStream into which to write the entry's data.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="canReadEntryData(org.apache.tools.tar.TarEntry)">
<h3>canReadEntryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">canReadEntryData</span><wbr><span class="parameters">(<a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;te)</span></div>
<div class="block">Whether this class is able to read the given entry.

 <p>May return false if the current entry is a sparse file.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>te</code> - TarEntry</dd>
<dt>Returns:</dt>
<dd>boolean</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
