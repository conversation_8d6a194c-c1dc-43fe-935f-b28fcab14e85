<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>TarUtils (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.tar, class: TarUtils">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.tar</a></div>
<h1 title="Class TarUtils" class="title">Class TarUtils</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.tar.TarUtils</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarUtils</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">This class provides static utility methods to work with byte streams.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#computeCheckSum(byte%5B%5D)" class="member-name-link">computeCheckSum</a><wbr>(byte[]&nbsp;buf)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Compute the checksum of a tar entry header.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatCheckSumOctalBytes(long,byte%5B%5D,int,int)" class="member-name-link">formatCheckSumOctalBytes</a><wbr>(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Writes an octal value into a buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatLongOctalBytes(long,byte%5B%5D,int,int)" class="member-name-link">formatLongOctalBytes</a><wbr>(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Write an octal long integer into a buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatLongOctalOrBinaryBytes(long,byte%5B%5D,int,int)" class="member-name-link">formatLongOctalOrBinaryBytes</a><wbr>(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Write an long integer into a buffer as an octal string if this
 will fit, or as a binary number otherwise.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatNameBytes(java.lang.String,byte%5B%5D,int,int)" class="member-name-link">formatNameBytes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Copy a name into a buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatNameBytes(java.lang.String,byte%5B%5D,int,int,org.apache.tools.zip.ZipEncoding)" class="member-name-link">formatNameBytes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Copy a name into a buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatOctalBytes(long,byte%5B%5D,int,int)" class="member-name-link">formatOctalBytes</a><wbr>(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Write an octal integer into a buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatUnsignedOctalString(long,byte%5B%5D,int,int)" class="member-name-link">formatUnsignedOctalString</a><wbr>(long&nbsp;value,
 byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Fill buffer with unsigned octal number, padded with leading zeroes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parseBoolean(byte%5B%5D,int)" class="member-name-link">parseBoolean</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Parse a boolean byte from a buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parseName(byte%5B%5D,int,int)" class="member-name-link">parseName</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Parse an entry name from a buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parseName(byte%5B%5D,int,int,org.apache.tools.zip.ZipEncoding)" class="member-name-link">parseName</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Parse an entry name from a buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parseOctal(byte%5B%5D,int,int)" class="member-name-link">parseOctal</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Parse an octal string from a buffer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parseOctalOrBinary(byte%5B%5D,int,int)" class="member-name-link">parseOctalOrBinary</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Compute the value contained in a byte buffer.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="parseOctal(byte[],int,int)">
<h3>parseOctal</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">parseOctal</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Parse an octal string from a buffer.

 <p>Leading spaces are ignored.
 The buffer must contain a trailing space or NUL,
 and may contain an additional trailing space or NUL.</p>

 <p>The input buffer is allowed to contain all NULs,
 in which case the method returns 0L
 (this allows for missing fields).</p>

 <p>To work-around some tar implementations that insert a
 leading NUL this method returns 0 if it detects a leading NUL
 since Ant 1.9.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - The buffer from which to parse.</dd>
<dd><code>offset</code> - The offset into the buffer from which to parse.</dd>
<dd><code>length</code> - The maximum number of bytes to parse - must be at least 2 bytes.</dd>
<dt>Returns:</dt>
<dd>The long value of the octal string.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the trailing space/NUL is missing or if a invalid byte is detected.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseOctalOrBinary(byte[],int,int)">
<h3>parseOctalOrBinary</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">parseOctalOrBinary</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Compute the value contained in a byte buffer.  If the most
 significant bit of the first byte in the buffer is set, this
 bit is ignored and the rest of the buffer is interpreted as a
 binary number.  Otherwise, the buffer is interpreted as an
 octal number as per the parseOctal function above.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - The buffer from which to parse.</dd>
<dd><code>offset</code> - The offset into the buffer from which to parse.</dd>
<dd><code>length</code> - The maximum number of bytes to parse.</dd>
<dt>Returns:</dt>
<dd>The long value of the octal or binary string.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the trailing space/NUL is
 missing or an invalid byte is detected in an octal number, or
 if a binary number would exceed the size of a signed long
 64-bit integer.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseBoolean(byte[],int)">
<h3>parseBoolean</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">parseBoolean</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset)</span></div>
<div class="block">Parse a boolean byte from a buffer.
 Leading spaces and NUL are ignored.
 The buffer may contain trailing spaces or NULs.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - The buffer from which to parse.</dd>
<dd><code>offset</code> - The offset into the buffer from which to parse.</dd>
<dt>Returns:</dt>
<dd>The boolean value of the bytes.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if an invalid byte is detected.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseName(byte[],int,int)">
<h3>parseName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">parseName</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Parse an entry name from a buffer.
 Parsing stops when a NUL is found
 or the buffer length is reached.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - The buffer from which to parse.</dd>
<dd><code>offset</code> - The offset into the buffer from which to parse.</dd>
<dd><code>length</code> - The maximum number of bytes to parse.</dd>
<dt>Returns:</dt>
<dd>The entry name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseName(byte[],int,int,org.apache.tools.zip.ZipEncoding)">
<h3>parseName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">parseName</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</span>
                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Parse an entry name from a buffer.
 Parsing stops when a NUL is found
 or the buffer length is reached.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - The buffer from which to parse.</dd>
<dd><code>offset</code> - The offset into the buffer from which to parse.</dd>
<dd><code>length</code> - The maximum number of bytes to parse.</dd>
<dd><code>encoding</code> - name of the encoding to use for file names</dd>
<dt>Returns:</dt>
<dd>The entry name.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if decode fails</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatNameBytes(java.lang.String,byte[],int,int)">
<h3>formatNameBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">formatNameBytes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Copy a name into a buffer.
 Copies characters from the name into the buffer
 starting at the specified offset.
 If the buffer is longer than the name, the buffer
 is filled with trailing NULs.
 If the name is longer than the buffer,
 the output is truncated.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The header name from which to copy the characters.</dd>
<dd><code>buf</code> - The buffer where the name is to be stored.</dd>
<dd><code>offset</code> - The starting offset into the buffer</dd>
<dd><code>length</code> - The maximum number of header bytes to copy.</dd>
<dt>Returns:</dt>
<dd>The updated offset, i.e. offset + length</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatNameBytes(java.lang.String,byte[],int,int,org.apache.tools.zip.ZipEncoding)">
<h3>formatNameBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">formatNameBytes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length,
 <a href="../zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a>&nbsp;encoding)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copy a name into a buffer.
 Copies characters from the name into the buffer
 starting at the specified offset.
 If the buffer is longer than the name, the buffer
 is filled with trailing NULs.
 If the name is longer than the buffer,
 the output is truncated.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The header name from which to copy the characters.</dd>
<dd><code>buf</code> - The buffer where the name is to be stored.</dd>
<dd><code>offset</code> - The starting offset into the buffer</dd>
<dd><code>length</code> - The maximum number of header bytes to copy.</dd>
<dd><code>encoding</code> - name of the encoding to use for file names</dd>
<dt>Returns:</dt>
<dd>The updated offset, i.e. offset + length</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if encode fails</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatUnsignedOctalString(long,byte[],int,int)">
<h3>formatUnsignedOctalString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">formatUnsignedOctalString</span><wbr><span class="parameters">(long&nbsp;value,
 byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Fill buffer with unsigned octal number, padded with leading zeroes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - number to convert to octal - treated as unsigned</dd>
<dd><code>buffer</code> - destination buffer</dd>
<dd><code>offset</code> - starting offset in buffer</dd>
<dd><code>length</code> - length of buffer to fill</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the value will not fit in the buffer</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatOctalBytes(long,byte[],int,int)">
<h3>formatOctalBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">formatOctalBytes</span><wbr><span class="parameters">(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Write an octal integer into a buffer.

 Uses <a href="#formatUnsignedOctalString(long,byte%5B%5D,int,int)"><code>formatUnsignedOctalString(long, byte[], int, int)</code></a> to format
 the value as an octal string with leading zeros.
 The converted number is followed by space and NUL</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The value to write</dd>
<dd><code>buf</code> - The buffer to receive the output</dd>
<dd><code>offset</code> - The starting offset into the buffer</dd>
<dd><code>length</code> - The size of the output buffer</dd>
<dt>Returns:</dt>
<dd>The updated offset, i.e offset+length</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the value (and trailer) will not fit in the buffer</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatLongOctalBytes(long,byte[],int,int)">
<h3>formatLongOctalBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">formatLongOctalBytes</span><wbr><span class="parameters">(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Write an octal long integer into a buffer.

 Uses <a href="#formatUnsignedOctalString(long,byte%5B%5D,int,int)"><code>formatUnsignedOctalString(long, byte[], int, int)</code></a> to format
 the value as an octal string with leading zeros.
 The converted number is followed by a space.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The value to write as octal</dd>
<dd><code>buf</code> - The destinationbuffer.</dd>
<dd><code>offset</code> - The starting offset into the buffer.</dd>
<dd><code>length</code> - The length of the buffer</dd>
<dt>Returns:</dt>
<dd>The updated offset</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the value (and trailer) will not fit in the buffer</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatLongOctalOrBinaryBytes(long,byte[],int,int)">
<h3>formatLongOctalOrBinaryBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">formatLongOctalOrBinaryBytes</span><wbr><span class="parameters">(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Write an long integer into a buffer as an octal string if this
 will fit, or as a binary number otherwise.

 Uses <a href="#formatUnsignedOctalString(long,byte%5B%5D,int,int)"><code>formatUnsignedOctalString(long, byte[], int, int)</code></a> to format
 the value as an octal string with leading zeros.
 The converted number is followed by a space.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The value to write into the buffer.</dd>
<dd><code>buf</code> - The destination buffer.</dd>
<dd><code>offset</code> - The starting offset into the buffer.</dd>
<dd><code>length</code> - The length of the buffer.</dd>
<dt>Returns:</dt>
<dd>The updated offset.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the value (and trailer)
 will not fit in the buffer.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="formatCheckSumOctalBytes(long,byte[],int,int)">
<h3>formatCheckSumOctalBytes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">formatCheckSumOctalBytes</span><wbr><span class="parameters">(long&nbsp;value,
 byte[]&nbsp;buf,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block">Writes an octal value into a buffer.

 Uses <a href="#formatUnsignedOctalString(long,byte%5B%5D,int,int)"><code>formatUnsignedOctalString(long, byte[], int, int)</code></a> to format
 the value as an octal string with leading zeros.
 The converted number is followed by NUL and then space.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The value to convert</dd>
<dd><code>buf</code> - The destination buffer</dd>
<dd><code>offset</code> - The starting offset into the buffer.</dd>
<dd><code>length</code> - The size of the buffer.</dd>
<dt>Returns:</dt>
<dd>The updated value of offset, i.e. offset+length</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the value (and trailer) will not fit in the buffer</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="computeCheckSum(byte[])">
<h3>computeCheckSum</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">computeCheckSum</span><wbr><span class="parameters">(byte[]&nbsp;buf)</span></div>
<div class="block">Compute the checksum of a tar entry header.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buf</code> - The tar entry's header buffer.</dd>
<dt>Returns:</dt>
<dd>The computed checksum.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
