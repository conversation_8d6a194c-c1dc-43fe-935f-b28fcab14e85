<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ModifiedSelector (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors.modifiedselector, class: ModifiedSelector">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.selectors.modifiedselector</a></div>
<h1 title="Class ModifiedSelector" class="title">Class ModifiedSelector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseSelector</a>
<div class="inheritance"><a href="../BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseExtendSelector</a>
<div class="inheritance">org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code>, <code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code>, <code><a href="../../Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a></code>, <code><a href="../../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code>, <code><a href="../ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a></code>, <code><a href="../FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ModifiedSelector</span>
<span class="extends-implements">extends <a href="../BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a>
implements <a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>, <a href="../../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></span></div>
<div class="block"><p>Selector class that uses <i>Algorithm</i>, <i>Cache</i> and <i>Comparator</i>
 for its work.
 The <i>Algorithm</i> is used for computing a hashvalue for a file.
 The <i>Comparator</i> decides whether to select or not.
 The <i>Cache</i> stores the other value for comparison by the <i>Comparator</i>
 in a persistent manner.</p>

 <p>The ModifiedSelector is implemented as a <b>CoreSelector</b> and uses default
 values for all its attributes therefore the simplest example is</p><pre>
   &lt;copy todir="dest"&gt;
       &lt;fileset dir="src"&gt;
           &lt;modified/&gt;
       &lt;/fileset&gt;
   &lt;/copy&gt;
 </pre>

 <p>The same example rewritten as CoreSelector with setting the all values
 (same as defaults are) would be</p><pre>
   &lt;copy todir="dest"&gt;
       &lt;fileset dir="src"&gt;
           &lt;modified update="true"
                     cache="propertyfile"
                     algorithm="digest"
                     comparator="equal"&gt;
               &lt;param name="cache.cachefile"     value="cache.properties"/&gt;
               &lt;param name="algorithm.algorithm" value="MD5"/&gt;
           &lt;/modified&gt;
       &lt;/fileset&gt;
   &lt;/copy&gt;
 </pre>

 <p>And the same rewritten as CustomSelector would be</p><pre>
   &lt;copy todir="dest"&gt;
       &lt;fileset dir="src"&gt;
           &lt;custom class="org.apache.tools.ant.type.selectors.ModifiedSelector"&gt;
               &lt;param name="update"     value="true"/&gt;
               &lt;param name="cache"      value="propertyfile"/&gt;
               &lt;param name="algorithm"  value="digest"/&gt;
               &lt;param name="comparator" value="equal"/&gt;
               &lt;param name="cache.cachefile"     value="cache.properties"/&gt;
               &lt;param name="algorithm.algorithm" value="MD5"/&gt;
           &lt;/custom&gt;
       &lt;/fileset&gt;
   &lt;/copy&gt;
 </pre>

 <p>If you want to provide your own interface implementation you can do
 that via the *classname attributes. If the classes are not on Ant's core
 classpath, you will have to provide the path via nested &lt;classpath&gt;
 element, so that the selector can find the classes.</p><pre>
   &lt;modified cacheclassname="com.mycompany.MyCache"&gt;
       &lt;classpath&gt;
           &lt;pathelement location="lib/mycompany-antutil.jar"/&gt;
       &lt;/classpath&gt;
   &lt;/modified&gt;
 </pre>

 <p>All these three examples copy the files from <i>src</i> to <i>dest</i>
 using the ModifiedSelector. The ModifiedSelector uses the <i>PropertyfileCache
 </i>, the <i>DigestAlgorithm</i> and the <i>EqualComparator</i> for its
 work. The PropertyfileCache stores key-value-pairs in a simple java
 properties file. The filename is <i>cache.properties</i>. The <i>update</i>
 flag lets the selector update the values in the cache (and on first call
 creates the cache). The <i>DigestAlgorithm</i> computes a hashvalue using the
 java.security.MessageDigest class with its MD5-Algorithm and its standard
 provider. The new computed hashvalue and the stored one are compared by
 the <i>EqualComparator</i> which returns 'true' (more correct a value not
 equals zero (1)) if the values are not the same using simple String
 comparison.</p>

 <p>A useful scenario for this selector is inside a build environment
 for homepage generation (e.g. with <a href="https://forrest.apache.org/">
 Apache Forrest</a>).</p><pre>
 &lt;target name="generate-and-upload-site"&gt;
     &lt;echo&gt; generate the site using forrest &lt;/echo&gt;
     &lt;antcall target="site"/&gt;

     &lt;echo&gt; upload the changed files &lt;/echo&gt;
     &lt;ftp server="${ftp.server}" userid="${ftp.user}" password="${ftp.pwd}"&gt;
         &lt;fileset dir="htdocs/manual"&gt;
             &lt;modified/&gt;
         &lt;/fileset&gt;
     &lt;/ftp&gt;
 &lt;/target&gt;
 </pre><p>Here all <b>changed</b> files are uploaded to the server. The
 ModifiedSelector saves therefore much upload time.</p>


 <p>This selector uses reflection for setting the values of its three interfaces
 (using org.apache.tools.ant.IntrospectionHelper) therefore no special
 'configuration interfaces' has to be implemented by new caches, algorithms or
 comparators. All present <i>set</i>XX methods can be used. E.g. the DigestAlgorithm
 can use a specified provider for computing its value. For selecting this
 there is a <i>setProvider(String providername)</i> method. So you can use
 a nested <i>&lt;param name="algorithm.provider" value="MyProvider"/&gt;</i>.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ModifiedSelector.AlgorithmName.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.AlgorithmName</a></code></div>
<div class="col-last even-row-color">
<div class="block">The enumerated type for algorithm.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="ModifiedSelector.CacheName.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.CacheName</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The enumerated type for cache.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ModifiedSelector.ComparatorName.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.ComparatorName</a></code></div>
<div class="col-last even-row-color">
<div class="block">The enumerated type for algorithm.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.selectors.BaseExtendSelector">Fields inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="../BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></h3>
<code><a href="../BaseExtendSelector.html#parameters">parameters</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../DataType.html#checked">checked</a>, <a href="../../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ModifiedSelector</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Bean-Constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">addClasspath</a><wbr>(<a href="../../Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addParam(java.lang.String,java.lang.Object)" class="member-name-link">addParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Support for nested &lt;param&gt; tags.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addParam(org.apache.tools.ant.types.Parameter)" class="member-name-link">addParam</a><wbr>(<a href="../../Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a>&nbsp;parameter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Support for nested &lt;param&gt; tags.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildFinished</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that the last target has finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildStarted</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that a build has started.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure()" class="member-name-link">configure</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configures this Selector.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAlgorithm()" class="member-name-link">getAlgorithm</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the algorithm type to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Cache.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Cache</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCache()" class="member-name-link">getCache</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the cache type to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassLoader()" class="member-name-link">getClassLoader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns and initializes the classloader for this class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a><wbr>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getComparator()" class="member-name-link">getComparator</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the comparator type to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDelayUpdate()" class="member-name-link">getDelayUpdate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Getter for the delay update</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModified()" class="member-name-link">getModified</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Getter for the modified count</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelected(java.io.File,java.lang.String,java.io.File)" class="member-name-link">isSelected</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Implementation of BaseExtendSelector.isSelected().</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelected(org.apache.tools.ant.types.Resource)" class="member-name-link">isSelected</a><wbr>(<a href="../../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;resource)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Implementation of ResourceSelector.isSelected().</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected &lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String,java.lang.String,java.lang.Class)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends T&gt;&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads the specified class and initializes an object of that class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#messageLogged(org.apache.tools.ant.BuildEvent)" class="member-name-link">messageLogged</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals a message logging event.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#saveCache()" class="member-name-link">saveCache</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">save the cache file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAlgorithm(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector.AlgorithmName)" class="member-name-link">setAlgorithm</a><wbr>(<a href="ModifiedSelector.AlgorithmName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.AlgorithmName</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the algorithm type to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAlgorithmClass(java.lang.String)" class="member-name-link">setAlgorithmClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter for algorithmClass.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCache(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector.CacheName)" class="member-name-link">setCache</a><wbr>(<a href="ModifiedSelector.CacheName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.CacheName</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the cache type to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCacheClass(java.lang.String)" class="member-name-link">setCacheClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter for cacheClass.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassLoader(java.lang.ClassLoader)" class="member-name-link">setClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the used ClassLoader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComparator(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector.ComparatorName)" class="member-name-link">setComparator</a><wbr>(<a href="ModifiedSelector.ComparatorName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.ComparatorName</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the comparator type to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComparatorClass(java.lang.String)" class="member-name-link">setComparatorClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter for comparatorClass.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDelayUpdate(boolean)" class="member-name-link">setDelayUpdate</a><wbr>(boolean&nbsp;delayUpdate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter for the delay update</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModified(int)" class="member-name-link">setModified</a><wbr>(int&nbsp;modified)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter for the modified count</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParameters(org.apache.tools.ant.types.Parameter...)" class="member-name-link">setParameters</a><wbr>(<a href="../../Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a>...&nbsp;parameters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Defined in org.apache.tools.ant.types.Parameterizable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSeldirs(boolean)" class="member-name-link">setSeldirs</a><wbr>(boolean&nbsp;seldirs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Support for <i>seldirs</i> attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSelres(boolean)" class="member-name-link">setSelres</a><wbr>(boolean&nbsp;newValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Support for <i>selres</i> attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUpdate(boolean)" class="member-name-link">setUpdate</a><wbr>(boolean&nbsp;update)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Support for <i>update</i> attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetFinished</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that a target has finished.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetStarted</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that a target is starting.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskFinished</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that a task has finished.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskStarted</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that a task is starting.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override Object.toString().</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#tryToSetAParameter(java.lang.Object,java.lang.String,java.lang.String)" class="member-name-link">tryToSetAParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Try to set a value on an object using reflection.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#useParameter(org.apache.tools.ant.types.Parameter)" class="member-name-link">useParameter</a><wbr>(<a href="../../Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a>&nbsp;parameter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Support for nested <code>&lt;param name="" value=""/&gt;</code> tags.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#verifySettings()" class="member-name-link">verifySettings</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overrides BaseSelector.verifySettings().</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseExtendSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="../BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></h3>
<code><a href="../BaseExtendSelector.html#getParameters()">getParameters</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="../BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></h3>
<code><a href="../BaseSelector.html#getError()">getError</a>, <a href="../BaseSelector.html#setError(java.lang.String)">setError</a>, <a href="../BaseSelector.html#setError(java.lang.String,java.lang.Throwable)">setError</a>, <a href="../BaseSelector.html#validate()">validate</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../../DataType.html#circularReference()">circularReference</a>, <a href="../../DataType.html#clone()">clone</a>, <a href="../../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../../DataType.html#getRefid()">getRefid</a>, <a href="../../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../../DataType.html#isChecked()">isChecked</a>, <a href="../../DataType.html#isReference()">isReference</a>, <a href="../../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ModifiedSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ModifiedSelector</span>()</div>
<div class="block">Bean-Constructor.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="verifySettings()">
<h3>verifySettings</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">verifySettings</span>()</div>
<div class="block">Overrides BaseSelector.verifySettings().</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../BaseSelector.html#verifySettings()">verifySettings</a></code>&nbsp;in class&nbsp;<code><a href="../BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="configure()">
<h3>configure</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span>()</div>
<div class="block">Configures this Selector.
 Does this work only once per Selector object.
 <p>Because some problems while configuring from &lt;custom&gt;Selector
 the configuration is done in the following order:</p><ol>
 <li>collect the configuration data</li>
 <li>wait for the first isSelected() call</li>
 <li>set the default values</li>
 <li>set values for name pattern '*': update, cache, algorithm, comparator</li>
 <li>set values for name pattern '*.*: cache.cachefile, ...</li>
 </ol>
 <p>This configuration algorithm is needed because you don't know
 the order of arriving config-data. E.g. if you first set the
 <i>cache.cachefilename</i> and after that the <i>cache</i> itself,
 the default value for cachefilename is used, because setting the
 cache implies creating a new Cache instance - with its defaults.</p></div>
</div>
</section>
</li>
<li>
<section class="detail" id="loadClass(java.lang.String,java.lang.String,java.lang.Class)">
<h3>loadClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends T&gt;&nbsp;type)</span></div>
<div class="block">Loads the specified class and initializes an object of that class.
 Throws a BuildException using the given message if an error occurs during
 loading/instantiation or if the object is not from the given type.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - desired type</dd>
<dt>Parameters:</dt>
<dd><code>classname</code> - the classname</dd>
<dd><code>msg</code> - the message-part for the BuildException</dd>
<dd><code>type</code> - the type to check against</dd>
<dt>Returns:</dt>
<dd>a castable object</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected(org.apache.tools.ant.types.Resource)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="../../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;resource)</span></div>
<div class="block">Implementation of ResourceSelector.isSelected().</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../FileSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="../FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="../../resources/selectors/ResourceSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="../../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>resource</code> - The resource to check</dd>
<dt>Returns:</dt>
<dd>whether the resource is selected</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../../resources/selectors/ResourceSelector.html#isSelected(org.apache.tools.ant.types.Resource)"><code>ResourceSelector.isSelected(Resource)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected(java.io.File,java.lang.String,java.io.File)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Implementation of BaseExtendSelector.isSelected().</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../FileSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="../FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="../BaseExtendSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in class&nbsp;<code><a href="../BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>basedir</code> - as described in BaseExtendSelector</dd>
<dd><code>filename</code> - as described in BaseExtendSelector</dd>
<dd><code>file</code> - as described in BaseExtendSelector</dd>
<dt>Returns:</dt>
<dd>as described in BaseExtendSelector</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="saveCache()">
<h3>saveCache</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">saveCache</span>()</div>
<div class="block">save the cache file</div>
</div>
</section>
</li>
<li>
<section class="detail" id="setAlgorithmClass(java.lang.String)">
<h3>setAlgorithmClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAlgorithmClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span></div>
<div class="block">Setter for algorithmClass.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setComparatorClass(java.lang.String)">
<h3>setComparatorClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComparatorClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span></div>
<div class="block">Setter for comparatorClass.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCacheClass(java.lang.String)">
<h3>setCacheClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCacheClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span></div>
<div class="block">Setter for cacheClass.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUpdate(boolean)">
<h3>setUpdate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUpdate</span><wbr><span class="parameters">(boolean&nbsp;update)</span></div>
<div class="block">Support for <i>update</i> attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>update</code> - new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSeldirs(boolean)">
<h3>setSeldirs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSeldirs</span><wbr><span class="parameters">(boolean&nbsp;seldirs)</span></div>
<div class="block">Support for <i>seldirs</i> attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>seldirs</code> - new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSelres(boolean)">
<h3>setSelres</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSelres</span><wbr><span class="parameters">(boolean&nbsp;newValue)</span></div>
<div class="block">Support for <i>selres</i> attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newValue</code> - the new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getModified()">
<h3>getModified</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getModified</span>()</div>
<div class="block">Getter for the modified count</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>modified count</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModified(int)">
<h3>setModified</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModified</span><wbr><span class="parameters">(int&nbsp;modified)</span></div>
<div class="block">Setter for the modified count</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>modified</code> - count</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDelayUpdate()">
<h3>getDelayUpdate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDelayUpdate</span>()</div>
<div class="block">Getter for the delay update</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if we should delay for performance</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDelayUpdate(boolean)">
<h3>setDelayUpdate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDelayUpdate</span><wbr><span class="parameters">(boolean&nbsp;delayUpdate)</span></div>
<div class="block">Setter for the delay update</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delayUpdate</code> - true if we should delay for performance</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addClasspath(org.apache.tools.ant.types.Path)">
<h3>addClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addClasspath</span><wbr><span class="parameters">(<a href="../../Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Add the classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - the classpath</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getClassLoader()">
<h3>getClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getClassLoader</span>()</div>
<div class="block">Returns and initializes the classloader for this class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classloader</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClassLoader(java.lang.ClassLoader)">
<h3>setClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Set the used ClassLoader.
 If you invoke this selector by API (e.g. inside some testcases) the selector
 will use a different classloader for loading the interface implementations than
 the caller. Therefore you will get a ClassCastException if you get the
 implementations from the selector and cast them.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>loader</code> - the ClassLoader to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addParam(java.lang.String,java.lang.Object)">
<h3>addParam</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Support for nested &lt;param&gt; tags.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>key</code> - the key of the parameter</dd>
<dd><code>value</code> - the value of the parameter</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addParam(org.apache.tools.ant.types.Parameter)">
<h3>addParam</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addParam</span><wbr><span class="parameters">(<a href="../../Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a>&nbsp;parameter)</span></div>
<div class="block">Support for nested &lt;param&gt; tags.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parameter</code> - the parameter object</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setParameters(org.apache.tools.ant.types.Parameter...)">
<h3>setParameters</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParameters</span><wbr><span class="parameters">(<a href="../../Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a>...&nbsp;parameters)</span></div>
<div class="block">Defined in org.apache.tools.ant.types.Parameterizable.
 Overwrite implementation in superclass because only special
 parameters are valid.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../Parameterizable.html#setParameters(org.apache.tools.ant.types.Parameter...)">setParameters</a></code>&nbsp;in interface&nbsp;<code><a href="../../Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../BaseExtendSelector.html#setParameters(org.apache.tools.ant.types.Parameter...)">setParameters</a></code>&nbsp;in class&nbsp;<code><a href="../BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>parameters</code> - the parameters to set.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#addParam(java.lang.String,java.lang.Object)"><code>addParam(String,Object)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="useParameter(org.apache.tools.ant.types.Parameter)">
<h3>useParameter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">useParameter</span><wbr><span class="parameters">(<a href="../../Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a>&nbsp;parameter)</span></div>
<div class="block">Support for nested <code>&lt;param name="" value=""/&gt;</code> tags.
 Parameter named <i>cache</i>, <i>algorithm</i>,
 <i>comparator</i> or <i>update</i> are mapped to
 the respective set-Method.
 Parameter which names starts with <i>cache.</i> or
 <i>algorithm.</i> or <i>comparator.</i> are tried
 to set on the appropriate object via its set-methods.
 Other parameters are invalid and an BuildException will
 be thrown.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parameter</code> - Key and value as parameter object</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="tryToSetAParameter(java.lang.Object,java.lang.String,java.lang.String)">
<h3>tryToSetAParameter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">tryToSetAParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Try to set a value on an object using reflection.
 Helper method for easier access to IntrospectionHelper.setAttribute().</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>obj</code> - the object on which the attribute should be set</dd>
<dd><code>name</code> - the attributename</dd>
<dd><code>value</code> - the new value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Override Object.toString().</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../DataType.html#toString()">toString</a></code>&nbsp;in class&nbsp;<code><a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Returns:</dt>
<dd>information about this selector</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="buildFinished(org.apache.tools.ant.BuildEvent)">
<h3>buildFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildFinished</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that the last target has finished.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="targetFinished(org.apache.tools.ant.BuildEvent)">
<h3>targetFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetFinished</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a target has finished.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="taskFinished(org.apache.tools.ant.BuildEvent)">
<h3>taskFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskFinished</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a task has finished.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="buildStarted(org.apache.tools.ant.BuildEvent)">
<h3>buildStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildStarted</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a build has started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="targetStarted(org.apache.tools.ant.BuildEvent)">
<h3>targetStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetStarted</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a target is starting.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getTarget()"><code>BuildEvent.getTarget()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="taskStarted(org.apache.tools.ant.BuildEvent)">
<h3>taskStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskStarted</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a task is starting.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getTask()"><code>BuildEvent.getTask()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="messageLogged(org.apache.tools.ant.BuildEvent)">
<h3>messageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">messageLogged</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals a message logging event.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - received BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getMessage()"><code>BuildEvent.getMessage()</code></a></li>
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
<li><a href="../../../BuildEvent.html#getPriority()"><code>BuildEvent.getPriority()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCache()">
<h3>getCache</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Cache.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Cache</a></span>&nbsp;<span class="element-name">getCache</span>()</div>
<div class="block">Get the cache type to use.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the enumerated cache type</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCache(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector.CacheName)">
<h3>setCache</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCache</span><wbr><span class="parameters">(<a href="ModifiedSelector.CacheName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.CacheName</a>&nbsp;name)</span></div>
<div class="block">Set the cache type to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - an enumerated cache type.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getAlgorithm()">
<h3>getAlgorithm</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a></span>&nbsp;<span class="element-name">getAlgorithm</span>()</div>
<div class="block">Get the algorithm type to use.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the enumerated algorithm type</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setAlgorithm(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector.AlgorithmName)">
<h3>setAlgorithm</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAlgorithm</span><wbr><span class="parameters">(<a href="ModifiedSelector.AlgorithmName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.AlgorithmName</a>&nbsp;name)</span></div>
<div class="block">Set the algorithm type to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - an enumerated algorithm type.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getComparator()">
<h3>getComparator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getComparator</span>()</div>
<div class="block">Get the comparator type to use.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the enumerated comparator type</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setComparator(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector.ComparatorName)">
<h3>setComparator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComparator</span><wbr><span class="parameters">(<a href="ModifiedSelector.ComparatorName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.ComparatorName</a>&nbsp;name)</span></div>
<div class="block">Set the comparator type to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - an enumerated comparator type.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
