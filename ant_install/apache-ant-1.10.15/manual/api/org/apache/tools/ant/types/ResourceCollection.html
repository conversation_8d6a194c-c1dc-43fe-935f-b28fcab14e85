<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ResourceCollection (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, interface: ResourceCollection">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Interface ResourceCollection" class="title">Interface ResourceCollection</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></dd>
</dl>
<dl class="notes">
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="resources/AppendableResourceCollection.html" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="resources/AbstractClasspathResource.html" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource</a></code>, <code><a href="resources/AbstractResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">AbstractResourceCollectionWrapper</a></code>, <code><a href="resources/AllButFirst.html" title="class in org.apache.tools.ant.types.resources">AllButFirst</a></code>, <code><a href="resources/AllButLast.html" title="class in org.apache.tools.ant.types.resources">AllButLast</a></code>, <code><a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></code>, <code><a href="resources/ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a></code>, <code><a href="resources/Archives.html" title="class in org.apache.tools.ant.types.resources">Archives</a></code>, <code><a href="resources/BaseResourceCollectionContainer.html" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionContainer</a></code>, <code><a href="resources/BaseResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionWrapper</a></code>, <code><a href="resources/BCFileSet.html" title="class in org.apache.tools.ant.types.resources">BCFileSet</a></code>, <code><a href="resources/BZip2Resource.html" title="class in org.apache.tools.ant.types.resources">BZip2Resource</a></code>, <code><a href="optional/depend/ClassfileSet.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet</a></code>, <code><a href="resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</a></code>, <code><a href="../taskdefs/Concat.html" title="class in org.apache.tools.ant.taskdefs">Concat</a></code>, <code><a href="resources/ContentTransformingResource.html" title="class in org.apache.tools.ant.types.resources">ContentTransformingResource</a></code>, <code><a href="resources/Difference.html" title="class in org.apache.tools.ant.types.resources">Difference</a></code>, <code><a href="DirSet.html" title="class in org.apache.tools.ant.types">DirSet</a></code>, <code><a href="FileList.html" title="class in org.apache.tools.ant.types">FileList</a></code>, <code><a href="resources/FileResource.html" title="class in org.apache.tools.ant.types.resources">FileResource</a></code>, <code><a href="resources/Files.html" title="class in org.apache.tools.ant.types.resources">Files</a></code>, <code><a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code>, <code><a href="resources/First.html" title="class in org.apache.tools.ant.types.resources">First</a></code>, <code><a href="resources/GZipResource.html" title="class in org.apache.tools.ant.types.resources">GZipResource</a></code>, <code><a href="resources/Intersect.html" title="class in org.apache.tools.ant.types.resources">Intersect</a></code>, <code><a href="resources/JavaConstantResource.html" title="class in org.apache.tools.ant.types.resources">JavaConstantResource</a></code>, <code><a href="../taskdefs/Javadoc.TagArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.TagArgument</a></code>, <code><a href="resources/JavaResource.html" title="class in org.apache.tools.ant.types.resources">JavaResource</a></code>, <code><a href="resources/Last.html" title="class in org.apache.tools.ant.types.resources">Last</a></code>, <code><a href="resources/LazyResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">LazyResourceCollectionWrapper</a></code>, <code><a href="../taskdefs/optional/extension/LibFileSet.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">LibFileSet</a></code>, <code><a href="resources/LogOutputResource.html" title="class in org.apache.tools.ant.types.resources">LogOutputResource</a></code>, <code><a href="resources/MappedResource.html" title="class in org.apache.tools.ant.types.resources">MappedResource</a></code>, <code><a href="resources/MappedResourceCollection.html" title="class in org.apache.tools.ant.types.resources">MappedResourceCollection</a></code>, <code><a href="resources/MultiRootFileSet.html" title="class in org.apache.tools.ant.types.resources">MultiRootFileSet</a></code>, <code><a href="Path.html" title="class in org.apache.tools.ant.types">Path</a></code>, <code><a href="Path.PathElement.html" title="class in org.apache.tools.ant.types">Path.PathElement</a></code>, <code><a href="resources/PropertyResource.html" title="class in org.apache.tools.ant.types.resources">PropertyResource</a></code>, <code><a href="PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a></code>, <code><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code>, <code><a href="resources/ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></code>, <code><a href="resources/ResourceList.html" title="class in org.apache.tools.ant.types.resources">ResourceList</a></code>, <code><a href="resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</a></code>, <code><a href="resources/Restrict.html" title="class in org.apache.tools.ant.types.resources">Restrict</a></code>, <code><a href="resources/SizeLimitCollection.html" title="class in org.apache.tools.ant.types.resources">SizeLimitCollection</a></code>, <code><a href="resources/Sort.html" title="class in org.apache.tools.ant.types.resources">Sort</a></code>, <code><a href="resources/StringResource.html" title="class in org.apache.tools.ant.types.resources">StringResource</a></code>, <code><a href="../taskdefs/Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarFileSet</a></code>, <code><a href="TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a></code>, <code><a href="resources/TarResource.html" title="class in org.apache.tools.ant.types.resources">TarResource</a></code>, <code><a href="resources/Tokens.html" title="class in org.apache.tools.ant.types.resources">Tokens</a></code>, <code><a href="resources/Union.html" title="class in org.apache.tools.ant.types.resources">Union</a></code>, <code><a href="resources/URLResource.html" title="class in org.apache.tools.ant.types.resources">URLResource</a></code>, <code><a href="optional/xz/XzResource.html" title="class in org.apache.tools.ant.types.optional.xz">XzResource</a></code>, <code><a href="ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a></code>, <code><a href="resources/ZipResource.html" title="class in org.apache.tools.ant.types.resources">ZipResource</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">ResourceCollection</span><span class="extends-implements">
extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</span></div>
<div class="block">Interface describing a collection of Resources.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab5" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab5', 3)" class="table-tab">Default Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#isEmpty()" class="member-name-link">isEmpty</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">Learn whether this <a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a> is empty.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#isFilesystemOnly()" class="member-name-link">isFilesystemOnly</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Indicate whether this ResourceCollection is composed entirely of
 Resources accessible via local filesystem conventions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#size()" class="member-name-link">size</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Learn the number of contained Resources.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a><wbr>&lt;? extends <a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#stream()" class="member-name-link">stream</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">Return a <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> over this <a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a>.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#iterator()" title="class or interface in java.lang" class="external-link">iterator</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="size()">
<h3>size</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">int</span>&nbsp;<span class="element-name">size</span>()</div>
<div class="block">Learn the number of contained Resources.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>number of elements as int.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isFilesystemOnly()">
<h3>isFilesystemOnly</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">isFilesystemOnly</span>()</div>
<div class="block">Indicate whether this ResourceCollection is composed entirely of
 Resources accessible via local filesystem conventions. If true, all
 resources returned from this collection should respond with a
 <a href="resources/FileProvider.html" title="interface in org.apache.tools.ant.types.resources"><code>FileProvider</code></a> when asked via
 <a href="Resource.html#as(java.lang.Class)"><code>Resource.as(java.lang.Class&lt;T&gt;)</code></a>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether this is a filesystem-only resource collection.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="stream()">
<h3>stream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link">Stream</a>&lt;? extends <a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</span>&nbsp;<span class="element-name">stream</span>()</div>
<div class="block">Return a <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> over this <a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/stream/Stream.html" title="class or interface in java.util.stream" class="external-link"><code>Stream</code></a> of <a href="Resource.html" title="class in org.apache.tools.ant.types"><code>Resource</code></a></dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isEmpty()">
<h3>isEmpty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEmpty</span>()</div>
<div class="block">Learn whether this <a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a> is empty.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
