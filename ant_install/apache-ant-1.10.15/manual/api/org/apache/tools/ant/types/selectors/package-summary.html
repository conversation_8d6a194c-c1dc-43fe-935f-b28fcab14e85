<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.types.selectors (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.types.selectors" class="title">Package org.apache.tools.ant.types.selectors</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.types.selectors</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.types</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="modifiedselector/package-summary.html">org.apache.tools.ant.types.selectors.modifiedselector</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../mappers/package-summary.html">org.apache.tools.ant.types.mappers</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../optional/package-summary.html">org.apache.tools.ant.types.optional</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../resolver/package-summary.html">org.apache.tools.ant.types.resolver</a></div>
<div class="col-last even-row-color">
<div class="block">Ant integration with xml-commons resolver.</div>
</div>
<div class="col-first odd-row-color"><a href="../resources/package-summary.html">org.apache.tools.ant.types.resources</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../spi/package-summary.html">org.apache.tools.ant.types.spi</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is the a base class a container of selectors - it does
 not need do be a selector itself.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This selector has a collection of other selectors, all of which have to
 select a file in order for this selector to select it.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Convenience base class for all selectors accessed through ExtendSelector.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A convenience base class that you can subclass Selectors from.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is the base class for selectors that can contain other selectors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files based on a regular expression.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files/resources based on whether they contain a
 particular string.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that chooses files based on their last modified date.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DateSelector.TimeComparisons.html" title="class in org.apache.tools.ant.types.selectors">DateSelector.TimeComparisons</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values for time comparison.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files based on whether they are newer than
 a matching file in another directory tree.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files based on the how deep in the directory
 tree they are.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This selector selects files against a mapped set of target files, selecting
 all those files which are different.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExecutableSelector.html" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects executable files.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">This is the interface to be used by all custom selectors, those that are
 called through the &lt;custom&gt; tag.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Selector that selects files by forwarding the request on to other classes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files based on the filename.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">This is the interface to be used by all selectors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This selector is here just to shake up your thinking a bit.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MappingSelector.html" title="class in org.apache.tools.ant.types.selectors">MappingSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A mapping selector is an abstract class adding mapping support to the base
 selector</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This selector has a collection of other selectors.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This selector has one other selectors whose meaning it inverts.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This selector has a collection of other selectors, any of which have to
 select a file in order for this selector to select it.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="OwnedBySelector.html" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects files based on their owner.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PosixGroupSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects files based on their POSIX group.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PosixPermissionsSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects files based on their POSIX permissions.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files based on whether they appear in another
 directory tree.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PresentSelector.FilePresence.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values for indicating where a file's
 presence is allowed and required.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ReadableSelector.html" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects readable files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">This is the interface for selectors that can contain other selectors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">An interface used to describe the actions required by any type of
 directory scanner that supports Selectors.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SelectorUtils.html" title="class in org.apache.tools.ant.types.selectors">SelectorUtils</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a utility class used by selectors and DirectoryScanner.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This selector just holds one other selector and forwards all
 requests to it.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SignedSelector.html" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Selector that chooses files based on whether they are signed or not.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that filters files based on their size.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SizeSelector.ByteUnits.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector.ByteUnits</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values for units.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SizeSelector.SizeComparisons.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector.SizeComparisons</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values for size comparison.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SymlinkSelector.html" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects symbolic links.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TokenizedPath.html" title="class in org.apache.tools.ant.types.selectors">TokenizedPath</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Container for a path that has been split into its components.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenizedPattern.html" title="class in org.apache.tools.ant.types.selectors">TokenizedPattern</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Provides reusable path pattern matching.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Selector that selects a certain kind of file: directory or regular.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TypeSelector.FileType.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector.FileType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values for types of file</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="WritableSelector.html" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A selector that selects writable files.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</body>
</html>
