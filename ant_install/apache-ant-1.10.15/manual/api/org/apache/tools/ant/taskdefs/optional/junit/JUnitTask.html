<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>JUnitTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: JUnitTask">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class JUnitTask" class="title">Class JUnitTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.JUnitTask</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JUnitTask</span>
<span class="extends-implements">extends <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Runs JUnit tests.

 <p>JUnit is a framework to create unit tests. It has been initially
 created by Erich Gamma and Kent Beck.  JUnit can be found at <a href="https://www.junit.org">https://www.junit.org</a>.

 <p><code>JUnitTask</code> can run a single specific
 <code>JUnitTest</code> using the <code>test</code> element.</p>
 For example, the following target <pre>
   &lt;target name="test-int-chars" depends="jar-test"&gt;
       &lt;echo message="testing international characters"/&gt;
       &lt;junit printsummary="no" haltonfailure="yes" fork="false"&gt;
           &lt;classpath refid="classpath"/&gt;
           &lt;formatter type="plain" usefile="false" /&gt;
           &lt;test name="org.apache.ecs.InternationalCharTest" /&gt;
       &lt;/junit&gt;
   &lt;/target&gt;
 </pre>
 <p>runs a single junit test
 (<code>org.apache.ecs.InternationalCharTest</code>) in the current
 VM using the path with id <code>classpath</code> as classpath and
 presents the results formatted using the standard
 <code>plain</code> formatter on the command line.</p>

 <p>This task can also run batches of tests.  The
 <code>batchtest</code> element creates a <code>BatchTest</code>
 based on a fileset.  This allows, for example, all classes found in
 directory to be run as testcases.</p>

 <p>For example,</p><pre>
 &lt;target name="run-tests" depends="dump-info,compile-tests" if="junit.present"&gt;
   &lt;junit printsummary="no" haltonfailure="yes" fork="${junit.fork}"&gt;
     &lt;jvmarg value="-classic"/&gt;
     &lt;classpath refid="tests-classpath"/&gt;
     &lt;sysproperty key="build.tests.value" value="${build.tests.value}"/&gt;
     &lt;formatter type="brief" usefile="false" /&gt;
     &lt;batchtest&gt;
       &lt;fileset dir="${tests.dir}"&gt;
         &lt;include name="**&#047;*Test*" /&gt;
       &lt;/fileset&gt;
     &lt;/batchtest&gt;
   &lt;/junit&gt;
 &lt;/target&gt;
 </pre>
 <p>this target finds any classes with a <code>test</code> directory
 anywhere in their path (under the top <code>${tests.dir}</code>, of
 course) and creates <code>JUnitTest</code>'s for each one.</p>

 <p>Of course, <code>&lt;junit&gt;</code> and
 <code>&lt;batch&gt;</code> elements can be combined for more
 complex tests. For an example, see the ant <code>build.xml</code>
 target <code>run-tests</code> (the second example is an edited
 version).</p>

 <p>To spawn a new Java VM to prevent interferences between
 different testcases, you need to enable <code>fork</code>.  A
 number of attributes and elements allow you to set up how this JVM
 runs.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTest</code></a></li>
<li><a href="BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>BatchTest</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="JUnitTask.ForkMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a></code></div>
<div class="col-last even-row-color">
<div class="block">These are the different forking options</div>
</div>
<div class="col-first odd-row-color"><code>protected static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="JUnitTask.JUnitLogOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A stream handler for handling the junit task.</div>
</div>
<div class="col-first even-row-color"><code>protected static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="JUnitTask.JUnitLogStreamHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</a></code></div>
<div class="col-last even-row-color">
<div class="block">A log stream handler for junit.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="JUnitTask.SummaryAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Print summary enumeration values.</div>
</div>
<div class="col-first even-row-color"><code>protected static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="JUnitTask.TestResultHolder.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a></code></div>
<div class="col-last even-row-color">
<div class="block">A value class that contains the result of a test.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ENABLE_TESTLISTENER_EVENTS" class="member-name-link">ENABLE_TESTLISTENER_EVENTS</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of magic property that enables test listener events.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#TESTLISTENER_PREFIX" class="member-name-link">TESTLISTENER_PREFIX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JUnitTask</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new JUnitRunner and enables fork of a new Java VM.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#actOnTestResult(int,boolean,org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String)" class="member-name-link">actOnTestResult</a><wbr>(int&nbsp;exitValue,
 boolean&nbsp;wasKilled,
 <a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#actOnTestResult(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TestResultHolder,org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String)" class="member-name-link">actOnTestResult</a><wbr>(<a href="JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a>&nbsp;result,
 <a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addAssertions(org.apache.tools.ant.types.Assertions)" class="member-name-link">addAssertions</a><wbr>(<a href="../../../types/Assertions.html" title="class in org.apache.tools.ant.types">Assertions</a>&nbsp;asserts)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assertions to enable in this program (if fork=true)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addClasspathEntry(java.lang.String)" class="member-name-link">addClasspathEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resource)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Search for the given resource and add the directory or archive
 that contains it to the classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredSysproperty(org.apache.tools.ant.types.Environment.Variable)" class="member-name-link">addConfiguredSysproperty</a><wbr>(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a system property that tests can access.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addEnv(org.apache.tools.ant.types.Environment.Variable)" class="member-name-link">addEnv</a><wbr>(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;var)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an environment variable; used when forking.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement)" class="member-name-link">addFormatter</a><wbr>(<a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a>&nbsp;fe)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new formatter to all tests of this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addSysproperty(org.apache.tools.ant.types.Environment.Variable)" class="member-name-link">addSysproperty</a><wbr>(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.6</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSyspropertyset(org.apache.tools.ant.types.PropertySet)" class="member-name-link">addSyspropertyset</a><wbr>(<a href="../../../types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a>&nbsp;sysp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of properties that will be used as system properties
 that tests can access.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTest(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)" class="member-name-link">addTest</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new single testcase.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a><wbr>&lt;<a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#allTests()" class="member-name-link">allTests</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">return an enumeration listing each test, then each batchtest</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanup()" class="member-name-link">cleanup</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Removes resources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createBatchTest()" class="member-name-link">createBatchTest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of tests based on pattern matching.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createBootclasspath()" class="member-name-link">createBootclasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to the bootclasspath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds path to classpath used for tests.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createJvmarg()" class="member-name-link">createJvmarg</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a JVM argument; ignored if not forking.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModulepath()" class="member-name-link">createModulepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a path to the modulepath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createPermissions()" class="member-name-link">createPermissions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the permissions for the application run inside the same JVM.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createUpgrademodulepath()" class="member-name-link">createUpgrademodulepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a path to the upgrademodulepath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWatchdog()" class="member-name-link">createWatchdog</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs the testcase.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute(java.util.List)" class="member-name-link">execute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&nbsp;testList)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Execute a list of tests in a single forked Java VM.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute(java.util.List,int)" class="member-name-link">execute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&nbsp;testList,
 int&nbsp;thread)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Execute a list of tests in a single forked Java VM.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)" class="member-name-link">execute</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;arg)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the tests.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,int)" class="member-name-link">execute</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;arg,
 int&nbsp;thread)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the tests.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeOrQueue(java.util.Enumeration,boolean)" class="member-name-link">executeOrQueue</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&nbsp;testList,
 boolean&nbsp;runIndividual)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes all tests that don't need to be forked (or all tests
 if the runIndividual argument is true.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCommandline()" class="member-name-link">getCommandline</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the command line used to run the tests.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultOutput()" class="member-name-link">getDefaultOutput</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the default output for a formatter.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEnableTestListenerEvents()" class="member-name-link">getEnableTestListenerEvents</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether test listener events shall be generated.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a><wbr>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIndividualTests()" class="member-name-link">getIndividualTests</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Merge all individual tests from the batchtest with all individual tests
 and return an enumeration over all <code>JUnitTest</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutput(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement,org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)" class="member-name-link">getOutput</a><wbr>(<a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a>&nbsp;fe,
 <a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If the formatter sends output to a file, return that file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorFlush(java.lang.String)" class="member-name-link">handleErrorFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorOutput(java.lang.String)" class="member-name-link">handleErrorOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleFlush(java.lang.String)" class="member-name-link">handleFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pass output sent to System.out to the TestRunner so it can
 collect ot for the formatters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleInput(byte%5B%5D,int,int)" class="member-name-link">handleInput</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle an input request by this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleOutput(java.lang.String)" class="member-name-link">handleOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pass output sent to System.out to the TestRunner so it can
 collect it for the formatters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds the jars or directories containing Ant, this task and
 JUnit to the classpath - this should make the forked JVM work
 without having to specify them directly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCloneVm(boolean)" class="member-name-link">setCloneVm</a><wbr>(boolean&nbsp;cloneVm)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set, system properties will be copied to the cloned VM - as
 well as the bootclasspath unless you have explicitly specified
 a bootclasspath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDir(java.io.File)" class="member-name-link">setDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The directory to invoke the VM in.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEnableTestListenerEvents(boolean)" class="member-name-link">setEnableTestListenerEvents</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether test listener events shall be generated.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorProperty(java.lang.String)" class="member-name-link">setErrorProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Property to set to "true" if there is a error in a test.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailureProperty(java.lang.String)" class="member-name-link">setFailureProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Property to set to "true" if there is a failure in a test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFiltertrace(boolean)" class="member-name-link">setFiltertrace</a><wbr>(boolean&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, smartly filter the stack frames of
 JUnit errors and failures before reporting them.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFork(boolean)" class="member-name-link">setFork</a><wbr>(boolean&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, JVM should be forked for each test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForkMode(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode)" class="member-name-link">setForkMode</a><wbr>(<a href="JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a>&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the behavior when <a href="#setFork(boolean)"><code>fork</code></a> fork has been enabled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHaltonerror(boolean)" class="member-name-link">setHaltonerror</a><wbr>(boolean&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, stop the build process when there is an error in a test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHaltonfailure(boolean)" class="member-name-link">setHaltonfailure</a><wbr>(boolean&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, stop the build process if a test fails
 (errors are considered failures as well).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeantruntime(boolean)" class="member-name-link">setIncludeantruntime</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, include ant.jar, optional.jar and junit.jar in the forked VM.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJvm(java.lang.String)" class="member-name-link">setJvm</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The command used to invoke the Java Virtual Machine,
 default is 'java'.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogFailedTests(boolean)" class="member-name-link">setLogFailedTests</a><wbr>(boolean&nbsp;logFailedTests)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, write a single "FAILED" line for failed tests to Ant's
 log system.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxmemory(java.lang.String)" class="member-name-link">setMaxmemory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;max)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the maximum memory to be used by all forked JVMs.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewenvironment(boolean)" class="member-name-link">setNewenvironment</a><wbr>(boolean&nbsp;newenv)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, use a new environment when forked.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputToFormatters(boolean)" class="member-name-link">setOutputToFormatters</a><wbr>(boolean&nbsp;outputToFormatters)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, send any output generated by tests to the formatters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPrintsummary(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.SummaryAttribute)" class="member-name-link">setPrintsummary</a><wbr>(<a href="JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, print one-line statistics for each test, or "withOutAndErr"
 to also show standard output and error.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setReloading(boolean)" class="member-name-link">setReloading</a><wbr>(boolean&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, force ant to re-classload all classes for each JUnit TestCase</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShowOutput(boolean)" class="member-name-link">setShowOutput</a><wbr>(boolean&nbsp;showOutput)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, send any output generated by tests to Ant's logging system
 as well as to the formatters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTempdir(java.io.File)" class="member-name-link">setTempdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;tmpDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Where Ant should place temporary files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setThreads(int)" class="member-name-link">setThreads</a><wbr>(int&nbsp;threads)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the number of test threads to be used for parallel test
 execution.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeout(java.lang.Integer)" class="member-name-link">setTimeout</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the timeout value (in milliseconds).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupJUnitDelegate()" class="member-name-link">setupJUnitDelegate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets up the delegate that will actually run the tests.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="TESTLISTENER_PREFIX">
<h3>TESTLISTENER_PREFIX</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TESTLISTENER_PREFIX</span></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TESTLISTENER_PREFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ENABLE_TESTLISTENER_EVENTS">
<h3>ENABLE_TESTLISTENER_EVENTS</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ENABLE_TESTLISTENER_EVENTS</span></div>
<div class="block">Name of magic property that enables test listener events.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ENABLE_TESTLISTENER_EVENTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JUnitTask</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTask</span>()
          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Creates a new JUnitRunner and enables fork of a new Java VM.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code> - never</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setReloading(boolean)">
<h3>setReloading</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setReloading</span><wbr><span class="parameters">(boolean&nbsp;value)</span></div>
<div class="block">If true, force ant to re-classload all classes for each JUnit TestCase</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - force class reloading for each test case</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFiltertrace(boolean)">
<h3>setFiltertrace</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFiltertrace</span><wbr><span class="parameters">(boolean&nbsp;value)</span></div>
<div class="block">If true, smartly filter the stack frames of
 JUnit errors and failures before reporting them.

 <p>This property is applied on all BatchTest (batchtest) and
 JUnitTest (test) however it can possibly be overridden by their
 own properties.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - <code>false</code> if it should not filter, otherwise
 <code>true</code></dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setHaltonerror(boolean)">
<h3>setHaltonerror</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHaltonerror</span><wbr><span class="parameters">(boolean&nbsp;value)</span></div>
<div class="block">If true, stop the build process when there is an error in a test.
 This property is applied on all BatchTest (batchtest) and JUnitTest
 (test) however it can possibly be overridden by their own
 properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - <code>true</code> if it should halt, otherwise
 <code>false</code></dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setErrorProperty(java.lang.String)">
<h3>setErrorProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</span></div>
<div class="block">Property to set to "true" if there is a error in a test.

 <p>This property is applied on all BatchTest (batchtest) and
 JUnitTest (test), however, it can possibly be overridden by
 their own properties.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propertyName</code> - the name of the property to set in the
 event of an error.</dd>
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setHaltonfailure(boolean)">
<h3>setHaltonfailure</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHaltonfailure</span><wbr><span class="parameters">(boolean&nbsp;value)</span></div>
<div class="block">If true, stop the build process if a test fails
 (errors are considered failures as well).
 This property is applied on all BatchTest (batchtest) and
 JUnitTest (test) however it can possibly be overridden by their
 own properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - <code>true</code> if it should halt, otherwise
 <code>false</code></dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFailureProperty(java.lang.String)">
<h3>setFailureProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailureProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</span></div>
<div class="block">Property to set to "true" if there is a failure in a test.

 <p>This property is applied on all BatchTest (batchtest) and
 JUnitTest (test), however, it can possibly be overridden by
 their own properties.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propertyName</code> - the name of the property to set in the
 event of an failure.</dd>
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFork(boolean)">
<h3>setFork</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFork</span><wbr><span class="parameters">(boolean&nbsp;value)</span></div>
<div class="block">If true, JVM should be forked for each test.

 <p>It avoids interference between testcases and possibly avoids
 hanging the build.  this property is applied on all BatchTest
 (batchtest) and JUnitTest (test) however it can possibly be
 overridden by their own properties.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - <code>true</code> if a JVM should be forked, otherwise
 <code>false</code></dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setTimeout(java.lang.Integer)"><code>setTimeout(java.lang.Integer)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setForkMode(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode)">
<h3>setForkMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForkMode</span><wbr><span class="parameters">(<a href="JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a>&nbsp;mode)</span></div>
<div class="block">Set the behavior when <a href="#setFork(boolean)"><code>fork</code></a> fork has been enabled.

 <p>Possible values are "once", "perTest" and "perBatch".  If
 set to "once", only a single Java VM will be forked for all
 tests, with "perTest" (the default) each test will run in a
 fresh Java VM and "perBatch" will run all tests from the same
 &lt;batchtest&gt; in the same Java VM.</p>

 <p>This attribute will be ignored if tests run in the same VM
 as Ant.</p>

 <p>Only tests with the same configuration of haltonerror,
 haltonfailure, errorproperty, failureproperty and filtertrace
 can share a forked Java VM, so even if you set the value to
 "once", Ant may need to fork multiple VMs.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - the mode to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setThreads(int)">
<h3>setThreads</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setThreads</span><wbr><span class="parameters">(int&nbsp;threads)</span></div>
<div class="block">Set the number of test threads to be used for parallel test
 execution.  The default is 1, which is the same behavior as
 before parallel test execution was possible.

 <p>This attribute will be ignored if tests run in the same VM
 as Ant.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>threads</code> - int</dd>
<dt>Since:</dt>
<dd>Ant 1.9.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setPrintsummary(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.SummaryAttribute)">
<h3>setPrintsummary</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPrintsummary</span><wbr><span class="parameters">(<a href="JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a>&nbsp;value)</span></div>
<div class="block">If true, print one-line statistics for each test, or "withOutAndErr"
 to also show standard output and error.

 Can take the values on, off, and withOutAndErr.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - <code>true</code> to print a summary,
 <code>withOutAndErr</code> to include the test's output as
 well, <code>false</code> otherwise.</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="SummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>SummaryJUnitResultFormatter</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTimeout(java.lang.Integer)">
<h3>setTimeout</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeout</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;value)</span></div>
<div class="block">Set the timeout value (in milliseconds).

 <p>If the test is running for more than this value, the test
 will be canceled. (works only when in 'fork' mode).</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the maximum time (in milliseconds) allowed before
 declaring the test as 'timed-out'</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setFork(boolean)"><code>setFork(boolean)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMaxmemory(java.lang.String)">
<h3>setMaxmemory</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxmemory</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;max)</span></div>
<div class="block">Set the maximum memory to be used by all forked JVMs.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max</code> - the value as defined by <code>-mx</code> or <code>-Xmx</code>
                  in the java command line options.</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setJvm(java.lang.String)">
<h3>setJvm</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJvm</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">The command used to invoke the Java Virtual Machine,
 default is 'java'. The command is resolved by
 java.lang.Runtime.exec(). Ignored if fork is disabled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the new VM to use instead of <code>java</code></dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setFork(boolean)"><code>setFork(boolean)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createJvmarg()">
<h3>createJvmarg</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></span>&nbsp;<span class="element-name">createJvmarg</span>()</div>
<div class="block">Adds a JVM argument; ignored if not forking.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>create a new JVM argument so that any argument can be
 passed to the JVM.</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setFork(boolean)"><code>setFork(boolean)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDir(java.io.File)">
<h3>setDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</span></div>
<div class="block">The directory to invoke the VM in. Ignored if no JVM is forked.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the directory to invoke the JVM from.</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setFork(boolean)"><code>setFork(boolean)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSysproperty(org.apache.tools.ant.types.Environment.Variable)">
<h3>addSysproperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSysproperty</span><wbr><span class="parameters">(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.6</div>
</div>
<div class="block">Adds a system property that tests can access.
 This might be useful to transfer Ant properties to the
 testcases when JVM forking is not enabled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sysp</code> - environment variable to add</dd>
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addConfiguredSysproperty(org.apache.tools.ant.types.Environment.Variable)">
<h3>addConfiguredSysproperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredSysproperty</span><wbr><span class="parameters">(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</span></div>
<div class="block">Adds a system property that tests can access.
 This might be useful to transfer Ant properties to the
 testcases when JVM forking is not enabled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sysp</code> - new environment variable to add</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSyspropertyset(org.apache.tools.ant.types.PropertySet)">
<h3>addSyspropertyset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSyspropertyset</span><wbr><span class="parameters">(<a href="../../../types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a>&nbsp;sysp)</span></div>
<div class="block">Adds a set of properties that will be used as system properties
 that tests can access.

 <p>This might be useful to transfer Ant properties to the
 testcases when JVM forking is not enabled.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sysp</code> - set of properties to be added</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Adds path to classpath used for tests.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>reference to the classpath in the embedded java command line</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createBootclasspath()">
<h3>createBootclasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createBootclasspath</span>()</div>
<div class="block">Adds a path to the bootclasspath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>reference to the bootclasspath in the embedded java command line</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createModulepath()">
<h3>createModulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createModulepath</span>()</div>
<div class="block">Add a path to the modulepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>created modulepath.</dd>
<dt>Since:</dt>
<dd>1.9.8</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createUpgrademodulepath()">
<h3>createUpgrademodulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createUpgrademodulepath</span>()</div>
<div class="block">Add a path to the upgrademodulepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>created upgrademodulepath.</dd>
<dt>Since:</dt>
<dd>1.9.8</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addEnv(org.apache.tools.ant.types.Environment.Variable)">
<h3>addEnv</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addEnv</span><wbr><span class="parameters">(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;var)</span></div>
<div class="block">Adds an environment variable; used when forking.

 <p>Will be ignored if we are not forking a new VM.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>var</code> - environment variable to be added</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNewenvironment(boolean)">
<h3>setNewenvironment</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewenvironment</span><wbr><span class="parameters">(boolean&nbsp;newenv)</span></div>
<div class="block">If true, use a new environment when forked.

 <p>Will be ignored if we are not forking a new VM.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newenv</code> - boolean indicating if setting a new environment is wished</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addTest(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">
<h3>addTest</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTest</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test)</span></div>
<div class="block">Add a new single testcase.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - a new single testcase</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTest</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createBatchTest()">
<h3>createBatchTest</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</a></span>&nbsp;<span class="element-name">createBatchTest</span>()</div>
<div class="block">Adds a set of tests based on pattern matching.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a new instance of a batch test.</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>BatchTest</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addFormatter(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement)">
<h3>addFormatter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFormatter</span><wbr><span class="parameters">(<a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a>&nbsp;fe)</span></div>
<div class="block">Add a new formatter to all tests of this task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fe</code> - formatter element</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludeantruntime(boolean)">
<h3>setIncludeantruntime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeantruntime</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">If true, include ant.jar, optional.jar and junit.jar in the forked VM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - include ant run time yes or no</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setShowOutput(boolean)">
<h3>setShowOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShowOutput</span><wbr><span class="parameters">(boolean&nbsp;showOutput)</span></div>
<div class="block">If true, send any output generated by tests to Ant's logging system
 as well as to the formatters.
 By default only the formatters receive the output.

 <p>Output will always be passed to the formatters and not by
 shown by default.  This option should for example be set for
 tests that are interactive and prompt the user to do
 something.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>showOutput</code> - if true, send output to Ant's logging system too</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOutputToFormatters(boolean)">
<h3>setOutputToFormatters</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputToFormatters</span><wbr><span class="parameters">(boolean&nbsp;outputToFormatters)</span></div>
<div class="block">If true, send any output generated by tests to the formatters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputToFormatters</code> - if true, send output to formatters (Default
                           is true).</dd>
<dt>Since:</dt>
<dd>Ant 1.7.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLogFailedTests(boolean)">
<h3>setLogFailedTests</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogFailedTests</span><wbr><span class="parameters">(boolean&nbsp;logFailedTests)</span></div>
<div class="block">If true, write a single "FAILED" line for failed tests to Ant's
 log system.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>logFailedTests</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addAssertions(org.apache.tools.ant.types.Assertions)">
<h3>addAssertions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addAssertions</span><wbr><span class="parameters">(<a href="../../../types/Assertions.html" title="class in org.apache.tools.ant.types">Assertions</a>&nbsp;asserts)</span></div>
<div class="block">Assertions to enable in this program (if fork=true)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>asserts</code> - assertion set</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createPermissions()">
<h3>createPermissions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</a></span>&nbsp;<span class="element-name">createPermissions</span>()</div>
<div class="block">Sets the permissions for the application run inside the same JVM.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Permissions</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCloneVm(boolean)">
<h3>setCloneVm</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCloneVm</span><wbr><span class="parameters">(boolean&nbsp;cloneVm)</span></div>
<div class="block">If set, system properties will be copied to the cloned VM - as
 well as the bootclasspath unless you have explicitly specified
 a bootclasspath.

 <p>Doesn't have any effect unless fork is true.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cloneVm</code> - a <code>boolean</code> value.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTempdir(java.io.File)">
<h3>setTempdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTempdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;tmpDir)</span></div>
<div class="block">Where Ant should place temporary files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tmpDir</code> - location where temporary files should go to</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEnableTestListenerEvents(boolean)">
<h3>setEnableTestListenerEvents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEnableTestListenerEvents</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether test listener events shall be generated.

 <p>Defaults to false.</p>

 <p>This value will be overridden by the magic property
 ant.junit.enabletestlistenerevents if it has been set.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getEnableTestListenerEvents()">
<h3>getEnableTestListenerEvents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getEnableTestListenerEvents</span>()</div>
<div class="block">Whether test listener events shall be generated.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()</div>
<div class="block">Adds the jars or directories containing Ant, this task and
 JUnit to the classpath - this should make the forked JVM work
 without having to specify them directly.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#init()">init</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setupJUnitDelegate()">
<h3>setupJUnitDelegate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setupJUnitDelegate</span>()</div>
<div class="block">Sets up the delegate that will actually run the tests.

 <p>Will be invoked implicitly once the delegate is needed.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Runs the testcase.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in case of test failures or errors</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,int)">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;arg,
 int&nbsp;thread)</span>
                throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the tests.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>arg</code> - one JUnitTest</dd>
<dd><code>thread</code> - Identifies which thread is test running in (0 for single-threaded runs)</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in case of test failures or errors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;arg)</span>
                throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the tests.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>arg</code> - one JUnitTest</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in case of test failures or errors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute(java.util.List,int)">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&nbsp;testList,
 int&nbsp;thread)</span>
                throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Execute a list of tests in a single forked Java VM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>testList</code> - the list of tests to execute.</dd>
<dd><code>thread</code> - Identifies which thread is test running in (0 for single-threaded runs)</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute(java.util.List)">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&nbsp;testList)</span>
                throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Execute a list of tests in a single forked Java VM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>testList</code> - the list of tests to execute.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="handleOutput(java.lang.String)">
<h3>handleOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Pass output sent to System.out to the TestRunner so it can
 collect it for the formatters.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - output coming from System.out</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="handleInput(byte[],int,int)">
<h3>handleInput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">handleInput</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Handle an input request by this task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buffer</code> - the buffer into which data is to be read.</dd>
<dd><code>offset</code> - the offset into the buffer at which data is stored.</dd>
<dd><code>length</code> - the amount of data to read.</dd>
<dt>Returns:</dt>
<dd>the number of bytes read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the data cannot be read.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">This implementation delegates to a runner if it
 present.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="handleFlush(java.lang.String)">
<h3>handleFlush</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Pass output sent to System.out to the TestRunner so it can
 collect ot for the formatters.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - output coming from System.out</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="handleErrorOutput(java.lang.String)">
<h3>handleErrorOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - output coming from System.err</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="handleErrorFlush(java.lang.String)">
<h3>handleErrorFlush</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - coming from System.err</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createWatchdog()">
<h3>createWatchdog</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></span>&nbsp;<span class="element-name">createWatchdog</span>()
                                  throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>null</code> if there is a timeout value, otherwise the
 watchdog instance.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - under unspecified circumstances</dd>
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultOutput()">
<h3>getDefaultOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></span>&nbsp;<span class="element-name">getDefaultOutput</span>()</div>
<div class="block">Get the default output for a formatter.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>default output stream for a formatter</dd>
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIndividualTests()">
<h3>getIndividualTests</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;</span>&nbsp;<span class="element-name">getIndividualTests</span>()</div>
<div class="block">Merge all individual tests from the batchtest with all individual tests
 and return an enumeration over all <code>JUnitTest</code>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>enumeration over individual tests</dd>
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="allTests()">
<h3>allTests</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a>&gt;</span>&nbsp;<span class="element-name">allTests</span>()</div>
<div class="block">return an enumeration listing each test, then each batchtest</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>enumeration</dd>
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getOutput(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement,org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">
<h3>getOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getOutput</span><wbr><span class="parameters">(<a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a>&nbsp;fe,
 <a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test)</span></div>
<div class="block">If the formatter sends output to a file, return that file.
 null otherwise.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fe</code> - formatter element</dd>
<dd><code>test</code> - one JUnit test</dd>
<dt>Returns:</dt>
<dd>file reference</dd>
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addClasspathEntry(java.lang.String)">
<h3>addClasspathEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addClasspathEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resource)</span></div>
<div class="block">Search for the given resource and add the directory or archive
 that contains it to the classpath.

 <p>Doesn't work for archives in JDK 1.1 as the URL returned by
 getResource doesn't contain the name of the archive.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>resource</code> - resource that one wants to lookup</dd>
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="cleanup()">
<h3>cleanup</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanup</span>()</div>
<div class="block">Removes resources.

 <p>Is invoked in <a href="#execute()"><code>execute</code></a>.  Subclasses that
 don't invoke execute should invoke this method in a finally
 block.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCommandline()">
<h3>getCommandline</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a></span>&nbsp;<span class="element-name">getCommandline</span>()</div>
<div class="block">Get the command line used to run the tests.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the command line.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="executeOrQueue(java.util.Enumeration,boolean)">
<h3>executeOrQueue</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&gt;</span>&nbsp;<span class="element-name">executeOrQueue</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&gt;&nbsp;testList,
 boolean&nbsp;runIndividual)</span></div>
<div class="block">Executes all tests that don't need to be forked (or all tests
 if the runIndividual argument is true.  Returns a collection of
 lists of tests that share the same VM configuration and haven't
 been executed yet.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>testList</code> - the list of tests to be executed or queued.</dd>
<dd><code>runIndividual</code> - if true execute each test individually.</dd>
<dt>Returns:</dt>
<dd>a list of tasks to be executed.</dd>
<dt>Since:</dt>
<dd>1.6.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="actOnTestResult(int,boolean,org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String)">
<h3>actOnTestResult</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">actOnTestResult</span><wbr><span class="parameters">(int&nbsp;exitValue,
 boolean&nbsp;wasKilled,
 <a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exitValue</code> - the exitValue of the test.</dd>
<dd><code>wasKilled</code> - if true, the test had been killed.</dd>
<dd><code>test</code> - the test in question.</dd>
<dd><code>name</code> - the name of the test.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="actOnTestResult(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TestResultHolder,org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String)">
<h3>actOnTestResult</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">actOnTestResult</span><wbr><span class="parameters">(<a href="JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a>&nbsp;result,
 <a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>result</code> - the result of the test.</dd>
<dd><code>test</code> - the test in question.</dd>
<dd><code>name</code> - the name of the test.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
