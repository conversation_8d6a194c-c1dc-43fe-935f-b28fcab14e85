<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>Condition (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.condition, interface: Condition">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.condition</a></div>
<h1 title="Interface Condition" class="title">Interface Condition</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="And.html" title="class in org.apache.tools.ant.taskdefs.condition">And</a></code>, <code><a href="AntVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">AntVersion</a></code>, <code><a href="../Available.html" title="class in org.apache.tools.ant.taskdefs">Available</a></code>, <code><a href="../Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</a></code>, <code><a href="Contains.html" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a></code>, <code><a href="Equals.html" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a></code>, <code><a href="FilesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a></code>, <code><a href="HasFreeSpace.html" title="class in org.apache.tools.ant.taskdefs.condition">HasFreeSpace</a></code>, <code><a href="HasMethod.html" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</a></code>, <code><a href="Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</a></code>, <code><a href="IsFailure.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFailure</a></code>, <code><a href="IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a></code>, <code><a href="IsFileSelected.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a></code>, <code><a href="IsLastModified.html" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified</a></code>, <code><a href="IsReachable.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</a></code>, <code><a href="IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a></code>, <code><a href="IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a></code>, <code><a href="IsSigned.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSigned</a></code>, <code><a href="IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a></code>, <code><a href="JavaVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">JavaVersion</a></code>, <code><a href="../Length.html" title="class in org.apache.tools.ant.taskdefs">Length</a></code>, <code><a href="Matches.html" title="class in org.apache.tools.ant.taskdefs.condition">Matches</a></code>, <code><a href="Not.html" title="class in org.apache.tools.ant.taskdefs.condition">Not</a></code>, <code><a href="Or.html" title="class in org.apache.tools.ant.taskdefs.condition">Or</a></code>, <code><a href="Os.html" title="class in org.apache.tools.ant.taskdefs.condition">Os</a></code>, <code><a href="ParserSupports.html" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</a></code>, <code><a href="ResourceContains.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceContains</a></code>, <code><a href="../ResourceCount.html" title="class in org.apache.tools.ant.taskdefs">ResourceCount</a></code>, <code><a href="ResourceExists.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceExists</a></code>, <code><a href="ResourcesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourcesMatch</a></code>, <code><a href="../../types/optional/ScriptCondition.html" title="class in org.apache.tools.ant.types.optional">ScriptCondition</a></code>, <code><a href="Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a></code>, <code><a href="TypeFound.html" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</a></code>, <code><a href="../UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</a></code>, <code><a href="Xor.html" title="class in org.apache.tools.ant.taskdefs.condition">Xor</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">Condition</span></div>
<div class="block">Interface for conditions to use inside the &lt;condition&gt; task.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#eval()" class="member-name-link">eval</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Is this condition true?</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="eval()">
<h3>eval</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">eval</span>()
      throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Is this condition true?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the condition is true</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an error occurs</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
