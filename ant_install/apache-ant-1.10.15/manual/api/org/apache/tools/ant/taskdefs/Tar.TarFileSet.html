<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>Tar.TarFileSet (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Tar, class: TarFileSet">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Tar.TarFileSet" class="title">Class Tar.TarFileSet</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</a>
<div class="inheritance"><a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</a>
<div class="inheritance"><a href="../types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.ArchiveFileSet</a>
<div class="inheritance"><a href="../types/TarFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.TarFileSet</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Tar.TarFileSet</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">Tar.TarFileSet</span>
<span class="extends-implements">extends <a href="../types/TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a></span></div>
<div class="block">This is a FileSet with the option to specify permissions
 and other attributes.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.ArchiveFileSet">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></h3>
<code><a href="../types/ArchiveFileSet.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</a>, <a href="../types/ArchiveFileSet.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../types/DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../types/DataType.html#checked">checked</a>, <a href="../types/DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TarFileSet</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new <code>TarFileSet</code> instance.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.FileSet)" class="member-name-link">TarFileSet</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new <code>TarFileSet</code> instance.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFiles(org.apache.tools.ant.Project)" class="member-name-link">getFiles</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a list of files and directories specified in the fileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMode()" class="member-name-link">getMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreserveLeadingSlashes()" class="member-name-link">getPreserveLeadingSlashes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMode(java.lang.String)" class="member-name-link">setMode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;octalString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A 3 digit octal string, specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0644</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreserveLeadingSlashes(boolean)" class="member-name-link">setPreserveLeadingSlashes</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to indicates whether leading `/'s should
 be preserved in the file names.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.TarFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../types/TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a></h3>
<code><a href="../types/TarFileSet.html#clone()">clone</a>, <a href="../types/TarFileSet.html#configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)">configureFileSet</a>, <a href="../types/TarFileSet.html#getGid()">getGid</a>, <a href="../types/TarFileSet.html#getGroup()">getGroup</a>, <a href="../types/TarFileSet.html#getRef()">getRef</a>, <a href="../types/TarFileSet.html#getRef(org.apache.tools.ant.Project)">getRef</a>, <a href="../types/TarFileSet.html#getUid()">getUid</a>, <a href="../types/TarFileSet.html#getUserName()">getUserName</a>, <a href="../types/TarFileSet.html#hasGroupBeenSet()">hasGroupBeenSet</a>, <a href="../types/TarFileSet.html#hasGroupIdBeenSet()">hasGroupIdBeenSet</a>, <a href="../types/TarFileSet.html#hasUserIdBeenSet()">hasUserIdBeenSet</a>, <a href="../types/TarFileSet.html#hasUserNameBeenSet()">hasUserNameBeenSet</a>, <a href="../types/TarFileSet.html#newArchiveScanner()">newArchiveScanner</a>, <a href="../types/TarFileSet.html#setGid(int)">setGid</a>, <a href="../types/TarFileSet.html#setGroup(java.lang.String)">setGroup</a>, <a href="../types/TarFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../types/TarFileSet.html#setUid(int)">setUid</a>, <a href="../types/TarFileSet.html#setUserName(java.lang.String)">setUserName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ArchiveFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></h3>
<code><a href="../types/ArchiveFileSet.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</a>, <a href="../types/ArchiveFileSet.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../types/ArchiveFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</a>, <a href="../types/ArchiveFileSet.html#getDirMode()">getDirMode</a>, <a href="../types/ArchiveFileSet.html#getDirMode(org.apache.tools.ant.Project)">getDirMode</a>, <a href="../types/ArchiveFileSet.html#getEncoding()">getEncoding</a>, <a href="../types/ArchiveFileSet.html#getFileMode()">getFileMode</a>, <a href="../types/ArchiveFileSet.html#getFileMode(org.apache.tools.ant.Project)">getFileMode</a>, <a href="../types/ArchiveFileSet.html#getFullpath()">getFullpath</a>, <a href="../types/ArchiveFileSet.html#getFullpath(org.apache.tools.ant.Project)">getFullpath</a>, <a href="../types/ArchiveFileSet.html#getPrefix()">getPrefix</a>, <a href="../types/ArchiveFileSet.html#getPrefix(org.apache.tools.ant.Project)">getPrefix</a>, <a href="../types/ArchiveFileSet.html#getSrc()">getSrc</a>, <a href="../types/ArchiveFileSet.html#getSrc(org.apache.tools.ant.Project)">getSrc</a>, <a href="../types/ArchiveFileSet.html#hasDirModeBeenSet()">hasDirModeBeenSet</a>, <a href="../types/ArchiveFileSet.html#hasFileModeBeenSet()">hasFileModeBeenSet</a>, <a href="../types/ArchiveFileSet.html#integerSetDirMode(int)">integerSetDirMode</a>, <a href="../types/ArchiveFileSet.html#integerSetFileMode(int)">integerSetFileMode</a>, <a href="../types/ArchiveFileSet.html#isFilesystemOnly()">isFilesystemOnly</a>, <a href="../types/ArchiveFileSet.html#iterator()">iterator</a>, <a href="../types/ArchiveFileSet.html#setDir(java.io.File)">setDir</a>, <a href="../types/ArchiveFileSet.html#setDirMode(java.lang.String)">setDirMode</a>, <a href="../types/ArchiveFileSet.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="../types/ArchiveFileSet.html#setErrorOnMissingArchive(boolean)">setErrorOnMissingArchive</a>, <a href="../types/ArchiveFileSet.html#setFileMode(java.lang.String)">setFileMode</a>, <a href="../types/ArchiveFileSet.html#setFullpath(java.lang.String)">setFullpath</a>, <a href="../types/ArchiveFileSet.html#setPrefix(java.lang.String)">setPrefix</a>, <a href="../types/ArchiveFileSet.html#setSrc(java.io.File)">setSrc</a>, <a href="../types/ArchiveFileSet.html#setSrcResource(org.apache.tools.ant.types.Resource)">setSrcResource</a>, <a href="../types/ArchiveFileSet.html#size()">size</a>, <a href="../types/ArchiveFileSet.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.AbstractFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></h3>
<code><a href="../types/AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="../types/AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="../types/AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="../types/AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="../types/AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="../types/AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="../types/AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="../types/AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="../types/AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="../types/AbstractFileSet.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="../types/AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="../types/AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="../types/AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="../types/AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="../types/AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="../types/AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="../types/AbstractFileSet.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="../types/AbstractFileSet.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="../types/AbstractFileSet.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="../types/AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="../types/AbstractFileSet.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="../types/AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="../types/AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="../types/AbstractFileSet.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="../types/AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="../types/AbstractFileSet.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="../types/AbstractFileSet.html#appendExcludes(java.lang.String%5B%5D)">appendExcludes</a>, <a href="../types/AbstractFileSet.html#appendIncludes(java.lang.String%5B%5D)">appendIncludes</a>, <a href="../types/AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="../types/AbstractFileSet.html#createExclude()">createExclude</a>, <a href="../types/AbstractFileSet.html#createExcludesFile()">createExcludesFile</a>, <a href="../types/AbstractFileSet.html#createInclude()">createInclude</a>, <a href="../types/AbstractFileSet.html#createIncludesFile()">createIncludesFile</a>, <a href="../types/AbstractFileSet.html#createPatternSet()">createPatternSet</a>, <a href="../types/AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</a>, <a href="../types/AbstractFileSet.html#getDir()">getDir</a>, <a href="../types/AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</a>, <a href="../types/AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</a>, <a href="../types/AbstractFileSet.html#getErrorOnMissingDir()">getErrorOnMissingDir</a>, <a href="../types/AbstractFileSet.html#getMaxLevelsOfSymlinks()">getMaxLevelsOfSymlinks</a>, <a href="../types/AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="../types/AbstractFileSet.html#hasPatterns()">hasPatterns</a>, <a href="../types/AbstractFileSet.html#hasSelectors()">hasSelectors</a>, <a href="../types/AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</a>, <a href="../types/AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</a>, <a href="../types/AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</a>, <a href="../types/AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</a>, <a href="../types/AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</a>, <a href="../types/AbstractFileSet.html#selectorCount()">selectorCount</a>, <a href="../types/AbstractFileSet.html#selectorElements()">selectorElements</a>, <a href="../types/AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="../types/AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="../types/AbstractFileSet.html#setErrorOnMissingDir(boolean)">setErrorOnMissingDir</a>, <a href="../types/AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="../types/AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="../types/AbstractFileSet.html#setFile(java.io.File)">setFile</a>, <a href="../types/AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="../types/AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="../types/AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="../types/AbstractFileSet.html#setMaxLevelsOfSymlinks(int)">setMaxLevelsOfSymlinks</a>, <a href="../types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</a>, <a href="../types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner,org.apache.tools.ant.Project)">setupDirectoryScanner</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../types/DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../types/DataType.html#circularReference()">circularReference</a>, <a href="../types/DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../types/DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../types/DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../types/DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../types/DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../types/DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../types/DataType.html#getRefid()">getRefid</a>, <a href="../types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../types/DataType.html#isChecked()">isChecked</a>, <a href="../types/DataType.html#isReference()">isReference</a>, <a href="../types/DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../types/DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../types/DataType.html#setChecked(boolean)">setChecked</a>, <a href="../types/DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="../types/ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="../types/ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.FileSet)">
<h3>TarFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarFileSet</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset)</span></div>
<div class="block">Creates a new <code>TarFileSet</code> instance.
 Using a fileset as a constructor argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileset</code> - a <code>FileSet</code> value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TarFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarFileSet</span>()</div>
<div class="block">Creates a new <code>TarFileSet</code> instance.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getFiles(org.apache.tools.ant.Project)">
<h3>getFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getFiles</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get a list of files and directories specified in the fileset.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>a list of file and directory names, relative to
    the baseDir for the project.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMode(java.lang.String)">
<h3>setMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;octalString)</span></div>
<div class="block">A 3 digit octal string, specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0644</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>octalString</code> - a 3 digit octal string.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMode()">
<h3>getMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMode</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the current mode.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setPreserveLeadingSlashes(boolean)">
<h3>setPreserveLeadingSlashes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreserveLeadingSlashes</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Flag to indicates whether leading `/'s should
 be preserved in the file names.
 Optional, default is <code>false</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - the leading slashes flag.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPreserveLeadingSlashes()">
<h3>getPreserveLeadingSlashes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getPreserveLeadingSlashes</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the leading slashes flag.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
