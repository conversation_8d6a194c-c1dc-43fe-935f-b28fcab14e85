<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>FileScanner (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant, interface: FileScanner">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Interface FileScanner" class="title">Interface FileScanner</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></code>, <code><a href="types/optional/depend/DependScanner.html" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</a></code>, <code><a href="DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code>, <code><a href="taskdefs/optional/net/FTP.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</a></code>, <code><a href="taskdefs/optional/net/FTPTaskMirrorImpl.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner</a></code>, <code><a href="types/TarScanner.html" title="class in org.apache.tools.ant.types">TarScanner</a></code>, <code><a href="types/ZipScanner.html" title="class in org.apache.tools.ant.types">ZipScanner</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">FileScanner</span></div>
<div class="block">An interface used to describe the actions required of any type of
 directory scanner.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#addDefaultExcludes()" class="member-name-link">addDefaultExcludes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Adds default exclusions to the current exclusions set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getBasedir()" class="member-name-link">getBasedir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the base directory to be scanned.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getExcludedDirectories()" class="member-name-link">getExcludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getExcludedFiles()" class="member-name-link">getExcludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getIncludedDirectories()" class="member-name-link">getIncludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getIncludedFiles()" class="member-name-link">getIncludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getNotIncludedDirectories()" class="member-name-link">getNotIncludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the names of the directories which matched none of the include
 patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getNotIncludedFiles()" class="member-name-link">getNotIncludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns the names of the files which matched none of the include
 patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#scan()" class="member-name-link">scan</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Scans the base directory for files which match at least one include
 pattern and don't match any exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setBasedir(java.io.File)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the base directory to be scanned.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setBasedir(java.lang.String)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;basedir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the base directory to be scanned.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setCaseSensitive(boolean)" class="member-name-link">setCaseSensitive</a><wbr>(boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets whether or not the file system should be regarded as case sensitive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setExcludes(java.lang.String%5B%5D)" class="member-name-link">setExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the list of exclude patterns to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setIncludes(java.lang.String%5B%5D)" class="member-name-link">setIncludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the list of include patterns to use.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addDefaultExcludes()">
<h3>addDefaultExcludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">addDefaultExcludes</span>()</div>
<div class="block">Adds default exclusions to the current exclusions set.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="getBasedir()">
<h3>getBasedir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBasedir</span>()</div>
<div class="block">Returns the base directory to be scanned.
 This is the directory which is scanned recursively.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the base directory to be scanned</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExcludedDirectories()">
<h3>getExcludedDirectories</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getExcludedDirectories</span>()</div>
<div class="block">Returns the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExcludedFiles()">
<h3>getExcludedFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getExcludedFiles</span>()</div>
<div class="block">Returns the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of the files which matched at least one of the
         include patterns and at least one of the exclude patterns.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludedDirectories()">
<h3>getIncludedDirectories</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getIncludedDirectories</span>()</div>
<div class="block">Returns the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludedFiles()">
<h3>getIncludedFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getIncludedFiles</span>()</div>
<div class="block">Returns the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of the files which matched at least one of the
         include patterns and none of the exclude patterns.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNotIncludedDirectories()">
<h3>getNotIncludedDirectories</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotIncludedDirectories</span>()</div>
<div class="block">Returns the names of the directories which matched none of the include
 patterns. The names are relative to the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of the directories which matched none of the include
 patterns.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNotIncludedFiles()">
<h3>getNotIncludedFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotIncludedFiles</span>()</div>
<div class="block">Returns the names of the files which matched none of the include
 patterns. The names are relative to the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of the files which matched none of the include
         patterns.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="scan()">
<h3>scan</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">scan</span>()
   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></span></div>
<div class="block">Scans the base directory for files which match at least one include
 pattern and don't match any exclude patterns.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></code> - if the base directory was set
            incorrectly (i.e. if it is <code>null</code>, doesn't exist,
            or isn't a directory).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.lang.String)">
<h3>setBasedir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;basedir)</span></div>
<div class="block">Sets the base directory to be scanned. This is the directory which is
 scanned recursively. All '/' and '\' characters should be replaced by
 <code>File.separatorChar</code>, so the separator used need not match
 <code>File.separatorChar</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>basedir</code> - The base directory to scan.
                Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.io.File)">
<h3>setBasedir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir)</span></div>
<div class="block">Sets the base directory to be scanned. This is the directory which is
 scanned recursively.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>basedir</code> - The base directory for scanning.
                Should not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExcludes(java.lang.String[])">
<h3>setExcludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</span></div>
<div class="block">Sets the list of exclude patterns to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludes</code> - A list of exclude patterns.
                 May be <code>null</code>, indicating that no files
                 should be excluded. If a non-<code>null</code> list is
                 given, all elements must be non-<code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludes(java.lang.String[])">
<h3>setIncludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setIncludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</span></div>
<div class="block">Sets the list of include patterns to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includes</code> - A list of include patterns.
                 May be <code>null</code>, indicating that all files
                 should be included. If a non-<code>null</code>
                 list is given, all elements must be
 non-<code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCaseSensitive(boolean)">
<h3>setCaseSensitive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setCaseSensitive</span><wbr><span class="parameters">(boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Sets whether or not the file system should be regarded as case sensitive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>isCaseSensitive</code> - whether or not the file system should be
                        regarded as a case sensitive one</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
