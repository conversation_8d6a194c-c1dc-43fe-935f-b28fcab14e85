<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.util.optional (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.util.optional">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.util.optional" class="title">Package org.apache.tools.ant.util.optional</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.util.optional</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.util</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../depend/package-summary.html">org.apache.tools.ant.util.depend</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../facade/package-summary.html">org.apache.tools.ant.util.facade</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../java15/package-summary.html">org.apache.tools.ant.util.java15</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../regexp/package-summary.html">org.apache.tools.ant.util.regexp</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JavaxScriptRunner.html" title="class in org.apache.tools.ant.util.optional">JavaxScriptRunner</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class is used to run scripts using JSR 223.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="NoExitSecurityManager.html" title="class in org.apache.tools.ant.util.optional">NoExitSecurityManager</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is intended as a replacement for the default system manager.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ScriptRunner.html" title="class in org.apache.tools.ant.util.optional">ScriptRunner</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class is used to run BSF scripts</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="WeakishReference12.html" title="class in org.apache.tools.ant.util.optional">WeakishReference12</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</body>
</html>
