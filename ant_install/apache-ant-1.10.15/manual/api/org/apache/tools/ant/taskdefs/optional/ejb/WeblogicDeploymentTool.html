<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>WeblogicDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: WeblogicDeploymentTool">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class WeblogicDeploymentTool" class="title">Class WeblogicDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">WeblogicDeploymentTool</span>
<span class="extends-implements">extends <a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></span></div>
<div class="block">The weblogic element is used to control the weblogic.ejbc compiler for
    generating WebLogic EJB jars. Prior to Ant 1.3, the method of locating CMP
    descriptors was to use the ejbjar naming convention. So if your ejb-jar was
    called, Customer-ejb-jar.xml, your WebLogic descriptor was called Customer-
    weblogic-ejb-jar.xml and your CMP descriptor had to be Customer-weblogic-cmp-
    rdbms-jar.xml. In addition, the &lt;type-storage&gt; element in the WebLogic
    descriptor had to be set to the standard name META-INF/weblogic-cmp-rdbms-
    jar.xml, as that is where the CMP descriptor was mapped to in the generated
    jar.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMPILER_EJB11" class="member-name-link">COMPILER_EJB11</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COMPILER_EJB20" class="member-name-link">COMPILER_EJB20</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_COMPILER" class="member-name-link">DEFAULT_COMPILER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_WL51_DTD_LOCATION" class="member-name-link">DEFAULT_WL51_DTD_LOCATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_WL51_EJB11_DTD_LOCATION" class="member-name-link">DEFAULT_WL51_EJB11_DTD_LOCATION</a></code></div>
<div class="col-last even-row-color">
<div class="block">WebLogic 5.1 dtd location</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_WL60_51_DTD_LOCATION" class="member-name-link">DEFAULT_WL60_51_DTD_LOCATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_WL60_DTD_LOCATION" class="member-name-link">DEFAULT_WL60_DTD_LOCATION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_WL60_EJB11_DTD_LOCATION" class="member-name-link">DEFAULT_WL60_EJB11_DTD_LOCATION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">WebLogic 6.0 ejb 1.1 dtd location</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_WL60_EJB20_DTD_LOCATION" class="member-name-link">DEFAULT_WL60_EJB20_DTD_LOCATION</a></code></div>
<div class="col-last even-row-color">
<div class="block">WebLogic 6.0 ejb 2.0 dtd location</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_WL70_DTD_LOCATION" class="member-name-link">DEFAULT_WL70_DTD_LOCATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PUBLICID_EJB11" class="member-name-link">PUBLICID_EJB11</a></code></div>
<div class="col-last even-row-color">
<div class="block">EJB11 id</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PUBLICID_EJB20" class="member-name-link">PUBLICID_EJB20</a></code></div>
<div class="col-last odd-row-color">
<div class="block">EJB20 id</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PUBLICID_WEBLOGIC_EJB510" class="member-name-link">PUBLICID_WEBLOGIC_EJB510</a></code></div>
<div class="col-last even-row-color">
<div class="block">WebLogic 5.1.0 id</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PUBLICID_WEBLOGIC_EJB600" class="member-name-link">PUBLICID_WEBLOGIC_EJB600</a></code></div>
<div class="col-last odd-row-color">
<div class="block">WebLogic 6.0.0 id</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PUBLICID_WEBLOGIC_EJB700" class="member-name-link">PUBLICID_WEBLOGIC_EJB700</a></code></div>
<div class="col-last even-row-color">
<div class="block">WebLogic 7.0.0 id</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#WL_CMP_DD" class="member-name-link">WL_CMP_DD</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#WL_DD" class="member-name-link">WL_DD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</a>, <a href="GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</a>, <a href="GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</a>, <a href="GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</a>, <a href="GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</a>, <a href="GenericDeploymentTool.html#EJB_DD">EJB_DD</a>, <a href="GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</a>, <a href="GenericDeploymentTool.html#MANIFEST">MANIFEST</a>, <a href="GenericDeploymentTool.html#META_DIR">META_DIR</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">WeblogicDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSysproperty(org.apache.tools.ant.types.Environment.Variable)" class="member-name-link">addSysproperty</a><wbr>(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a nested sysproperty element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVendorFiles(java.util.Hashtable,java.lang.String)" class="member-name-link">addVendorFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add any vendor specific files which should be included in the EJB Jar.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWLClasspath()" class="member-name-link">createWLClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the classpath to the WebLogic classpaths.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassLoaderFromJar(java.io.File)" class="member-name-link">getClassLoaderFromJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;classjar)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Helper method invoked by isRebuildRequired to get a ClassLoader for a
 Jar File passed to it.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEjbcClass()" class="member-name-link">getEjbcClass</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the ejbc compiler class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJvmDebugLevel()" class="member-name-link">getJvmDebugLevel</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the debug level.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWeblogicDescriptorHandler(java.io.File)" class="member-name-link">getWeblogicDescriptorHandler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the WebLogic descriptor handler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isRebuildRequired(java.io.File,java.io.File)" class="member-name-link">isRebuildRequired</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;genericJarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;weblogicJarFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Helper method to check to see if a WebLogic EJB 1.1 jar needs to be
 rebuilt using ejbc.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)" class="member-name-link">registerKnownDTDs</a><wbr>(<a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a>&nbsp;handler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Register the DTDs.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setArgs(java.lang.String)" class="member-name-link">setArgs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;args)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Any optional extra arguments pass to the weblogic.ejbc
 tool.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompiler(java.lang.String)" class="member-name-link">setCompiler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compiler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The compiler (switch <code>-compiler</code>) to use; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEjbcClass(java.lang.String)" class="member-name-link">setEjbcClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ejbcClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classname of the ejbc compiler;  optional
 Normally ejbjar determines
 the appropriate class based on the DTD used for the EJB.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEJBdtd(java.lang.String)" class="member-name-link">setEJBdtd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block"><b>Deprecated</b>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJvmargs(java.lang.String)" class="member-name-link">setJvmargs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;args)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set any additional arguments to pass to the WebLogic JVM; optional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJvmDebugLevel(java.lang.Integer)" class="member-name-link">setJvmDebugLevel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;jvmDebugLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the weblogic.StdoutSeverityLevel to use when running the JVM that
 executes ejbc; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepgenerated(java.lang.String)" class="member-name-link">setKeepgenerated</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Controls whether WebLogic will keep the generated Java
 files used to build the class files added to the
 jar.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepgeneric(boolean)" class="member-name-link">setKeepgeneric</a><wbr>(boolean&nbsp;inValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">controls whether the generic file used as input to
 ejbc is retained; defaults to false</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewCMP(boolean)" class="member-name-link">setNewCMP</a><wbr>(boolean&nbsp;newCMP)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If this is set to true, the new method for locating
 CMP descriptors will be used; optional, default false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNoEJBC(boolean)" class="member-name-link">setNoEJBC</a><wbr>(boolean&nbsp;noEJBC)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do not EJBC the jar after it has been put together;
 optional, default false</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOldCMP(boolean)" class="member-name-link">setOldCMP</a><wbr>(boolean&nbsp;oldCMP)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the value of the oldCMP scheme.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputDir(java.io.File)" class="member-name-link">setOutputDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outputDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If set ejbc will use this directory as the output
 destination rather than a jar file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRebuild(boolean)" class="member-name-link">setRebuild</a><wbr>(boolean&nbsp;rebuild)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the rebuild flag to false to only update changes in the jar rather
 than rerunning ejbc; optional, default true.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuffix(java.lang.String)" class="member-name-link">setSuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter used to store the suffix for the generated WebLogic jar file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWeblogicdtd(java.lang.String)" class="member-name-link">setWeblogicdtd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block"><b>Deprecated</b>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWLClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setWLClasspath</a><wbr>(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;wlClasspath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Optional classpath to WL6.0.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWLdtd(java.lang.String)" class="member-name-link">setWLdtd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block"><b>Deprecated</b>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validateConfigured()" class="member-name-link">validateConfigured</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called to validate that the tool parameters have been configured.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)" class="member-name-link">writeJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method used to encapsulate the writing of the JAR file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)">addFileToJar</a>, <a href="GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</a>, <a href="GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</a>, <a href="GenericDeploymentTool.html#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">checkConfiguration</a>, <a href="GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</a>, <a href="GenericDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</a>, <a href="GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</a>, <a href="GenericDeploymentTool.html#getConfig()">getConfig</a>, <a href="GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</a>, <a href="GenericDeploymentTool.html#getDestDir()">getDestDir</a>, <a href="GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</a>, <a href="GenericDeploymentTool.html#getLocation()">getLocation</a>, <a href="GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</a>, <a href="GenericDeploymentTool.html#getPublicId()">getPublicId</a>, <a href="GenericDeploymentTool.html#getTask()">getTask</a>, <a href="GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String,java.lang.String)">getVendorDDPrefix</a>, <a href="GenericDeploymentTool.html#log(java.lang.String,int)">log</a>, <a href="GenericDeploymentTool.html#needToRebuild(java.util.Hashtable,java.io.File)">needToRebuild</a>, <a href="GenericDeploymentTool.html#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">parseEjbFiles</a>, <a href="GenericDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a>, <a href="GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</a>, <a href="GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</a>, <a href="GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</a>, <a href="GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PUBLICID_EJB11">
<h3>PUBLICID_EJB11</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_EJB11</span></div>
<div class="block">EJB11 id</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_EJB11">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PUBLICID_EJB20">
<h3>PUBLICID_EJB20</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_EJB20</span></div>
<div class="block">EJB20 id</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_EJB20">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PUBLICID_WEBLOGIC_EJB510">
<h3>PUBLICID_WEBLOGIC_EJB510</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_WEBLOGIC_EJB510</span></div>
<div class="block">WebLogic 5.1.0 id</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB510">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PUBLICID_WEBLOGIC_EJB600">
<h3>PUBLICID_WEBLOGIC_EJB600</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_WEBLOGIC_EJB600</span></div>
<div class="block">WebLogic 6.0.0 id</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB600">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PUBLICID_WEBLOGIC_EJB700">
<h3>PUBLICID_WEBLOGIC_EJB700</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_WEBLOGIC_EJB700</span></div>
<div class="block">WebLogic 7.0.0 id</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB700">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL51_EJB11_DTD_LOCATION">
<h3>DEFAULT_WL51_EJB11_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL51_EJB11_DTD_LOCATION</span></div>
<div class="block">WebLogic 5.1 dtd location</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL51_EJB11_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL60_EJB11_DTD_LOCATION">
<h3>DEFAULT_WL60_EJB11_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL60_EJB11_DTD_LOCATION</span></div>
<div class="block">WebLogic 6.0 ejb 1.1 dtd location</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_EJB11_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL60_EJB20_DTD_LOCATION">
<h3>DEFAULT_WL60_EJB20_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL60_EJB20_DTD_LOCATION</span></div>
<div class="block">WebLogic 6.0 ejb 2.0 dtd location</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_EJB20_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL51_DTD_LOCATION">
<h3>DEFAULT_WL51_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL51_DTD_LOCATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL51_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL60_51_DTD_LOCATION">
<h3>DEFAULT_WL60_51_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL60_51_DTD_LOCATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_51_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL60_DTD_LOCATION">
<h3>DEFAULT_WL60_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL60_DTD_LOCATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_WL70_DTD_LOCATION">
<h3>DEFAULT_WL70_DTD_LOCATION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_WL70_DTD_LOCATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL70_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_COMPILER">
<h3>DEFAULT_COMPILER</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_COMPILER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_COMPILER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WL_DD">
<h3>WL_DD</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WL_DD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.WL_DD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WL_CMP_DD">
<h3>WL_CMP_DD</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WL_CMP_DD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.WL_CMP_DD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMPILER_EJB11">
<h3>COMPILER_EJB11</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMPILER_EJB11</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.COMPILER_EJB11">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMPILER_EJB20">
<h3>COMPILER_EJB20</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMPILER_EJB20</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.COMPILER_EJB20">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>WeblogicDeploymentTool</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">WeblogicDeploymentTool</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addSysproperty(org.apache.tools.ant.types.Environment.Variable)">
<h3>addSysproperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSysproperty</span><wbr><span class="parameters">(<a href="../../../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</span></div>
<div class="block">Add a nested sysproperty element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sysp</code> - the element to add.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createWLClasspath()">
<h3>createWLClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createWLClasspath</span>()</div>
<div class="block">Get the classpath to the WebLogic classpaths.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classpath to configure.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOutputDir(java.io.File)">
<h3>setOutputDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outputDir)</span></div>
<div class="block">If set ejbc will use this directory as the output
 destination rather than a jar file. This allows for the
 generation of &quot;exploded&quot; jars.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputDir</code> - the directory to be used.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setWLClasspath(org.apache.tools.ant.types.Path)">
<h3>setWLClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWLClasspath</span><wbr><span class="parameters">(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;wlClasspath)</span></div>
<div class="block">Optional classpath to WL6.0.
 WebLogic 6.0 will give a warning if the home and remote interfaces
 of a bean are on the system classpath used to run weblogic.ejbc.
 In that case, the standard WebLogic classes should be set with
 this attribute (or equivalent nested element) and the
 home and remote interfaces located with the standard classpath
 attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>wlClasspath</code> - the path to be used.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCompiler(java.lang.String)">
<h3>setCompiler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompiler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compiler)</span></div>
<div class="block">The compiler (switch <code>-compiler</code>) to use; optional.
 This allows for the selection of a different compiler
 to be used for the compilation of the generated Java
 files. This could be set, for example, to Jikes to
 compile with the Jikes compiler. If this is not set
 and the <code>build.compiler</code> property is set
 to jikes, the Jikes compiler will be used. If this
 is not desired, the value &quot;<code>default</code>&quot;
 may be given to use the default compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compiler</code> - the compiler to be used.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRebuild(boolean)">
<h3>setRebuild</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRebuild</span><wbr><span class="parameters">(boolean&nbsp;rebuild)</span></div>
<div class="block">Set the rebuild flag to false to only update changes in the jar rather
 than rerunning ejbc; optional, default true.
 This flag controls whether weblogic.ejbc is always
 invoked to build the jar file. In certain circumstances,
 such as when only a bean class has been changed, the jar
 can be generated by merely replacing the changed classes
 and not rerunning ejbc. Setting this to false will reduce
 the time to run ejbjar.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rebuild</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setJvmDebugLevel(java.lang.Integer)">
<h3>setJvmDebugLevel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJvmDebugLevel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;jvmDebugLevel)</span></div>
<div class="block">Sets the weblogic.StdoutSeverityLevel to use when running the JVM that
 executes ejbc; optional. Set to 16 to avoid the warnings about EJB Home and
 Remotes being in the classpath</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jvmDebugLevel</code> - the value to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getJvmDebugLevel()">
<h3>getJvmDebugLevel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getJvmDebugLevel</span>()</div>
<div class="block">Get the debug level.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the jvm debug level (may be null).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSuffix(java.lang.String)">
<h3>setSuffix</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Setter used to store the suffix for the generated WebLogic jar file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the suffix.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setKeepgeneric(boolean)">
<h3>setKeepgeneric</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepgeneric</span><wbr><span class="parameters">(boolean&nbsp;inValue)</span></div>
<div class="block">controls whether the generic file used as input to
 ejbc is retained; defaults to false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - true for keep generic</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setKeepgenerated(java.lang.String)">
<h3>setKeepgenerated</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepgenerated</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</span></div>
<div class="block">Controls whether WebLogic will keep the generated Java
 files used to build the class files added to the
 jar. This can be useful when debugging; default is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - either 'true' or 'false'</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setArgs(java.lang.String)">
<h3>setArgs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setArgs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;args)</span></div>
<div class="block">Any optional extra arguments pass to the weblogic.ejbc
 tool.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - extra arguments to pass to the ejbc tool.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setJvmargs(java.lang.String)">
<h3>setJvmargs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJvmargs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;args)</span></div>
<div class="block">Set any additional arguments to pass to the WebLogic JVM; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - the arguments to be passed to the JVM</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEjbcClass(java.lang.String)">
<h3>setEjbcClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEjbcClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ejbcClass)</span></div>
<div class="block">Set the classname of the ejbc compiler;  optional
 Normally ejbjar determines
 the appropriate class based on the DTD used for the EJB. The EJB 2.0 compiler
 featured in WebLogic 6 has, however, been deprecated in version 7. When
 using with version 7 this attribute should be set to
 &quot;weblogic.ejbc&quot; to avoid the deprecation warning.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ejbcClass</code> - the name of the class to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getEjbcClass()">
<h3>getEjbcClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getEjbcClass</span>()</div>
<div class="block">Get the ejbc compiler class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the ejbc compiler class.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setWeblogicdtd(java.lang.String)">
<h3>setWeblogicdtd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWeblogicdtd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block"><b>Deprecated</b>. Defines the location of the ejb-jar DTD in
  the WebLogic class hierarchy. Should not be needed, and the
 nested &lt;dtd&gt; element is recommended when it is.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the DTD location.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setWLdtd(java.lang.String)">
<h3>setWLdtd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWLdtd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block"><b>Deprecated</b>. Defines the location of WebLogic DTD in
  the WebLogic class hierarchy. Should not be needed, and the
 nested &lt;dtd&gt; element is recommended when it is.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the DTD location.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEJBdtd(java.lang.String)">
<h3>setEJBdtd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEJBdtd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block"><b>Deprecated</b>. Defines the location of Sun's EJB DTD in
  the WebLogic class hierarchy. Should not be needed, and the
 nested &lt;dtd&gt; element is recommended when it is.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the DTD location.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOldCMP(boolean)">
<h3>setOldCMP</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOldCMP</span><wbr><span class="parameters">(boolean&nbsp;oldCMP)</span></div>
<div class="block">Set the value of the oldCMP scheme. This is an antonym for newCMP</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>oldCMP</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNewCMP(boolean)">
<h3>setNewCMP</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewCMP</span><wbr><span class="parameters">(boolean&nbsp;newCMP)</span></div>
<div class="block">If this is set to true, the new method for locating
 CMP descriptors will be used; optional, default false.
 <P>
 The old CMP scheme locates the
 WebLogic CMP descriptor based on the naming convention where the
 WebLogic CMP file is expected to be named with the bean name as the
 prefix. Under this scheme the name of the CMP descriptor does not match
 the name actually used in the main WebLogic EJB descriptor. Also,
 descriptors which contain multiple CMP references could not be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newCMP</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNoEJBC(boolean)">
<h3>setNoEJBC</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNoEJBC</span><wbr><span class="parameters">(boolean&nbsp;noEJBC)</span></div>
<div class="block">Do not EJBC the jar after it has been put together;
 optional, default false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>noEJBC</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">
<h3>registerKnownDTDs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registerKnownDTDs</span><wbr><span class="parameters">(<a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a>&nbsp;handler)</span></div>
<div class="block">Register the DTDs.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>handler</code> - the handler to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getWeblogicDescriptorHandler(java.io.File)">
<h3>getWeblogicDescriptorHandler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></span>&nbsp;<span class="element-name">getWeblogicDescriptorHandler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Get the WebLogic descriptor handler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directory.</dd>
<dt>Returns:</dt>
<dd>the descriptor.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addVendorFiles(java.util.Hashtable,java.lang.String)">
<h3>addVendorFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVendorFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</span></div>
<div class="block">Add any vendor specific files which should be included in the EJB Jar.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable,java.lang.String)">addVendorFiles</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - the hash table to be populated.</dd>
<dd><code>ddPrefix</code> - the prefix to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">
<h3>writeJar</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</span>
                 throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Method used to encapsulate the writing of the JAR file. Iterates over
 the filenames/java.io.Files in the Hashtable stored on the instance
 variable ejbFiles.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">writeJar</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>baseName</code> - the base name.</dd>
<dd><code>jarFile</code> - the jar file to populate.</dd>
<dd><code>files</code> - the hash table of files to write.</dd>
<dd><code>publicId</code> - the id to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="validateConfigured()">
<h3>validateConfigured</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validateConfigured</span>()
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called to validate that the tool parameters have been configured.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#validateConfigured()">validateConfigured</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#validateConfigured()">validateConfigured</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isRebuildRequired(java.io.File,java.io.File)">
<h3>isRebuildRequired</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isRebuildRequired</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;genericJarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;weblogicJarFile)</span></div>
<div class="block">Helper method to check to see if a WebLogic EJB 1.1 jar needs to be
 rebuilt using ejbc. Called from writeJar it sees if the "Bean" classes
 are the only thing that needs to be updated and either updates the Jar
 with the Bean classfile or returns true, saying that the whole WebLogic
 jar needs to be regenerated with ejbc. This allows faster build times for
 working developers. <p>

 The way WebLogic ejbc works is it creates wrappers for the publicly
 defined methods as they are exposed in the remote interface. If the
 actual bean changes without changing the the method signatures then
 only the bean classfile needs to be updated and the rest of the
 WebLogic jar file can remain the same. If the Interfaces, ie. the
 method signatures change or if the xml deployment descriptors changed,
 the whole jar needs to be rebuilt with ejbc. This is not strictly true
 for the xml files. If the JNDI name changes then the jar doesn't have to
 be rebuild, but if the resources references change then it does. At
 this point the WebLogic jar gets rebuilt if the xml files change at
 all.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>genericJarFile</code> - java.io.File The generic jar file.</dd>
<dd><code>weblogicJarFile</code> - java.io.File The WebLogic jar file to check to
      see if it needs to be rebuilt.</dd>
<dt>Returns:</dt>
<dd>true if the jar needs to be rebuilt.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getClassLoaderFromJar(java.io.File)">
<h3>getClassLoaderFromJar</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getClassLoaderFromJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;classjar)</span>
                                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Helper method invoked by isRebuildRequired to get a ClassLoader for a
 Jar File passed to it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classjar</code> - java.io.File representing jar file to get classes from.</dd>
<dt>Returns:</dt>
<dd>the classloader for the jarfile.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
