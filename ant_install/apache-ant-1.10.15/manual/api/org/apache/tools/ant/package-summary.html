<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant" class="title">Package org.apache.tools.ant</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">org.apache.tools.ant</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="attribute/package-summary.html">org.apache.tools.ant.attribute</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="dispatch/package-summary.html">org.apache.tools.ant.dispatch</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="filters/package-summary.html">org.apache.tools.ant.filters</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="helper/package-summary.html">org.apache.tools.ant.helper</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="input/package-summary.html">org.apache.tools.ant.input</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="launch/package-summary.html">org.apache.tools.ant.launch</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="listener/package-summary.html">org.apache.tools.ant.listener</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="loader/package-summary.html">org.apache.tools.ant.loader</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="property/package-summary.html">org.apache.tools.ant.property</a></div>
<div class="col-last even-row-color">
<div class="block">Contains helper classes for Ant properties.</div>
</div>
<div class="col-first odd-row-color"><a href="taskdefs/package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="types/package-summary.html">org.apache.tools.ant.types</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="util/package-summary.html">org.apache.tools.ant.util</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab5" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab5', 2)" class="table-tab">Exception Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Used to load classes within ant with a different classpath from
 that used to start ant.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This class contains all the information
 on a particular ant type,
 the classname, adapter and the class
 it should be assignable from.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="ArgumentProcessor.html" title="interface in org.apache.tools.ant">ArgumentProcessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Processor of arguments of the command line.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ArgumentProcessorRegistry.html" title="class in org.apache.tools.ant">ArgumentProcessorRegistry</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The global registry for <a href="ArgumentProcessor.html" title="interface in org.apache.tools.ant"><code>ArgumentProcessor</code></a>s.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class representing an event occurring during a build.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Signals an error condition during a build</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Instances of classes that implement this interface can register
 to be notified when things happened during a build.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Interface used by Ant to log the build output.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Component creation and configuration.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DefaultDefinitions.html" title="class in org.apache.tools.ant">DefaultDefinitions</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Default definitions.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Writes build events to a PrintStream.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DemuxInputStream.html" title="class in org.apache.tools.ant">DemuxInputStream</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Passes input requests to the project object for demultiplexing into
 individual tasks and threads.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DemuxOutputStream.html" title="class in org.apache.tools.ant">DemuxOutputStream</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Logs content written by a thread and forwards the buffers onto the
 project object which will forward the content to the appropriate
 task.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Diagnostics.html" title="class in org.apache.tools.ant">Diagnostics</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A little diagnostic helper that output some information that may help
 in support.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class for scanning a directory for files/directories which match certain
 criteria.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown attributes</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="DynamicAttributeNS.html" title="interface in org.apache.tools.ant">DynamicAttributeNS</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown attributes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="DynamicConfigurator.html" title="interface in org.apache.tools.ant">DynamicConfigurator</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown attributes and elements.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown attributes and elements.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown elements.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown elements.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="DynamicObjectAttribute.html" title="interface in org.apache.tools.ant">DynamicObjectAttribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Enables a task to control unknown attributes.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Evaluable.html" title="interface in org.apache.tools.ant">Evaluable</a>&lt;T&gt;</div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Kind of task attribute that can be evaluated before being assigned</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Executor.html" title="interface in org.apache.tools.ant">Executor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Target executor abstraction.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="ExitException.html" title="class in org.apache.tools.ant">ExitException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Used to report exit status of classes which call System.exit().</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="ExitStatusException.html" title="class in org.apache.tools.ant">ExitStatusException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">BuildException + exit status.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExtensionPoint.html" title="class in org.apache.tools.ant">ExtensionPoint</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">An extension point build files can provide as a place where other
 build files can add new dependencies.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">An interface used to describe the actions required of any type of
 directory scanner.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Helper class that collects the methods a task or nested element
 holds to set attributes, create nested elements or hold PCDATA
 elements.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">creator - allows use of create/store external
 to IntrospectionHelper.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Location.html" title="class in org.apache.tools.ant">Location</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Stores the location of a piece of text within a file (file name,
 line number and column number).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MagicNames.html" title="class in org.apache.tools.ant">MagicNames</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Magic names used within Ant.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Main.html" title="class in org.apache.tools.ant">Main</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Command line entry point into Ant.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Extends DefaultLogger to strip out empty targets.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PathTokenizer.html" title="class in org.apache.tools.ant">PathTokenizer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A Path tokenizer takes a path and returns the components that make up
 that path.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Central representation of an Ant project.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for components of a project, including tasks and data types.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Configures a Project (complete with Targets and Tasks) based on
 a build file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ProjectHelper.OnMissingExtensionPoint.html" title="class in org.apache.tools.ant">ProjectHelper.OnMissingExtensionPoint</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Possible value for target's onMissingExtensionPoint attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ProjectHelperRepository.html" title="class in org.apache.tools.ant">ProjectHelperRepository</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Repository of <a href="ProjectHelper.html" title="class in org.apache.tools.ant"><code>ProjectHelper</code></a> found in the classpath or via
 some System properties.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Deals with properties - substitution, dynamic properties, etc.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Marker interface for a PropertyHelper delegate.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="PropertyHelper.PropertyEnumerator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Obtains the names of all known properties.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Looks up a property's value based on its name.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Sets or overrides a property.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wrapper class that holds the attributes of an element, its children, and
 any text within it.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Instances of classes that implement this interface can register
 to be also notified when things happened during a subbuild.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Target.html" title="class in org.apache.tools.ant">Target</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class to implement a target object with required parameters.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Task.html" title="class in org.apache.tools.ant">Task</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for all tasks.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TaskAdapter.html" title="class in org.apache.tools.ant">TaskAdapter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Uses introspection to "adapt" an arbitrary Bean which doesn't
 itself extend Task, but still contains an execute method and optionally
 a setProject method.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TaskConfigurationChecker.html" title="class in org.apache.tools.ant">TaskConfigurationChecker</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Helper class for the check of the configuration of a given task.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Interface for objects which can contain tasks.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="TypeAdapter.html" title="interface in org.apache.tools.ant">TypeAdapter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Used to wrap types.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wrapper class that holds all the information necessary to create a task
 or data type that did not exist when Ant started, or one which
 has had its definition updated to use a different implementation class.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="UnsupportedAttributeException.html" title="class in org.apache.tools.ant">UnsupportedAttributeException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Used to report attempts to set an unsupported attribute</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="UnsupportedElementException.html" title="class in org.apache.tools.ant">UnsupportedElementException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Used to report attempts to set an unsupported element
 When the attempt to set the element is made,
 the code does not not know the name of the task/type
 based on a mapping from the classname to the task/type.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XmlLogger.html" title="class in org.apache.tools.ant">XmlLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Generates a file in the current directory with
 an XML description of what happened during a build.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</body>
</html>
