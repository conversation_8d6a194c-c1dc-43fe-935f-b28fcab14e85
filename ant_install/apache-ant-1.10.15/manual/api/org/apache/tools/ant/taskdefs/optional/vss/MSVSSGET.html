<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>MSVSSGET (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.vss, class: MSVSSGET">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.vss</a></div>
<h1 title="Class MSVSSGET" class="title">Class MSVSSGET</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">org.apache.tools.ant.taskdefs.optional.vss.MSVSS</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.vss.MSVSSGET</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">MSVSSGET</span>
<span class="extends-implements">extends <a href="MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</a></span></div>
<div class="block">Perform Get commands from Microsoft Visual SourceSafe.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h3 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.optional.vss.MSVSS">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.vss.<a href="MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</a></h3>
<code><a href="MSVSS.CurrentModUpdated.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a>, <a href="MSVSS.WritableFiles.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.vss.<a href="MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></h3>
<code><a href="MSVSSConstants.html#COMMAND_ADD">COMMAND_ADD</a>, <a href="MSVSSConstants.html#COMMAND_CHECKIN">COMMAND_CHECKIN</a>, <a href="MSVSSConstants.html#COMMAND_CHECKOUT">COMMAND_CHECKOUT</a>, <a href="MSVSSConstants.html#COMMAND_CP">COMMAND_CP</a>, <a href="MSVSSConstants.html#COMMAND_CREATE">COMMAND_CREATE</a>, <a href="MSVSSConstants.html#COMMAND_GET">COMMAND_GET</a>, <a href="MSVSSConstants.html#COMMAND_HISTORY">COMMAND_HISTORY</a>, <a href="MSVSSConstants.html#COMMAND_LABEL">COMMAND_LABEL</a>, <a href="MSVSSConstants.html#FLAG_AUTORESPONSE_DEF">FLAG_AUTORESPONSE_DEF</a>, <a href="MSVSSConstants.html#FLAG_AUTORESPONSE_NO">FLAG_AUTORESPONSE_NO</a>, <a href="MSVSSConstants.html#FLAG_AUTORESPONSE_YES">FLAG_AUTORESPONSE_YES</a>, <a href="MSVSSConstants.html#FLAG_BRIEF">FLAG_BRIEF</a>, <a href="MSVSSConstants.html#FLAG_CODEDIFF">FLAG_CODEDIFF</a>, <a href="MSVSSConstants.html#FLAG_COMMENT">FLAG_COMMENT</a>, <a href="MSVSSConstants.html#FLAG_FILETIME_DEF">FLAG_FILETIME_DEF</a>, <a href="MSVSSConstants.html#FLAG_FILETIME_MODIFIED">FLAG_FILETIME_MODIFIED</a>, <a href="MSVSSConstants.html#FLAG_FILETIME_UPDATED">FLAG_FILETIME_UPDATED</a>, <a href="MSVSSConstants.html#FLAG_LABEL">FLAG_LABEL</a>, <a href="MSVSSConstants.html#FLAG_LOGIN">FLAG_LOGIN</a>, <a href="MSVSSConstants.html#FLAG_NO_FILE">FLAG_NO_FILE</a>, <a href="MSVSSConstants.html#FLAG_NO_GET">FLAG_NO_GET</a>, <a href="MSVSSConstants.html#FLAG_OUTPUT">FLAG_OUTPUT</a>, <a href="MSVSSConstants.html#FLAG_OVERRIDE_WORKING_DIR">FLAG_OVERRIDE_WORKING_DIR</a>, <a href="MSVSSConstants.html#FLAG_QUIET">FLAG_QUIET</a>, <a href="MSVSSConstants.html#FLAG_RECURSION">FLAG_RECURSION</a>, <a href="MSVSSConstants.html#FLAG_REPLACE_WRITABLE">FLAG_REPLACE_WRITABLE</a>, <a href="MSVSSConstants.html#FLAG_SKIP_WRITABLE">FLAG_SKIP_WRITABLE</a>, <a href="MSVSSConstants.html#FLAG_USER">FLAG_USER</a>, <a href="MSVSSConstants.html#FLAG_VERSION">FLAG_VERSION</a>, <a href="MSVSSConstants.html#FLAG_VERSION_DATE">FLAG_VERSION_DATE</a>, <a href="MSVSSConstants.html#FLAG_VERSION_LABEL">FLAG_VERSION_LABEL</a>, <a href="MSVSSConstants.html#FLAG_WRITABLE">FLAG_WRITABLE</a>, <a href="MSVSSConstants.html#PROJECT_PREFIX">PROJECT_PREFIX</a>, <a href="MSVSSConstants.html#SS_EXE">SS_EXE</a>, <a href="MSVSSConstants.html#STYLE_BRIEF">STYLE_BRIEF</a>, <a href="MSVSSConstants.html#STYLE_CODEDIFF">STYLE_CODEDIFF</a>, <a href="MSVSSConstants.html#STYLE_DEFAULT">STYLE_DEFAULT</a>, <a href="MSVSSConstants.html#STYLE_NOFILE">STYLE_NOFILE</a>, <a href="MSVSSConstants.html#TIME_CURRENT">TIME_CURRENT</a>, <a href="MSVSSConstants.html#TIME_MODIFIED">TIME_MODIFIED</a>, <a href="MSVSSConstants.html#TIME_UPDATED">TIME_UPDATED</a>, <a href="MSVSSConstants.html#VALUE_FROMDATE">VALUE_FROMDATE</a>, <a href="MSVSSConstants.html#VALUE_FROMLABEL">VALUE_FROMLABEL</a>, <a href="MSVSSConstants.html#VALUE_NO">VALUE_NO</a>, <a href="MSVSSConstants.html#VALUE_YES">VALUE_YES</a>, <a href="MSVSSConstants.html#WRITABLE_FAIL">WRITABLE_FAIL</a>, <a href="MSVSSConstants.html#WRITABLE_REPLACE">WRITABLE_REPLACE</a>, <a href="MSVSSConstants.html#WRITABLE_SKIP">WRITABLE_SKIP</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">MSVSSGET</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAutoresponse(java.lang.String)" class="member-name-link">setAutoresponse</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;response)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Autoresponse behaviour.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDate(java.lang.String)" class="member-name-link">setDate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;date)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Date to get.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFileTimeStamp(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.CurrentModUpdated)" class="member-name-link">setFileTimeStamp</a><wbr>(<a href="MSVSS.CurrentModUpdated.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a>&nbsp;timestamp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Date and time stamp given to the local copy.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLabel(java.lang.String)" class="member-name-link">setLabel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;label)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Label to get.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLocalpath(org.apache.tools.ant.types.Path)" class="member-name-link">setLocalpath</a><wbr>(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;localPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override the project working directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setQuiet(boolean)" class="member-name-link">setQuiet</a><wbr>(boolean&nbsp;quiet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable quiet mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRecursive(boolean)" class="member-name-link">setRecursive</a><wbr>(boolean&nbsp;recursive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get files recursively.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVersion(java.lang.String)" class="member-name-link">setVersion</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;version)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Version to get.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWritable(boolean)" class="member-name-link">setWritable</a><wbr>(boolean&nbsp;writable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Unset the READ-ONLY flag on files retrieved from VSS.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWritableFiles(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.WritableFiles)" class="member-name-link">setWritableFiles</a><wbr>(<a href="MSVSS.WritableFiles.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a>&nbsp;files)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Action taken when local files are writable.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.vss.MSVSS">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.vss.<a href="MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</a></h3>
<code><a href="MSVSS.html#execute()">execute</a>, <a href="MSVSS.html#getAutoresponse()">getAutoresponse</a>, <a href="MSVSS.html#getComment()">getComment</a>, <a href="MSVSS.html#getFileTimeStamp()">getFileTimeStamp</a>, <a href="MSVSS.html#getGetLocalCopy()">getGetLocalCopy</a>, <a href="MSVSS.html#getLabel()">getLabel</a>, <a href="MSVSS.html#getLocalpath()">getLocalpath</a>, <a href="MSVSS.html#getLogin()">getLogin</a>, <a href="MSVSS.html#getOutput()">getOutput</a>, <a href="MSVSS.html#getQuiet()">getQuiet</a>, <a href="MSVSS.html#getRecursive()">getRecursive</a>, <a href="MSVSS.html#getSSCommand()">getSSCommand</a>, <a href="MSVSS.html#getStyle()">getStyle</a>, <a href="MSVSS.html#getUser()">getUser</a>, <a href="MSVSS.html#getVersion()">getVersion</a>, <a href="MSVSS.html#getVersionDate()">getVersionDate</a>, <a href="MSVSS.html#getVersionDateLabel()">getVersionDateLabel</a>, <a href="MSVSS.html#getVersionLabel()">getVersionLabel</a>, <a href="MSVSS.html#getVsspath()">getVsspath</a>, <a href="MSVSS.html#getWritable()">getWritable</a>, <a href="MSVSS.html#getWritableFiles()">getWritableFiles</a>, <a href="MSVSS.html#setFailOnError(boolean)">setFailOnError</a>, <a href="MSVSS.html#setInternalAutoResponse(java.lang.String)">setInternalAutoResponse</a>, <a href="MSVSS.html#setInternalComment(java.lang.String)">setInternalComment</a>, <a href="MSVSS.html#setInternalDate(java.lang.String)">setInternalDate</a>, <a href="MSVSS.html#setInternalDateFormat(java.text.DateFormat)">setInternalDateFormat</a>, <a href="MSVSS.html#setInternalFailOnError(boolean)">setInternalFailOnError</a>, <a href="MSVSS.html#setInternalFileTimeStamp(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.CurrentModUpdated)">setInternalFileTimeStamp</a>, <a href="MSVSS.html#setInternalFromDate(java.lang.String)">setInternalFromDate</a>, <a href="MSVSS.html#setInternalFromLabel(java.lang.String)">setInternalFromLabel</a>, <a href="MSVSS.html#setInternalGetLocalCopy(boolean)">setInternalGetLocalCopy</a>, <a href="MSVSS.html#setInternalLabel(java.lang.String)">setInternalLabel</a>, <a href="MSVSS.html#setInternalLocalPath(java.lang.String)">setInternalLocalPath</a>, <a href="MSVSS.html#setInternalNumDays(int)">setInternalNumDays</a>, <a href="MSVSS.html#setInternalOutputFilename(java.lang.String)">setInternalOutputFilename</a>, <a href="MSVSS.html#setInternalQuiet(boolean)">setInternalQuiet</a>, <a href="MSVSS.html#setInternalRecursive(boolean)">setInternalRecursive</a>, <a href="MSVSS.html#setInternalStyle(java.lang.String)">setInternalStyle</a>, <a href="MSVSS.html#setInternalToDate(java.lang.String)">setInternalToDate</a>, <a href="MSVSS.html#setInternalToLabel(java.lang.String)">setInternalToLabel</a>, <a href="MSVSS.html#setInternalUser(java.lang.String)">setInternalUser</a>, <a href="MSVSS.html#setInternalVersion(java.lang.String)">setInternalVersion</a>, <a href="MSVSS.html#setInternalWritable(boolean)">setInternalWritable</a>, <a href="MSVSS.html#setInternalWritableFiles(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.WritableFiles)">setInternalWritableFiles</a>, <a href="MSVSS.html#setLogin(java.lang.String)">setLogin</a>, <a href="MSVSS.html#setServerpath(java.lang.String)">setServerpath</a>, <a href="MSVSS.html#setSsdir(java.lang.String)">setSsdir</a>, <a href="MSVSS.html#setVsspath(java.lang.String)">setVsspath</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>MSVSSGET</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MSVSSGET</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setLocalpath(org.apache.tools.ant.types.Path)">
<h3>setLocalpath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLocalpath</span><wbr><span class="parameters">(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;localPath)</span></div>
<div class="block">Override the project working directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>localPath</code> - The path on disk.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRecursive(boolean)">
<h3>setRecursive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRecursive</span><wbr><span class="parameters">(boolean&nbsp;recursive)</span></div>
<div class="block">Get files recursively. Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>recursive</code> - The boolean value for recursive.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setQuiet(boolean)">
<h3>setQuiet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setQuiet</span><wbr><span class="parameters">(boolean&nbsp;quiet)</span></div>
<div class="block">Enable quiet mode. Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>quiet</code> - The boolean value for quiet.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setWritable(boolean)">
<h3>setWritable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWritable</span><wbr><span class="parameters">(boolean&nbsp;writable)</span></div>
<div class="block">Unset the READ-ONLY flag on files retrieved from VSS. Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>writable</code> - The boolean value for writable.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setVersion(java.lang.String)">
<h3>setVersion</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVersion</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;version)</span></div>
<div class="block">Version to get.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>version</code> - The version to get.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDate(java.lang.String)">
<h3>setDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;date)</span></div>
<div class="block">Date to get.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - The date to get.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLabel(java.lang.String)">
<h3>setLabel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLabel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;label)</span></div>
<div class="block">Label to get.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>label</code> - The label to get.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setAutoresponse(java.lang.String)">
<h3>setAutoresponse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAutoresponse</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;response)</span></div>
<div class="block">Autoresponse behaviour. Valid options are Y and N.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>response</code> - The auto response value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFileTimeStamp(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.CurrentModUpdated)">
<h3>setFileTimeStamp</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFileTimeStamp</span><wbr><span class="parameters">(<a href="MSVSS.CurrentModUpdated.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a>&nbsp;timestamp)</span></div>
<div class="block">Date and time stamp given to the local copy. Defaults to <code>current</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timestamp</code> - The file time stamping behaviour.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setWritableFiles(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.WritableFiles)">
<h3>setWritableFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWritableFiles</span><wbr><span class="parameters">(<a href="MSVSS.WritableFiles.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a>&nbsp;files)</span></div>
<div class="block">Action taken when local files are writable. Defaults to <code>fail</code>.
 <p>
 Due to ss.exe returning with an exit code of '100' for both errors and when
 a file has been skipped, <code>failonerror</code> is set to false when using
 the <code>skip</code> option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>files</code> - The action to take.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
