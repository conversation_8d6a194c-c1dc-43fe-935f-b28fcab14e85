<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>JUnitTaskMirror (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, interface: JUnitTaskMirror">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Interface JUnitTaskMirror" class="title">Interface JUnitTaskMirror</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="JUnitTaskMirrorImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirrorImpl</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">JUnitTaskMirror</span></div>
<div class="block">Handles the portions of <a href="JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTask</code></a> which need to directly access
 actual JUnit classes, so that junit.jar need not be on Ant's startup classpath.
 Neither JUnitTask.java nor JUnitTaskMirror.java nor their transitive static
 deps may import any junit.** classes!
 Specifically, need to not refer to
 - JUnitResultFormatter or its subclasses
 - JUnitVersionHelper
 - JUnitTestRunner
 Cf.  JUnitTask.SplitLoader#isSplit(String)
 Public only to permit access from classes in this package; do not use directly.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li>"bug #38799"</li>
</ul>
</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Interface</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a></code></div>
<div class="col-last even-row-color">
<div class="block">The interface that JUnitResultFormatter extends.</div>
</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Interface that test runners implement.</div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></code></div>
<div class="col-last even-row-color">
<div class="block">The interface that SummaryJUnitResultFormatter extends.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#addVmExit(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror,java.io.OutputStream,java.lang.String,java.lang.String)" class="member-name-link">addVmExit</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>&nbsp;formatter,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;testCase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Add the formatter to be called when the jvm exits before
 the test suite finishes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#newJUnitTestRunner(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String%5B%5D,boolean,boolean,boolean,boolean,boolean,org.apache.tools.ant.AntClassLoader)" class="member-name-link">newJUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filterTrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="../../../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a>&nbsp;classLoader)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Create a new test runner for a test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#newSummaryJUnitResultFormatter()" class="member-name-link">newSummaryJUnitResultFormatter</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Create a summary result formatter.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addVmExit(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror,java.io.OutputStream,java.lang.String,java.lang.String)">
<h3>addVmExit</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">addVmExit</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>&nbsp;formatter,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;testCase)</span></div>
<div class="block">Add the formatter to be called when the jvm exits before
 the test suite finishes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test.</dd>
<dd><code>formatter</code> - the formatter to use.</dd>
<dd><code>out</code> - the output stream to use.</dd>
<dd><code>message</code> - the message to write out.</dd>
<dd><code>testCase</code> - the name of the test.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="newJUnitTestRunner(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String[],boolean,boolean,boolean,boolean,boolean,org.apache.tools.ant.AntClassLoader)">
<h3>newJUnitTestRunner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></span>&nbsp;<span class="element-name">newJUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filterTrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="../../../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a>&nbsp;classLoader)</span></div>
<div class="block">Create a new test runner for a test.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>methods</code> - names of the test methods to be run.</dd>
<dd><code>haltOnError</code> - if true halt the tests if an error occurs.</dd>
<dd><code>filterTrace</code> - if true filter the stack traces.</dd>
<dd><code>haltOnFailure</code> - if true halt the test if a failure occurs.</dd>
<dd><code>showOutput</code> - if true show output.</dd>
<dd><code>logTestListenerEvents</code> - if true log test listener events.</dd>
<dd><code>classLoader</code> - the classloader to use to create the runner.</dd>
<dt>Returns:</dt>
<dd>the test runner.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="newSummaryJUnitResultFormatter()">
<h3>newSummaryJUnitResultFormatter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type"><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></span>&nbsp;<span class="element-name">newSummaryJUnitResultFormatter</span>()</div>
<div class="block">Create a summary result formatter.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the created formatter.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
