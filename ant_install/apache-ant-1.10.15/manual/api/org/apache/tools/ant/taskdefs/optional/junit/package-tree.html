<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.taskdefs.optional.junit Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="tree: package: org.apache.tools.ant.taskdefs.optional.junit">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.taskdefs.optional.junit</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractCollection.html" class="type-name-link external-link" title="class or interface in java.util">AbstractCollection</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;E&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractList.html" class="type-name-link external-link" title="class or interface in java.util">AbstractList</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" class="type-name-link external-link" title="class or interface in java.util">Vector</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/RandomAccess.html" title="class or interface in java.util" class="external-link">RandomAccess</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="DOMUtil.NodeListImpl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeListImpl</a> (implements org.w3c.dom.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/w3c/dom/NodeList.html" title="class or interface in org.w3c.dom" class="external-link">NodeList</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractMap.html" class="type-name-link external-link" title="class or interface in java.util">AbstractMap</a>&lt;K,<wbr>V&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/HashMap.html" class="type-name-link external-link" title="class or interface in java.util">HashMap</a>&lt;K,<wbr>V&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">junit.framework.JUnit4TestAdapterCache
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="CustomJUnit4TestAdapterCache.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">CustomJUnit4TestAdapterCache</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="AggregateTransformer.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="BaseTest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="BatchTest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTest.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="BriefJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">BriefJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="Constants.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">Constants</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="DOMUtil.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="../../../types/EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="AggregateTransformer.Format.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer.Format</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="FormatterElement.TypeAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement.TypeAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTask.ForkMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTask.SummaryAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="Enumerations.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">Enumerations</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="FailureRecorder.TestInfos.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder.TestInfos</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="FormatterElement.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnit4TestMethodAdapter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnit4TestMethodAdapter</a> (implements junit.framework.Test)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTask.TestResultHolder.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirrorImpl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirrorImpl</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTestRunner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTestRunner</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a>, junit.framework.TestListener)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitVersionHelper.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitVersionHelper</a></li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" class="type-name-link external-link" title="class or interface in java.io">OutputStream</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="../../../util/LineOrientedOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="../../LogOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">LogOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTask.JUnitLogOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="PlainJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">PlainJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="../../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="FailureRecorder.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder</a> (implements org.apache.tools.ant.<a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="../../../Task.html" class="type-name-link" title="class in org.apache.tools.ant">Task</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="XMLResultAggregator.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="../../PumpStreamHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler</a> (implements org.apache.tools.ant.taskdefs.<a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTask.JUnitLogStreamHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="SummaryJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">SummaryJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="OutErrSummaryJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">OutErrSummaryJUnitResultFormatter</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="TearDownOnVmCrash.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">TearDownOnVmCrash</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="TestIgnored.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestIgnored</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="TestListenerWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestListenerWrapper</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, junit.framework.TestListener)</li>
<li class="circle">junit.framework.TestResult
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="IgnoredTestResult.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestResult</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="XMLJUnitResultFormatter.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLJUnitResultFormatter</a> (implements org.apache.tools.ant.taskdefs.optional.junit.<a href="IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>, org.apache.tools.ant.taskdefs.optional.junit.<a href="XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="DOMUtil.NodeFilter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeFilter</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a> (also extends junit.framework.TestListener)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></li>
<li class="circle">junit.framework.TestListener
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="IgnoredTestListener.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitResultFormatter.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a> (also extends org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.junit.<a href="XMLConstants.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></li>
</ul>
</section>
</main>
</body>
</html>
