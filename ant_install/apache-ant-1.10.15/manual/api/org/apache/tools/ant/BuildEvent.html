<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>BuildEvent (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: BuildEvent">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class BuildEvent" class="title">Class BuildEvent</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html" title="class or interface in java.util" class="external-link">java.util.EventObject</a>
<div class="inheritance">org.apache.tools.ant.BuildEvent</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">BuildEvent</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html" title="class or interface in java.util" class="external-link">EventObject</a></span></div>
<div class="block">Class representing an event occurring during a build. An
 event is built by specifying either a project, a task or a target.
 A project level event will only have a project reference;
 a target level event will have project and target references;
 a task level event will have project, target and task references.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../serialized-form.html#org.apache.tools.ant.BuildEvent">Serialized Form</a></li>
</ul>
</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.EventObject">Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html" title="class or interface in java.util" class="external-link">EventObject</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html#source" title="class or interface in java.util" class="external-link">source</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Project)" class="member-name-link">BuildEvent</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct a BuildEvent for a project level event.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Target)" class="member-name-link">BuildEvent</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct a BuildEvent for a target level event.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Task)" class="member-name-link">BuildEvent</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct a BuildEvent for a task level event.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getException()" class="member-name-link">getException</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the exception that was thrown, if any.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMessage()" class="member-name-link">getMessage</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the logging message.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPriority()" class="member-name-link">getPriority</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the priority of the logging message.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProject()" class="member-name-link">getProject</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the project that fired this event.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Target.html" title="class in org.apache.tools.ant">Target</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTarget()" class="member-name-link">getTarget</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the target that fired this event.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTask()" class="member-name-link">getTask</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the task that fired this event.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setException(java.lang.Throwable)" class="member-name-link">setException</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the exception associated with this event.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMessage(java.lang.String,int)" class="member-name-link">setMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the message and priority associated with this event.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.EventObject">Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html" title="class or interface in java.util" class="external-link">EventObject</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html#getSource()" title="class or interface in java.util" class="external-link">getSource</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventObject.html#toString()" title="class or interface in java.util" class="external-link">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Project)">
<h3>BuildEvent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BuildEvent</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Construct a BuildEvent for a project level event.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project that emitted the event.
                Should not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Target)">
<h3>BuildEvent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BuildEvent</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">Construct a BuildEvent for a target level event.
 The project associated with the event is derived
 from the given target.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - the target that emitted the event.
               Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Task)">
<h3>BuildEvent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BuildEvent</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</span></div>
<div class="block">Construct a BuildEvent for a task level event.
 The project and target associated with the event
 are derived from the given task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - the task that emitted the event.
             Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setMessage(java.lang.String,int)">
<h3>setMessage</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMessage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</span></div>
<div class="block">Sets the message and priority associated with this event.
 This is used for "messageLogged" events.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - the message to be associated with this event.
                Should not be <code>null</code>.</dd>
<dd><code>priority</code> - the priority to be associated with this event,
                 as defined in the <a href="Project.html" title="class in org.apache.tools.ant"><code>Project</code></a> class.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><code>BuildListener.messageLogged(BuildEvent)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setException(java.lang.Throwable)">
<h3>setException</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setException</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</span></div>
<div class="block">Sets the exception associated with this event. This is used
 for "messageLogged", "taskFinished", "targetFinished", and "buildFinished"
 events.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exception</code> - The exception to be associated with this event.
                  May be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><code>BuildListener.messageLogged(BuildEvent)</code></a></li>
<li><a href="BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)"><code>BuildListener.taskFinished(BuildEvent)</code></a></li>
<li><a href="BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)"><code>BuildListener.targetFinished(BuildEvent)</code></a></li>
<li><a href="BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)"><code>BuildListener.buildFinished(BuildEvent)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getProject()">
<h3>getProject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span>()</div>
<div class="block">Returns the project that fired this event.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the project that fired this event</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTarget()">
<h3>getTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Target.html" title="class in org.apache.tools.ant">Target</a></span>&nbsp;<span class="element-name">getTarget</span>()</div>
<div class="block">Returns the target that fired this event.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the project that fired this event, or <code>null</code>
          if this event is a project level event.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTask()">
<h3>getTask</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Task.html" title="class in org.apache.tools.ant">Task</a></span>&nbsp;<span class="element-name">getTask</span>()</div>
<div class="block">Returns the task that fired this event.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the task that fired this event, or <code>null</code>
         if this event is a project or target level event.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMessage()">
<h3>getMessage</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMessage</span>()</div>
<div class="block">Returns the logging message. This field will only be set
 for "messageLogged" events.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the message associated with this event, or <code>null</code>
         if no message has been set.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><code>BuildListener.messageLogged(BuildEvent)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPriority()">
<h3>getPriority</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPriority</span>()</div>
<div class="block">Returns the priority of the logging message. This field will only
 be set for "messageLogged" events. The meaning of this priority
 is as specified by the constants in the <a href="Project.html" title="class in org.apache.tools.ant"><code>Project</code></a> class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the priority associated with this event.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><code>BuildListener.messageLogged(BuildEvent)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getException()">
<h3>getException</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></span>&nbsp;<span class="element-name">getException</span>()</div>
<div class="block">Returns the exception that was thrown, if any. This field will only
 be set for "messageLogged", "taskFinished", "targetFinished", and "buildFinished"
 events.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the exception associated with this exception, or
         <code>null</code> if no exception has been set.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><code>BuildListener.messageLogged(BuildEvent)</code></a></li>
<li><a href="BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)"><code>BuildListener.taskFinished(BuildEvent)</code></a></li>
<li><a href="BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)"><code>BuildListener.targetFinished(BuildEvent)</code></a></li>
<li><a href="BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)"><code>BuildListener.buildFinished(BuildEvent)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
