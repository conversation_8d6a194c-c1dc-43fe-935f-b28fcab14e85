<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.types Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="tree: package: org.apache.tools.ant.types">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.types</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="Assertions.BaseAssertion.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions.BaseAssertion</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="Assertions.DisabledAssertion.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions.DisabledAssertion</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Assertions.EnabledAssertion.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions.EnabledAssertion</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="Commandline.html" class="type-name-link" title="class in org.apache.tools.ant.types">Commandline</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="Commandline.Marker.html" class="type-name-link" title="class in org.apache.tools.ant.types">Commandline.Marker</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="CommandlineJava.html" class="type-name-link" title="class in org.apache.tools.ant.types">CommandlineJava</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="../DirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant">DirectoryScanner</a> (implements org.apache.tools.ant.<a href="../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a>, org.apache.tools.ant.types.<a href="ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>, org.apache.tools.ant.types.selectors.<a href="selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="ArchiveScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types">ArchiveScanner</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="TarScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types">TarScanner</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="ZipScanner.html" class="type-name-link" title="class in org.apache.tools.ant.types">ZipScanner</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="CharSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">CharSet</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Comparison.html" class="type-name-link" title="class in org.apache.tools.ant.types">Comparison</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FilterSet.OnMissing.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet.OnMissing</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="LogLevel.html" class="type-name-link" title="class in org.apache.tools.ant.types">LogLevel</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Mapper.MapperType.html" class="type-name-link" title="class in org.apache.tools.ant.types">Mapper.MapperType</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="PropertySet.BuiltinPropertySetName.html" class="type-name-link" title="class in org.apache.tools.ant.types">PropertySet.BuiltinPropertySetName</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Quantifier.html" class="type-name-link" title="class in org.apache.tools.ant.types">Quantifier</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="TimeComparison.html" class="type-name-link" title="class in org.apache.tools.ant.types">TimeComparison</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="Environment.html" class="type-name-link" title="class in org.apache.tools.ant.types">Environment</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="CommandlineJava.SysProperties.html" class="type-name-link" title="class in org.apache.tools.ant.types">CommandlineJava.SysProperties</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="Environment.Variable.html" class="type-name-link" title="class in org.apache.tools.ant.types">Environment.Variable</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FileList.FileName.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileList.FileName</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FilterSet.Filter.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet.Filter</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FilterSet.FiltersFile.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet.FiltersFile</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FilterSetCollection.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSetCollection</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FlexInteger.html" class="type-name-link" title="class in org.apache.tools.ant.types">FlexInteger</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="ModuleVersion.html" class="type-name-link" title="class in org.apache.tools.ant.types">ModuleVersion</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Parameter.html" class="type-name-link" title="class in org.apache.tools.ant.types">Parameter</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Path.PathElement.html" class="type-name-link" title="class in org.apache.tools.ant.types">Path.PathElement</a> (implements org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="PatternSet.NameEntry.html" class="type-name-link" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="PatternSet.PatternFileNameEntry.html" class="type-name-link" title="class in org.apache.tools.ant.types">PatternSet.PatternFileNameEntry</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="Permissions.Permission.html" class="type-name-link" title="class in org.apache.tools.ant.types">Permissions.Permission</a></li>
<li class="circle">org.apache.tools.ant.<a href="../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="Commandline.Argument.html" class="type-name-link" title="class in org.apache.tools.ant.types">Commandline.Argument</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="DataType.html" class="type-name-link" title="class in org.apache.tools.ant.types">DataType</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="AbstractFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">AbstractFileSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="DirSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">DirSet</a> (implements org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="FileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileSet</a> (implements org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="ArchiveFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="TarFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">TarFileSet</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="ZipFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">ZipFileSet</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="AntFilterReader.html" class="type-name-link" title="class in org.apache.tools.ant.types">AntFilterReader</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Assertions.html" class="type-name-link" title="class in org.apache.tools.ant.types">Assertions</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="Description.html" class="type-name-link" title="class in org.apache.tools.ant.types">Description</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FileList.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileList</a> (implements org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="FilterChain.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterChain</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="FilterSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">FilterSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="Mapper.html" class="type-name-link" title="class in org.apache.tools.ant.types">Mapper</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Path.html" class="type-name-link" title="class in org.apache.tools.ant.types">Path</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="PatternSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">PatternSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="PropertySet.html" class="type-name-link" title="class in org.apache.tools.ant.types">PropertySet</a> (implements org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="RedirectorElement.html" class="type-name-link" title="class in org.apache.tools.ant.types">RedirectorElement</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="RegularExpression.html" class="type-name-link" title="class in org.apache.tools.ant.types">RegularExpression</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Resource.html" class="type-name-link" title="class in org.apache.tools.ant.types">Resource</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="Substitution.html" class="type-name-link" title="class in org.apache.tools.ant.types">Substitution</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="XMLCatalog.html" class="type-name-link" title="class in org.apache.tools.ant.types">XMLCatalog</a> (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>, javax.xml.transform.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/transform/URIResolver.html" title="class or interface in javax.xml.transform" class="external-link">URIResolver</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="Permissions.html" class="type-name-link" title="class in org.apache.tools.ant.types">Permissions</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="PropertySet.PropertyRef.html" class="type-name-link" title="class in org.apache.tools.ant.types">PropertySet.PropertyRef</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="Reference.html" class="type-name-link" title="class in org.apache.tools.ant.types">Reference</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="ResourceLocation.html" class="type-name-link" title="class in org.apache.tools.ant.types">ResourceLocation</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="DTDLocation.html" class="type-name-link" title="class in org.apache.tools.ant.types">DTDLocation</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" class="type-name-link external-link" title="class or interface in java.lang">Iterable</a>&lt;T&gt;
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="ResourceCollection.html" class="type-name-link" title="interface in org.apache.tools.ant.types">ResourceCollection</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="Parameterizable.html" class="type-name-link" title="interface in org.apache.tools.ant.types">Parameterizable</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="ResourceFactory.html" class="type-name-link" title="interface in org.apache.tools.ant.types">ResourceFactory</a></li>
</ul>
</section>
</main>
</body>
</html>
