<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>DataType (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, class: DataType">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Class DataType" class="title">Class DataType</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance">org.apache.tools.ant.types.DataType</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code>, <code><a href="resources/AbstractResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">AbstractResourceCollectionWrapper</a></code>, <code><a href="selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></code>, <code><a href="AntFilterReader.html" title="class in org.apache.tools.ant.types">AntFilterReader</a></code>, <code><a href="resources/Archives.html" title="class in org.apache.tools.ant.types.resources">Archives</a></code>, <code><a href="Assertions.html" title="class in org.apache.tools.ant.types">Assertions</a></code>, <code><a href="resources/BaseResourceCollectionContainer.html" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionContainer</a></code>, <code><a href="selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code>, <code><a href="resources/selectors/Compare.html" title="class in org.apache.tools.ant.types.resources.selectors">Compare</a></code>, <code><a href="Description.html" title="class in org.apache.tools.ant.types">Description</a></code>, <code><a href="../taskdefs/optional/extension/ExtensionAdapter.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionAdapter</a></code>, <code><a href="../taskdefs/optional/extension/ExtensionSet.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionSet</a></code>, <code><a href="FileList.html" title="class in org.apache.tools.ant.types">FileList</a></code>, <code><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></code>, <code><a href="FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</a></code>, <code><a href="optional/image/ImageOperation.html" title="class in org.apache.tools.ant.types.optional.image">ImageOperation</a></code>, <code><a href="optional/imageio/ImageOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">ImageOperation</a></code>, <code><a href="../taskdefs/condition/IsSigned.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSigned</a></code>, <code><a href="resources/MappedResourceCollection.html" title="class in org.apache.tools.ant.types.resources">MappedResourceCollection</a></code>, <code><a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></code>, <code><a href="Path.html" title="class in org.apache.tools.ant.types">Path</a></code>, <code><a href="PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></code>, <code><a href="PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a></code>, <code><a href="RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</a></code>, <code><a href="RegularExpression.html" title="class in org.apache.tools.ant.types">RegularExpression</a></code>, <code><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code>, <code><a href="resources/comparators/ResourceComparator.html" title="class in org.apache.tools.ant.types.resources.comparators">ResourceComparator</a></code>, <code><a href="resources/ResourceList.html" title="class in org.apache.tools.ant.types.resources">ResourceList</a></code>, <code><a href="resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</a></code>, <code><a href="resources/selectors/ResourceSelectorContainer.html" title="class in org.apache.tools.ant.types.resources.selectors">ResourceSelectorContainer</a></code>, <code><a href="selectors/SignedSelector.html" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a></code>, <code><a href="Substitution.html" title="class in org.apache.tools.ant.types">Substitution</a></code>, <code><a href="XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">DataType</span>
<span class="extends-implements">extends <a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>
implements <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></span></div>
<div class="block">Base class for those classes that can appear inside the build file
 as stand alone data types.

 <p>This class handles the common description attribute and provides
 a default implementation for reference handling and checking for
 circular references that is appropriate for types that can not be
 nested inside elements of the same type (i.e. &lt;patternset&gt;
 but not &lt;path&gt;).</p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#checked" class="member-name-link">checked</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ref" class="member-name-link">ref</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DataType</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkAttributesAllowed()" class="member-name-link">checkAttributesAllowed</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">check that it is ok to set attributes, i.e that no reference is defined</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkChildrenAllowed()" class="member-name-link">checkChildrenAllowed</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">check that it is ok to add children, i.e that no reference is defined</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#circularReference()" class="member-name-link">circularReference</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates an exception that indicates the user has generated a
 loop of data types referencing each other.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference()" class="member-name-link">dieOnCircularReference</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience method.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stack,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience method.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected &lt;T&gt;&nbsp;T</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getCheckedRef()" class="member-name-link">getCheckedRef</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use getCheckedRef(Class)</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected &lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCheckedRef(java.lang.Class)" class="member-name-link">getCheckedRef</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;requiredClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected &lt;T&gt;&nbsp;T</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCheckedRef(java.lang.Class,java.lang.String)" class="member-name-link">getCheckedRef</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;requiredClass,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dataTypeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected &lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)" class="member-name-link">getCheckedRef</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;requiredClass,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dataTypeName,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected &lt;T&gt;&nbsp;T</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getCheckedRef(org.apache.tools.ant.Project)" class="member-name-link">getCheckedRef</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use getCheckedRef(Class)</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDataTypeName()" class="member-name-link">getDataTypeName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets as descriptive as possible a name used for this datatype instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRefid()" class="member-name-link">getRefid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the reference set on this object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">invokeCircularReferenceCheck</a><wbr>(<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a>&nbsp;dt,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Allow DataTypes outside org.apache.tools.ant.types to indirectly call
 dieOnCircularReference on nested DataTypes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isChecked()" class="member-name-link">isChecked</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The flag that is used to indicate that circular references have been checked.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isReference()" class="member-name-link">isReference</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Has the refid attribute of this element been set?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#noChildrenAllowed()" class="member-name-link">noChildrenAllowed</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates an exception that indicates that this XML element must
 not have child elements if the refid attribute is set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">pushAndInvokeCircularReferenceCheck</a><wbr>(<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a>&nbsp;dt,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Allow DataTypes outside org.apache.tools.ant.types to indirectly call
 dieOnCircularReference on nested DataTypes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setChecked(boolean)" class="member-name-link">setChecked</a><wbr>(boolean&nbsp;checked)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the flag that is used to indicate that circular references have been checked.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefid(org.apache.tools.ant.types.Reference)" class="member-name-link">setRefid</a><wbr>(<a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the value of the refid attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#tooManyAttributes()" class="member-name-link">tooManyAttributes</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates an exception that indicates that refid has to be the
 only attribute if it is set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Basic DataType toString().</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ref">
<h3>ref</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a></span>&nbsp;<span class="element-name">ref</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.
             The user should not be directly referencing
             variable. Please use <a href="#getRefid()"><code>getRefid()</code></a> instead.</div>
</div>
<div class="block">Value to the refid attribute.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="checked">
<h3>checked</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">checked</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.
             The user should not be directly referencing
             variable. Please use <a href="#setChecked(boolean)"><code>setChecked(boolean)</code></a> or
             <a href="#isChecked()"><code>isChecked()</code></a> instead.</div>
</div>
<div class="block">Are we sure we don't hold circular references?

 <p>Subclasses are responsible for setting this value to false
 if we'd need to investigate this condition (usually because a
 child element has been added that is a subclass of
 DataType).</p></div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DataType</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DataType</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isReference()">
<h3>isReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isReference</span>()</div>
<div class="block">Has the refid attribute of this element been set?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the refid attribute has been set</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRefid(org.apache.tools.ant.types.Reference)">
<h3>setRefid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefid</span><wbr><span class="parameters">(<a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Set the value of the refid attribute.

 <p>Subclasses may need to check whether any other attributes
 have been set as well or child elements have been created and
 thus override this method. if they do the must call
 <code>super.setRefid</code>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - the reference to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDataTypeName()">
<h3>getDataTypeName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDataTypeName</span>()</div>
<div class="block">Gets as descriptive as possible a name used for this datatype instance.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>String</code> name.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference()">
<h3>dieOnCircularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span>()</div>
<div class="block">Convenience method.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Convenience method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the Ant Project instance against which to resolve references.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stack,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span>
                               throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).

 <p>If one is included, throw a BuildException created by <a href="#circularReference()"><code>circularReference</code></a>.</p>

 <p>This implementation is appropriate only for a DataType that
 cannot hold other DataTypes as children.</p>

 <p>The general contract of this method is that it shouldn't do
 anything if <a href="#checked"><code>checked</code></a> is true and
 set it to true on exit.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stack</code> - the stack of references to check.</dd>
<dd><code>project</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">
<h3>invokeCircularReferenceCheck</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">invokeCircularReferenceCheck</span><wbr><span class="parameters">(<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a>&nbsp;dt,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Allow DataTypes outside org.apache.tools.ant.types to indirectly call
 dieOnCircularReference on nested DataTypes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dt</code> - the DataType to check.</dd>
<dd><code>stk</code> - the stack of references to check.</dd>
<dd><code>p</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">
<h3>pushAndInvokeCircularReferenceCheck</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushAndInvokeCircularReferenceCheck</span><wbr><span class="parameters">(<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a>&nbsp;dt,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Allow DataTypes outside org.apache.tools.ant.types to indirectly call
 dieOnCircularReference on nested DataTypes.

 <p>Pushes dt on the stack, runs dieOnCircularReference and pops
 it again.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dt</code> - the DataType to check.</dd>
<dd><code>stk</code> - the stack of references to check.</dd>
<dd><code>p</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCheckedRef()">
<h3>getCheckedRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getCheckedRef</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use getCheckedRef(Class)</div>
</div>
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - required reference type</dd>
<dt>Returns:</dt>
<dd>the dereferenced object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the reference is invalid (circular ref, wrong class, etc).</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCheckedRef(java.lang.Class)">
<h3>getCheckedRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getCheckedRef</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;requiredClass)</span></div>
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - required reference type</dd>
<dt>Parameters:</dt>
<dd><code>requiredClass</code> - the class that this reference should be a subclass of.</dd>
<dt>Returns:</dt>
<dd>the dereferenced object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the reference is invalid (circular ref, wrong class, etc).</dd>
<dt>Since:</dt>
<dd>Ant 1.10.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCheckedRef(org.apache.tools.ant.Project)">
<h3>getCheckedRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getCheckedRef</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use getCheckedRef(Class)</div>
</div>
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - required reference type</dd>
<dt>Parameters:</dt>
<dd><code>p</code> - the Ant Project instance against which to resolve references.</dd>
<dt>Returns:</dt>
<dd>the dereferenced object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the reference is invalid (circular ref, wrong class, etc).</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCheckedRef(java.lang.Class,java.lang.String)">
<h3>getCheckedRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getCheckedRef</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;requiredClass,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dataTypeName)</span></div>
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - required reference type</dd>
<dt>Parameters:</dt>
<dd><code>requiredClass</code> - the class that this reference should be a subclass of.</dd>
<dd><code>dataTypeName</code> - the name of the datatype that the reference should be
                      (error message use only).</dd>
<dt>Returns:</dt>
<dd>the dereferenced object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the reference is invalid (circular ref, wrong class, etc).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">
<h3>getCheckedRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getCheckedRef</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;requiredClass,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dataTypeName,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Performs the check for circular references and returns the
 referenced object.  This version allows the fallback Project instance to be specified.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - required reference type</dd>
<dt>Parameters:</dt>
<dd><code>requiredClass</code> - the class that this reference should be a subclass of.</dd>
<dd><code>dataTypeName</code> - the name of the datatype that the reference should be
                      (error message use only).</dd>
<dd><code>project</code> - the fallback Project instance for dereferencing.</dd>
<dt>Returns:</dt>
<dd>the dereferenced object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the reference is invalid (circular ref, wrong class, etc),
                        or if <code>project</code> is <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="tooManyAttributes()">
<h3>tooManyAttributes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">tooManyAttributes</span>()</div>
<div class="block">Creates an exception that indicates that refid has to be the
 only attribute if it is set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the exception to throw</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="noChildrenAllowed()">
<h3>noChildrenAllowed</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">noChildrenAllowed</span>()</div>
<div class="block">Creates an exception that indicates that this XML element must
 not have child elements if the refid attribute is set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the exception to throw</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="circularReference()">
<h3>circularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">circularReference</span>()</div>
<div class="block">Creates an exception that indicates the user has generated a
 loop of data types referencing each other.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the exception to throw</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isChecked()">
<h3>isChecked</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isChecked</span>()</div>
<div class="block">The flag that is used to indicate that circular references have been checked.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if circular references have been checked</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setChecked(boolean)">
<h3>setChecked</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setChecked</span><wbr><span class="parameters">(boolean&nbsp;checked)</span></div>
<div class="block">Set the flag that is used to indicate that circular references have been checked.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>checked</code> - if true, if circular references have been checked</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRefid()">
<h3>getRefid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a></span>&nbsp;<span class="element-name">getRefid</span>()</div>
<div class="block">get the reference set on this object</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the reference or null</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="checkAttributesAllowed()">
<h3>checkAttributesAllowed</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkAttributesAllowed</span>()</div>
<div class="block">check that it is ok to set attributes, i.e that no reference is defined</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if not allowed</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="checkChildrenAllowed()">
<h3>checkChildrenAllowed</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkChildrenAllowed</span>()</div>
<div class="block">check that it is ok to add children, i.e that no reference is defined</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if not allowed</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Basic DataType toString().</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>this DataType formatted as a String.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()
             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/CloneNotSupportedException.html" title="class or interface in java.lang" class="external-link">CloneNotSupportedException</a></span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../ProjectComponent.html#clone()">clone</a></code>&nbsp;in class&nbsp;<code><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></code></dd>
<dt>Returns:</dt>
<dd>a shallow copy of this DataType.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/CloneNotSupportedException.html" title="class or interface in java.lang" class="external-link">CloneNotSupportedException</a></code> - if there is a problem.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
