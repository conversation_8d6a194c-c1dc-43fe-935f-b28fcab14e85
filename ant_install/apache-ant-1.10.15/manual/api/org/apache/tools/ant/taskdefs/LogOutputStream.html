<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>LogOutputStream (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: LogOutputStream">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class LogOutputStream" class="title">Class LogOutputStream</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">java.io.OutputStream</a>
<div class="inheritance"><a href="../util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.LineOrientedOutputStream</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.LogOutputStream</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="optional/junit/JUnitTask.JUnitLogOutputStream.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">LogOutputStream</span>
<span class="extends-implements">extends <a href="../util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a></span></div>
<div class="block">Logs each line written to this stream to the log system of ant.

 <p>Tries to be smart about line separators.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.ProjectComponent)" class="member-name-link">LogOutputStream</a><wbr>(<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;pc)</code></div>
<div class="col-last even-row-color">
<div class="block">Create a new LogOutputStream for the specified ProjectComponent.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.ProjectComponent,int)" class="member-name-link">LogOutputStream</a><wbr>(<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;pc,
 int&nbsp;level)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new instance of this class.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Task,int)" class="member-name-link">LogOutputStream</a><wbr>(<a href="../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 int&nbsp;level)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new instance of this class.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMessageLevel()" class="member-name-link">getMessageLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the level.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processBuffer()" class="member-name-link">processBuffer</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Converts the buffer to a string and sends it to <code>processLine</code></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processLine(java.lang.String)" class="member-name-link">processLine</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;line)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a line to the log system of ant.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processLine(java.lang.String,int)" class="member-name-link">processLine</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;line,
 int&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a line to the log system of ant.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.util.LineOrientedOutputStream">Methods inherited from class&nbsp;org.apache.tools.ant.util.<a href="../util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a></h3>
<code><a href="../util/LineOrientedOutputStream.html#close()">close</a>, <a href="../util/LineOrientedOutputStream.html#flush()">flush</a>, <a href="../util/LineOrientedOutputStream.html#processLine(byte%5B%5D)">processLine</a>, <a href="../util/LineOrientedOutputStream.html#write(byte%5B%5D,int,int)">write</a>, <a href="../util/LineOrientedOutputStream.html#write(int)">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.OutputStream">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#nullOutputStream()" title="class or interface in java.io" class="external-link">nullOutputStream</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#write(byte%5B%5D)" title="class or interface in java.io" class="external-link">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.ProjectComponent)">
<h3>LogOutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">LogOutputStream</span><wbr><span class="parameters">(<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;pc)</span></div>
<div class="block">Create a new LogOutputStream for the specified ProjectComponent.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pc</code> - the project component for whom to log</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Task,int)">
<h3>LogOutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">LogOutputStream</span><wbr><span class="parameters">(<a href="../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 int&nbsp;level)</span></div>
<div class="block">Creates a new instance of this class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - the task for whom to log</dd>
<dd><code>level</code> - loglevel used to log data written to this stream.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.ProjectComponent,int)">
<h3>LogOutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">LogOutputStream</span><wbr><span class="parameters">(<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;pc,
 int&nbsp;level)</span></div>
<div class="block">Creates a new instance of this class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pc</code> - the project component for whom to log</dd>
<dd><code>level</code> - loglevel used to log data written to this stream.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="processBuffer()">
<h3>processBuffer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processBuffer</span>()</div>
<div class="block">Converts the buffer to a string and sends it to <code>processLine</code></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../util/LineOrientedOutputStream.html#processBuffer()">processBuffer</a></code>&nbsp;in class&nbsp;<code><a href="../util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="processLine(java.lang.String)">
<h3>processLine</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processLine</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;line)</span></div>
<div class="block">Logs a line to the log system of ant.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../util/LineOrientedOutputStream.html#processLine(java.lang.String)">processLine</a></code>&nbsp;in class&nbsp;<code><a href="../util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a></code></dd>
<dt>Parameters:</dt>
<dd><code>line</code> - the line to log.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="processLine(java.lang.String,int)">
<h3>processLine</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processLine</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;line,
 int&nbsp;level)</span></div>
<div class="block">Logs a line to the log system of ant.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>line</code> - the line to log.</dd>
<dd><code>level</code> - the logging level to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMessageLevel()">
<h3>getMessageLevel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMessageLevel</span>()</div>
<div class="block">Get the level.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the log level.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
