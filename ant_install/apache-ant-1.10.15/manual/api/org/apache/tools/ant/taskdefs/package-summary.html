<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.taskdefs (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs" class="title">Package org.apache.tools.ant.taskdefs</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="compilers/package-summary.html">org.apache.tools.ant.taskdefs.compilers</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="condition/package-summary.html">org.apache.tools.ant.taskdefs.condition</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="cvslib/package-summary.html">org.apache.tools.ant.taskdefs.cvslib</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="email/package-summary.html">org.apache.tools.ant.taskdefs.email</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="launcher/package-summary.html">org.apache.tools.ant.taskdefs.launcher</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="modules/package-summary.html">org.apache.tools.ant.taskdefs.modules</a></div>
<div class="col-last even-row-color">
<div class="block">Tasks for dealing with Java modules, which are supported starting with
 Java 9.</div>
</div>
<div class="col-first odd-row-color"><a href="optional/package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="rmic/package-summary.html">org.apache.tools.ant.taskdefs.rmic</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button><button id="class-summary-tab5" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab5', 2)" class="table-tab">Exception Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">original Cvs.java 1.20</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AbstractCvsTask.Module.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask.Module</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is factored out from <a href="SignJar.html" title="class in org.apache.tools.ant.taskdefs"><code>SignJar</code></a>; a base class that can be used
 for both signing and verifying JAR files using jarsigner</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Ant.html" title="class in org.apache.tools.ant.taskdefs">Ant</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Build a sub-project.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Ant.Reference.html" title="class in org.apache.tools.ant.taskdefs">Ant.Reference</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Helper class that implements the nested &lt;reference&gt;
 element of &lt;ant&gt; and &lt;antcall&gt;.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Ant.TargetElement.html" title="class in org.apache.tools.ant.taskdefs">Ant.TargetElement</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Helper class that implements the nested &lt;target&gt;
 element of &lt;ant&gt; and &lt;antcall&gt;.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Antlib.html" title="class in org.apache.tools.ant.taskdefs">Antlib</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Antlib task.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base class for tasks that that can be used in antlibs.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AntStructure.html" title="class in org.apache.tools.ant.taskdefs">AntStructure</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Creates a partial DTD for Ant from the currently known tasks.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs">AntStructure.StructurePrinter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Writes the actual structure information.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AttributeNamespaceDef.html" title="class in org.apache.tools.ant.taskdefs">AttributeNamespaceDef</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Definition to allow the URI to be considered for
 Ant attributes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AugmentReference.html" title="class in org.apache.tools.ant.taskdefs">AugmentReference</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Ant task to dynamically augment a previously declared reference.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Available.html" title="class in org.apache.tools.ant.taskdefs">Available</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Will set the given property if the requested resource is available at
 runtime.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Available.FileDir.html" title="class in org.apache.tools.ant.taskdefs">Available.FileDir</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">EnumeratedAttribute covering the file types to be checked for, either
 file or dir.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Basename.html" title="class in org.apache.tools.ant.taskdefs">Basename</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Sets a property to the base name of a specified file, optionally minus a
 suffix.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BindTargets.html" title="class in org.apache.tools.ant.taskdefs">BindTargets</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Simple task which bind some targets to some defined extension point</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BuildNumber.html" title="class in org.apache.tools.ant.taskdefs">BuildNumber</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Read, increment, and write a build number in a file
 It will first
 attempt to read a build number from a file, then set the property
 "build.number" to the value that was read in (or 0 if no such value).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BUnzip2.html" title="class in org.apache.tools.ant.taskdefs">BUnzip2</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Expands a file that has been compressed with the BZIP2
 algorithm.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BZip2.html" title="class in org.apache.tools.ant.taskdefs">BZip2</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compresses a file with the BZIP2 algorithm.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CallTarget.html" title="class in org.apache.tools.ant.taskdefs">CallTarget</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Call another target in the same project.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Used to create or verify file checksums.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Checksum.FormatElement.html" title="class in org.apache.tools.ant.taskdefs">Checksum.FormatElement</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Helper class for the format attribute.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Chmod.html" title="class in org.apache.tools.ant.taskdefs">Chmod</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Chmod equivalent for unix-like environments.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Classloader.html" title="class in org.apache.tools.ant.taskdefs">Classloader</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">EXPERIMENTAL
 Create or modifies ClassLoader.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CloseResources.html" title="class in org.apache.tools.ant.taskdefs">CloseResources</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Not a real task but used during tests.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CommandLauncherTask.html" title="class in org.apache.tools.ant.taskdefs">CommandLauncherTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Task that configures the <a href="launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a> to used
 when starting external processes.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Componentdef.html" title="class in org.apache.tools.ant.taskdefs">Componentdef</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Adds a component definition to the current project.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Concat.html" title="class in org.apache.tools.ant.taskdefs">Concat</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This class contains the 'concat' task, used to concatenate a series
 of files into a single stream.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs">Concat.TextElement</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">sub element points to a file or contains text</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ConditionTask.html" title="class in org.apache.tools.ant.taskdefs">ConditionTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Task to set a property conditionally using &lt;uptodate&gt;, &lt;available&gt;,
 and many other supported conditions.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Copies a file or directory to a new file
 or directory.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Copydir.html" title="class in org.apache.tools.ant.taskdefs">Copydir</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">The copydir task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Copyfile.html" title="class in org.apache.tools.ant.taskdefs">Copyfile</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">The copyfile task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CopyPath.html" title="class in org.apache.tools.ant.taskdefs">CopyPath</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">this task should have never been released and was
 obsoleted by ResourceCollection support in Copy available since Ant
 1.7.0.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Cvs.html" title="class in org.apache.tools.ant.taskdefs">Cvs</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Performs operations on a CVS repository.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CVSPass.html" title="class in org.apache.tools.ant.taskdefs">CVSPass</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adds an new entry to a CVS password file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs">DefaultExcludes</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Alters the default excludes for the <strong>entire</strong> build..</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DefBase.html" title="class in org.apache.tools.ant.taskdefs">DefBase</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base class for Definitions handling uri and class loading.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Definer.html" title="class in org.apache.tools.ant.taskdefs">Definer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for Taskdef and Typedef - handles all
 the attributes for Typedef.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Definer.Format.html" title="class in org.apache.tools.ant.taskdefs">Definer.Format</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Enumerated type for format attribute</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Definer.OnError.html" title="class in org.apache.tools.ant.taskdefs">Definer.OnError</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated type for onError attribute</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Delete.html" title="class in org.apache.tools.ant.taskdefs">Delete</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Deletes a file or directory, or set of files defined by a fileset.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Deltree.html" title="class in org.apache.tools.ant.taskdefs">Deltree</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">The deltree task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DependSet.html" title="class in org.apache.tools.ant.taskdefs">DependSet</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Examines and removes out of date target files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs">DiagnosticsTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a task that hands off work to the Diagnostics module.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Dirname.html" title="class in org.apache.tools.ant.taskdefs">Dirname</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Determines the directory name of the specified file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Ear.html" title="class in org.apache.tools.ant.taskdefs">Ear</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Creates a EAR archive.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Echo.html" title="class in org.apache.tools.ant.taskdefs">Echo</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Writes a message to the Ant logging facilities.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Echo.EchoLevel.html" title="class in org.apache.tools.ant.taskdefs">Echo.EchoLevel</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The enumerated values for the level attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="EchoXML.html" title="class in org.apache.tools.ant.taskdefs">EchoXML</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Echo XML.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="EchoXML.NamespacePolicy.html" title="class in org.apache.tools.ant.taskdefs">EchoXML.NamespacePolicy</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Exec.html" title="class in org.apache.tools.ant.taskdefs">Exec</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Executes a given command if the os platform is appropriate.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Runs an external program.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs">ExecuteJava</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Execute a Java class.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Executes a given command, supplying a set of files as arguments.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "file", "dir" and "both"
 for the type attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Used by <code>Execute</code> to handle input and output stream of
 subprocesses.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Destroys a process running for too long.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Exit.html" title="class in org.apache.tools.ant.taskdefs">Exit</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Exits the active build, giving an additional message
 if available.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Expand.html" title="class in org.apache.tools.ant.taskdefs">Expand</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Unzip a file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Filter.html" title="class in org.apache.tools.ant.taskdefs">Filter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sets a token filter that is used by the file copy tasks
 to do token substitution.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Converts text source files to local OS formatting conventions, as
 well as repair text files damaged by misconfigured or misguided editors or
 file transfer programs.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "asis", "add" and "remove".</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "asis", "cr", "lf", "crlf", "mac", "unix" and "dos.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GenerateKey.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Generates a key in a keystore.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GenerateKey.DistinguishedName.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DistinguishedName</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A class corresponding to the dname nested element.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GenerateKey.DnameParam.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DnameParam</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A DistinguishedName parameter.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Get.html" title="class in org.apache.tools.ant.taskdefs">Get</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Gets a particular file from a URL source.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Get.Base64Converter.html" title="class in org.apache.tools.ant.taskdefs">Get.Base64Converter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Provide this for Backward Compatibility.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Interface implemented for reporting
 progress of downloading.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Get.NullProgress.html" title="class in org.apache.tools.ant.taskdefs">Get.NullProgress</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">do nothing with progress info</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Get.VerboseProgress.html" title="class in org.apache.tools.ant.taskdefs">Get.VerboseProgress</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">verbose progress system prints to some output stream</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GUnzip.html" title="class in org.apache.tools.ant.taskdefs">GUnzip</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Expands a file that has been compressed with the GZIP
 algorithm.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GZip.html" title="class in org.apache.tools.ant.taskdefs">GZip</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compresses a file with the GZIP algorithm.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="HostInfo.html" title="class in org.apache.tools.ant.taskdefs">HostInfo</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sets properties to the host provided, or localhost if no information is
 provided.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ImportTask.html" title="class in org.apache.tools.ant.taskdefs">ImportTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Task to import another build file into the current project.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Input.html" title="class in org.apache.tools.ant.taskdefs">Input</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Reads an input line from the console.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Input.HandlerType.html" title="class in org.apache.tools.ant.taskdefs">Input.HandlerType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">EnumeratedAttribute representing the built-in input handler types:
 "default", "propertyfile", "greedy", "secure" (since Ant 1.8).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Creates a JAR archive.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The manifest config enumerated type.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Jar.StrictMode.html" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The strict enumerated type.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Java.html" title="class in org.apache.tools.ant.taskdefs">Java</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Launcher for Java applications.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Compiles Java source files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Javadoc.html" title="class in org.apache.tools.ant.taskdefs">Javadoc</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Generates Javadoc documentation for a collection
 of source code.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">EnumeratedAttribute implementation supporting the Javadoc scoping
 values.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A project aware class used for Javadoc extensions which take a name
 and a path such as doclet and taglet arguments.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An HTML element in the Javadoc.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Used to track info about the packages to be javadoc'd</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This class is used to manage the source files to be processed.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Handles JDBC configuration needed by SQL type tasks.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Jikes.html" title="class in org.apache.tools.ant.taskdefs">Jikes</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs">JikesOutputParser</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="KeySubst.html" title="class in org.apache.tools.ant.taskdefs">KeySubst</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">KeySubst is deprecated since Ant 1.1.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Length.html" title="class in org.apache.tools.ant.taskdefs">Length</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Gets lengths:  of files/resources, byte size; of strings, length (optionally trimmed).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Length.FileMode.html" title="class in org.apache.tools.ant.taskdefs">Length.FileMode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">EnumeratedAttribute operation mode</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Length.When.html" title="class in org.apache.tools.ant.taskdefs">Length.When</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">EnumeratedAttribute for the when attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LoadFile.html" title="class in org.apache.tools.ant.taskdefs">LoadFile</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Load a file into a property</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LoadProperties.html" title="class in org.apache.tools.ant.taskdefs">LoadProperties</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Load a file's contents as Ant properties.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LoadResource.html" title="class in org.apache.tools.ant.taskdefs">LoadResource</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Load a resource into a property</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Local.html" title="class in org.apache.tools.ant.taskdefs">Local</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Task to create local properties in the current scope.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Local.Name.html" title="class in org.apache.tools.ant.taskdefs">Local.Name</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Nested <code>name</code> element.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LogOutputStream.html" title="class in org.apache.tools.ant.taskdefs">LogOutputStream</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Logs each line written to this stream to the log system of ant.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">LogStreamHandler</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Logs standard output and error of a subprocess to the log system of ant.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MacroDef.html" title="class in org.apache.tools.ant.taskdefs">MacroDef</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Describe class <code>MacroDef</code> here.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An attribute for the MacroDef task.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The class corresponding to the sequential nested element.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A nested element for the MacroDef task.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A nested text element for the MacroDef task.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MacroInstance.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The class to be placed in the ant type definition.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MacroInstance.Element.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance.Element</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Embedded element in macro instance</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MakeUrl.html" title="class in org.apache.tools.ant.taskdefs">MakeUrl</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This task takes file and turns them into a URL, which it then assigns
 to a property.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Holds the data of a jar manifest.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An attribute for the manifest.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A manifest section - you can nest attribute elements into sections.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs">ManifestClassPath</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Converts a Path into a property suitable as a Manifest classpath.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Exception thrown indicating problems in a JAR Manifest</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ManifestTask.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Creates a manifest file for inclusion in a JAR, Ant task wrapper
 around <a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs"><code>Manifest</code></a>.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask.Mode</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Helper class for Manifest's mode attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is an abstract task that should be used by all those tasks that
 require to include or exclude files based on pattern matching.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Mkdir.html" title="class in org.apache.tools.ant.taskdefs">Mkdir</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Creates a given directory.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Move.html" title="class in org.apache.tools.ant.taskdefs">Move</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Moves a file or directory to a new file or directory.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Nice.html" title="class in org.apache.tools.ant.taskdefs">Nice</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A task to provide "nice-ness" to the current thread, and/or to
 query the current value.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Pack.html" title="class in org.apache.tools.ant.taskdefs">Pack</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Abstract Base class for pack tasks.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Parallel.html" title="class in org.apache.tools.ant.taskdefs">Parallel</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Executes the contained tasks in separate threads, continuing
 once all are completed.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Parallel.TaskList.html" title="class in org.apache.tools.ant.taskdefs">Parallel.TaskList</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class which holds a list of tasks to execute</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Patch.html" title="class in org.apache.tools.ant.taskdefs">Patch</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Patches a file by applying a 'diff' file to it; requires "patch" to be
 on the execution path.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PathConvert.html" title="class in org.apache.tools.ant.taskdefs">PathConvert</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Converts path and classpath information to a specific target OS
 format.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PathConvert.TargetOs.html" title="class in org.apache.tools.ant.taskdefs">PathConvert.TargetOs</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">An enumeration of supported targets:
 "windows", "unix", "netware", and "os/2".</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PreSetDef.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The preset definition task generates a new definition
 based on a current definition with some attributes or
 elements preset.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef.PreSetDefinition</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class contains the unknown element and the object
 that is predefined.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ProjectHelperTask.html" title="class in org.apache.tools.ant.taskdefs">ProjectHelperTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Task to install project helper into Ant's runtime</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Property.html" title="class in org.apache.tools.ant.taskdefs">Property</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Sets a property by name, or set of properties (from file or
 resource) in the project.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PropertyHelperTask.html" title="class in org.apache.tools.ant.taskdefs">PropertyHelperTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This task is designed to allow the user to install a different
 PropertyHelper on the current Project.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Copies standard output and error of subprocesses to standard output and
 error of the parent process.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PumpStreamHandler.ThreadWithPumper.html" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler.ThreadWithPumper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Specialized subclass that allows access to the running StreamPumper.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Recorder.html" title="class in org.apache.tools.ant.taskdefs">Recorder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Adds a listener to the current build process that records the
 output to a file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Recorder.ActionChoices.html" title="class in org.apache.tools.ant.taskdefs">Recorder.ActionChoices</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A list of possible values for the <code>setAction()</code> method.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs">Recorder.VerbosityLevelChoices</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A list of possible values for the <code>setLoglevel()</code> method.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is a class that represents a recorder.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The Redirector class manages the setup and connection of input and output
 redirection for an Ant project component.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Rename.html" title="class in org.apache.tools.ant.taskdefs">Rename</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">The rename task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Replace.html" title="class in org.apache.tools.ant.taskdefs">Replace</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Replaces all occurrences of one or more string tokens with given
 values in the indicated files.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ResourceCount.html" title="class in org.apache.tools.ant.taskdefs">ResourceCount</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Count resources from a ResourceCollection, storing to a property or
 writing to the log.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Retry.html" title="class in org.apache.tools.ant.taskdefs">Retry</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Retries the nested task a set number of times</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Runs the rmic compiler against classes.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SendEmail.html" title="class in org.apache.tools.ant.taskdefs">SendEmail</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A task to send SMTP email.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sequential is a container task - it can contain other Ant tasks.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SetPermissions.html" title="class in org.apache.tools.ant.taskdefs">SetPermissions</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Sets <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/nio/file/attribute/PosixFilePermission.html" title="class or interface in java.nio.file.attribute" class="external-link"><code>PosixFilePermission</code></a>s for resources.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="SetPermissions.NonPosixMode.html" title="enum class in org.apache.tools.ant.taskdefs">SetPermissions.NonPosixMode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">Options for dealing with file systems that don't support POSIX
 permissions.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SignJar.html" title="class in org.apache.tools.ant.taskdefs">SignJar</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Signs JAR or ZIP files with the javasign command line tool.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Sleep.html" title="class in org.apache.tools.ant.taskdefs">Sleep</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sleep, or pause, for a period of time.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SQLExec.html" title="class in org.apache.tools.ant.taskdefs">SQLExec</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Executes a series of SQL statements on a database using JDBC.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">delimiters we support, "normal" and "row"</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The action a task should perform on an error,
 one of "continue", "stop" and "abort"</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Copies all data from an input stream to an output stream.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SubAnt.html" title="class in org.apache.tools.ant.taskdefs">SubAnt</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Calls a given target for all defined sub-builds.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Synchronize a local target directory from the files defined
 in one or more filesets.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Sync.MyCopy.html" title="class in org.apache.tools.ant.taskdefs">Sync.MyCopy</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Subclass Copy in order to access it's file/dir maps.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs">Sync.SyncTarget</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Inner class used to hold exclude patterns and selectors to save
 stuff that happens to live in the target directory but should
 not get removed.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Creates a tar archive.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarCompressionMethod</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Valid Modes for Compression attribute to Tar Task</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarFileSet</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a FileSet with the option to specify permissions
 and other attributes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarLongFileMode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Set of options for long file handling in the task.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Taskdef.html" title="class in org.apache.tools.ant.taskdefs">Taskdef</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Adds a task definition to the current project, such that this new task can be
 used in the current project.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs">TaskOutputStream</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.x.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TempFile.html" title="class in org.apache.tools.ant.taskdefs">TempFile</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This task sets a property to the name of a temporary file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Touch.html" title="class in org.apache.tools.ant.taskdefs">Touch</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Touch a file and/or fileset(s) and/or filelist(s);
 corresponds to the Unix touch command.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Touch.DateFormatFactory.html" title="interface in org.apache.tools.ant.taskdefs">Touch.DateFormatFactory</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Transform.html" title="class in org.apache.tools.ant.taskdefs">Transform</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Has been merged into ExecuteOn, empty class for backwards compatibility.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Truncate.html" title="class in org.apache.tools.ant.taskdefs">Truncate</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Set the length of one or more files, as the intermittently available
 <code>truncate</code> Unix utility/function.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Tstamp.html" title="class in org.apache.tools.ant.taskdefs">Tstamp</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sets properties to the current time, or offsets from the current time.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Tstamp.Unit.html" title="class in org.apache.tools.ant.taskdefs">Tstamp.Unit</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">set of valid units to use for time offsets.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Typedef.html" title="class in org.apache.tools.ant.taskdefs">Typedef</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adds a data type definition to the current project.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Unpack.html" title="class in org.apache.tools.ant.taskdefs">Unpack</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Abstract Base class for unpack tasks.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Untar.html" title="class in org.apache.tools.ant.taskdefs">Untar</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Untar a file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Untar.UntarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs">Untar.UntarCompressionMethod</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Valid Modes for Compression attribute to Untar Task</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sets the given property if the specified target has a timestamp
 greater than all of the source files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="VerifyJar.html" title="class in org.apache.tools.ant.taskdefs">VerifyJar</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">JAR verification task.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="WaitFor.html" title="class in org.apache.tools.ant.taskdefs">WaitFor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wait for an external event to occur.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The enumeration of units:
 millisecond, second, minute, hour, day, week</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="War.html" title="class in org.apache.tools.ant.taskdefs">War</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An extension of &lt;jar&gt; to create a WAR archive.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="WhichResource.html" title="class in org.apache.tools.ant.taskdefs">WhichResource</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Find a class or resource on the supplied classpath, or the
 system classpath if none is supplied.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="XmlProperty.html" title="class in org.apache.tools.ant.taskdefs">XmlProperty</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Loads property values from a valid XML file, generating the
 property names from the file's element and attribute names.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Proxy interface for XSLT processors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Extended Proxy interface for XSLT processors.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Extends Proxy interface for XSLT processors.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Extends Proxy interface for XSLT processors: adds support for XSLT parameters
 of various types (not only String)</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Interface to log messages for XSLT</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Interface for a class that one can set an XSLTLogger on.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Processes a set of XML documents via XSLT.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The factory element to configure a transformer factory</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XSLTProcess.Factory.Attribute.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Attribute</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A JAXP factory attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="XSLTProcess.Factory.Feature.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Feature</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A feature for the TraX factory.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Specify how the result tree should be output as specified
 in the <a href="https://www.w3.org/TR/xslt#output">
 specification</a>.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The Param inner class used to store XSL parameters</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="XSLTProcess.ParamType.html" title="enum class in org.apache.tools.ant.taskdefs">XSLTProcess.ParamType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">Enum for types of the parameter expression.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Create a Zip file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Holds the up-to-date status and the out-of-date resources of
 the original archive.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Possible behaviors when a duplicate file is added:
 "add", "preserve" or "fail"</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Policy for creation of Unicode extra fields: never, always or
 not-encodeable.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Possible behaviors when there are no matching files for the task:
 "fail", "skip", or "create".</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The choices for Zip64 extensions.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</body>
</html>
