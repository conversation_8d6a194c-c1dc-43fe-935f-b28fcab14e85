<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>FileSelector (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors, interface: FileSelector">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<h1 title="Interface FileSelector" class="title">Interface FileSelector</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></code>, <code><a href="BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></code>, <code><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code>, <code><a href="BaseSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a></code>, <code><a href="ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a></code>, <code><a href="ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a></code>, <code><a href="DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a></code>, <code><a href="DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a></code>, <code><a href="DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a></code>, <code><a href="DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a></code>, <code><a href="ExecutableSelector.html" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a></code>, <code><a href="ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a></code>, <code><a href="FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a></code>, <code><a href="MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></code>, <code><a href="MappingSelector.html" title="class in org.apache.tools.ant.types.selectors">MappingSelector</a></code>, <code><a href="modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a></code>, <code><a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></code>, <code><a href="NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a></code>, <code><a href="OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></code>, <code><a href="OwnedBySelector.html" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a></code>, <code><a href="PosixGroupSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a></code>, <code><a href="PosixPermissionsSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a></code>, <code><a href="PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a></code>, <code><a href="ReadableSelector.html" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a></code>, <code><a href="../optional/ScriptSelector.html" title="class in org.apache.tools.ant.types.optional">ScriptSelector</a></code>, <code><a href="SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></code>, <code><a href="SignedSelector.html" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a></code>, <code><a href="SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a></code>, <code><a href="SymlinkSelector.html" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a></code>, <code><a href="TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a></code>, <code><a href="WritableSelector.html" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">FileSelector</span><span class="extends-implements">
extends <a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></span></div>
<div class="block">This is the interface to be used by all selectors.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab5" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab5', 3)" class="table-tab">Default Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#isSelected(java.io.File,java.lang.String,java.io.File)" class="member-name-link">isSelected</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Method that each selector will implement to create their
 selection behaviour.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#isSelected(org.apache.tools.ant.types.Resource)" class="member-name-link">isSelected</a><wbr>(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">Implement a basic <a href="../Resource.html" title="class in org.apache.tools.ant.types"><code>Resource</code></a> selection that delegates to this
 <a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors"><code>FileSelector</code></a>.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isSelected(java.io.File,java.lang.String,java.io.File)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span>
            throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Method that each selector will implement to create their
 selection behaviour. If there is a problem with the setup
 of a selector, it can throw a BuildException to indicate
 the problem.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>basedir</code> - A java.io.File object for the base directory</dd>
<dd><code>filename</code> - The name of the file to check</dd>
<dd><code>file</code> - A File object for this filename</dd>
<dt>Returns:</dt>
<dd>whether the file should be selected or not</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the selector was not configured correctly</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected(org.apache.tools.ant.types.Resource)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;r)</span></div>
<div class="block">Implement a basic <a href="../Resource.html" title="class in org.apache.tools.ant.types"><code>Resource</code></a> selection that delegates to this
 <a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors"><code>FileSelector</code></a>.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../resources/selectors/ResourceSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>r</code> - resource</dd>
<dt>Returns:</dt>
<dd>whether the resource is selected</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
