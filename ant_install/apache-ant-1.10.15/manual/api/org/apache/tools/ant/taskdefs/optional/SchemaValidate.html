<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>SchemaValidate (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional, class: SchemaValidate">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<h1 title="Class SchemaValidate" class="title">Class SchemaValidate</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">org.apache.tools.ant.taskdefs.optional.XMLValidateTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.SchemaValidate</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SchemaValidate</span>
<span class="extends-implements">extends <a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></span></div>
<div class="block">Validate XML Schema documents.
 This task validates XML schema documents. It requires an XML parser
 that handles the relevant SAX, Xerces or JAXP options.

 To resolve remote references, Ant may need its proxy set up, using the
 setproxy task.

 Hands off most of the work to its parent, <a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional"><code>XMLValidateTask</code></a></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant1.7</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="SchemaValidate.SchemaLocation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a></code></div>
<div class="col-last even-row-color">
<div class="block">representation of a schema location.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.optional.XMLValidateTask">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></h3>
<code><a href="XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a>, <a href="XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a>, <a href="XMLValidateTask.ValidatorErrorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_DUPLICATE_SCHEMA" class="member-name-link">ERROR_DUPLICATE_SCHEMA</a></code></div>
<div class="col-last even-row-color">
<div class="block">Duplicate declaration of schema</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_NO_XSD_SUPPORT" class="member-name-link">ERROR_NO_XSD_SUPPORT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">schema features not supported</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_PARSER_CREATION_FAILURE" class="member-name-link">ERROR_PARSER_CREATION_FAILURE</a></code></div>
<div class="col-last even-row-color">
<div class="block">unable to create parser</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_SAX_1" class="member-name-link">ERROR_SAX_1</a></code></div>
<div class="col-last odd-row-color">
<div class="block">SAX1 not supported</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_TOO_MANY_DEFAULT_SCHEMAS" class="member-name-link">ERROR_TOO_MANY_DEFAULT_SCHEMAS</a></code></div>
<div class="col-last even-row-color">
<div class="block">too many default schemas</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#MESSAGE_ADDING_SCHEMA" class="member-name-link">MESSAGE_ADDING_SCHEMA</a></code></div>
<div class="col-last odd-row-color">
<div class="block">adding schema</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.XMLValidateTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></h3>
<code><a href="XMLValidateTask.html#classpath">classpath</a>, <a href="XMLValidateTask.html#errorHandler">errorHandler</a>, <a href="XMLValidateTask.html#failOnError">failOnError</a>, <a href="XMLValidateTask.html#file">file</a>, <a href="XMLValidateTask.html#filesets">filesets</a>, <a href="XMLValidateTask.html#INIT_FAILED_MSG">INIT_FAILED_MSG</a>, <a href="XMLValidateTask.html#lenient">lenient</a>, <a href="XMLValidateTask.html#MESSAGE_FILES_VALIDATED">MESSAGE_FILES_VALIDATED</a>, <a href="XMLValidateTask.html#readerClassName">readerClassName</a>, <a href="XMLValidateTask.html#warn">warn</a>, <a href="XMLValidateTask.html#xmlReader">xmlReader</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SchemaValidate</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredSchema(org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation)" class="member-name-link">addConfiguredSchema</a><wbr>(<a href="SchemaValidate.SchemaLocation.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a>&nbsp;location)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add the schema</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSchemaLocations()" class="member-name-link">addSchemaLocations</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">build a string list of all schema locations, then set the relevant
 property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createAnonymousSchema()" class="member-name-link">createAnonymousSchema</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">create a schema location to hold the anonymous
 schema</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createDefaultReader()" class="member-name-link">createDefaultReader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a reader if the use of the class did not specify another one.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enableJAXP12SchemaValidation()" class="member-name-link">enableJAXP12SchemaValidation</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set schema attributes in a JAXP 1.2 engine.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enableXercesSchemaValidation()" class="member-name-link">enableXercesSchemaValidation</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Turn on XSD support in Xerces.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNoNamespaceSchemaURL()" class="member-name-link">getNoNamespaceSchemaURL</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the URL of the no namespace schema</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called by the project to let the task initialize properly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initValidator()" class="member-name-link">initValidator</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">init the parser : load the parser class, and set features if necessary It
 is only after this that the reader is valid</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onSuccessfulValidation(int)" class="member-name-link">onSuccessfulValidation</a><wbr>(int&nbsp;fileProcessed)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">handler called on successful file validation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDisableDTD(boolean)" class="member-name-link">setDisableDTD</a><wbr>(boolean&nbsp;disableDTD)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">flag to disable DTD support.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFeatureIfSupported(java.lang.String,boolean)" class="member-name-link">setFeatureIfSupported</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;feature,
 boolean&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set a feature if it is supported, log at verbose level if
 not</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFullChecking(boolean)" class="member-name-link">setFullChecking</a><wbr>(boolean&nbsp;fullChecking)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">enable full schema checking.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNoNamespaceFile(java.io.File)" class="member-name-link">setNoNamespaceFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;defaultSchemaFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">identify a file containing the default schema</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNoNamespaceURL(java.lang.String)" class="member-name-link">setNoNamespaceURL</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultSchemaURL)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">identify the URL of the default schema</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.XMLValidateTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></h3>
<code><a href="XMLValidateTask.html#addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)">addConfiguredXMLCatalog</a>, <a href="XMLValidateTask.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="XMLValidateTask.html#cleanup()">cleanup</a>, <a href="XMLValidateTask.html#createAttribute()">createAttribute</a>, <a href="XMLValidateTask.html#createClasspath()">createClasspath</a>, <a href="XMLValidateTask.html#createDTD()">createDTD</a>, <a href="XMLValidateTask.html#createProperty()">createProperty</a>, <a href="XMLValidateTask.html#createXmlReader()">createXmlReader</a>, <a href="XMLValidateTask.html#doValidate(java.io.File)">doValidate</a>, <a href="XMLValidateTask.html#execute()">execute</a>, <a href="XMLValidateTask.html#getEntityResolver()">getEntityResolver</a>, <a href="XMLValidateTask.html#getXmlReader()">getXmlReader</a>, <a href="XMLValidateTask.html#isSax1Parser()">isSax1Parser</a>, <a href="XMLValidateTask.html#setClassName(java.lang.String)">setClassName</a>, <a href="XMLValidateTask.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="XMLValidateTask.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</a>, <a href="XMLValidateTask.html#setFailOnError(boolean)">setFailOnError</a>, <a href="XMLValidateTask.html#setFeature(java.lang.String,boolean)">setFeature</a>, <a href="XMLValidateTask.html#setFile(java.io.File)">setFile</a>, <a href="XMLValidateTask.html#setLenient(boolean)">setLenient</a>, <a href="XMLValidateTask.html#setProperty(java.lang.String,java.lang.String)">setProperty</a>, <a href="XMLValidateTask.html#setWarn(boolean)">setWarn</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ERROR_SAX_1">
<h3>ERROR_SAX_1</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_SAX_1</span></div>
<div class="block">SAX1 not supported</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_SAX_1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ERROR_NO_XSD_SUPPORT">
<h3>ERROR_NO_XSD_SUPPORT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_XSD_SUPPORT</span></div>
<div class="block">schema features not supported</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_NO_XSD_SUPPORT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ERROR_TOO_MANY_DEFAULT_SCHEMAS">
<h3>ERROR_TOO_MANY_DEFAULT_SCHEMAS</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_TOO_MANY_DEFAULT_SCHEMAS</span></div>
<div class="block">too many default schemas</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_TOO_MANY_DEFAULT_SCHEMAS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ERROR_PARSER_CREATION_FAILURE">
<h3>ERROR_PARSER_CREATION_FAILURE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_PARSER_CREATION_FAILURE</span></div>
<div class="block">unable to create parser</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_PARSER_CREATION_FAILURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MESSAGE_ADDING_SCHEMA">
<h3>MESSAGE_ADDING_SCHEMA</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">MESSAGE_ADDING_SCHEMA</span></div>
<div class="block">adding schema</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.SchemaValidate.MESSAGE_ADDING_SCHEMA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="ERROR_DUPLICATE_SCHEMA">
<h3>ERROR_DUPLICATE_SCHEMA</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_DUPLICATE_SCHEMA</span></div>
<div class="block">Duplicate declaration of schema</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_DUPLICATE_SCHEMA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SchemaValidate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SchemaValidate</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()
          throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called by the project to let the task initialize properly. The default
 implementation is a no-op.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="XMLValidateTask.html#init()">init</a></code>&nbsp;in class&nbsp;<code><a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong with the build</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="enableXercesSchemaValidation()">
<h3>enableXercesSchemaValidation</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">enableXercesSchemaValidation</span>()</div>
<div class="block">Turn on XSD support in Xerces.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true on success, false on failure</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="enableJAXP12SchemaValidation()">
<h3>enableJAXP12SchemaValidation</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">enableJAXP12SchemaValidation</span>()</div>
<div class="block">Set schema attributes in a JAXP 1.2 engine.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true on success, false on failure</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><A href="https://java.sun.com/xml/jaxp/change-requests-11.html">
 JAXP 1.2 Approved CHANGES</A></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addConfiguredSchema(org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation)">
<h3>addConfiguredSchema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredSchema</span><wbr><span class="parameters">(<a href="SchemaValidate.SchemaLocation.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a>&nbsp;location)</span></div>
<div class="block">add the schema</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>location</code> - the schema location.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is no namespace, or if there already
 is a declaration of this schema with a different value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFullChecking(boolean)">
<h3>setFullChecking</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFullChecking</span><wbr><span class="parameters">(boolean&nbsp;fullChecking)</span></div>
<div class="block">enable full schema checking. Slower but better.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fullChecking</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createAnonymousSchema()">
<h3>createAnonymousSchema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">createAnonymousSchema</span>()</div>
<div class="block">create a schema location to hold the anonymous
 schema</div>
</div>
</section>
</li>
<li>
<section class="detail" id="setNoNamespaceURL(java.lang.String)">
<h3>setNoNamespaceURL</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNoNamespaceURL</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultSchemaURL)</span></div>
<div class="block">identify the URL of the default schema</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>defaultSchemaURL</code> - the URL of the default schema.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNoNamespaceFile(java.io.File)">
<h3>setNoNamespaceFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNoNamespaceFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;defaultSchemaFile)</span></div>
<div class="block">identify a file containing the default schema</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>defaultSchemaFile</code> - the location of the default schema.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDisableDTD(boolean)">
<h3>setDisableDTD</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDisableDTD</span><wbr><span class="parameters">(boolean&nbsp;disableDTD)</span></div>
<div class="block">flag to disable DTD support.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>disableDTD</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="initValidator()">
<h3>initValidator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initValidator</span>()</div>
<div class="block">init the parser : load the parser class, and set features if necessary It
 is only after this that the reader is valid</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="XMLValidateTask.html#initValidator()">initValidator</a></code>&nbsp;in class&nbsp;<code><a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something went wrong</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createDefaultReader()">
<h3>createDefaultReader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></span>&nbsp;<span class="element-name">createDefaultReader</span>()</div>
<div class="block">Create a reader if the use of the class did not specify another one.
 The reason to not use <a href="../../util/JAXPUtils.html#getXMLReader()"><code>JAXPUtils.getXMLReader()</code></a> was to
 create our own factory with our own options.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="XMLValidateTask.html#createDefaultReader()">createDefaultReader</a></code>&nbsp;in class&nbsp;<code><a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></code></dd>
<dt>Returns:</dt>
<dd>a default XML parser</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSchemaLocations()">
<h3>addSchemaLocations</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSchemaLocations</span>()</div>
<div class="block">build a string list of all schema locations, then set the relevant
 property.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="getNoNamespaceSchemaURL()">
<h3>getNoNamespaceSchemaURL</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getNoNamespaceSchemaURL</span>()</div>
<div class="block">get the URL of the no namespace schema</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the schema URL</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFeatureIfSupported(java.lang.String,boolean)">
<h3>setFeatureIfSupported</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFeatureIfSupported</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;feature,
 boolean&nbsp;value)</span></div>
<div class="block">set a feature if it is supported, log at verbose level if
 not</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>feature</code> - the feature.</dd>
<dd><code>value</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="onSuccessfulValidation(int)">
<h3>onSuccessfulValidation</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onSuccessfulValidation</span><wbr><span class="parameters">(int&nbsp;fileProcessed)</span></div>
<div class="block">handler called on successful file validation.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="XMLValidateTask.html#onSuccessfulValidation(int)">onSuccessfulValidation</a></code>&nbsp;in class&nbsp;<code><a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></code></dd>
<dt>Parameters:</dt>
<dd><code>fileProcessed</code> - number of files processed.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
