<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>TarFileSet (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, class: TarFileSet">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Class TarFileSet" class="title">Class TarFileSet</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</a>
<div class="inheritance"><a href="FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</a>
<div class="inheritance"><a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.ArchiveFileSet</a>
<div class="inheritance">org.apache.tools.ant.types.TarFileSet</div>
</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code>, <code><a href="selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../taskdefs/Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarFileSet</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarFileSet</span>
<span class="extends-implements">extends <a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></span></div>
<div class="block">A TarFileSet is a FileSet with extra attributes useful in the context of
 Tar/Jar tasks.

 A TarFileSet extends FileSets with the ability to extract a subset of the
 entries of a Tar file for inclusion in another Tar file.  It also includes
 a prefix attribute which is prepended to each entry in the output Tar file.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.ArchiveFileSet">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></h3>
<code><a href="ArchiveFileSet.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</a>, <a href="ArchiveFileSet.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="DataType.html#checked">checked</a>, <a href="DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TarFileSet</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for TarFileSet</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.FileSet)" class="member-name-link">TarFileSet</a><wbr>(<a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor using a fileset argument.</div>
</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.TarFileSet)" class="member-name-link">TarFileSet</a><wbr>(<a href="TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a>&nbsp;fileset)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor using a tarfileset argument.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a TarFileSet that has the same properties
 as this one.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)" class="member-name-link">configureFileSet</a><wbr>(<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>&nbsp;zfs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configure a fileset based on this fileset.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGid()" class="member-name-link">getGid</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGroup()" class="member-name-link">getGroup</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRef()" class="member-name-link">getRef</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A TarFileset accepts another TarFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRef(org.apache.tools.ant.Project)" class="member-name-link">getRef</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A TarFileset accepts another TarFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUid()" class="member-name-link">getUid</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserName()" class="member-name-link">getUserName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasGroupBeenSet()" class="member-name-link">hasGroupBeenSet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasGroupIdBeenSet()" class="member-name-link">hasGroupIdBeenSet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasUserIdBeenSet()" class="member-name-link">hasUserIdBeenSet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasUserNameBeenSet()" class="member-name-link">hasUserNameBeenSet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newArchiveScanner()" class="member-name-link">newArchiveScanner</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new scanner.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGid(int)" class="member-name-link">setGid</a><wbr>(int&nbsp;gid)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The GID for the tar entry; optional, default="0"
 This is not the same as the group name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGroup(java.lang.String)" class="member-name-link">setGroup</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The groupname for the tar entry; optional, default=""
 This is not the same as the GID.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefid(org.apache.tools.ant.types.Reference)" class="member-name-link">setRefid</a><wbr>(<a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Makes this instance in effect a reference to another instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUid(int)" class="member-name-link">setUid</a><wbr>(int&nbsp;uid)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The uid for the tar entry
 This is not the same as the User name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserName(java.lang.String)" class="member-name-link">setUserName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The username for the tar entry
 This is not the same as the UID.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ArchiveFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></h3>
<code><a href="ArchiveFileSet.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</a>, <a href="ArchiveFileSet.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="ArchiveFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</a>, <a href="ArchiveFileSet.html#getDirMode()">getDirMode</a>, <a href="ArchiveFileSet.html#getDirMode(org.apache.tools.ant.Project)">getDirMode</a>, <a href="ArchiveFileSet.html#getEncoding()">getEncoding</a>, <a href="ArchiveFileSet.html#getFileMode()">getFileMode</a>, <a href="ArchiveFileSet.html#getFileMode(org.apache.tools.ant.Project)">getFileMode</a>, <a href="ArchiveFileSet.html#getFullpath()">getFullpath</a>, <a href="ArchiveFileSet.html#getFullpath(org.apache.tools.ant.Project)">getFullpath</a>, <a href="ArchiveFileSet.html#getPrefix()">getPrefix</a>, <a href="ArchiveFileSet.html#getPrefix(org.apache.tools.ant.Project)">getPrefix</a>, <a href="ArchiveFileSet.html#getSrc()">getSrc</a>, <a href="ArchiveFileSet.html#getSrc(org.apache.tools.ant.Project)">getSrc</a>, <a href="ArchiveFileSet.html#hasDirModeBeenSet()">hasDirModeBeenSet</a>, <a href="ArchiveFileSet.html#hasFileModeBeenSet()">hasFileModeBeenSet</a>, <a href="ArchiveFileSet.html#integerSetDirMode(int)">integerSetDirMode</a>, <a href="ArchiveFileSet.html#integerSetFileMode(int)">integerSetFileMode</a>, <a href="ArchiveFileSet.html#isFilesystemOnly()">isFilesystemOnly</a>, <a href="ArchiveFileSet.html#iterator()">iterator</a>, <a href="ArchiveFileSet.html#setDir(java.io.File)">setDir</a>, <a href="ArchiveFileSet.html#setDirMode(java.lang.String)">setDirMode</a>, <a href="ArchiveFileSet.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="ArchiveFileSet.html#setErrorOnMissingArchive(boolean)">setErrorOnMissingArchive</a>, <a href="ArchiveFileSet.html#setFileMode(java.lang.String)">setFileMode</a>, <a href="ArchiveFileSet.html#setFullpath(java.lang.String)">setFullpath</a>, <a href="ArchiveFileSet.html#setPrefix(java.lang.String)">setPrefix</a>, <a href="ArchiveFileSet.html#setSrc(java.io.File)">setSrc</a>, <a href="ArchiveFileSet.html#setSrcResource(org.apache.tools.ant.types.Resource)">setSrcResource</a>, <a href="ArchiveFileSet.html#size()">size</a>, <a href="ArchiveFileSet.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.AbstractFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></h3>
<code><a href="AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="AbstractFileSet.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="AbstractFileSet.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="AbstractFileSet.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="AbstractFileSet.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="AbstractFileSet.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="AbstractFileSet.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="AbstractFileSet.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="AbstractFileSet.html#appendExcludes(java.lang.String%5B%5D)">appendExcludes</a>, <a href="AbstractFileSet.html#appendIncludes(java.lang.String%5B%5D)">appendIncludes</a>, <a href="AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="AbstractFileSet.html#createExclude()">createExclude</a>, <a href="AbstractFileSet.html#createExcludesFile()">createExcludesFile</a>, <a href="AbstractFileSet.html#createInclude()">createInclude</a>, <a href="AbstractFileSet.html#createIncludesFile()">createIncludesFile</a>, <a href="AbstractFileSet.html#createPatternSet()">createPatternSet</a>, <a href="AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</a>, <a href="AbstractFileSet.html#getDir()">getDir</a>, <a href="AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</a>, <a href="AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</a>, <a href="AbstractFileSet.html#getErrorOnMissingDir()">getErrorOnMissingDir</a>, <a href="AbstractFileSet.html#getMaxLevelsOfSymlinks()">getMaxLevelsOfSymlinks</a>, <a href="AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="AbstractFileSet.html#hasPatterns()">hasPatterns</a>, <a href="AbstractFileSet.html#hasSelectors()">hasSelectors</a>, <a href="AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</a>, <a href="AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</a>, <a href="AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</a>, <a href="AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</a>, <a href="AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</a>, <a href="AbstractFileSet.html#selectorCount()">selectorCount</a>, <a href="AbstractFileSet.html#selectorElements()">selectorElements</a>, <a href="AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="AbstractFileSet.html#setErrorOnMissingDir(boolean)">setErrorOnMissingDir</a>, <a href="AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="AbstractFileSet.html#setFile(java.io.File)">setFile</a>, <a href="AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="AbstractFileSet.html#setMaxLevelsOfSymlinks(int)">setMaxLevelsOfSymlinks</a>, <a href="AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</a>, <a href="AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner,org.apache.tools.ant.Project)">setupDirectoryScanner</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="DataType.html#circularReference()">circularReference</a>, <a href="DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="DataType.html#getRefid()">getRefid</a>, <a href="DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="DataType.html#isChecked()">isChecked</a>, <a href="DataType.html#isReference()">isReference</a>, <a href="DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="DataType.html#setChecked(boolean)">setChecked</a>, <a href="DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TarFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarFileSet</span>()</div>
<div class="block">Constructor for TarFileSet</div>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.FileSet)">
<h3>TarFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">TarFileSet</span><wbr><span class="parameters">(<a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset)</span></div>
<div class="block">Constructor using a fileset argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileset</code> - the fileset to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.TarFileSet)">
<h3>TarFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">TarFileSet</span><wbr><span class="parameters">(<a href="TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a>&nbsp;fileset)</span></div>
<div class="block">Constructor using a tarfileset argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileset</code> - the tarfileset to use</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setUserName(java.lang.String)">
<h3>setUserName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userName)</span></div>
<div class="block">The username for the tar entry
 This is not the same as the UID.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userName</code> - the user name for the tar entry.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUserName()">
<h3>getUserName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getUserName</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the user name for the tar entry</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasUserNameBeenSet()">
<h3>hasUserNameBeenSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasUserNameBeenSet</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether the user name has been explicitly set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUid(int)">
<h3>setUid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUid</span><wbr><span class="parameters">(int&nbsp;uid)</span></div>
<div class="block">The uid for the tar entry
 This is not the same as the User name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uid</code> - the id of the user for the tar entry.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUid()">
<h3>getUid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUid</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the uid for the tar entry</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasUserIdBeenSet()">
<h3>hasUserIdBeenSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasUserIdBeenSet</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether the user id has been explicitly set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGroup(java.lang.String)">
<h3>setGroup</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGroup</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupName)</span></div>
<div class="block">The groupname for the tar entry; optional, default=""
 This is not the same as the GID.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>groupName</code> - the group name string.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGroup()">
<h3>getGroup</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getGroup</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the group name string.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasGroupBeenSet()">
<h3>hasGroupBeenSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasGroupBeenSet</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether the group name has been explicitly set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGid(int)">
<h3>setGid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGid</span><wbr><span class="parameters">(int&nbsp;gid)</span></div>
<div class="block">The GID for the tar entry; optional, default="0"
 This is not the same as the group name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gid</code> - the group id.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGid()">
<h3>getGid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getGid</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the group identifier.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasGroupIdBeenSet()">
<h3>hasGroupIdBeenSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasGroupIdBeenSet</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether the group id has been explicitly set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="newArchiveScanner()">
<h3>newArchiveScanner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></span>&nbsp;<span class="element-name">newArchiveScanner</span>()</div>
<div class="block">Create a new scanner.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ArchiveFileSet.html#newArchiveScanner()">newArchiveScanner</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></code></dd>
<dt>Returns:</dt>
<dd>the created scanner.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRefid(org.apache.tools.ant.types.Reference)">
<h3>setRefid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefid</span><wbr><span class="parameters">(<a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span>
              throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Makes this instance in effect a reference to another instance.

 <p>You must not set another attribute or nest elements inside
 this element if you make it a reference.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a></code>&nbsp;in class&nbsp;<code><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>r</code> - the <code>Reference</code> to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRef(org.apache.tools.ant.Project)">
<h3>getRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></span>&nbsp;<span class="element-name">getRef</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">A TarFileset accepts another TarFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="FileSet.html#getRef(org.apache.tools.ant.Project)">getRef</a></code>&nbsp;in class&nbsp;<code><a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>the abstract fileset instance</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRef()">
<h3>getRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></span>&nbsp;<span class="element-name">getRef</span>()</div>
<div class="block">A TarFileset accepts another TarFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ArchiveFileSet.html#getRef()">getRef</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></code></dd>
<dt>Returns:</dt>
<dd>the abstract fileset instance</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)">
<h3>configureFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureFileSet</span><wbr><span class="parameters">(<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>&nbsp;zfs)</span></div>
<div class="block">Configure a fileset based on this fileset.
 If the fileset is a TarFileSet copy in the tarfileset
 specific attributes.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ArchiveFileSet.html#configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)">configureFileSet</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>zfs</code> - the archive fileset to configure.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<div class="block">Return a TarFileSet that has the same properties
 as this one.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ArchiveFileSet.html#clone()">clone</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></code></dd>
<dt>Returns:</dt>
<dd>the cloned tarFileSet</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
