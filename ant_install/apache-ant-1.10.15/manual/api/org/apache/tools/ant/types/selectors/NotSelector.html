<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>NotSelector (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors, class: NotSelector">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<h1 title="Class NotSelector" class="title">Class NotSelector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseSelector</a>
<div class="inheritance"><a href="BaseSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseSelectorContainer</a>
<div class="inheritance"><a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.NoneSelector</a>
<div class="inheritance">org.apache.tools.ant.types.selectors.NotSelector</div>
</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code>, <code><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code>, <code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">NotSelector</span>
<span class="extends-implements">extends <a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></span></div>
<div class="block">This selector has one other selectors whose meaning it inverts. It
 actually relies on NoneSelector for its implementation of the
 isSelected() method, but it adds a check to ensure there is only one
 other selector contained within.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">NotSelector</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.selectors.FileSelector)" class="member-name-link">NotSelector</a><wbr>(<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor that inverts the meaning of its argument.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert the Selectors within this container to a string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#verifySettings()" class="member-name-link">verifySettings</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Makes sure that there is only one entry, sets an error message if
 not.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.NoneSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></h3>
<code><a href="NoneSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseSelectorContainer">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="BaseSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a></h3>
<code><a href="BaseSelectorContainer.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="BaseSelectorContainer.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="BaseSelectorContainer.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="BaseSelectorContainer.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="BaseSelectorContainer.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="BaseSelectorContainer.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="BaseSelectorContainer.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="BaseSelectorContainer.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="BaseSelectorContainer.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="BaseSelectorContainer.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="BaseSelectorContainer.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="BaseSelectorContainer.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="BaseSelectorContainer.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="BaseSelectorContainer.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="BaseSelectorContainer.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="BaseSelectorContainer.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="BaseSelectorContainer.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="BaseSelectorContainer.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="BaseSelectorContainer.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="BaseSelectorContainer.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="BaseSelectorContainer.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="BaseSelectorContainer.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="BaseSelectorContainer.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="BaseSelectorContainer.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="BaseSelectorContainer.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="BaseSelectorContainer.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="BaseSelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="BaseSelectorContainer.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="BaseSelectorContainer.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="BaseSelectorContainer.html#hasSelectors()">hasSelectors</a>, <a href="BaseSelectorContainer.html#selectorCount()">selectorCount</a>, <a href="BaseSelectorContainer.html#selectorElements()">selectorElements</a>, <a href="BaseSelectorContainer.html#validate()">validate</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></h3>
<code><a href="BaseSelector.html#getError()">getError</a>, <a href="BaseSelector.html#setError(java.lang.String)">setError</a>, <a href="BaseSelector.html#setError(java.lang.String,java.lang.Throwable)">setError</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#clone()">clone</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.FileSelector">Methods inherited from interface&nbsp;org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></h3>
<code><a href="FileSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>NotSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">NotSelector</span>()</div>
<div class="block">Default constructor.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.selectors.FileSelector)">
<h3>NotSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">NotSelector</span><wbr><span class="parameters">(<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;other)</span></div>
<div class="block">Constructor that inverts the meaning of its argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the selector to invert</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="BaseSelectorContainer.html#toString()">BaseSelectorContainer</a></code></span></div>
<div class="block">Convert the Selectors within this container to a string. This will
 just be a helper class for the subclasses that put their own name
 around the contents listed here.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="NoneSelector.html#toString()">toString</a></code>&nbsp;in class&nbsp;<code><a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></code></dd>
<dt>Returns:</dt>
<dd>a string representation of the selector</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="verifySettings()">
<h3>verifySettings</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">verifySettings</span>()</div>
<div class="block">Makes sure that there is only one entry, sets an error message if
 not.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="BaseSelector.html#verifySettings()">verifySettings</a></code>&nbsp;in class&nbsp;<code><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
