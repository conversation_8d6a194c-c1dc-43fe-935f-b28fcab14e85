<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>Sort<PERSON>ilter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.filters, class: SortFilter">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.filters</a></div>
<h1 title="Class SortFilter" class="title">Class SortFilter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">java.io.Reader</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">java.io.FilterReader</a>
<div class="inheritance"><a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseFilterReader</a>
<div class="inheritance"><a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseParamFilterReader</a>
<div class="inheritance">org.apache.tools.ant.filters.SortFilter</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Readable.html" title="class or interface in java.lang" class="external-link">Readable</a></code>, <code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code>, <code><a href="../types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">SortFilter</span>
<span class="extends-implements">extends <a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a>
implements <a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></span></div>
<div class="block"><p>
 Sort a file before and/or after the file.
 </p>

 <p>
 Examples:
 </p>

 <pre>
   &lt;copy todir=&quot;build&quot;&gt;
       &lt;fileset dir=&quot;input&quot; includes=&quot;*.txt&quot;/&gt;
       &lt;filterchain&gt;
           &lt;sortfilter/&gt;
       &lt;/filterchain&gt;
   &lt;/copy&gt;
 </pre>

 <p>
 Sort all files <code>*.txt</code> from <i>src</i> location and copy
 them into <i>build</i> location. The lines of each file are sorted
 in ascendant order comparing the lines via the
 <code>String.compareTo(Object o)</code> method.
 </p>

 <pre>
   &lt;copy todir=&quot;build&quot;&gt;
       &lt;fileset dir=&quot;input&quot; includes=&quot;*.txt&quot;/&gt;
       &lt;filterchain&gt;
           &lt;sortfilter reverse=&quot;true&quot;/&gt;
       &lt;/filterchain&gt;
   &lt;/copy&gt;
 </pre>

 <p>
 Sort all files <code>*.txt</code> from <i>src</i> location into reverse
 order and copy them into <i>build</i> location. If reverse parameter has
 value <code>true</code> (default value), then the output line of the files
 will be in ascendant order.
 </p>

 <pre>
   &lt;copy todir=&quot;build&quot;&gt;
       &lt;fileset dir=&quot;input&quot; includes=&quot;*.txt&quot;/&gt;
       &lt;filterchain&gt;
           &lt;filterreader classname=&quot;org.apache.tools.ant.filters.SortFilter&quot;&gt;
             &lt;param name=&quot;comparator&quot; value=&quot;org.apache.tools.ant.filters.EvenFirstCmp&quot;/&gt;
           &lt;/filterreader&gt;
       &lt;/filterchain&gt;
   &lt;/copy&gt;
 </pre>

 <p>
 Sort all files <code>*.txt</code> from <i>src</i> location using as
 sorting criterion <code>EvenFirstCmp</code> class, that sorts the file
 lines putting even lines first then odd lines for example. The modified files
 are copied into <i>build</i> location. The <code>EvenFirstCmp</code>,
 has to an instantiable class via <code>Class.newInstance()</code>,
 therefore in case of inner class has to be <em>static</em>. It also has to
 implement <code>java.util.Comparator</code> interface, for example:
 </p>

 <pre>
         package org.apache.tools.ant.filters;
         ...(omitted)
           public final class EvenFirstCmp implements &lt;b&gt;Comparator&lt;/b&gt; {
             public int compare(Object o1, Object o2) {
             ...(omitted)
             }
           }
 </pre>

 <p>The example above is equivalent to:</p>

 <pre>
   &lt;componentdef name="evenfirst"
                 classname="org.apache.tools.ant.filters.EvenFirstCmp&quot;/&gt;
   &lt;copy todir=&quot;build&quot;&gt;
       &lt;fileset dir=&quot;input&quot; includes=&quot;*.txt&quot;/&gt;
       &lt;filterchain&gt;
           &lt;sortfilter&gt;
               &lt;evenfirst/&gt;
           &lt;/sortfilter&gt;
       &lt;/filterchain&gt;
   &lt;/copy&gt;
 </pre>

 <p>If parameter <code>comparator</code> is present, then
 <code>reverse</code> parameter will not be taken into account.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.FilterReader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#in" title="class or interface in java.io" class="external-link">in</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.Reader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html#lock" title="class or interface in java.io" class="external-link">lock</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SortFilter</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for "dummy" instances.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.Reader)" class="member-name-link">SortFilter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new filtered reader.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(java.util.Comparator)" class="member-name-link">add</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;comparator)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the comparator to be used as sorting criterion as nested element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#chain(java.io.Reader)" class="member-name-link">chain</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new SortReader using the passed in Reader for instantiation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a><wbr>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getComparator()" class="member-name-link">getComparator</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the comparator to be used for sorting.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isReverse()" class="member-name-link">isReverse</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns <code>true</code> if the sorting process will be in reverse
 order, otherwise the sorting process will be in ascendant order.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read()" class="member-name-link">read</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the next character in the filtered stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComparator(java.util.Comparator)" class="member-name-link">setComparator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;comparator)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the comparator to be used as sorting criterion.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setReverse(boolean)" class="member-name-link">setReverse</a><wbr>(boolean&nbsp;reverse)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the sorting process will be in ascendant (<code>reverse=false</code>)
 or to descendant (<code>reverse=true</code>).</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseParamFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a></h3>
<code><a href="BaseParamFilterReader.html#getParameters()">getParameters</a>, <a href="BaseParamFilterReader.html#setParameters(org.apache.tools.ant.types.Parameter...)">setParameters</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a></h3>
<code><a href="BaseFilterReader.html#getInitialized()">getInitialized</a>, <a href="BaseFilterReader.html#getProject()">getProject</a>, <a href="BaseFilterReader.html#read(char%5B%5D,int,int)">read</a>, <a href="BaseFilterReader.html#readFully()">readFully</a>, <a href="BaseFilterReader.html#readLine()">readLine</a>, <a href="BaseFilterReader.html#setInitialized(boolean)">setInitialized</a>, <a href="BaseFilterReader.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="BaseFilterReader.html#skip(long)">skip</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.FilterReader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#close()" title="class or interface in java.io" class="external-link">close</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#mark(int)" title="class or interface in java.io" class="external-link">mark</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#markSupported()" title="class or interface in java.io" class="external-link">markSupported</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#ready()" title="class or interface in java.io" class="external-link">ready</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#reset()" title="class or interface in java.io" class="external-link">reset</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.Reader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html#nullReader()" title="class or interface in java.io" class="external-link">nullReader</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html#read(char%5B%5D)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html#read(java.nio.CharBuffer)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html#transferTo(java.io.Writer)" title="class or interface in java.io" class="external-link">transferTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SortFilter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SortFilter</span>()</div>
<div class="block">Constructor for "dummy" instances.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BaseFilterReader.html#%3Cinit%3E()"><code>BaseFilterReader()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.Reader)">
<h3>SortFilter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SortFilter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</span></div>
<div class="block">Creates a new filtered reader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - A Reader object providing the underlying stream. Must not be
            <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="read()">
<h3>read</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">read</span>()
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Returns the next character in the filtered stream. If the desired number
 of lines have already been read, the resulting stream is effectively at
 an end. Otherwise, the next character from the underlying stream is read
 and returned.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html#read()" title="class or interface in java.io" class="external-link">read</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></code></dd>
<dt>Returns:</dt>
<dd>the next character in the resulting stream, or -1 if the end of
         the resulting stream has been reached</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the underlying stream throws an IOException during
                reading</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="chain(java.io.Reader)">
<h3>chain</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></span>&nbsp;<span class="element-name">chain</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</span></div>
<div class="block">Creates a new SortReader using the passed in Reader for instantiation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ChainableReader.html#chain(java.io.Reader)">chain</a></code>&nbsp;in interface&nbsp;<code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>rdr</code> - A Reader object providing the underlying stream. Must not be
            <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a new filter based on this configuration, but filtering the
         specified reader</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isReverse()">
<h3>isReverse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isReverse</span>()</div>
<div class="block">Returns <code>true</code> if the sorting process will be in reverse
 order, otherwise the sorting process will be in ascendant order.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> if the sorting process will be in reverse
         order, otherwise the sorting process will be in ascendant order.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setReverse(boolean)">
<h3>setReverse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setReverse</span><wbr><span class="parameters">(boolean&nbsp;reverse)</span></div>
<div class="block">Sets the sorting process will be in ascendant (<code>reverse=false</code>)
 or to descendant (<code>reverse=true</code>).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>reverse</code> - Boolean representing reverse ordering process.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getComparator()">
<h3>getComparator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getComparator</span>()</div>
<div class="block">Returns the comparator to be used for sorting.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the comparator</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setComparator(java.util.Comparator)">
<h3>setComparator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComparator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;comparator)</span></div>
<div class="block">Set the comparator to be used as sorting criterion.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comparator</code> - the comparator to set</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="add(java.util.Comparator)">
<h3>add</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Comparator.html" title="class or interface in java.util" class="external-link">Comparator</a>&lt;? super <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;comparator)</span></div>
<div class="block">Set the comparator to be used as sorting criterion as nested element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comparator</code> - the comparator to set</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
