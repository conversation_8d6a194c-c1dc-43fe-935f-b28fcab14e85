<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>TimeComparison (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, class: TimeComparison">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Class TimeComparison" class="title">Class TimeComparison</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="EnumeratedAttribute.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.EnumeratedAttribute</a>
<div class="inheritance">org.apache.tools.ant.types.TimeComparison</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="selectors/DateSelector.TimeComparisons.html" title="class in org.apache.tools.ant.types.selectors">DateSelector.TimeComparisons</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TimeComparison</span>
<span class="extends-implements">extends <a href="EnumeratedAttribute.html" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a></span></div>
<div class="block">EnumeratedAttribute for time comparisons.  Accepts values
 "before", "after", "equal".</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></code></div>
<div class="col-second even-row-color"><code><a href="#AFTER" class="member-name-link">AFTER</a></code></div>
<div class="col-last even-row-color">
<div class="block">After Comparison.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></code></div>
<div class="col-second odd-row-color"><code><a href="#BEFORE" class="member-name-link">BEFORE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Before Comparison.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></code></div>
<div class="col-second even-row-color"><code><a href="#EQUAL" class="member-name-link">EQUAL</a></code></div>
<div class="col-last even-row-color">
<div class="block">Equal Comparison.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.EnumeratedAttribute">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="EnumeratedAttribute.html" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a></h3>
<code><a href="EnumeratedAttribute.html#value">value</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TimeComparison</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">TimeComparison</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct a new TimeComparison with the specified value.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#compare(long,long)" class="member-name-link">compare</a><wbr>(long&nbsp;t1,
 long&nbsp;t2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Compare two times.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#compare(long,long,long)" class="member-name-link">compare</a><wbr>(long&nbsp;t1,
 long&nbsp;t2,
 long&nbsp;g)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Compare two times.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#evaluate(long,long)" class="member-name-link">evaluate</a><wbr>(long&nbsp;t1,
 long&nbsp;t2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Evaluate two times against this TimeComparison.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#evaluate(long,long,long)" class="member-name-link">evaluate</a><wbr>(long&nbsp;t1,
 long&nbsp;t2,
 long&nbsp;g)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Evaluate two times against this TimeComparison.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getValues()" class="member-name-link">getValues</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the possible values.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.EnumeratedAttribute">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="EnumeratedAttribute.html" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a></h3>
<code><a href="EnumeratedAttribute.html#containsValue(java.lang.String)">containsValue</a>, <a href="EnumeratedAttribute.html#getIndex()">getIndex</a>, <a href="EnumeratedAttribute.html#getInstance(java.lang.Class,java.lang.String)">getInstance</a>, <a href="EnumeratedAttribute.html#getValue()">getValue</a>, <a href="EnumeratedAttribute.html#indexOfValue(java.lang.String)">indexOfValue</a>, <a href="EnumeratedAttribute.html#setValue(java.lang.String)">setValue</a>, <a href="EnumeratedAttribute.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="BEFORE">
<h3>BEFORE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></span>&nbsp;<span class="element-name">BEFORE</span></div>
<div class="block">Before Comparison.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="AFTER">
<h3>AFTER</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></span>&nbsp;<span class="element-name">AFTER</span></div>
<div class="block">After Comparison.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="EQUAL">
<h3>EQUAL</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></span>&nbsp;<span class="element-name">EQUAL</span></div>
<div class="block">Equal Comparison.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TimeComparison</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TimeComparison</span>()</div>
<div class="block">Default constructor.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>TimeComparison</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TimeComparison</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Construct a new TimeComparison with the specified value.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the EnumeratedAttribute value.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getValues()">
<h3>getValues</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getValues</span>()</div>
<div class="block">Return the possible values.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EnumeratedAttribute.html#getValues()">getValues</a></code>&nbsp;in class&nbsp;<code><a href="EnumeratedAttribute.html" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a></code></dd>
<dt>Returns:</dt>
<dd>String[] of EnumeratedAttribute values.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="evaluate(long,long)">
<h3>evaluate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">evaluate</span><wbr><span class="parameters">(long&nbsp;t1,
 long&nbsp;t2)</span></div>
<div class="block">Evaluate two times against this TimeComparison.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t1</code> - the first time to compare.</dd>
<dd><code>t2</code> - the second time to compare.</dd>
<dt>Returns:</dt>
<dd>true if the comparison result fell within the parameters of this TimeComparison.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="evaluate(long,long,long)">
<h3>evaluate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">evaluate</span><wbr><span class="parameters">(long&nbsp;t1,
 long&nbsp;t2,
 long&nbsp;g)</span></div>
<div class="block">Evaluate two times against this TimeComparison.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t1</code> - the first time to compare.</dd>
<dd><code>t2</code> - the second time to compare.</dd>
<dd><code>g</code> - the timestamp granularity.</dd>
<dt>Returns:</dt>
<dd>true if the comparison result fell within the parameters of this TimeComparison.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="compare(long,long)">
<h3>compare</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compare</span><wbr><span class="parameters">(long&nbsp;t1,
 long&nbsp;t2)</span></div>
<div class="block">Compare two times.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t1</code> - the first time to compare.</dd>
<dd><code>t2</code> - the second time to compare.</dd>
<dt>Returns:</dt>
<dd>a negative integer, a positive integer, or zero as t1 is
         before, after, or equal to t2 accounting for the default granularity.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="compare(long,long,long)">
<h3>compare</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compare</span><wbr><span class="parameters">(long&nbsp;t1,
 long&nbsp;t2,
 long&nbsp;g)</span></div>
<div class="block">Compare two times.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t1</code> - the first time to compare.</dd>
<dd><code>t2</code> - the second time to compare.</dd>
<dd><code>g</code> - the timestamp granularity.</dd>
<dt>Returns:</dt>
<dd>a negative integer, a positive integer, or zero as t1 is
         before, after, or equal to t2 accounting for the specified granularity.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
