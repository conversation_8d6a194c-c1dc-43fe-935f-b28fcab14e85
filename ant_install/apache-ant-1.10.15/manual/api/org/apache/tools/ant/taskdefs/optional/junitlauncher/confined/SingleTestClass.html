<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>SingleTestClass (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junitlauncher.confined, class: SingleTestClass">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined</a></div>
<h1 title="Class SingleTestClass" class="title">Class SingleTestClass</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.TestDefinition</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.SingleTestClass</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="NamedTest.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">NamedTest</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SingleTestClass</span>
<span class="extends-implements">extends <a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a>
implements <a href="NamedTest.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">NamedTest</a></span></div>
<div class="block">Represents the single <code>test</code> (class) that's configured to be launched by the <a href="JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>JUnitLauncherTask</code></a></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h3 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.TestDefinition">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></h3>
<code><a href="TestDefinition.ForkedRepresentation.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition.ForkedRepresentation</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.TestDefinition">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></h3>
<code><a href="TestDefinition.html#excludeEngines">excludeEngines</a>, <a href="TestDefinition.html#failureProperty">failureProperty</a>, <a href="TestDefinition.html#forkDefinition">forkDefinition</a>, <a href="TestDefinition.html#haltOnFailure">haltOnFailure</a>, <a href="TestDefinition.html#ifProperty">ifProperty</a>, <a href="TestDefinition.html#includeEngines">includeEngines</a>, <a href="TestDefinition.html#listeners">listeners</a>, <a href="TestDefinition.html#outputDir">outputDir</a>, <a href="TestDefinition.html#unlessProperty">unlessProperty</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SingleTestClass</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromForkedRepresentation(javax.xml.stream.XMLStreamReader)" class="member-name-link">fromForkedRepresentation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/stream/XMLStreamReader.html" title="class or interface in javax.xml.stream" class="external-link">XMLStreamReader</a>&nbsp;reader)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMethods()" class="member-name-link">getMethods</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMethods(java.lang.String)" class="member-name-link">setMethods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;methods)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="TestDefinition.ForkedRepresentation.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition.ForkedRepresentation</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toForkedRepresentations()" class="member-name-link">toForkedRepresentations</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.TestDefinition">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.<a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></h3>
<code><a href="TestDefinition.html#addConfiguredListener(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.ListenerDefinition)">addConfiguredListener</a>, <a href="TestDefinition.html#createFork()">createFork</a>, <a href="TestDefinition.html#getExcludeEngines()">getExcludeEngines</a>, <a href="TestDefinition.html#getFailureProperty()">getFailureProperty</a>, <a href="TestDefinition.html#getIncludeEngines()">getIncludeEngines</a>, <a href="TestDefinition.html#getListeners()">getListeners</a>, <a href="TestDefinition.html#getOutputDir()">getOutputDir</a>, <a href="TestDefinition.html#isHaltOnFailure()">isHaltOnFailure</a>, <a href="TestDefinition.html#setExcludeEngines(java.lang.String)">setExcludeEngines</a>, <a href="TestDefinition.html#setFailureProperty(java.lang.String)">setFailureProperty</a>, <a href="TestDefinition.html#setHaltOnFailure(boolean)">setHaltOnFailure</a>, <a href="TestDefinition.html#setIf(java.lang.String)">setIf</a>, <a href="TestDefinition.html#setIncludeEngines(java.lang.String)">setIncludeEngines</a>, <a href="TestDefinition.html#setOutputDir(java.io.File)">setOutputDir</a>, <a href="TestDefinition.html#setUnless(java.lang.String)">setUnless</a>, <a href="TestDefinition.html#shouldRun(org.apache.tools.ant.Project)">shouldRun</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SingleTestClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SingleTestClass</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setName(java.lang.String)">
<h3>setName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;test)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="NamedTest.html#getName()">getName</a></code>&nbsp;in interface&nbsp;<code><a href="NamedTest.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">NamedTest</a></code></dd>
<dt>Returns:</dt>
<dd>Returns the name of the test</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMethods(java.lang.String)">
<h3>setMethods</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMethods</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;methods)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="getMethods()">
<h3>getMethods</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getMethods</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="toForkedRepresentations()">
<h3>toForkedRepresentations</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="TestDefinition.ForkedRepresentation.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition.ForkedRepresentation</a>&gt;</span>&nbsp;<span class="element-name">toForkedRepresentations</span>()
                                                                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="TestDefinition.html#toForkedRepresentations()">toForkedRepresentations</a></code>&nbsp;in class&nbsp;<code><a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fromForkedRepresentation(javax.xml.stream.XMLStreamReader)">
<h3>fromForkedRepresentation</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></span>&nbsp;<span class="element-name">fromForkedRepresentation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/stream/XMLStreamReader.html" title="class or interface in javax.xml.stream" class="external-link">XMLStreamReader</a>&nbsp;reader)</span>
                                               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/stream/XMLStreamException.html" title="class or interface in javax.xml.stream" class="external-link">XMLStreamException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.xml/javax/xml/stream/XMLStreamException.html" title="class or interface in javax.xml.stream" class="external-link">XMLStreamException</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
