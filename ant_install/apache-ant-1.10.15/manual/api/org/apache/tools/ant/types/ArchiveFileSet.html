<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ArchiveFileSet (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, class: ArchiveFileSet">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Class ArchiveFileSet" class="title">Class ArchiveFileSet</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</a>
<div class="inheritance"><a href="FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</a>
<div class="inheritance">org.apache.tools.ant.types.ArchiveFileSet</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code>, <code><a href="selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a></code>, <code><a href="ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">ArchiveFileSet</span>
<span class="extends-implements">extends <a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></span></div>
<div class="block">A ArchiveFileSet is a FileSet with extra attributes useful in the
 context of archiving tasks.

 It includes a prefix attribute which is prepended to each entry in
 the output archive file as well as a fullpath attribute.  It also
 supports Unix file permissions for files and directories.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_DIR_MODE" class="member-name-link">DEFAULT_DIR_MODE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Default value for the dirmode attribute.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_FILE_MODE" class="member-name-link">DEFAULT_FILE_MODE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Default value for the filemode attribute.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="DataType.html#checked">checked</a>, <a href="DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ArchiveFileSet</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for ArchiveFileSet</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.ArchiveFileSet)" class="member-name-link">ArchiveFileSet</a><wbr>(<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>&nbsp;fileset)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor using a archive fileset argument.</div>
</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.FileSet)" class="member-name-link">ArchiveFileSet</a><wbr>(<a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor using a fileset argument.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfigured(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">addConfigured</a><wbr>(<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;a)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the source Archive file for the archivefileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a ArchiveFileSet that has the same properties
 as this one.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)" class="member-name-link">configureFileSet</a><wbr>(<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>&nbsp;zfs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A ArchiveFileset accepts another ArchiveFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDirectoryScanner(org.apache.tools.ant.Project)" class="member-name-link">getDirectoryScanner</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the DirectoryScanner associated with this FileSet.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getDirMode()" class="member-name-link">getDirMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDirMode(org.apache.tools.ant.Project)" class="member-name-link">getDirMode</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the dir mode of the archive fileset</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEncoding()" class="member-name-link">getEncoding</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the encoding used for this ZipFileSet.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getFileMode()" class="member-name-link">getFileMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileMode(org.apache.tools.ant.Project)" class="member-name-link">getFileMode</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the mode of the archive fileset</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getFullpath()" class="member-name-link">getFullpath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFullpath(org.apache.tools.ant.Project)" class="member-name-link">getFullpath</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the full pathname of the single entry in this fileset.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getPrefix()" class="member-name-link">getPrefix</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPrefix(org.apache.tools.ant.Project)" class="member-name-link">getPrefix</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the prefix prepended to entries in the archive file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRef()" class="member-name-link">getRef</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs the check for circular references and returns the
 referenced object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSrc()" class="member-name-link">getSrc</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the archive file from which entries will be extracted.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSrc(org.apache.tools.ant.Project)" class="member-name-link">getSrc</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the archive from which entries will be extracted.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasDirModeBeenSet()" class="member-name-link">hasDirModeBeenSet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the user has specified the mode explicitly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasFileModeBeenSet()" class="member-name-link">hasFileModeBeenSet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the user has specified the mode explicitly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#integerSetDirMode(int)" class="member-name-link">integerSetDirMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0755</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#integerSetFileMode(int)" class="member-name-link">integerSetFileMode</a><wbr>(int&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0644</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFilesystemOnly()" class="member-name-link">isFilesystemOnly</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether this ResourceCollection is composed entirely of
 Resources accessible via local filesystem conventions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a><wbr>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#iterator()" class="member-name-link">iterator</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fulfill the ResourceCollection contract.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>protected abstract <a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#newArchiveScanner()" class="member-name-link">newArchiveScanner</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Creates a scanner for this type of archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDir(java.io.File)" class="member-name-link">setDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the directory for the fileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDirMode(java.lang.String)" class="member-name-link">setDirMode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;octalString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A 3 digit octal string, specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0755</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;enc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the encoding used for this ZipFileSet.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorOnMissingArchive(boolean)" class="member-name-link">setErrorOnMissingArchive</a><wbr>(boolean&nbsp;errorOnMissingArchive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether an error is thrown if an archive does not exist.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFileMode(java.lang.String)" class="member-name-link">setFileMode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;octalString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A 3 digit octal string, specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0644</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFullpath(java.lang.String)" class="member-name-link">setFullpath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fullpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the full pathname of the single entry in this fileset.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPrefix(java.lang.String)" class="member-name-link">setPrefix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Prepend this prefix to the path for each archive entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrc(java.io.File)" class="member-name-link">setSrc</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the source Archive file for the archivefileset.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrcResource(org.apache.tools.ant.types.Resource)" class="member-name-link">setSrcResource</a><wbr>(<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the source Archive file for the archivefileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#size()" class="member-name-link">size</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fulfill the ResourceCollection contract.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">For file-based archivefilesets, return the same as for normal filesets;
 else just return the path of the zip.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.FileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></h3>
<code><a href="FileSet.html#getRef(org.apache.tools.ant.Project)">getRef</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.AbstractFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></h3>
<code><a href="AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="AbstractFileSet.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="AbstractFileSet.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="AbstractFileSet.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="AbstractFileSet.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="AbstractFileSet.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="AbstractFileSet.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="AbstractFileSet.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="AbstractFileSet.html#appendExcludes(java.lang.String%5B%5D)">appendExcludes</a>, <a href="AbstractFileSet.html#appendIncludes(java.lang.String%5B%5D)">appendIncludes</a>, <a href="AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="AbstractFileSet.html#createExclude()">createExclude</a>, <a href="AbstractFileSet.html#createExcludesFile()">createExcludesFile</a>, <a href="AbstractFileSet.html#createInclude()">createInclude</a>, <a href="AbstractFileSet.html#createIncludesFile()">createIncludesFile</a>, <a href="AbstractFileSet.html#createPatternSet()">createPatternSet</a>, <a href="AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</a>, <a href="AbstractFileSet.html#getDir()">getDir</a>, <a href="AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</a>, <a href="AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</a>, <a href="AbstractFileSet.html#getErrorOnMissingDir()">getErrorOnMissingDir</a>, <a href="AbstractFileSet.html#getMaxLevelsOfSymlinks()">getMaxLevelsOfSymlinks</a>, <a href="AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="AbstractFileSet.html#hasPatterns()">hasPatterns</a>, <a href="AbstractFileSet.html#hasSelectors()">hasSelectors</a>, <a href="AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</a>, <a href="AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</a>, <a href="AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</a>, <a href="AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</a>, <a href="AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</a>, <a href="AbstractFileSet.html#selectorCount()">selectorCount</a>, <a href="AbstractFileSet.html#selectorElements()">selectorElements</a>, <a href="AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="AbstractFileSet.html#setErrorOnMissingDir(boolean)">setErrorOnMissingDir</a>, <a href="AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="AbstractFileSet.html#setFile(java.io.File)">setFile</a>, <a href="AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="AbstractFileSet.html#setMaxLevelsOfSymlinks(int)">setMaxLevelsOfSymlinks</a>, <a href="AbstractFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</a>, <a href="AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner,org.apache.tools.ant.Project)">setupDirectoryScanner</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="DataType.html#circularReference()">circularReference</a>, <a href="DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="DataType.html#getRefid()">getRefid</a>, <a href="DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="DataType.html#isChecked()">isChecked</a>, <a href="DataType.html#isReference()">isReference</a>, <a href="DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="DataType.html#setChecked(boolean)">setChecked</a>, <a href="DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULT_DIR_MODE">
<h3>DEFAULT_DIR_MODE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_DIR_MODE</span></div>
<div class="block">Default value for the dirmode attribute.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.types.ArchiveFileSet.DEFAULT_DIR_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_FILE_MODE">
<h3>DEFAULT_FILE_MODE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_FILE_MODE</span></div>
<div class="block">Default value for the filemode attribute.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.types.ArchiveFileSet.DEFAULT_FILE_MODE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ArchiveFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ArchiveFileSet</span>()</div>
<div class="block">Constructor for ArchiveFileSet</div>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.FileSet)">
<h3>ArchiveFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ArchiveFileSet</span><wbr><span class="parameters">(<a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset)</span></div>
<div class="block">Constructor using a fileset argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileset</code> - the fileset to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.ArchiveFileSet)">
<h3>ArchiveFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ArchiveFileSet</span><wbr><span class="parameters">(<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>&nbsp;fileset)</span></div>
<div class="block">Constructor using a archive fileset argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileset</code> - the archivefileset to use</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDir(java.io.File)">
<h3>setDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</span>
            throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set the directory for the fileset.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractFileSet.html#setDir(java.io.File)">setDir</a></code>&nbsp;in class&nbsp;<code><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>dir</code> - the directory for the fileset</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addConfigured(org.apache.tools.ant.types.ResourceCollection)">
<h3>addConfigured</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfigured</span><wbr><span class="parameters">(<a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;a)</span></div>
<div class="block">Set the source Archive file for the archivefileset.  Prevents both
 "dir" and "src" from being specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - the archive as a single element Resource collection.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSrc(java.io.File)">
<h3>setSrc</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrc</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcFile)</span></div>
<div class="block">Set the source Archive file for the archivefileset.  Prevents both
 "dir" and "src" from being specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcFile</code> - The archive from which to extract entries.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSrcResource(org.apache.tools.ant.types.Resource)">
<h3>setSrcResource</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrcResource</span><wbr><span class="parameters">(<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src)</span></div>
<div class="block">Set the source Archive file for the archivefileset.  Prevents both
 "dir" and "src" from being specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - The archive from which to extract entries.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSrc(org.apache.tools.ant.Project)">
<h3>getSrc</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getSrc</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get the archive from which entries will be extracted.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>the source file</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setErrorOnMissingArchive(boolean)">
<h3>setErrorOnMissingArchive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorOnMissingArchive</span><wbr><span class="parameters">(boolean&nbsp;errorOnMissingArchive)</span></div>
<div class="block">Sets whether an error is thrown if an archive does not exist.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorOnMissingArchive</code> - true if missing archives cause errors,
                        false if not.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSrc()">
<h3>getSrc</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getSrc</span>()</div>
<div class="block">Get the archive file from which entries will be extracted.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the archive in case the archive is a file, null otherwise.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRef()">
<h3>getRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></span>&nbsp;<span class="element-name">getRef</span>()</div>
<div class="block">Performs the check for circular references and returns the
 referenced object.
 This method must be overridden together with
 <a href="AbstractFileSet.html#getRef(org.apache.tools.ant.Project)"><code>getRef(Project)</code></a>
 providing implementations containing the special support
 for FileSet references, which can be handled by all ArchiveFileSets.
 NB! This method cannot be implemented in AbstractFileSet in order to allow
 FileSet and DirSet to implement it as a private method.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the dereferenced object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the reference is invalid (circular ref, wrong class, etc).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setPrefix(java.lang.String)">
<h3>setPrefix</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPrefix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">Prepend this prefix to the path for each archive entry.
 Prevents both prefix and fullpath from being specified</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - The prefix to prepend to entries in the archive file.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPrefix(org.apache.tools.ant.Project)">
<h3>getPrefix</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPrefix</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Return the prefix prepended to entries in the archive file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>the prefix</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFullpath(java.lang.String)">
<h3>setFullpath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFullpath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fullpath)</span></div>
<div class="block">Set the full pathname of the single entry in this fileset.
 Prevents both prefix and fullpath from being specified</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fullpath</code> - the full pathname of the single entry in this fileset.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFullpath(org.apache.tools.ant.Project)">
<h3>getFullpath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFullpath</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Return the full pathname of the single entry in this fileset.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>the full path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;enc)</span></div>
<div class="block">Set the encoding used for this ZipFileSet.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enc</code> - encoding as String.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getEncoding()">
<h3>getEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getEncoding</span>()</div>
<div class="block">Get the encoding used for this ZipFileSet.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String encoding.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="newArchiveScanner()">
<h3>newArchiveScanner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected abstract</span>&nbsp;<span class="return-type"><a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></span>&nbsp;<span class="element-name">newArchiveScanner</span>()</div>
<div class="block">Creates a scanner for this type of archive.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the scanner.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDirectoryScanner(org.apache.tools.ant.Project)">
<h3>getDirectoryScanner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></span>&nbsp;<span class="element-name">getDirectoryScanner</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Return the DirectoryScanner associated with this FileSet.
 If the ArchiveFileSet defines a source Archive file, then an ArchiveScanner
 is returned instead.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</a></code>&nbsp;in class&nbsp;<code><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>a directory scanner</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="iterator()">
<h3>iterator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</span>&nbsp;<span class="element-name">iterator</span>()</div>
<div class="block">Fulfill the ResourceCollection contract.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html#iterator()" title="class or interface in java.lang" class="external-link">iterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></dd>
<dt>Overrides:</dt>
<dd><code><a href="FileSet.html#iterator()">iterator</a></code>&nbsp;in class&nbsp;<code><a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></dd>
<dt>Returns:</dt>
<dd>Iterator of Resources.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="size()">
<h3>size</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">size</span>()</div>
<div class="block">Fulfill the ResourceCollection contract.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ResourceCollection.html#size()">size</a></code>&nbsp;in interface&nbsp;<code><a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="FileSet.html#size()">size</a></code>&nbsp;in class&nbsp;<code><a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></dd>
<dt>Returns:</dt>
<dd>size of the collection as int.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isFilesystemOnly()">
<h3>isFilesystemOnly</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFilesystemOnly</span>()</div>
<div class="block">Indicate whether this ResourceCollection is composed entirely of
 Resources accessible via local filesystem conventions.  If true,
 all Resources returned from this ResourceCollection should be
 instances of FileResource.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ResourceCollection.html#isFilesystemOnly()">isFilesystemOnly</a></code>&nbsp;in interface&nbsp;<code><a href="ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="FileSet.html#isFilesystemOnly()">isFilesystemOnly</a></code>&nbsp;in class&nbsp;<code><a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></dd>
<dt>Returns:</dt>
<dd>whether this is a filesystem-only resource collection.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFileMode(java.lang.String)">
<h3>setFileMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFileMode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;octalString)</span></div>
<div class="block">A 3 digit octal string, specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0644</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>octalString</code> - a <code>String</code> value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="integerSetFileMode(int)">
<h3>integerSetFileMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">integerSetFileMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
<div class="block">specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0644

 <p>We use the strange name so this method doesn't appear in
 IntrospectionHelpers list of attribute setters.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - a <code>int</code> value</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFileMode(org.apache.tools.ant.Project)">
<h3>getFileMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFileMode</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get the mode of the archive fileset</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>the mode</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasFileModeBeenSet()">
<h3>hasFileModeBeenSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasFileModeBeenSet</span>()</div>
<div class="block">Whether the user has specified the mode explicitly.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if it has been set</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDirMode(java.lang.String)">
<h3>setDirMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDirMode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;octalString)</span></div>
<div class="block">A 3 digit octal string, specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0755</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>octalString</code> - a <code>String</code> value</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="integerSetDirMode(int)">
<h3>integerSetDirMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">integerSetDirMode</span><wbr><span class="parameters">(int&nbsp;mode)</span></div>
<div class="block">specify the user, group and
 other modes in the standard Unix fashion;
 optional, default=0755
 <p>We use the strange name so this method doesn't appear in
 IntrospectionHelpers list of attribute setters.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - a <code>int</code> value</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDirMode(org.apache.tools.ant.Project)">
<h3>getDirMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDirMode</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get the dir mode of the archive fileset</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use</dd>
<dt>Returns:</dt>
<dd>the mode</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasDirModeBeenSet()">
<h3>hasDirModeBeenSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasDirModeBeenSet</span>()</div>
<div class="block">Whether the user has specified the mode explicitly.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if it has been set</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)">
<h3>configureFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureFileSet</span><wbr><span class="parameters">(<a href="ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a>&nbsp;zfs)</span></div>
<div class="block">A ArchiveFileset accepts another ArchiveFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zfs</code> - the project to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<div class="block">Return a ArchiveFileSet that has the same properties
 as this one.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="FileSet.html#clone()">clone</a></code>&nbsp;in class&nbsp;<code><a href="FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></dd>
<dt>Returns:</dt>
<dd>the cloned archiveFileSet</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">For file-based archivefilesets, return the same as for normal filesets;
 else just return the path of the zip.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractFileSet.html#toString()">toString</a></code>&nbsp;in class&nbsp;<code><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Returns:</dt>
<dd>for file based archivefilesets, included files as a list
 of semicolon-separated filenames. else just the name of the zip.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPrefix()">
<h3>getPrefix</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPrefix</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
<div class="block">Return the prefix prepended to entries in the archive file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the prefix.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFullpath()">
<h3>getFullpath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFullpath</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
<div class="block">Return the full pathname of the single entryZ in this fileset.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the full pathname.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFileMode()">
<h3>getFileMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFileMode</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the file mode.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDirMode()">
<h3>getDirMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getDirMode</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.</div>
</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the dir mode.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span>
                               throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">DataType</a></code></span></div>
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).

 <p>If one is included, throw a BuildException created by <a href="DataType.html#circularReference()"><code>circularReference</code></a>.</p>

 <p>This implementation is appropriate only for a DataType that
 cannot hold other DataTypes as children.</p>

 <p>The general contract of this method is that it shouldn't do
 anything if <a href="DataType.html#checked"><code>DataType.checked</code></a> is true and
 set it to true on exit.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractFileSet.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a></code>&nbsp;in class&nbsp;<code><a href="AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stk</code> - the stack of references to check.</dd>
<dd><code>p</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
