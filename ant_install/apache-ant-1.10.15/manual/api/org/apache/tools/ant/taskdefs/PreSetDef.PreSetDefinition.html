<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>PreSetDef.PreSetDefinition (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: PreSetDef, class: PreSetDefinition">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class PreSetDef.PreSetDefinition" class="title">Class PreSetDef.PreSetDefinition</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">org.apache.tools.ant.AntTypeDefinition</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.PreSetDef.PreSetDefinition</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="PreSetDef.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">PreSetDef.PreSetDefinition</span>
<span class="extends-implements">extends <a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></span></div>
<div class="block">This class contains the unknown element and the object
 that is predefined.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant"><code>AntTypeDefinition</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.UnknownElement)" class="member-name-link">PreSetDefinition</a><wbr>(<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a>&nbsp;parent,
 <a href="../UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;el)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new <code>PresetDefinition</code> instance.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkClass(org.apache.tools.ant.Project)" class="member-name-link">checkClass</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the attributes are correct.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#create(org.apache.tools.ant.Project)" class="member-name-link">create</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fake create an object, used by IntrospectionHelper and UnknownElement
 to see that this is a predefined object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createObject(org.apache.tools.ant.Project)" class="member-name-link">createObject</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an instance of the definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassLoader()" class="member-name-link">getClassLoader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the classloader for this definition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassName()" class="member-name-link">getClassName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the classname of the definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a><wbr>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExposedClass(org.apache.tools.ant.Project)" class="member-name-link">getExposedClass</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the exposed class for this definition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreSets()" class="member-name-link">getPreSets</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the preset values.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a><wbr>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTypeClass(org.apache.tools.ant.Project)" class="member-name-link">getTypeClass</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the definition class.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sameDefinition(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.Project)" class="member-name-link">sameDefinition</a><wbr>(<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a>&nbsp;other,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Equality method for this definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAdapterClass(java.lang.Class)" class="member-name-link">setAdapterClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;adapterClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the adapter class for this definition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAdaptToClass(java.lang.Class)" class="member-name-link">setAdaptToClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;adaptToClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the assignable class for this definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClass(java.lang.Class)" class="member-name-link">setClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;clazz)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override so that it is not allowed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassLoader(java.lang.ClassLoader)" class="member-name-link">setClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;classLoader)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classloader to use to create an instance
 of the definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassName(java.lang.String)" class="member-name-link">setClassName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override so that it is not allowed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#similarDefinition(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.Project)" class="member-name-link">similarDefinition</a><wbr>(<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a>&nbsp;other,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Similar method for this definition.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.AntTypeDefinition">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></h3>
<code><a href="../AntTypeDefinition.html#getName()">getName</a>, <a href="../AntTypeDefinition.html#innerCreateAndSet(java.lang.Class,org.apache.tools.ant.Project)">innerCreateAndSet</a>, <a href="../AntTypeDefinition.html#innerGetTypeClass()">innerGetTypeClass</a>, <a href="../AntTypeDefinition.html#isRestrict()">isRestrict</a>, <a href="../AntTypeDefinition.html#setName(java.lang.String)">setName</a>, <a href="../AntTypeDefinition.html#setRestrict(boolean)">setRestrict</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.UnknownElement)">
<h3>PreSetDefinition</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">PreSetDefinition</span><wbr><span class="parameters">(<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a>&nbsp;parent,
 <a href="../UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;el)</span></div>
<div class="block">Creates a new <code>PresetDefinition</code> instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - The parent of this predefinition.</dd>
<dd><code>el</code> - The predefined attributes, nested elements and text.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setClass(java.lang.Class)">
<h3>setClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;clazz)</span></div>
<div class="block">Override so that it is not allowed.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#setClass(java.lang.Class)">setClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>clazz</code> - a <code>Class</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClassName(java.lang.String)">
<h3>setClassName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</span></div>
<div class="block">Override so that it is not allowed.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#setClassName(java.lang.String)">setClassName</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>className</code> - a <code>String</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getClassName()">
<h3>getClassName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClassName</span>()</div>
<div class="block">Get the classname of the definition.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#getClassName()">getClassName</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Returns:</dt>
<dd>the name of the class of this definition.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setAdapterClass(java.lang.Class)">
<h3>setAdapterClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAdapterClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;adapterClass)</span></div>
<div class="block">Set the adapter class for this definition.
 NOT Supported</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#setAdapterClass(java.lang.Class)">setAdapterClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>adapterClass</code> - the adapterClass.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setAdaptToClass(java.lang.Class)">
<h3>setAdaptToClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAdaptToClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;adaptToClass)</span></div>
<div class="block">Set the assignable class for this definition.
 NOT SUPPORTED</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#setAdaptToClass(java.lang.Class)">setAdaptToClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>adaptToClass</code> - the assignable class.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClassLoader(java.lang.ClassLoader)">
<h3>setClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;classLoader)</span></div>
<div class="block">Set the classloader to use to create an instance
 of the definition.
 NOT SUPPORTED</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#setClassLoader(java.lang.ClassLoader)">setClassLoader</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>classLoader</code> - the classLoader.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getClassLoader()">
<h3>getClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getClassLoader</span>()</div>
<div class="block">Get the classloader for this definition.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#getClassLoader()">getClassLoader</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Returns:</dt>
<dd>the classloader for this definition.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExposedClass(org.apache.tools.ant.Project)">
<h3>getExposedClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">getExposedClass</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Get the exposed class for this definition.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#getExposedClass(org.apache.tools.ant.Project)">getExposedClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>the exposed class.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTypeClass(org.apache.tools.ant.Project)">
<h3>getTypeClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">getTypeClass</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Get the definition class.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#getTypeClass(org.apache.tools.ant.Project)">getTypeClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>the type of the definition.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="checkClass(org.apache.tools.ant.Project)">
<h3>checkClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkClass</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Check if the attributes are correct.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#checkClass(org.apache.tools.ant.Project)">checkClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - the current project.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createObject(org.apache.tools.ant.Project)">
<h3>createObject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">createObject</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Create an instance of the definition. The instance may be wrapped
 in a proxy class. This is a special version of create for
 IntrospectionHelper and UnknownElement.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>the created object.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPreSets()">
<h3>getPreSets</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></span>&nbsp;<span class="element-name">getPreSets</span>()</div>
<div class="block">Get the preset values.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the predefined attributes, elements and text as
         an UnknownElement.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="create(org.apache.tools.ant.Project)">
<h3>create</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Fake create an object, used by IntrospectionHelper and UnknownElement
 to see that this is a predefined object.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#create(org.apache.tools.ant.Project)">create</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>this object.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="sameDefinition(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.Project)">
<h3>sameDefinition</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">sameDefinition</span><wbr><span class="parameters">(<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a>&nbsp;other,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Equality method for this definition.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#sameDefinition(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.Project)">sameDefinition</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>other</code> - another definition.</dd>
<dd><code>project</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>true if the definitions are the same.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="similarDefinition(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.Project)">
<h3>similarDefinition</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">similarDefinition</span><wbr><span class="parameters">(<a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a>&nbsp;other,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Similar method for this definition.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntTypeDefinition.html#similarDefinition(org.apache.tools.ant.AntTypeDefinition,org.apache.tools.ant.Project)">similarDefinition</a></code>&nbsp;in class&nbsp;<code><a href="../AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></code></dd>
<dt>Parameters:</dt>
<dd><code>other</code> - another definition.</dd>
<dd><code>project</code> - the current project.</dd>
<dt>Returns:</dt>
<dd>true if the definitions are similar.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
