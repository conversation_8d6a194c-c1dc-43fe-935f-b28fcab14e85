<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ConditionBase (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.condition, class: ConditionBase">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.condition</a></div>
<h1 title="Class ConditionBase" class="title">Class ConditionBase</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.condition.ConditionBase</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="And.html" title="class in org.apache.tools.ant.taskdefs.condition">And</a></code>, <code><a href="../ConditionTask.html" title="class in org.apache.tools.ant.taskdefs">ConditionTask</a></code>, <code><a href="Not.html" title="class in org.apache.tools.ant.taskdefs.condition">Not</a></code>, <code><a href="Or.html" title="class in org.apache.tools.ant.taskdefs.condition">Or</a></code>, <code><a href="../WaitFor.html" title="class in org.apache.tools.ant.taskdefs">WaitFor</a></code>, <code><a href="Xor.html" title="class in org.apache.tools.ant.taskdefs.condition">Xor</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">ConditionBase</span>
<span class="extends-implements">extends <a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></span></div>
<div class="block">Baseclass for the &lt;condition&gt; task as well as several
 conditions - ensures that the types of conditions inside the task
 and the "container" conditions are in sync.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ConditionBase</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Simple constructor.</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">ConditionBase</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskName)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor that takes the name of the task in the task name.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.taskdefs.condition.Condition)" class="member-name-link">add</a><wbr>(<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>&nbsp;c)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an arbitrary condition</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addAnd(org.apache.tools.ant.taskdefs.condition.And)" class="member-name-link">addAnd</a><wbr>(<a href="And.html" title="class in org.apache.tools.ant.taskdefs.condition">And</a>&nbsp;a)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;and&gt; condition "container".</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addAvailable(org.apache.tools.ant.taskdefs.Available)" class="member-name-link">addAvailable</a><wbr>(<a href="../Available.html" title="class in org.apache.tools.ant.taskdefs">Available</a>&nbsp;a)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;available&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addChecksum(org.apache.tools.ant.taskdefs.Checksum)" class="member-name-link">addChecksum</a><wbr>(<a href="../Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</a>&nbsp;c)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;checksum&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContains(org.apache.tools.ant.taskdefs.condition.Contains)" class="member-name-link">addContains</a><wbr>(<a href="Contains.html" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a &lt;contains&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addEquals(org.apache.tools.ant.taskdefs.condition.Equals)" class="member-name-link">addEquals</a><wbr>(<a href="Equals.html" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;equals&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFilesMatch(org.apache.tools.ant.taskdefs.condition.FilesMatch)" class="member-name-link">addFilesMatch</a><wbr>(<a href="FilesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a &lt;filesmatch&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addHttp(org.apache.tools.ant.taskdefs.condition.Http)" class="member-name-link">addHttp</a><wbr>(<a href="Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</a>&nbsp;h)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;http&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIsFalse(org.apache.tools.ant.taskdefs.condition.IsFalse)" class="member-name-link">addIsFalse</a><wbr>(<a href="IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a &lt;isfalse&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIsFileSelected(org.apache.tools.ant.taskdefs.condition.IsFileSelected)" class="member-name-link">addIsFileSelected</a><wbr>(<a href="IsFileSelected.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a>&nbsp;test)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;isfileselected&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIsReference(org.apache.tools.ant.taskdefs.condition.IsReference)" class="member-name-link">addIsReference</a><wbr>(<a href="IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a>&nbsp;i)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;isreference&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIsSet(org.apache.tools.ant.taskdefs.condition.IsSet)" class="member-name-link">addIsSet</a><wbr>(<a href="IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a>&nbsp;i)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;isset&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIsTrue(org.apache.tools.ant.taskdefs.condition.IsTrue)" class="member-name-link">addIsTrue</a><wbr>(<a href="IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a &lt;istrue&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNot(org.apache.tools.ant.taskdefs.condition.Not)" class="member-name-link">addNot</a><wbr>(<a href="Not.html" title="class in org.apache.tools.ant.taskdefs.condition">Not</a>&nbsp;n)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;not&gt; condition "container".</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOr(org.apache.tools.ant.taskdefs.condition.Or)" class="member-name-link">addOr</a><wbr>(<a href="Or.html" title="class in org.apache.tools.ant.taskdefs.condition">Or</a>&nbsp;o)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;or&gt; condition "container".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOs(org.apache.tools.ant.taskdefs.condition.Os)" class="member-name-link">addOs</a><wbr>(<a href="Os.html" title="class in org.apache.tools.ant.taskdefs.condition">Os</a>&nbsp;o)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;os&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSocket(org.apache.tools.ant.taskdefs.condition.Socket)" class="member-name-link">addSocket</a><wbr>(<a href="Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a>&nbsp;s)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a &lt;socket&gt; condition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addUptodate(org.apache.tools.ant.taskdefs.UpToDate)" class="member-name-link">addUptodate</a><wbr>(<a href="../UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</a>&nbsp;u)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an &lt;uptodate&gt; condition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#countConditions()" class="member-name-link">countConditions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Count the conditions.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a><wbr>&lt;<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getConditions()" class="member-name-link">getConditions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Iterate through all conditions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskName()" class="member-name-link">getTaskName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the name to use in logging messages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTaskName(java.lang.String)" class="member-name-link">setTaskName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name to use in logging messages.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ConditionBase</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ConditionBase</span>()</div>
<div class="block">Simple constructor.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>ConditionBase</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ConditionBase</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskName)</span></div>
<div class="block">Constructor that takes the name of the task in the task name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>taskName</code> - the name of the task.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="countConditions()">
<h3>countConditions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">countConditions</span>()</div>
<div class="block">Count the conditions.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of conditions in the container</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getConditions()">
<h3>getConditions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>&gt;</span>&nbsp;<span class="element-name">getConditions</span>()</div>
<div class="block">Iterate through all conditions.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an enumeration to use for iteration</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTaskName(java.lang.String)">
<h3>setTaskName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTaskName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Sets the name to use in logging messages.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name to use in logging messages.
             Should not be <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTaskName()">
<h3>getTaskName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTaskName</span>()</div>
<div class="block">Returns the name to use in logging messages.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name to use in logging messages.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addAvailable(org.apache.tools.ant.taskdefs.Available)">
<h3>addAvailable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addAvailable</span><wbr><span class="parameters">(<a href="../Available.html" title="class in org.apache.tools.ant.taskdefs">Available</a>&nbsp;a)</span></div>
<div class="block">Add an &lt;available&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - an available condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addChecksum(org.apache.tools.ant.taskdefs.Checksum)">
<h3>addChecksum</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addChecksum</span><wbr><span class="parameters">(<a href="../Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</a>&nbsp;c)</span></div>
<div class="block">Add an &lt;checksum&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c</code> - a Checksum condition</dd>
<dt>Since:</dt>
<dd>1.4, Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addUptodate(org.apache.tools.ant.taskdefs.UpToDate)">
<h3>addUptodate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addUptodate</span><wbr><span class="parameters">(<a href="../UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</a>&nbsp;u)</span></div>
<div class="block">Add an &lt;uptodate&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>u</code> - an UpToDate condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addNot(org.apache.tools.ant.taskdefs.condition.Not)">
<h3>addNot</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNot</span><wbr><span class="parameters">(<a href="Not.html" title="class in org.apache.tools.ant.taskdefs.condition">Not</a>&nbsp;n)</span></div>
<div class="block">Add an &lt;not&gt; condition "container".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>n</code> - a Not condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addAnd(org.apache.tools.ant.taskdefs.condition.And)">
<h3>addAnd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addAnd</span><wbr><span class="parameters">(<a href="And.html" title="class in org.apache.tools.ant.taskdefs.condition">And</a>&nbsp;a)</span></div>
<div class="block">Add an &lt;and&gt; condition "container".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - an And condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOr(org.apache.tools.ant.taskdefs.condition.Or)">
<h3>addOr</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOr</span><wbr><span class="parameters">(<a href="Or.html" title="class in org.apache.tools.ant.taskdefs.condition">Or</a>&nbsp;o)</span></div>
<div class="block">Add an &lt;or&gt; condition "container".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - an Or condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addEquals(org.apache.tools.ant.taskdefs.condition.Equals)">
<h3>addEquals</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addEquals</span><wbr><span class="parameters">(<a href="Equals.html" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a>&nbsp;e)</span></div>
<div class="block">Add an &lt;equals&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>e</code> - an Equals condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOs(org.apache.tools.ant.taskdefs.condition.Os)">
<h3>addOs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOs</span><wbr><span class="parameters">(<a href="Os.html" title="class in org.apache.tools.ant.taskdefs.condition">Os</a>&nbsp;o)</span></div>
<div class="block">Add an &lt;os&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - an Os condition</dd>
<dt>Since:</dt>
<dd>1.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addIsSet(org.apache.tools.ant.taskdefs.condition.IsSet)">
<h3>addIsSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIsSet</span><wbr><span class="parameters">(<a href="IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a>&nbsp;i)</span></div>
<div class="block">Add an &lt;isset&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>i</code> - an IsSet condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addHttp(org.apache.tools.ant.taskdefs.condition.Http)">
<h3>addHttp</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addHttp</span><wbr><span class="parameters">(<a href="Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</a>&nbsp;h)</span></div>
<div class="block">Add an &lt;http&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>h</code> - an Http condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSocket(org.apache.tools.ant.taskdefs.condition.Socket)">
<h3>addSocket</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSocket</span><wbr><span class="parameters">(<a href="Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a>&nbsp;s)</span></div>
<div class="block">Add a &lt;socket&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - a Socket condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addFilesMatch(org.apache.tools.ant.taskdefs.condition.FilesMatch)">
<h3>addFilesMatch</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFilesMatch</span><wbr><span class="parameters">(<a href="FilesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a>&nbsp;test)</span></div>
<div class="block">Add a &lt;filesmatch&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - a FilesMatch condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addContains(org.apache.tools.ant.taskdefs.condition.Contains)">
<h3>addContains</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContains</span><wbr><span class="parameters">(<a href="Contains.html" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a>&nbsp;test)</span></div>
<div class="block">Add a &lt;contains&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - a Contains condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addIsTrue(org.apache.tools.ant.taskdefs.condition.IsTrue)">
<h3>addIsTrue</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIsTrue</span><wbr><span class="parameters">(<a href="IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a>&nbsp;test)</span></div>
<div class="block">Add a &lt;istrue&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - an IsTrue condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addIsFalse(org.apache.tools.ant.taskdefs.condition.IsFalse)">
<h3>addIsFalse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIsFalse</span><wbr><span class="parameters">(<a href="IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a>&nbsp;test)</span></div>
<div class="block">Add a &lt;isfalse&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - an IsFalse condition</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addIsReference(org.apache.tools.ant.taskdefs.condition.IsReference)">
<h3>addIsReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIsReference</span><wbr><span class="parameters">(<a href="IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a>&nbsp;i)</span></div>
<div class="block">Add an &lt;isreference&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>i</code> - an IsReference condition</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addIsFileSelected(org.apache.tools.ant.taskdefs.condition.IsFileSelected)">
<h3>addIsFileSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIsFileSelected</span><wbr><span class="parameters">(<a href="IsFileSelected.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a>&nbsp;test)</span></div>
<div class="block">Add an &lt;isfileselected&gt; condition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the condition</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.taskdefs.condition.Condition)">
<h3>add</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>&nbsp;c)</span></div>
<div class="block">Add an arbitrary condition</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c</code> - a condition</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
