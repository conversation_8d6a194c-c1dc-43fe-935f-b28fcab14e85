<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>Project (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: Project">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class Project" class="title">Class Project</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.Project</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Project</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></span></div>
<div class="block">Central representation of an Ant project. This class defines an
 Ant project with all of its targets, tasks and various other
 properties. It also provides the mechanism to kick off a build using
 a particular target name.
 <p>
 This class also encapsulates methods which allow files to be referred
 to using abstract path names which are translated to native system
 file paths at runtime.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JAVA_1_0" class="member-name-link">JAVA_1_0</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#JAVA_1_1" class="member-name-link">JAVA_1_1</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JAVA_1_2" class="member-name-link">JAVA_1_2</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#JAVA_1_3" class="member-name-link">JAVA_1_3</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JAVA_1_4" class="member-name-link">JAVA_1_4</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MSG_DEBUG" class="member-name-link">MSG_DEBUG</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Message priority of &quot;debug&quot;.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MSG_ERR" class="member-name-link">MSG_ERR</a></code></div>
<div class="col-last even-row-color">
<div class="block">Message priority of &quot;error&quot;.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MSG_INFO" class="member-name-link">MSG_INFO</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Message priority of &quot;information&quot;.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MSG_VERBOSE" class="member-name-link">MSG_VERBOSE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Message priority of &quot;verbose&quot;.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MSG_WARN" class="member-name-link">MSG_WARN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Message priority of &quot;warning&quot;.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TOKEN_END" class="member-name-link">TOKEN_END</a></code></div>
<div class="col-last even-row-color">
<div class="block">Default filter end token.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#TOKEN_START" class="member-name-link">TOKEN_START</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Default filter start token.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Project</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Create a new Ant project.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addBuildListener(org.apache.tools.ant.BuildListener)" class="member-name-link">addBuildListener</a><wbr>(<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a build listener to the list.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDataTypeDefinition(java.lang.String,java.lang.Class)" class="member-name-link">addDataTypeDefinition</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;typeName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;typeClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new datatype definition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addFilter(java.lang.String,java.lang.String)" class="member-name-link">addFilter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIdReference(java.lang.String,java.lang.Object)" class="member-name-link">addIdReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an id reference.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOrReplaceTarget(java.lang.String,org.apache.tools.ant.Target)" class="member-name-link">addOrReplaceTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetName,
 <a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a target to the project, or replaces one with the same
 name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOrReplaceTarget(org.apache.tools.ant.Target)" class="member-name-link">addOrReplaceTarget</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a target to the project, or replaces one with the same
 name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addReference(java.lang.String,java.lang.Object)" class="member-name-link">addReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;referenceName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a reference to the project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTarget(java.lang.String,org.apache.tools.ant.Target)" class="member-name-link">addTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetName,
 <a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a <em>new</em> target to the project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTarget(org.apache.tools.ant.Target)" class="member-name-link">addTarget</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a <em>new</em> target to the project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTaskDefinition(java.lang.String,java.lang.Class)" class="member-name-link">addTaskDefinition</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;taskClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new task definition to the project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkTaskClass(java.lang.Class)" class="member-name-link">checkTaskClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;taskClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check whether or not a class is suitable for serving as Ant task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.io.File,java.io.File)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.io.File,java.io.File,boolean)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.io.File,java.io.File,boolean,boolean)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.io.File,java.io.File,boolean,boolean,boolean)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite,
 boolean&nbsp;preserveLastModified)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.lang.String,java.lang.String)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.lang.String,java.lang.String,boolean)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile,
 boolean&nbsp;filtering)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.lang.String,java.lang.String,boolean,boolean)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#copyFile(java.lang.String,java.lang.String,boolean,boolean,boolean)" class="member-name-link">copyFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite,
 boolean&nbsp;preserveLastModified)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyInheritedProperties(org.apache.tools.ant.Project)" class="member-name-link">copyInheritedProperties</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copy all user properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyUserProperties(org.apache.tools.ant.Project)" class="member-name-link">copyUserProperties</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copy all user properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClassLoader(java.lang.ClassLoader,org.apache.tools.ant.types.Path)" class="member-name-link">createClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Factory method to create a class loader for loading classes from
 a given path.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClassLoader(org.apache.tools.ant.types.Path)" class="member-name-link">createClassLoader</a><wbr>(<a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Factory method to create a class loader for loading classes from
 a given path.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createDataType(java.lang.String)" class="member-name-link">createDataType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;typeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new instance of a data type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createSubProject()" class="member-name-link">createSubProject</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create and initialize a subproject.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createTask(java.lang.String)" class="member-name-link">createTask</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new instance of a task, adding it to a list of
 created tasks for later invalidation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#defaultInput(byte%5B%5D,int,int)" class="member-name-link">defaultInput</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Read data from the default input stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#demuxFlush(java.lang.String,boolean)" class="member-name-link">demuxFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output,
 boolean&nbsp;isError)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Demultiplex flush operations so that each task receives the appropriate
 messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#demuxInput(byte%5B%5D,int,int)" class="member-name-link">demuxInput</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Demux an input request to the correct task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#demuxOutput(java.lang.String,boolean)" class="member-name-link">demuxOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output,
 boolean&nbsp;isWarning)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Demultiplex output so that each task receives the appropriate
 messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeSortedTargets(java.util.Vector)" class="member-name-link">executeSortedTargets</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;sortedTargets)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Execute a <code>Vector</code> of sorted targets.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeTarget(java.lang.String)" class="member-name-link">executeTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Execute the specified target and any targets it depends on.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeTargets(java.util.Vector)" class="member-name-link">executeTargets</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;names)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Execute the specified sequence of targets, and the targets
 they depend on.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireBuildFinished(java.lang.Throwable)" class="member-name-link">fireBuildFinished</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;build finished&quot; event to the build listeners
 for this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireBuildStarted()" class="member-name-link">fireBuildStarted</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;build started&quot; event
 to the build listeners for this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireMessageLogged(org.apache.tools.ant.Project,java.lang.String,int)" class="member-name-link">fireMessageLogged</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;message logged&quot; project level event
 to the build listeners for this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireMessageLogged(org.apache.tools.ant.Project,java.lang.String,java.lang.Throwable,int)" class="member-name-link">fireMessageLogged</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;priority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;message logged&quot; project level event
 to the build listeners for this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireMessageLogged(org.apache.tools.ant.Target,java.lang.String,int)" class="member-name-link">fireMessageLogged</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;message logged&quot; target level event
 to the build listeners for this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireMessageLogged(org.apache.tools.ant.Target,java.lang.String,java.lang.Throwable,int)" class="member-name-link">fireMessageLogged</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;priority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;message logged&quot; target level event
 to the build listeners for this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireMessageLogged(org.apache.tools.ant.Task,java.lang.String,int)" class="member-name-link">fireMessageLogged</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;message logged&quot; task level event
 to the build listeners for this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireMessageLogged(org.apache.tools.ant.Task,java.lang.String,java.lang.Throwable,int)" class="member-name-link">fireMessageLogged</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;priority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;message logged&quot; task level event
 to the build listeners for this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireSubBuildFinished(java.lang.Throwable)" class="member-name-link">fireSubBuildFinished</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;subbuild finished&quot; event to the build listeners for
 this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireSubBuildStarted()" class="member-name-link">fireSubBuildStarted</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;subbuild started&quot; event to the build listeners for
 this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireTargetFinished(org.apache.tools.ant.Target,java.lang.Throwable)" class="member-name-link">fireTargetFinished</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;target finished&quot; event to the build listeners
 for this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireTargetStarted(org.apache.tools.ant.Target)" class="member-name-link">fireTargetStarted</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;target started&quot; event to the build listeners
 for this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireTaskFinished(org.apache.tools.ant.Task,java.lang.Throwable)" class="member-name-link">fireTaskFinished</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;task finished&quot; event to the build listeners for this
 project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fireTaskStarted(org.apache.tools.ant.Task)" class="member-name-link">fireTaskStarted</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send a &quot;task started&quot; event to the build listeners
 for this project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBaseDir()" class="member-name-link">getBaseDir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the base directory of the project as a file object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a><wbr>&lt;<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildListeners()" class="member-name-link">getBuildListeners</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a copy of the list of build listeners for the project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCopyOfDataTypeDefinitions()" class="member-name-link">getCopyOfDataTypeDefinitions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the current datatype definition map.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCopyOfReferences()" class="member-name-link">getCopyOfReferences</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a map of the references in the project (String to
 Object).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCopyOfTargets()" class="member-name-link">getCopyOfTargets</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the map of targets.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCopyOfTaskDefinitions()" class="member-name-link">getCopyOfTaskDefinitions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the current task definition map.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCoreLoader()" class="member-name-link">getCoreLoader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the core classloader to use for this project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDataTypeDefinitions()" class="member-name-link">getDataTypeDefinitions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the current datatype definition hashtable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultInputStream()" class="member-name-link">getDefaultInputStream</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this project's input stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultTarget()" class="member-name-link">getDefaultTarget</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the name of the default target of the project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDescription()" class="member-name-link">getDescription</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the project description, if one has been set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getElementName(java.lang.Object)" class="member-name-link">getElementName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a description of the type of the given element, with
 special handling for instances of tasks and data types.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Executor.html" title="interface in org.apache.tools.ant">Executor</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExecutor()" class="member-name-link">getExecutor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this Project's Executor (setting it if necessary).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getFilters()" class="member-name-link">getFilters</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x
             Use getGlobalFilterSet().getFilterHash().</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="types/FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGlobalFilterSet()" class="member-name-link">getGlobalFilterSet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the set of global filters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInheritedProperties()" class="member-name-link">getInheritedProperties</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a copy of the inherited property hashtable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputHandler()" class="member-name-link">getInputHandler</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieve the current input handler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getJavaVersion()" class="member-name-link">getJavaVersion</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the project name, if one has been set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getProject(java.lang.Object)" class="member-name-link">getProject</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get the Project instance associated with the specified object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProperties()" class="member-name-link">getProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a copy of the properties table.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProperty(java.lang.String)" class="member-name-link">getProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the value of a property, if it is set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPropertyNames()" class="member-name-link">getPropertyNames</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the names of all known properties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getReference(java.lang.String)" class="member-name-link">getReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Look up a reference by its key (ID).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getReferences()" class="member-name-link">getReferences</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a map of the references in the project (String to Object).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResource(java.lang.String)" class="member-name-link">getResource</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resolve the file relative to the project's basedir and return it as a
 FileResource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTargets()" class="member-name-link">getTargets</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the hashtable of targets.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskDefinitions()" class="member-name-link">getTaskDefinitions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the current task definition hashtable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getThreadTask(java.lang.Thread)" class="member-name-link">getThreadTask</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a>&nbsp;thread)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the current task associated with a thread, if any.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserProperties()" class="member-name-link">getUserProperties</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a copy of the user property hashtable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserProperty(java.lang.String)" class="member-name-link">getUserProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the value of a user property, if it is set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasReference(java.lang.String)" class="member-name-link">hasReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Does the project know this reference?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#inheritIDReferences(org.apache.tools.ant.Project)" class="member-name-link">inheritIDReferences</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;parent)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Inherit the id references.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initProperties()" class="member-name-link">initProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initializes the properties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initSubProject(org.apache.tools.ant.Project)" class="member-name-link">initSubProject</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;subProject)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize a subproject.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isKeepGoingMode()" class="member-name-link">isKeepGoingMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the keep-going mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a message to the log with the default log level
 of MSG_INFO .</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;msgLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a project level message to the log with the given log level.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,java.lang.Throwable,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;msgLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a project level message to the log with the given log level.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(org.apache.tools.ant.Target,java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;msgLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a target level message to the log with the given log level.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(org.apache.tools.ant.Target,java.lang.String,java.lang.Throwable,int)" class="member-name-link">log</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;msgLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a target level message to the log with the given log level.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(org.apache.tools.ant.Task,java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;msgLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a task level message to the log with the given log level.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(org.apache.tools.ant.Task,java.lang.String,java.lang.Throwable,int)" class="member-name-link">log</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;msgLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write a task level message to the log with the given log level.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registerThreadTask(java.lang.Thread,org.apache.tools.ant.Task)" class="member-name-link">registerThreadTask</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a>&nbsp;thread,
 <a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Register a task as the current task for a thread.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeBuildListener(org.apache.tools.ant.BuildListener)" class="member-name-link">removeBuildListener</a><wbr>(<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>&nbsp;listener)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Remove a build listener from the list.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#replaceProperties(java.lang.String)" class="member-name-link">replaceProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replace ${} style constructions in the given value with the
 string value of the corresponding data types.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveFile(java.lang.String)" class="member-name-link">resolveFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the canonical form of a filename.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#resolveFile(java.lang.String,java.io.File)" class="member-name-link">resolveFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;rootDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasedir(java.lang.String)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseD)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base directory for the project, checking that
 the given filename exists and is a directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBaseDir(java.io.File)" class="member-name-link">setBaseDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base directory for the project, checking that
 the given file exists and is a directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCoreLoader(java.lang.ClassLoader)" class="member-name-link">setCoreLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;coreLoader)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the core classloader for the project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefault(java.lang.String)" class="member-name-link">setDefault</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTarget)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the default target of the project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultInputStream(java.io.InputStream)" class="member-name-link">setDefaultInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;defaultInputStream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the default System input stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setDefaultTarget(java.lang.String)" class="member-name-link">setDefaultTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTarget)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDescription(java.lang.String)" class="member-name-link">setDescription</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the project description.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExecutor(org.apache.tools.ant.Executor)" class="member-name-link">setExecutor</a><wbr>(<a href="Executor.html" title="interface in org.apache.tools.ant">Executor</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the Executor instance for this Project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setFileLastModified(java.io.File,long)" class="member-name-link">setFileLastModified</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 long&nbsp;time)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInheritedProperty(java.lang.String,java.lang.String)" class="member-name-link">setInheritedProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a user property, which cannot be overwritten by set/unset
 property calls.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputHandler(org.apache.tools.ant.input.InputHandler)" class="member-name-link">setInputHandler</a><wbr>(<a href="input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a>&nbsp;handler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the input handler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavaVersionProperty()" class="member-name-link">setJavaVersionProperty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the <code>ant.java.version</code> property and tests for
 unsupported JVM versions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepGoingMode(boolean)" class="member-name-link">setKeepGoingMode</a><wbr>(boolean&nbsp;keepGoingMode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set &quot;keep-going&quot; mode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the project, also setting the user
 property <code>ant.project.name</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewProperty(java.lang.String,java.lang.String)" class="member-name-link">setNewProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a property if no value currently exists.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProjectReference(java.lang.Object)" class="member-name-link">setProjectReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a reference to this Project on the parameterized object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProperty(java.lang.String,java.lang.String)" class="member-name-link">setProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a property.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSystemProperties()" class="member-name-link">setSystemProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add all system properties which aren't already defined as
 user properties to the project properties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserProperty(java.lang.String,java.lang.String)" class="member-name-link">setUserProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a user property, which cannot be overwritten by
 set/unset property calls.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toBoolean(java.lang.String)" class="member-name-link">toBoolean</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Return the boolean equivalent of a string, which is considered
 <code>true</code> if either <code>"on"</code>, <code>"true"</code>,
 or <code>"yes"</code> is found, ignoring case.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a><wbr>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#topoSort(java.lang.String%5B%5D,java.util.Hashtable,boolean)" class="member-name-link">topoSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;roots,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;targetTable,
 boolean&nbsp;returnAll)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Topologically sort a set of targets.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a><wbr>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#topoSort(java.lang.String,java.util.Hashtable)" class="member-name-link">topoSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;root,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;targetTable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Topologically sort a set of targets.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a><wbr>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#topoSort(java.lang.String,java.util.Hashtable,boolean)" class="member-name-link">topoSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;root,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;targetTable,
 boolean&nbsp;returnAll)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Topologically sort a set of targets.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#translatePath(java.lang.String)" class="member-name-link">translatePath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;toProcess)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7
             Use FileUtils.translatePath instead.</div>
</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="MSG_ERR">
<h3>MSG_ERR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MSG_ERR</span></div>
<div class="block">Message priority of &quot;error&quot;.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_ERR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MSG_WARN">
<h3>MSG_WARN</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MSG_WARN</span></div>
<div class="block">Message priority of &quot;warning&quot;.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_WARN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MSG_INFO">
<h3>MSG_INFO</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MSG_INFO</span></div>
<div class="block">Message priority of &quot;information&quot;.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_INFO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MSG_VERBOSE">
<h3>MSG_VERBOSE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MSG_VERBOSE</span></div>
<div class="block">Message priority of &quot;verbose&quot;.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_VERBOSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MSG_DEBUG">
<h3>MSG_DEBUG</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MSG_DEBUG</span></div>
<div class="block">Message priority of &quot;debug&quot;.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_DEBUG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="JAVA_1_0">
<h3>JAVA_1_0</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAVA_1_0</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use <a href="util/JavaEnvUtils.html#JAVA_1_0"><code>JavaEnvUtils.JAVA_1_0</code></a> instead.</div>
</div>
<div class="block">Version constant for Java 1.0 .</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_0">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="JAVA_1_1">
<h3>JAVA_1_1</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAVA_1_1</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use <a href="util/JavaEnvUtils.html#JAVA_1_1"><code>JavaEnvUtils.JAVA_1_1</code></a> instead.</div>
</div>
<div class="block">Version constant for Java 1.1 .</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="JAVA_1_2">
<h3>JAVA_1_2</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAVA_1_2</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use <a href="util/JavaEnvUtils.html#JAVA_1_2"><code>JavaEnvUtils.JAVA_1_2</code></a> instead.</div>
</div>
<div class="block">Version constant for Java 1.2 .</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="JAVA_1_3">
<h3>JAVA_1_3</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAVA_1_3</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use <a href="util/JavaEnvUtils.html#JAVA_1_3"><code>JavaEnvUtils.JAVA_1_3</code></a> instead.</div>
</div>
<div class="block">Version constant for Java 1.3 .</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_3">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="JAVA_1_4">
<h3>JAVA_1_4</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAVA_1_4</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use <a href="util/JavaEnvUtils.html#JAVA_1_4"><code>JavaEnvUtils.JAVA_1_4</code></a> instead.</div>
</div>
<div class="block">Version constant for Java 1.4 .</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_4">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="TOKEN_START">
<h3>TOKEN_START</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TOKEN_START</span></div>
<div class="block">Default filter start token.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.TOKEN_START">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="TOKEN_END">
<h3>TOKEN_END</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TOKEN_END</span></div>
<div class="block">Default filter end token.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.Project.TOKEN_END">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Project</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Project</span>()</div>
<div class="block">Create a new Ant project.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setInputHandler(org.apache.tools.ant.input.InputHandler)">
<h3>setInputHandler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputHandler</span><wbr><span class="parameters">(<a href="input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a>&nbsp;handler)</span></div>
<div class="block">Set the input handler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>handler</code> - the InputHandler instance to use for gathering input.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDefaultInputStream(java.io.InputStream)">
<h3>setDefaultInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;defaultInputStream)</span></div>
<div class="block">Set the default System input stream. Normally this stream is set to
 System.in. This inputStream is used when no task input redirection is
 being performed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>defaultInputStream</code> - the default input stream to use when input
        is requested.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultInputStream()">
<h3>getDefaultInputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></span>&nbsp;<span class="element-name">getDefaultInputStream</span>()</div>
<div class="block">Get this project's input stream.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the InputStream instance in use by this Project instance to
 read input.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getInputHandler()">
<h3>getInputHandler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a></span>&nbsp;<span class="element-name">getInputHandler</span>()</div>
<div class="block">Retrieve the current input handler.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the InputHandler instance currently in place for the project
         instance.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createSubProject()">
<h3>createSubProject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">createSubProject</span>()</div>
<div class="block">Create and initialize a subproject. By default the subproject will be of
 the same type as its parent. If a no-arg constructor is unavailable, the
 <code>Project</code> class will be used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a Project instance configured as a subproject of this Project.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="initSubProject(org.apache.tools.ant.Project)">
<h3>initSubProject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initSubProject</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;subProject)</span></div>
<div class="block">Initialize a subproject.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>subProject</code> - the subproject to initialize.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()
          throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Initialise the project.

 This involves setting the default task definitions and loading the
 system properties.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the default task list cannot be loaded.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="initProperties()">
<h3>initProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initProperties</span>()
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Initializes the properties.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an vital property could not be set.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createClassLoader(org.apache.tools.ant.types.Path)">
<h3>createClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></span>&nbsp;<span class="element-name">createClassLoader</span><wbr><span class="parameters">(<a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Factory method to create a class loader for loading classes from
 a given path.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - the path from which classes are to be loaded.</dd>
<dt>Returns:</dt>
<dd>an appropriate classloader.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createClassLoader(java.lang.ClassLoader,org.apache.tools.ant.types.Path)">
<h3>createClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></span>&nbsp;<span class="element-name">createClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Factory method to create a class loader for loading classes from
 a given path.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - the parent classloader for the new loader.</dd>
<dd><code>path</code> - the path from which classes are to be loaded.</dd>
<dt>Returns:</dt>
<dd>an appropriate classloader.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCoreLoader(java.lang.ClassLoader)">
<h3>setCoreLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCoreLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;coreLoader)</span></div>
<div class="block">Set the core classloader for the project. If a <code>null</code>
 classloader is specified, the parent classloader should be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>coreLoader</code> - The classloader to use for the project.
                   May be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCoreLoader()">
<h3>getCoreLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getCoreLoader</span>()</div>
<div class="block">Return the core classloader to use for this project.
 This may be <code>null</code>, indicating that
 the parent classloader should be used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the core classloader to use for this project.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addBuildListener(org.apache.tools.ant.BuildListener)">
<h3>addBuildListener</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addBuildListener</span><wbr><span class="parameters">(<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>&nbsp;listener)</span></div>
<div class="block">Add a build listener to the list. This listener will
 be notified of build events for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>listener</code> - The listener to add to the list.
                 Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="removeBuildListener(org.apache.tools.ant.BuildListener)">
<h3>removeBuildListener</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">removeBuildListener</span><wbr><span class="parameters">(<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>&nbsp;listener)</span></div>
<div class="block">Remove a build listener from the list. This listener
 will no longer be notified of build events for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>listener</code> - The listener to remove from the list.
                 Should not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getBuildListeners()">
<h3>getBuildListeners</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a>&gt;</span>&nbsp;<span class="element-name">getBuildListeners</span>()</div>
<div class="block">Return a copy of the list of build listeners for the project.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a list of build listeners for the project</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
<div class="block">Write a message to the log with the default log level
 of MSG_INFO .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,int)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;msgLevel)</span></div>
<div class="block">Write a project level message to the log with the given log level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The log priority level to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,java.lang.Throwable,int)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;msgLevel)</span></div>
<div class="block">Write a project level message to the log with the given log level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
<dd><code>throwable</code> - The exception causing this log, may be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The log priority level to use.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(org.apache.tools.ant.Task,java.lang.String,int)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;msgLevel)</span></div>
<div class="block">Write a task level message to the log with the given log level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - The task to use in the log. Must not be <code>null</code>.</dd>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The log priority level to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(org.apache.tools.ant.Task,java.lang.String,java.lang.Throwable,int)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;msgLevel)</span></div>
<div class="block">Write a task level message to the log with the given log level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - The task to use in the log. Must not be <code>null</code>.</dd>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
<dd><code>throwable</code> - The exception causing this log, may be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The log priority level to use.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(org.apache.tools.ant.Target,java.lang.String,int)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;msgLevel)</span></div>
<div class="block">Write a target level message to the log with the given log level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target to use in the log.
               Must not be <code>null</code>.</dd>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The log priority level to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="log(org.apache.tools.ant.Target,java.lang.String,java.lang.Throwable,int)">
<h3>log</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;msgLevel)</span></div>
<div class="block">Write a target level message to the log with the given log level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target to use in the log.
               Must not be <code>null</code>.</dd>
<dd><code>message</code> - The text to log. Should not be <code>null</code>.</dd>
<dd><code>throwable</code> - The exception causing this log, may be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The log priority level to use.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getGlobalFilterSet()">
<h3>getGlobalFilterSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="types/FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</a></span>&nbsp;<span class="element-name">getGlobalFilterSet</span>()</div>
<div class="block">Return the set of global filters.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the set of global filters.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setProperty(java.lang.String,java.lang.String)">
<h3>setProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set a property. Any existing property of the same name
 is overwritten, unless it is a user property.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNewProperty(java.lang.String,java.lang.String)">
<h3>setNewProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set a property if no value currently exists. If the property
 exists already, a message is logged and the method returns with
 no other effect.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUserProperty(java.lang.String,java.lang.String)">
<h3>setUserProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set a user property, which cannot be overwritten by
 set/unset property calls. Any previous value is overwritten.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setProperty(java.lang.String,java.lang.String)"><code>setProperty(String,String)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setInheritedProperty(java.lang.String,java.lang.String)">
<h3>setInheritedProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInheritedProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set a user property, which cannot be overwritten by set/unset
 property calls. Any previous value is overwritten. Also marks
 these properties as properties that have not come from the
 command line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setProperty(java.lang.String,java.lang.String)"><code>setProperty(String,String)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getProperty(java.lang.String)">
<h3>getProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</span></div>
<div class="block">Return the value of a property, if it is set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propertyName</code> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="replaceProperties(java.lang.String)">
<h3>replaceProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">replaceProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span>
                         throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Replace ${} style constructions in the given value with the
 string value of the corresponding data types.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The string to be scanned for property references.
              May be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the given string with embedded property names replaced
         by values, or <code>null</code> if the given string is
         <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the given value has an unclosed
                           property name, e.g. <code>${xxx</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUserProperty(java.lang.String)">
<h3>getUserProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getUserProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;propertyName)</span></div>
<div class="block">Return the value of a user property, if it is set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>propertyName</code> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getProperties()">
<h3>getProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getProperties</span>()</div>
<div class="block">Return a copy of the properties table.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable containing all properties (including user
         properties) known to the project directly, does not
         contain local properties.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getPropertyNames()">
<h3>getPropertyNames</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getPropertyNames</span>()</div>
<div class="block">Returns the names of all known properties.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of all known properties including local user and local properties.</dd>
<dt>Since:</dt>
<dd>1.10.9</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUserProperties()">
<h3>getUserProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getUserProperties</span>()</div>
<div class="block">Return a copy of the user property hashtable.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable containing just the user properties.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getInheritedProperties()">
<h3>getInheritedProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getInheritedProperties</span>()</div>
<div class="block">Return a copy of the inherited property hashtable.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable containing just the inherited properties.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyUserProperties(org.apache.tools.ant.Project)">
<h3>copyUserProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyUserProperties</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</span></div>
<div class="block">Copy all user properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.

 <p>To copy all &quot;user&quot; properties, you will also have to call
 <a href="#copyInheritedProperties(org.apache.tools.ant.Project)"><code>copyInheritedProperties</code></a>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the project to copy the properties to.  Must not be null.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyInheritedProperties(org.apache.tools.ant.Project)">
<h3>copyInheritedProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyInheritedProperties</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</span></div>
<div class="block">Copy all user properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.

 <p>To copy all &quot;user&quot; properties, you will also have to call
 <a href="#copyUserProperties(org.apache.tools.ant.Project)"><code>copyUserProperties</code></a>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the project to copy the properties to.  Must not be null.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDefaultTarget(java.lang.String)">
<h3>setDefaultTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTarget)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use setDefault.</div>
</div>
<div class="block">Set the default target of the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>defaultTarget</code> - The name of the default target for this project.
                      May be <code>null</code>, indicating that there is
                      no default target.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setDefault(java.lang.String)"><code>setDefault(String)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultTarget()">
<h3>getDefaultTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultTarget</span>()</div>
<div class="block">Return the name of the default target of the project.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>name of the default target or
         <code>null</code> if no default has been set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDefault(java.lang.String)">
<h3>setDefault</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefault</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTarget)</span></div>
<div class="block">Set the default target of the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>defaultTarget</code> - The name of the default target for this project.
                      May be <code>null</code>, indicating that there is
                      no default target.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setName(java.lang.String)">
<h3>setName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set the name of the project, also setting the user
 property <code>ant.project.name</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the project.
             Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<div class="block">Return the project name, if one has been set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the project name, or <code>null</code> if it hasn't been set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDescription(java.lang.String)">
<h3>setDescription</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDescription</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</span></div>
<div class="block">Set the project description.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>description</code> - The description of the project.
                    May be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDescription()">
<h3>getDescription</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDescription</span>()</div>
<div class="block">Return the project description, if one has been set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the project description, or <code>null</code> if it hasn't
         been set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addFilter(java.lang.String,java.lang.String)">
<h3>addFilter</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFilter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x.
             Use getGlobalFilterSet().addFilter(token,value)</div>
</div>
<div class="block">Add a filter to the set of global filters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>token</code> - The token to filter.
              Must not be <code>null</code>.</dd>
<dd><code>value</code> - The replacement value.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#getGlobalFilterSet()"><code>getGlobalFilterSet()</code></a></li>
<li><a href="types/FilterSet.html#addFilter(java.lang.String,java.lang.String)"><code>FilterSet.addFilter(String,String)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFilters()">
<h3>getFilters</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getFilters</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x
             Use getGlobalFilterSet().getFilterHash().</div>
</div>
<div class="block">Return a hashtable of global filters, mapping tokens to values.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable of global filters, mapping tokens to values
         (String to String).</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getGlobalFilterSet()"><code>getGlobalFilterSet()</code></a></li>
<li><a href="types/FilterSet.html#getFilterHash()"><code>FilterSet.getFilterHash()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.lang.String)">
<h3>setBasedir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseD)</span>
                throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set the base directory for the project, checking that
 the given filename exists and is a directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseD</code> - The project base directory.
              Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the directory if invalid.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBaseDir(java.io.File)">
<h3>setBaseDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBaseDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</span>
                throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set the base directory for the project, checking that
 the given file exists and is a directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - The project base directory.
                Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the specified file doesn't exist or
                           isn't a directory.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getBaseDir()">
<h3>getBaseDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBaseDir</span>()</div>
<div class="block">Return the base directory of the project as a file object.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the project base directory, or <code>null</code> if the
         base directory has not been successfully set to a valid value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setKeepGoingMode(boolean)">
<h3>setKeepGoingMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepGoingMode</span><wbr><span class="parameters">(boolean&nbsp;keepGoingMode)</span></div>
<div class="block">Set &quot;keep-going&quot; mode. In this mode Ant will try to execute
 as many targets as possible. All targets that do not depend
 on failed target(s) will be executed.  If the keepGoing setter/getter
 methods are used in conjunction with the <code>ant.executor.class</code>
 property, they will have no effect.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>keepGoingMode</code> - &quot;keep-going&quot; mode</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isKeepGoingMode()">
<h3>isKeepGoingMode</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isKeepGoingMode</span>()</div>
<div class="block">Return the keep-going mode.  If the keepGoing setter/getter
 methods are used in conjunction with the <code>ant.executor.class</code>
 property, they will have no effect.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>&quot;keep-going&quot; mode</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getJavaVersion()">
<h3>getJavaVersion</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getJavaVersion</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use org.apache.tools.ant.util.JavaEnvUtils instead.</div>
</div>
<div class="block">Return the version of Java this class is running under.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the version of Java as a String, e.g. "1.1" .</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="util/JavaEnvUtils.html#getJavaVersion()"><code>JavaEnvUtils.getJavaVersion()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setJavaVersionProperty()">
<h3>setJavaVersionProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavaVersionProperty</span>()
                            throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set the <code>ant.java.version</code> property and tests for
 unsupported JVM versions. If the version is supported,
 verbose log messages are generated to record the Java version
 and operating system name.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if this Java version is not supported.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="util/JavaEnvUtils.html#getJavaVersion()"><code>JavaEnvUtils.getJavaVersion()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSystemProperties()">
<h3>setSystemProperties</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSystemProperties</span>()</div>
<div class="block">Add all system properties which aren't already defined as
 user properties to the project properties.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="addTaskDefinition(java.lang.String,java.lang.Class)">
<h3>addTaskDefinition</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTaskDefinition</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;taskClass)</span>
                       throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Add a new task definition to the project.
 Attempting to override an existing definition with an
 equivalent one (i.e. with the same classname) results in
 a verbose log message. Attempting to override an existing definition
 with a different one results in a warning log message and
 invalidates any tasks which have already been created with the
 old definition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>taskName</code> - The name of the task to add.
                 Must not be <code>null</code>.</dd>
<dd><code>taskClass</code> - The full name of the class implementing the task.
                  Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the class is unsuitable for being an Ant
                           task. An error level message is logged before
                           this exception is thrown.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#checkTaskClass(java.lang.Class)"><code>checkTaskClass(Class)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="checkTaskClass(java.lang.Class)">
<h3>checkTaskClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkTaskClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;taskClass)</span>
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Check whether or not a class is suitable for serving as Ant task.
 Ant task implementation classes must be public, concrete, and have
 a no-arg constructor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>taskClass</code> - The class to be checked.
                  Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the class is unsuitable for being an Ant
                           task. An error level message is logged before
                           this exception is thrown.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTaskDefinitions()">
<h3>getTaskDefinitions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</span>&nbsp;<span class="element-name">getTaskDefinitions</span>()</div>
<div class="block">Return the current task definition hashtable. The returned hashtable is
 &quot;live&quot; and so should not be modified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map of from task name to implementing class
         (String to Class).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCopyOfTaskDefinitions()">
<h3>getCopyOfTaskDefinitions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</span>&nbsp;<span class="element-name">getCopyOfTaskDefinitions</span>()</div>
<div class="block">Return the current task definition map. The returned map is a
 copy of the &quot;live&quot; definitions.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map of from task name to implementing class
         (String to Class).</dd>
<dt>Since:</dt>
<dd>Ant 1.8.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDataTypeDefinition(java.lang.String,java.lang.Class)">
<h3>addDataTypeDefinition</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDataTypeDefinition</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;typeName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;typeClass)</span></div>
<div class="block">Add a new datatype definition.
 Attempting to override an existing definition with an
 equivalent one (i.e. with the same classname) results in
 a verbose log message. Attempting to override an existing definition
 with a different one results in a warning log message, but the
 definition is changed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>typeName</code> - The name of the datatype.
                 Must not be <code>null</code>.</dd>
<dd><code>typeClass</code> - The full name of the class implementing the datatype.
                  Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDataTypeDefinitions()">
<h3>getDataTypeDefinitions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</span>&nbsp;<span class="element-name">getDataTypeDefinitions</span>()</div>
<div class="block">Return the current datatype definition hashtable. The returned
 hashtable is &quot;live&quot; and so should not be modified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map of from datatype name to implementing class
         (String to Class).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCopyOfDataTypeDefinitions()">
<h3>getCopyOfDataTypeDefinitions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</span>&nbsp;<span class="element-name">getCopyOfDataTypeDefinitions</span>()</div>
<div class="block">Return the current datatype definition map. The returned
 map is a copy pf the &quot;live&quot; definitions.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map of from datatype name to implementing class
         (String to Class).</dd>
<dt>Since:</dt>
<dd>Ant 1.8.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addTarget(org.apache.tools.ant.Target)">
<h3>addTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTarget</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span>
               throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Add a <em>new</em> target to the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target to be added to the project.
               Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the target already exists in the project</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#addOrReplaceTarget(org.apache.tools.ant.Target)"><code>addOrReplaceTarget(Target)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addTarget(java.lang.String,org.apache.tools.ant.Target)">
<h3>addTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetName,
 <a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span>
               throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Add a <em>new</em> target to the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetName</code> - The name to use for the target.
             Must not be <code>null</code>.</dd>
<dd><code>target</code> - The target to be added to the project.
               Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the target already exists in the project.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#addOrReplaceTarget(java.lang.String,org.apache.tools.ant.Target)"><code>addOrReplaceTarget(String, Target)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOrReplaceTarget(org.apache.tools.ant.Target)">
<h3>addOrReplaceTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOrReplaceTarget</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">Add a target to the project, or replaces one with the same
 name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target to be added or replaced in the project.
               Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOrReplaceTarget(java.lang.String,org.apache.tools.ant.Target)">
<h3>addOrReplaceTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOrReplaceTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetName,
 <a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">Add a target to the project, or replaces one with the same
 name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetName</code> - The name to use for the target.
                   Must not be <code>null</code>.</dd>
<dd><code>target</code> - The target to be added or replaced in the project.
               Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTargets()">
<h3>getTargets</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">getTargets</span>()</div>
<div class="block">Return the hashtable of targets. The returned hashtable
 is &quot;live&quot; and so should not be modified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map from name to target (String to Target).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCopyOfTargets()">
<h3>getCopyOfTargets</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">getCopyOfTargets</span>()</div>
<div class="block">Return the map of targets. The returned map
 is a copy of the &quot;live&quot; targets.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map from name to target (String to Target).</dd>
<dt>Since:</dt>
<dd>Ant 1.8.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createTask(java.lang.String)">
<h3>createTask</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Task.html" title="class in org.apache.tools.ant">Task</a></span>&nbsp;<span class="element-name">createTask</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskType)</span>
                throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create a new instance of a task, adding it to a list of
 created tasks for later invalidation. This causes all tasks
 to be remembered until the containing project is removed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>taskType</code> - The name of the task to create an instance of.
                 Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>an instance of the specified task, or <code>null</code> if
         the task name is not recognised.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the task name is recognised but task
                           creation fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createDataType(java.lang.String)">
<h3>createDataType</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">createDataType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;typeName)</span>
                      throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create a new instance of a data type.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>typeName</code> - The name of the data type to create an instance of.
                 Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>an instance of the specified data type, or <code>null</code> if
         the data type name is not recognised.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the data type name is recognised but
                           instance creation fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExecutor(org.apache.tools.ant.Executor)">
<h3>setExecutor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExecutor</span><wbr><span class="parameters">(<a href="Executor.html" title="interface in org.apache.tools.ant">Executor</a>&nbsp;e)</span></div>
<div class="block">Set the Executor instance for this Project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>e</code> - the Executor to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExecutor()">
<h3>getExecutor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Executor.html" title="interface in org.apache.tools.ant">Executor</a></span>&nbsp;<span class="element-name">getExecutor</span>()</div>
<div class="block">Get this Project's Executor (setting it if necessary).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an Executor instance.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="executeTargets(java.util.Vector)">
<h3>executeTargets</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">executeTargets</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;names)</span>
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Execute the specified sequence of targets, and the targets
 they depend on.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>names</code> - A vector of target name strings to execute.
              Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the build failed.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="demuxOutput(java.lang.String,boolean)">
<h3>demuxOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">demuxOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output,
 boolean&nbsp;isWarning)</span></div>
<div class="block">Demultiplex output so that each task receives the appropriate
 messages. If the current thread is not currently executing a task,
 the message is logged directly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - Message to handle. Should not be <code>null</code>.</dd>
<dd><code>isWarning</code> - Whether the text represents an warning (<code>true</code>)
        or information (<code>false</code>).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="defaultInput(byte[],int,int)">
<h3>defaultInput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">defaultInput</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Read data from the default input stream. If no default has been
 specified, System.in is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - the buffer into which data is to be read.</dd>
<dd><code>offset</code> - the offset into the buffer at which data is stored.</dd>
<dd><code>length</code> - the amount of data to read.</dd>
<dt>Returns:</dt>
<dd>the number of bytes read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the data cannot be read.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="demuxInput(byte[],int,int)">
<h3>demuxInput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">demuxInput</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Demux an input request to the correct task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - the buffer into which data is to be read.</dd>
<dd><code>offset</code> - the offset into the buffer at which data is stored.</dd>
<dd><code>length</code> - the amount of data to read.</dd>
<dt>Returns:</dt>
<dd>the number of bytes read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the data cannot be read.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="demuxFlush(java.lang.String,boolean)">
<h3>demuxFlush</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">demuxFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output,
 boolean&nbsp;isError)</span></div>
<div class="block">Demultiplex flush operations so that each task receives the appropriate
 messages. If the current thread is not currently executing a task,
 the message is logged directly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - Message to handle. Should not be <code>null</code>.</dd>
<dd><code>isError</code> - Whether the text represents an error (<code>true</code>)
        or information (<code>false</code>).</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="executeTarget(java.lang.String)">
<h3>executeTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">executeTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetName)</span>
                   throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Execute the specified target and any targets it depends on.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetName</code> - The name of the target to execute.
                   Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the build failed.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="executeSortedTargets(java.util.Vector)">
<h3>executeSortedTargets</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">executeSortedTargets</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;sortedTargets)</span>
                          throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Execute a <code>Vector</code> of sorted targets.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sortedTargets</code> - the aforementioned <code>Vector</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="resolveFile(java.lang.String,java.io.File)">
<h3>resolveFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">resolveFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;rootDir)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Return the canonical form of a filename.
 <p>
 If the specified file name is relative it is resolved
 with respect to the given root directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileName</code> - The name of the file to resolve.
                 Must not be <code>null</code>.</dd>
<dd><code>rootDir</code> - The directory respective to which relative file names
                 are resolved. May be <code>null</code>, in which case
                 the current directory is used.</dd>
<dt>Returns:</dt>
<dd>the resolved File.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="resolveFile(java.lang.String)">
<h3>resolveFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">resolveFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName)</span></div>
<div class="block">Return the canonical form of a filename.
 <p>
 If the specified file name is relative it is resolved
 with respect to the project's base directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileName</code> - The name of the file to resolve.
                 Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the resolved File.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="translatePath(java.lang.String)">
<h3>translatePath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">translatePath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;toProcess)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7
             Use FileUtils.translatePath instead.</div>
</div>
<div class="block">Translate a path into its native (platform specific) format.
 <p>
 This method uses PathTokenizer to separate the input path
 into its components. This handles DOS style paths in a relatively
 sensible way. The file separators are then converted to their platform
 specific versions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>toProcess</code> - The path to be translated.
                  May be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the native version of the specified path or
         an empty string if the path is <code>null</code> or empty.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="PathTokenizer.html" title="class in org.apache.tools.ant"><code>PathTokenizer</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.lang.String,java.lang.String)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a destination.
 No filtering is performed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - Name of file to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - Name of file to copy to.
                 Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the copying fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.lang.String,java.lang.String,boolean)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile,
 boolean&nbsp;filtering)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a destination
 specifying if token filtering should be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - Name of file to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - Name of file to copy to.
                 Must not be <code>null</code>.</dd>
<dd><code>filtering</code> - Whether or not token filtering should be used during
                  the copy.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the copying fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.lang.String,java.lang.String,boolean,boolean)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used and if
 source files may overwrite newer destination files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - Name of file to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - Name of file to copy to.
                 Must not be <code>null</code>.</dd>
<dd><code>filtering</code> - Whether or not token filtering should be used during
                  the copy.</dd>
<dd><code>overwrite</code> - Whether or not the destination file should be
                  overwritten if it already exists.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the copying fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.lang.String,java.lang.String,boolean,boolean,boolean)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite,
 boolean&nbsp;preserveLastModified)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used, if
 source files may overwrite newer destination files, and if the
 last modified time of the resulting file should be set to
 that of the source file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - Name of file to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - Name of file to copy to.
                 Must not be <code>null</code>.</dd>
<dd><code>filtering</code> - Whether or not token filtering should be used during
                  the copy.</dd>
<dd><code>overwrite</code> - Whether or not the destination file should be
                  overwritten if it already exists.</dd>
<dd><code>preserveLastModified</code> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the copying fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.io.File,java.io.File)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a destination.
 No filtering is performed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - File to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - File to copy to.
                 Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the copying fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.io.File,java.io.File,boolean)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a destination
 specifying if token filtering should be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - File to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - File to copy to.
                 Must not be <code>null</code>.</dd>
<dd><code>filtering</code> - Whether or not token filtering should be used during
                  the copy.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the copying fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.io.File,java.io.File,boolean,boolean)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used and if
 source files may overwrite newer destination files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - File to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - File to copy to.
                 Must not be <code>null</code>.</dd>
<dd><code>filtering</code> - Whether or not token filtering should be used during
                  the copy.</dd>
<dd><code>overwrite</code> - Whether or not the destination file should be
                  overwritten if it already exists.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the file cannot be copied.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="copyFile(java.io.File,java.io.File,boolean,boolean,boolean)">
<h3>copyFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite,
 boolean&nbsp;preserveLastModified)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used, if
 source files may overwrite newer destination files, and if the
 last modified time of the resulting file should be set to
 that of the source file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - File to copy from.
                   Must not be <code>null</code>.</dd>
<dd><code>destFile</code> - File to copy to.
                 Must not be <code>null</code>.</dd>
<dd><code>filtering</code> - Whether or not token filtering should be used during
                  the copy.</dd>
<dd><code>overwrite</code> - Whether or not the destination file should be
                  overwritten if it already exists.</dd>
<dd><code>preserveLastModified</code> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the file cannot be copied.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFileLastModified(java.io.File,long)">
<h3>setFileLastModified</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFileLastModified</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 long&nbsp;time)</span>
                         throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x</div>
</div>
<div class="block">Call File.setLastModified(long time) on Java above 1.1, and logs
 a warning on Java 1.1.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - The file to set the last modified time on.
             Must not be <code>null</code>.</dd>
<dd><code>time</code> - the required modification time.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the last modified time cannot be set
                           despite running on a platform with a version
                           above 1.1.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toBoolean(java.lang.String)">
<h3>toBoolean</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">toBoolean</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</span></div>
<div class="block">Return the boolean equivalent of a string, which is considered
 <code>true</code> if either <code>"on"</code>, <code>"true"</code>,
 or <code>"yes"</code> is found, ignoring case.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - The string to convert to a boolean value.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the given string is <code>"on"</code>,
         <code>"true"</code> or <code>"yes"</code>, or
         <code>false</code> otherwise.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getProject(java.lang.Object)">
<h3>getProject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o)</span></div>
<div class="block">Get the Project instance associated with the specified object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - the object to query.</dd>
<dt>Returns:</dt>
<dd>Project instance, if any.</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="topoSort(java.lang.String,java.util.Hashtable)">
<h3>topoSort</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">topoSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;root,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;targetTable)</span>
                              throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Topologically sort a set of targets.  Equivalent to calling
 <code>topoSort(new String[] {root}, targets, true)</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>root</code> - The name of the root target. The sort is created in such
             a way that the sequence of Targets up to the root
             target is the minimum possible such sequence.
             Must not be <code>null</code>.</dd>
<dd><code>targetTable</code> - A Hashtable mapping names to Targets.
                Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a Vector of ALL Target objects in sorted order.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a cyclic dependency among the
                           targets, or if a named target does not exist.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="topoSort(java.lang.String,java.util.Hashtable,boolean)">
<h3>topoSort</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">topoSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;root,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;targetTable,
 boolean&nbsp;returnAll)</span>
                              throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Topologically sort a set of targets.  Equivalent to calling
 <code>topoSort(new String[] {root}, targets, returnAll)</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>root</code> - The name of the root target. The sort is created in such
             a way that the sequence of Targets up to the root
             target is the minimum possible such sequence.
             Must not be <code>null</code>.</dd>
<dd><code>targetTable</code> - A Hashtable mapping names to Targets.
                Must not be <code>null</code>.</dd>
<dd><code>returnAll</code> - <code>boolean</code> indicating whether to return all
                  targets, or the execution sequence only.</dd>
<dt>Returns:</dt>
<dd>a Vector of Target objects in sorted order.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a cyclic dependency among the
                           targets, or if a named target does not exist.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="topoSort(java.lang.String[],java.util.Hashtable,boolean)">
<h3>topoSort</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">topoSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;roots,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;targetTable,
 boolean&nbsp;returnAll)</span>
                              throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Topologically sort a set of targets.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>roots</code> - <code>String[]</code> containing the names of the root targets.
              The sort is created in such a way that the ordered sequence of
              Targets is the minimum possible such sequence to the specified
              root targets.
              Must not be <code>null</code>.</dd>
<dd><code>targetTable</code> - A map of names to targets (String to Target).
                    Must not be <code>null</code>.</dd>
<dd><code>returnAll</code> - <code>boolean</code> indicating whether to return all
                  targets, or the execution sequence only.</dd>
<dt>Returns:</dt>
<dd>a Vector of Target objects in sorted order.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a cyclic dependency among the
                           targets, or if a named target does not exist.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="inheritIDReferences(org.apache.tools.ant.Project)">
<h3>inheritIDReferences</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">inheritIDReferences</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;parent)</span></div>
<div class="block">Inherit the id references.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - the parent project of this project.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addIdReference(java.lang.String,java.lang.Object)">
<h3>addIdReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIdReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Add an id reference.
 Used for broken build files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - the id to set.</dd>
<dd><code>value</code> - the value to set it to (Unknown element in this case.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addReference(java.lang.String,java.lang.Object)">
<h3>addReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;referenceName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Add a reference to the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>referenceName</code> - The name of the reference. Must not be <code>null</code>.</dd>
<dd><code>value</code> - The value of the reference.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getReferences()">
<h3>getReferences</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getReferences</span>()</div>
<div class="block">Return a map of the references in the project (String to Object).
 The returned hashtable is &quot;live&quot; and so must not be modified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map of the references in the project (String to Object).</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasReference(java.lang.String)">
<h3>hasReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key)</span></div>
<div class="block">Does the project know this reference?</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>key</code> - String</dd>
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCopyOfReferences()">
<h3>getCopyOfReferences</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getCopyOfReferences</span>()</div>
<div class="block">Return a map of the references in the project (String to
 Object).  The returned hashtable is a copy of the
 &quot;live&quot; references.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a map of the references in the project (String to Object).</dd>
<dt>Since:</dt>
<dd>Ant 1.8.1</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getReference(java.lang.String)">
<h3>getReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key)</span></div>
<div class="block">Look up a reference by its key (ID).</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - desired type</dd>
<dt>Parameters:</dt>
<dd><code>key</code> - The key for the desired reference.
            Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the reference with the specified ID, or <code>null</code> if
         there is no such reference in the project, with type inference.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getElementName(java.lang.Object)">
<h3>getElementName</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getElementName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element)</span></div>
<div class="block">Return a description of the type of the given element, with
 special handling for instances of tasks and data types.
 <p>
 This is useful for logging purposes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>element</code> - The element to describe.
                Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a description of the element type.</dd>
<dt>Since:</dt>
<dd>1.95, Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireBuildStarted()">
<h3>fireBuildStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireBuildStarted</span>()</div>
<div class="block">Send a &quot;build started&quot; event
 to the build listeners for this project.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="fireBuildFinished(java.lang.Throwable)">
<h3>fireBuildFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireBuildFinished</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</span></div>
<div class="block">Send a &quot;build finished&quot; event to the build listeners
 for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exception</code> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireSubBuildStarted()">
<h3>fireSubBuildStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireSubBuildStarted</span>()</div>
<div class="block">Send a &quot;subbuild started&quot; event to the build listeners for
 this project.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireSubBuildFinished(java.lang.Throwable)">
<h3>fireSubBuildFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireSubBuildFinished</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</span></div>
<div class="block">Send a &quot;subbuild finished&quot; event to the build listeners for
 this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exception</code> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireTargetStarted(org.apache.tools.ant.Target)">
<h3>fireTargetStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireTargetStarted</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">Send a &quot;target started&quot; event to the build listeners
 for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target which is starting to build.
               Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireTargetFinished(org.apache.tools.ant.Target,java.lang.Throwable)">
<h3>fireTargetFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireTargetFinished</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</span></div>
<div class="block">Send a &quot;target finished&quot; event to the build listeners
 for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target which has finished building.
                  Must not be <code>null</code>.</dd>
<dd><code>exception</code> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireTaskStarted(org.apache.tools.ant.Task)">
<h3>fireTaskStarted</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireTaskStarted</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</span></div>
<div class="block">Send a &quot;task started&quot; event to the build listeners
 for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - The target which is starting to execute.
               Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireTaskFinished(org.apache.tools.ant.Task,java.lang.Throwable)">
<h3>fireTaskFinished</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireTaskFinished</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</span></div>
<div class="block">Send a &quot;task finished&quot; event to the build listeners for this
 project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - The task which has finished executing.
                  Must not be <code>null</code>.</dd>
<dd><code>exception</code> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireMessageLogged(org.apache.tools.ant.Project,java.lang.String,int)">
<h3>fireMessageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireMessageLogged</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</span></div>
<div class="block">Send a &quot;message logged&quot; project level event
 to the build listeners for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project generating the event.
                 Should not be <code>null</code>.</dd>
<dd><code>message</code> - The message to send. Should not be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireMessageLogged(org.apache.tools.ant.Project,java.lang.String,java.lang.Throwable,int)">
<h3>fireMessageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireMessageLogged</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;priority)</span></div>
<div class="block">Send a &quot;message logged&quot; project level event
 to the build listeners for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project generating the event.
                 Should not be <code>null</code>.</dd>
<dd><code>message</code> - The message to send. Should not be <code>null</code>.</dd>
<dd><code>throwable</code> - The exception that caused this message. May be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireMessageLogged(org.apache.tools.ant.Target,java.lang.String,int)">
<h3>fireMessageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireMessageLogged</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</span></div>
<div class="block">Send a &quot;message logged&quot; target level event
 to the build listeners for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target generating the event.
                 Must not be <code>null</code>.</dd>
<dd><code>message</code> - The message to send. Should not be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireMessageLogged(org.apache.tools.ant.Target,java.lang.String,java.lang.Throwable,int)">
<h3>fireMessageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireMessageLogged</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;priority)</span></div>
<div class="block">Send a &quot;message logged&quot; target level event
 to the build listeners for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target generating the event.
                 Must not be <code>null</code>.</dd>
<dd><code>message</code> - The message to send. Should not be <code>null</code>.</dd>
<dd><code>throwable</code> - The exception that caused this message. May be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireMessageLogged(org.apache.tools.ant.Task,java.lang.String,int)">
<h3>fireMessageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireMessageLogged</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</span></div>
<div class="block">Send a &quot;message logged&quot; task level event
 to the build listeners for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - The task generating the event.
                 Must not be <code>null</code>.</dd>
<dd><code>message</code> - The message to send. Should not be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fireMessageLogged(org.apache.tools.ant.Task,java.lang.String,java.lang.Throwable,int)">
<h3>fireMessageLogged</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fireMessageLogged</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;throwable,
 int&nbsp;priority)</span></div>
<div class="block">Send a &quot;message logged&quot; task level event
 to the build listeners for this project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - The task generating the event.
                 Must not be <code>null</code>.</dd>
<dd><code>message</code> - The message to send. Should not be <code>null</code>.</dd>
<dd><code>throwable</code> - The exception that caused this message. May be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="registerThreadTask(java.lang.Thread,org.apache.tools.ant.Task)">
<h3>registerThreadTask</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registerThreadTask</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a>&nbsp;thread,
 <a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</span></div>
<div class="block">Register a task as the current task for a thread.
 If the task is null, the thread's entry is removed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>thread</code> - the thread on which the task is registered.</dd>
<dd><code>task</code> - the task to be registered.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getThreadTask(java.lang.Thread)">
<h3>getThreadTask</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Task.html" title="class in org.apache.tools.ant">Task</a></span>&nbsp;<span class="element-name">getThreadTask</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a>&nbsp;thread)</span></div>
<div class="block">Get the current task associated with a thread, if any.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>thread</code> - the thread for which the task is required.</dd>
<dt>Returns:</dt>
<dd>the task which is currently registered for the given thread or
         null if no task is registered.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setProjectReference(java.lang.Object)">
<h3>setProjectReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProjectReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
<div class="block">Set a reference to this Project on the parameterized object.
 Need to set the project before other set/add elements
 are called.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>obj</code> - the object to invoke setProject(this) on.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getResource(java.lang.String)">
<h3>getResource</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a></span>&nbsp;<span class="element-name">getResource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Resolve the file relative to the project's basedir and return it as a
 FileResource.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="types/ResourceFactory.html#getResource(java.lang.String)">getResource</a></code>&nbsp;in interface&nbsp;<code><a href="types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the file to resolve.</dd>
<dt>Returns:</dt>
<dd>the file resource.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
