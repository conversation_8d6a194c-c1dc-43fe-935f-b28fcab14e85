<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>FTPTaskMirrorImpl (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.net, class: FTPTaskMirrorImpl">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.net</a></div>
<h1 title="Class FTPTaskMirrorImpl" class="title">Class FTPTaskMirrorImpl</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.net.FTPTaskMirrorImpl</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="FTPTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FTPTaskMirrorImpl</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="FTPTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a></span></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FTPTaskMirrorImpl.FTPDirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner</a></code></div>
<div class="col-last even-row-color">
<div class="block">internal class allowing to read the contents of a remote file system
 using the FTP protocol
 used in particular for ftp get operations
 differences with DirectoryScanner
 "" (the root of the fileset) is never included in the included directories
 followSymlinks defaults to false</div>
</div>
<div class="col-first odd-row-color"><code>protected static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="FTPTaskMirrorImpl.FTPFileProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPFileProxy</a></code></div>
<div class="col-last odd-row-color">
<div class="block">internal class providing a File-like interface to some of the information
 available from the FTP server</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.net.FTPTask)" class="member-name-link">FTPTaskMirrorImpl</a><wbr>(<a href="FTPTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask</a>&nbsp;task)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createParents(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">createParents</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates all parent directories specified in a complete relative
 pathname.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delFile(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">delFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete a file from the remote host.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#doFTP()" class="member-name-link">doFTP</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#doSiteCommand(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">doSiteCommand</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theCMD)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sends a site command to the ftp server</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeRetryable(org.apache.tools.ant.util.RetryHandler,org.apache.tools.ant.util.Retryable,java.lang.String)" class="member-name-link">executeRetryable</a><wbr>(<a href="../../../util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</a>&nbsp;h,
 <a href="../../../util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</a>&nbsp;r,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executable a retryable object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)" class="member-name-link">getFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieve a single file from the remote host.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isUpToDate(org.apache.commons.net.ftp.FTPClient,java.io.File,java.lang.String)" class="member-name-link">isUpToDate</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;localFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;remoteFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks to see if the remote file is current as compared with the local
 file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#listFile(org.apache.commons.net.ftp.FTPClient,java.io.BufferedWriter,java.lang.String)" class="member-name-link">listFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/BufferedWriter.html" title="class or interface in java.io" class="external-link">BufferedWriter</a>&nbsp;bw,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">List information about a single file from the remote host.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeRemoteDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">makeRemoteDir</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create the specified directory on the remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveFile(java.lang.String)" class="member-name-link">resolveFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Correct a file path to correspond to the remote host requirements.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#rmDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">rmDir</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dirname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete a directory, if empty, from the remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sendFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)" class="member-name-link">sendFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sends a single file to the remote host.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#transferFiles(org.apache.commons.net.ftp.FTPClient)" class="member-name-link">transferFiles</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sends all files specified by the configured filesets to the remote
 server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#transferFiles(org.apache.commons.net.ftp.FTPClient,org.apache.tools.ant.types.FileSet)" class="member-name-link">transferFiles</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">For each file in the fileset, do the appropriate action: send, get,
 delete, or list.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.net.FTPTask)">
<h3>FTPTaskMirrorImpl</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FTPTaskMirrorImpl</span><wbr><span class="parameters">(<a href="FTPTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask</a>&nbsp;task)</span></div>
<div class="block">Constructor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - the FTPTask that uses this mirror.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="executeRetryable(org.apache.tools.ant.util.RetryHandler,org.apache.tools.ant.util.Retryable,java.lang.String)">
<h3>executeRetryable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">executeRetryable</span><wbr><span class="parameters">(<a href="../../../util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</a>&nbsp;h,
 <a href="../../../util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</a>&nbsp;r,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descr)</span>
                         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Executable a retryable object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>h</code> - the retry handler.</dd>
<dd><code>r</code> - the object that should be retried until it succeeds
          or the number of retries is reached.</dd>
<dd><code>descr</code> - a description of the command that is being run.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="transferFiles(org.apache.commons.net.ftp.FTPClient,org.apache.tools.ant.types.FileSet)">
<h3>transferFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">transferFiles</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</span>
                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">For each file in the fileset, do the appropriate action: send, get,
 delete, or list.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the FTPClient instance used to perform FTP actions</dd>
<dd><code>fs</code> - the fileset on which the actions are performed.</dd>
<dt>Returns:</dt>
<dd>the number of files to be transferred.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem reading a file</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem in the configuration.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="transferFiles(org.apache.commons.net.ftp.FTPClient)">
<h3>transferFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">transferFiles</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sends all files specified by the configured filesets to the remote
 server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the FTPClient instance used to perform FTP actions</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem reading a file</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem in the configuration.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="resolveFile(java.lang.String)">
<h3>resolveFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">resolveFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file)</span></div>
<div class="block">Correct a file path to correspond to the remote host requirements. This
 implementation currently assumes that the remote end can handle
 Unix-style paths with forward-slash separators. This can be overridden
 with the <code>separator</code> task parameter. No attempt is made to
 determine what syntax is appropriate for the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the remote file name to be resolved</dd>
<dt>Returns:</dt>
<dd>the filename as it will appear on the server.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createParents(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>createParents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">createParents</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Creates all parent directories specified in a complete relative
 pathname. Attempts to create existing directories will not cause
 errors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the FTP client instance to use to execute FTP actions on
        the remote server.</dd>
<dd><code>filename</code> - the name of the file whose parents should be created.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - under non documented circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if it is impossible to cd to a remote directory</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isUpToDate(org.apache.commons.net.ftp.FTPClient,java.io.File,java.lang.String)">
<h3>isUpToDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isUpToDate</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;localFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;remoteFile)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Checks to see if the remote file is current as compared with the local
 file. Returns true if the target file is up to date.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftpclient</dd>
<dd><code>localFile</code> - local file</dd>
<dd><code>remoteFile</code> - remote file</dd>
<dt>Returns:</dt>
<dd>true if the target file is up to date</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the date of the remote files cannot be found and the action is
 GET_FILES</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="doSiteCommand(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>doSiteCommand</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">doSiteCommand</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theCMD)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sends a site command to the ftp server</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>theCMD</code> - command to execute</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in unknown circumstances</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="sendFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)">
<h3>sendFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sendFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sends a single file to the remote host. <code>filename</code> may
 contain a relative path specification. When this is the case, <code>sendFile</code>
 will attempt to create any necessary parent directories before sending
 the file. The file will then be sent using the entire relative path
 spec - no attempt is made to change directories. It is anticipated that
 this may eventually cause problems with some FTP servers, but it
 simplifies the coding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>dir</code> - base directory of the file to be sent (local)</dd>
<dd><code>filename</code> - relative path of the file to be send
        locally relative to dir
        remotely relative to the remotedir attribute</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in unknown circumstances</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="delFile(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>delFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">delFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Delete a file from the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>filename</code> - file to delete</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if skipFailedTransfers is set to false
 and the deletion could not be done</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="rmDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>rmDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">rmDir</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dirname)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Delete a directory, if empty, from the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>dirname</code> - directory to delete</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if skipFailedTransfers is set to false
 and the deletion could not be done</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)">
<h3>getFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Retrieve a single file from the remote host. <code>filename</code> may
 contain a relative path specification. <p>

 The file will then be retrieved using the entire relative path spec -
 no attempt is made to change directories. It is anticipated that this
 may eventually cause problems with some FTP servers, but it simplifies
 the coding.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the ftp client</dd>
<dd><code>dir</code> - local base directory to which the file should go back</dd>
<dd><code>filename</code> - relative path of the file based upon the ftp remote directory
        and/or the local base directory (dir)</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if skipFailedTransfers is false
 and the file cannot be retrieved.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="listFile(org.apache.commons.net.ftp.FTPClient,java.io.BufferedWriter,java.lang.String)">
<h3>listFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">listFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/BufferedWriter.html" title="class or interface in java.io" class="external-link">BufferedWriter</a>&nbsp;bw,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">List information about a single file from the remote host. <code>filename</code>
 may contain a relative path specification. <p>

 The file listing will then be retrieved using the entire relative path
 spec - no attempt is made to change directories. It is anticipated that
 this may eventually cause problems with some FTP servers, but it
 simplifies the coding.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>bw</code> - buffered writer</dd>
<dd><code>filename</code> - the directory one wants to list</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in unknown circumstances</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="makeRemoteDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>makeRemoteDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">makeRemoteDir</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create the specified directory on the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - The FTP client connection</dd>
<dd><code>dir</code> - The directory to create (format must be correct for host
      type)</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if ignoreNoncriticalErrors has not been set to true
         and a directory could not be created, for instance because it was
         already existing. Precisely, the codes 521, 550 and 553 will trigger
         a BuildException</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="doFTP()">
<h3>doFTP</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">doFTP</span>()
           throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FTPTaskMirror.html#doFTP()">doFTP</a></code>&nbsp;in interface&nbsp;<code><a href="FTPTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
