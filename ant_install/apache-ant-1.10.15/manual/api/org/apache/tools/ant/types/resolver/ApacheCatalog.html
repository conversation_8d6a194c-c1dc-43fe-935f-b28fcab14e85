<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ApacheCatalog (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resolver, class: ApacheCatalog">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.resolver</a></div>
<h1 title="Class ApacheCatalog" class="title">Class ApacheCatalog</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.xml.resolver.Catalog
<div class="inheritance">org.apache.tools.ant.types.resolver.ApacheCatalog</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ApacheCatalog</span>
<span class="extends-implements">extends org.apache.xml.resolver.Catalog</span></div>
<div class="block">This class extends the Catalog class provided by Norman Walsh's
 resolver library in xml-commons in order to add classpath entity
 and URI resolution.  Since XMLCatalog already does classpath
 resolution, we simply add all CatalogEntry instances back to the
 controlling XMLCatalog instance.  This is done via a callback
 mechanism.  ApacheCatalog is <em>only</em> used for external
 catalog files.  Inline entries (currently <code>&lt;dtd&gt;</code>
 and <code>&lt;entity&gt;</code>) are not added to ApacheCatalog.
 See XMLCatalog.java for the details of the entity and URI
 resolution algorithms.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../XMLCatalog.html" title="class in org.apache.tools.ant.types"><code>XMLCatalog</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.xml.resolver.Catalog">Fields inherited from class&nbsp;org.apache.xml.resolver.Catalog</h3>
<code>base, BASE, CATALOG, catalogCwd, catalogEntries, catalogFiles, catalogManager, catalogs, default_override, DELEGATE_PUBLIC, DELEGATE_SYSTEM, DELEGATE_URI, DOCTYPE, DOCUMENT, DTDDECL, ENTITY, LINKTYPE, localCatalogFiles, localDelegate, NOTATION, OVERRIDE, PUBLIC, readerArr, readerMap, REWRITE_SYSTEM, REWRITE_URI, SGMLDECL, SYSTEM, SYSTEM_SUFFIX, URI, URI_SUFFIX</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ApacheCatalog</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addEntry(org.apache.xml.resolver.CatalogEntry)" class="member-name-link">addEntry</a><wbr>(org.apache.xml.resolver.CatalogEntry&nbsp;entry)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method overrides the superclass method of the same name
 in order to add catalog entries back to the controlling
 XMLCatalog instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected org.apache.xml.resolver.Catalog</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newCatalog()" class="member-name-link">newCatalog</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new ApacheCatalog instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setResolver(org.apache.tools.ant.types.resolver.ApacheCatalogResolver)" class="member-name-link">setResolver</a><wbr>(<a href="ApacheCatalogResolver.html" title="class in org.apache.tools.ant.types.resolver">ApacheCatalogResolver</a>&nbsp;resolver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the resolver object to callback.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.xml.resolver.Catalog">Methods inherited from class&nbsp;org.apache.xml.resolver.Catalog</h3>
<code>addDelegate, addReader, copyReaders, encodedByte, fixSlashes, getCatalogManager, getCurrentBase, getDefaultOverride, loadSystemCatalogs, makeAbsolute, normalizeURI, parseAllCatalogs, parseCatalog, parseCatalog, parseCatalog, parseCatalogFile, parsePendingCatalogs, resolveDoctype, resolveDocument, resolveEntity, resolveLocalPublic, resolveLocalSystem, resolveLocalURI, resolveNotation, resolvePublic, resolveSubordinateCatalogs, resolveSystem, resolveURI, setCatalogManager, setupReaders, unknownEntry</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ApacheCatalog</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ApacheCatalog</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="newCatalog()">
<h3>newCatalog</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">org.apache.xml.resolver.Catalog</span>&nbsp;<span class="element-name">newCatalog</span>()</div>
<div class="block"><p>Create a new ApacheCatalog instance.</p>

 <p>This method overrides the superclass method of the same name
  in order to set the resolver object for callbacks.  The reason
  we have to do this is that internally Catalog creates a new
  instance of itself for each external catalog file processed.
  That is, if two external catalog files are processed, there
  will be a total of two ApacheCatalog instances, and so on.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code>newCatalog</code>&nbsp;in class&nbsp;<code>org.apache.xml.resolver.Catalog</code></dd>
<dt>Returns:</dt>
<dd>the catalog.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setResolver(org.apache.tools.ant.types.resolver.ApacheCatalogResolver)">
<h3>setResolver</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setResolver</span><wbr><span class="parameters">(<a href="ApacheCatalogResolver.html" title="class in org.apache.tools.ant.types.resolver">ApacheCatalogResolver</a>&nbsp;resolver)</span></div>
<div class="block">Set the resolver object to callback.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>resolver</code> - the apache catalog resolver.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addEntry(org.apache.xml.resolver.CatalogEntry)">
<h3>addEntry</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addEntry</span><wbr><span class="parameters">(org.apache.xml.resolver.CatalogEntry&nbsp;entry)</span></div>
<div class="block"><p>This method overrides the superclass method of the same name
 in order to add catalog entries back to the controlling
 XMLCatalog instance.  In this way, we can add classpath lookup
 for these entries.</p>

 <p>When we add an external catalog file, the entries inside it
 get parsed by this method.  Therefore, we override it to add
 each of them back to the controlling XMLCatalog instance.  This
 is done by performing a callback to the ApacheCatalogResolver,
 which in turn calls the XMLCatalog.</p>

 <p>XMLCatalog currently only understands <code>PUBLIC</code>
 and <code>URI</code> entry types, so we ignore the other types.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code>addEntry</code>&nbsp;in class&nbsp;<code>org.apache.xml.resolver.Catalog</code></dd>
<dt>Parameters:</dt>
<dd><code>entry</code> - The CatalogEntry to process.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
