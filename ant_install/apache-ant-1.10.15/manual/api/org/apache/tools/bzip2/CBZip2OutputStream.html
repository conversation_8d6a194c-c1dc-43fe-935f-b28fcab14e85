<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>CBZip2OutputStream (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.bzip2, class: CBZip2OutputStream">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.bzip2</a></div>
<h1 title="Class CBZip2OutputStream" class="title">Class CBZip2OutputStream</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">java.io.OutputStream</a>
<div class="inheritance">org.apache.tools.bzip2.CBZip2OutputStream</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">CBZip2OutputStream</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>
implements <a href="BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</a></span></div>
<div class="block">An output stream that compresses into the BZip2 format (without the file
 header chars) into another stream.

 <p>
 The compression requires large amounts of memory. Thus you should call the
 <a href="#close()"><code>close()</code></a> method as soon as possible, to force
 <code>CBZip2OutputStream</code> to release the allocated memory.
 </p>

 <p>You can shrink the amount of allocated memory and maybe raise
 the compression speed by choosing a lower blocksize, which in turn
 may cause a lower compression ratio. You can avoid unnecessary
 memory allocation by avoiding using a blocksize which is bigger
 than the size of the input.</p>

 <p>You can compute the memory usage for compressing by the
 following formula:</p>

 <pre>
 &lt;code&gt;400k + (9 * blocksize)&lt;/code&gt;.
 </pre>

 <p>To get the memory required for decompression by <a href="CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><code>CBZip2InputStream</code></a> use</p>

 <pre>
 &lt;code&gt;65k + (5 * blocksize)&lt;/code&gt;.
 </pre>

 <table style="border:1px solid black">
 <caption>Memory usage by blocksize</caption>
 <tr>
 <th style="text-align:right">Blocksize</th>
 <th style="text-align:right">Compression<br>memory usage</th>
 <th style="text-align:right">Decompression<br>memory usage</th>
 </tr>
 <tr>
 <td style="text-align:right">100k</td>
 <td style="text-align:right">1300k</td>
 <td style="text-align:right">565k</td>
 </tr>
 <tr>
 <td style="text-align:right">200k</td>
 <td style="text-align:right">2200k</td>
 <td style="text-align:right">1065k</td>
 </tr>
 <tr>
 <td style="text-align:right">300k</td>
 <td style="text-align:right">3100k</td>
 <td style="text-align:right">1565k</td>
 </tr>
 <tr>
 <td style="text-align:right">400k</td>
 <td style="text-align:right">4000k</td>
 <td style="text-align:right">2065k</td>
 </tr>
 <tr>
 <td style="text-align:right">500k</td>
 <td style="text-align:right">4900k</td>
 <td style="text-align:right">2565k</td>
 </tr>
 <tr>
 <td style="text-align:right">600k</td>
 <td style="text-align:right">5800k</td>
 <td style="text-align:right">3065k</td>
 </tr>
 <tr>
 <td style="text-align:right">700k</td>
 <td style="text-align:right">6700k</td>
 <td style="text-align:right">3565k</td>
 </tr>
 <tr>
 <td style="text-align:right">800k</td>
 <td style="text-align:right">7600k</td>
 <td style="text-align:right">4065k</td>
 </tr>
 <tr>
 <td style="text-align:right">900k</td>
 <td style="text-align:right">8500k</td>
 <td style="text-align:right">4565k</td>
 </tr>
 </table>

 <p>
 For decompression <code>CBZip2InputStream</code> allocates less memory if the
 bzipped input is smaller than one block.
 </p>

 <p>
 Instances of this class are not threadsafe.
 </p>

 <p>
 TODO: Update to BZip2 1.0.1
 </p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CLEARMASK" class="member-name-link">CLEARMASK</a></code></div>
<div class="col-last even-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEPTH_THRESH" class="member-name-link">DEPTH_THRESH</a></code></div>
<div class="col-last odd-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#GREATER_ICOST" class="member-name-link">GREATER_ICOST</a></code></div>
<div class="col-last even-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LESSER_ICOST" class="member-name-link">LESSER_ICOST</a></code></div>
<div class="col-last odd-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MAX_BLOCKSIZE" class="member-name-link">MAX_BLOCKSIZE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The maximum supported blocksize <code> == 9</code>.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MIN_BLOCKSIZE" class="member-name-link">MIN_BLOCKSIZE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The minimum supported blocksize <code> == 1</code>.</div>
</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#QSORT_STACK_SIZE" class="member-name-link">QSORT_STACK_SIZE</a></code></div>
<div class="col-last even-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SETMASK" class="member-name-link">SETMASK</a></code></div>
<div class="col-last odd-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#SMALL_THRESH" class="member-name-link">SMALL_THRESH</a></code></div>
<div class="col-last even-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#WORK_FACTOR" class="member-name-link">WORK_FACTOR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">This constant is accessible by subclasses for historical
 purposes.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.bzip2.BZip2Constants">Fields inherited from interface&nbsp;org.apache.tools.bzip2.<a href="BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</a></h3>
<code><a href="BZip2Constants.html#baseBlockSize">baseBlockSize</a>, <a href="BZip2Constants.html#G_SIZE">G_SIZE</a>, <a href="BZip2Constants.html#MAX_ALPHA_SIZE">MAX_ALPHA_SIZE</a>, <a href="BZip2Constants.html#MAX_CODE_LEN">MAX_CODE_LEN</a>, <a href="BZip2Constants.html#MAX_SELECTORS">MAX_SELECTORS</a>, <a href="BZip2Constants.html#N_GROUPS">N_GROUPS</a>, <a href="BZip2Constants.html#N_ITERS">N_ITERS</a>, <a href="BZip2Constants.html#NUM_OVERSHOOT_BYTES">NUM_OVERSHOOT_BYTES</a>, <a href="BZip2Constants.html#rNums">rNums</a>, <a href="BZip2Constants.html#RUNA">RUNA</a>, <a href="BZip2Constants.html#RUNB">RUNB</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.OutputStream)" class="member-name-link">CBZip2OutputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructs a new <code>CBZip2OutputStream</code> with a blocksize of 900k.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.OutputStream,int)" class="member-name-link">CBZip2OutputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out,
 int&nbsp;blockSize)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructs a new <code>CBZip2OutputStream</code> with specified blocksize.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#chooseBlockSize(long)" class="member-name-link">chooseBlockSize</a><wbr>(long&nbsp;inputLength)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Chooses a blocksize based on the given length of the data to compress.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#finalize()" class="member-name-link">finalize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overridden to close the stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#finish()" class="member-name-link">finish</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#flush()" class="member-name-link">flush</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBlockSize()" class="member-name-link">getBlockSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the blocksize parameter specified at construction time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#hbMakeCodeLengths(char%5B%5D,int%5B%5D,int,int)" class="member-name-link">hbMakeCodeLengths</a><wbr>(char[]&nbsp;len,
 int[]&nbsp;freq,
 int&nbsp;alphaSize,
 int&nbsp;maxLen)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">This method is accessible by subclasses for historical
 purposes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(byte%5B%5D,int,int)" class="member-name-link">write</a><wbr>(byte[]&nbsp;buf,
 int&nbsp;offs,
 int&nbsp;len)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(int)" class="member-name-link">write</a><wbr>(int&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"></div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.OutputStream">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#nullOutputStream()" title="class or interface in java.io" class="external-link">nullOutputStream</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#write(byte%5B%5D)" title="class or interface in java.io" class="external-link">write</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="MIN_BLOCKSIZE">
<h3>MIN_BLOCKSIZE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MIN_BLOCKSIZE</span></div>
<div class="block">The minimum supported blocksize <code> == 1</code>.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.MIN_BLOCKSIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="MAX_BLOCKSIZE">
<h3>MAX_BLOCKSIZE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAX_BLOCKSIZE</span></div>
<div class="block">The maximum supported blocksize <code> == 9</code>.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.MAX_BLOCKSIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="SETMASK">
<h3>SETMASK</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SETMASK</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.SETMASK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="CLEARMASK">
<h3>CLEARMASK</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CLEARMASK</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.CLEARMASK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="GREATER_ICOST">
<h3>GREATER_ICOST</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">GREATER_ICOST</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.GREATER_ICOST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="LESSER_ICOST">
<h3>LESSER_ICOST</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LESSER_ICOST</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.LESSER_ICOST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="SMALL_THRESH">
<h3>SMALL_THRESH</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SMALL_THRESH</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.SMALL_THRESH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="DEPTH_THRESH">
<h3>DEPTH_THRESH</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEPTH_THRESH</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.DEPTH_THRESH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WORK_FACTOR">
<h3>WORK_FACTOR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">WORK_FACTOR</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.WORK_FACTOR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="QSORT_STACK_SIZE">
<h3>QSORT_STACK_SIZE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">QSORT_STACK_SIZE</span></div>
<div class="block">This constant is accessible by subclasses for historical
 purposes. If you don't know what it means then you don't need
 it.
 <p>If you are ever unlucky/improbable enough to get a stack
 overflow whilst sorting, increase the following constant and
 try again. In practice I have never seen the stack go above 27
 elems, so the following limit seems very generous.</p></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.QSORT_STACK_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.io.OutputStream)">
<h3>CBZip2OutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CBZip2OutputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Constructs a new <code>CBZip2OutputStream</code> with a blocksize of 900k.

 <p>
 <b>Attention: </b>The caller is responsible to write the two BZip2 magic
 bytes <code>"BZ"</code> to the specified stream prior to calling this
 constructor.
 </p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - *
            the destination stream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an I/O error occurs in the specified stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/NullPointerException.html" title="class or interface in java.lang" class="external-link">NullPointerException</a></code> - if <code>out == null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.OutputStream,int)">
<h3>CBZip2OutputStream</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CBZip2OutputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out,
 int&nbsp;blockSize)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Constructs a new <code>CBZip2OutputStream</code> with specified blocksize.

 <p>
 <b>Attention: </b>The caller is responsible to write the two BZip2 magic
 bytes <code>"BZ"</code> to the specified stream prior to calling this
 constructor.
 </p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - the destination stream.</dd>
<dd><code>blockSize</code> - the blockSize as 100k units.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an I/O error occurs in the specified stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if <code>(blockSize &lt; 1) || (blockSize &gt; 9)</code>.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/NullPointerException.html" title="class or interface in java.lang" class="external-link">NullPointerException</a></code> - if <code>out == null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#MIN_BLOCKSIZE"><code>MIN_BLOCKSIZE</code></a></li>
<li><a href="#MAX_BLOCKSIZE"><code>MAX_BLOCKSIZE</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="hbMakeCodeLengths(char[],int[],int,int)">
<h3>hbMakeCodeLengths</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">hbMakeCodeLengths</span><wbr><span class="parameters">(char[]&nbsp;len,
 int[]&nbsp;freq,
 int&nbsp;alphaSize,
 int&nbsp;maxLen)</span></div>
<div class="block">This method is accessible by subclasses for historical
 purposes. If you don't know what it does then you don't need
 it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>len</code> - char[]</dd>
<dd><code>freq</code> - char[]</dd>
<dd><code>alphaSize</code> - int</dd>
<dd><code>maxLen</code> - int</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="chooseBlockSize(long)">
<h3>chooseBlockSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">chooseBlockSize</span><wbr><span class="parameters">(long&nbsp;inputLength)</span></div>
<div class="block">Chooses a blocksize based on the given length of the data to compress.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputLength</code> - The length of the data which will be compressed by
            <code>CBZip2OutputStream</code>.</dd>
<dt>Returns:</dt>
<dd>The blocksize, between <a href="#MIN_BLOCKSIZE"><code>MIN_BLOCKSIZE</code></a> and
         <a href="#MAX_BLOCKSIZE"><code>MAX_BLOCKSIZE</code></a> both inclusive. For a negative
         <code>inputLength</code> this method returns <code>MAX_BLOCKSIZE</code>
         always.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="write(int)">
<h3>write</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(int&nbsp;b)</span>
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#write(int)" title="class or interface in java.io" class="external-link">write</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="finalize()">
<h3>finalize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">finalize</span>()
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></span></div>
<div class="block">Overridden to close the stream.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="finish()">
<h3>finish</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">finish</span>()
            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html#close()" title="class or interface in java.lang" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="flush()">
<h3>flush</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">flush</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html#flush()" title="class or interface in java.io" class="external-link">flush</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#flush()" title="class or interface in java.io" class="external-link">flush</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getBlockSize()">
<h3>getBlockSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getBlockSize</span>()</div>
<div class="block">Returns the blocksize parameter specified at construction time.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>int</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="write(byte[],int,int)">
<h3>write</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(byte[]&nbsp;buf,
 int&nbsp;offs,
 int&nbsp;len)</span>
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html#write(byte%5B%5D,int,int)" title="class or interface in java.io" class="external-link">write</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
