<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>All Classes and Interfaces (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#all-classes">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">Interfaces</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">Classes</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">Enum Classes</button><button id="all-classes-table-tab5" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab5', 2)" class="table-tab">Exception Classes</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/unix/AbstractAccessTask.html" title="class in org.apache.tools.ant.taskdefs.optional.unix">AbstractAccessTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/depend/AbstractAnalyzer.html" title="class in org.apache.tools.ant.util.depend">AbstractAnalyzer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An abstract implementation of the analyzer interface providing support
 for the bulk of interface methods.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/AbstractClasspathResource.html" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Resource representation of anything that is accessed via a Java classloader.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/AbstractClasspathResource.ClassLoaderWithFlag.html" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource.ClassLoaderWithFlag</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">original Cvs.java 1.20</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AbstractCvsTask.Module.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask.Module</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that holds an implicit patternset and supports nested
 patternsets and creates a DirectoryScanner using these patterns.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/j2ee/AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract class to support vendor-specific hot deployment tools.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is factored out from <a href="org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs"><code>SignJar</code></a>; a base class that can be used
 for both signing and verifying JAR files using jarsigner</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/AbstractResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">AbstractResourceCollectionWrapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for a ResourceCollection that wraps a single nested
 ResourceCollection.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/AbstractScriptComponent.html" title="class in org.apache.tools.ant.types.optional">AbstractScriptComponent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a <a href="org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><code>ProjectComponent</code></a> that has script support built in
 Use it as a foundation for scriptable things.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is the a base class a container of selectors - it does
 not need do be a selector itself.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/AbstractSshMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">AbstractSshMessage</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract class for ssh upload and download</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/AbstractUnicodeExtraField.html" title="class in org.apache.tools.zip">AbstractUnicodeExtraField</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A common base class for Unicode extra information extra fields.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Transform a JUnit xml report.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.Format.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer.Format</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">defines acceptable formats.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/selectors/modifiedselector/Algorithm.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Algorithm</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The <i>Algorithm</i> defines how a value for a file is computed.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/AllButFirst.html" title="class in org.apache.tools.ant.types.resources">AllButFirst</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that contains all resources of another
 collection except for the first <code>count</code> elements, a la
 the UNIX tail command with parameter <code>-n +count</code>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/AllButLast.html" title="class in org.apache.tools.ant.types.resources">AllButLast</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that contains all resources of another
 collection except for the last <code>count</code> elements, a la
 the UNIX head command with parameter <code>-n -count</code>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/depend/bcel/AncestorAnalyzer.html" title="class in org.apache.tools.ant.util.depend.bcel">AncestorAnalyzer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A dependency analyzer which returns superclass and superinterface
 dependencies.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/And.html" title="class in org.apache.tools.ant.taskdefs.condition">And</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">&lt;and&gt; condition container.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/And.html" title="class in org.apache.tools.ant.types.resources.selectors">And</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">And ResourceSelector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector has a collection of other selectors, all of which have to
 select a file in order for this selector to select it.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Uses ANSI Color Code Sequences to colorize messages
 sent to the console.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs">Ant</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Build a sub-project.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Ant.Reference.html" title="class in org.apache.tools.ant.taskdefs">Ant.Reference</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class that implements the nested &lt;reference&gt;
 element of &lt;ant&gt; and &lt;antcall&gt;.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Ant.TargetElement.html" title="class in org.apache.tools.ant.taskdefs">Ant.TargetElement</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class that implements the nested &lt;target&gt;
 element of &lt;ant&gt; and &lt;antcall&gt;.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/AntAnalyzer.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">AntAnalyzer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An analyzer which uses the depend task's bytecode classes to analyze
 dependencies</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used to load classes within ant with a different classpath from
 that used to start ant.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/loader/AntClassLoader2.html" title="class in org.apache.tools.ant.loader">AntClassLoader2</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.7
             Just use <a href="org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><code>AntClassLoader</code></a> itself.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/loader/AntClassLoader5.html" title="class in org.apache.tools.ant.loader">AntClassLoader5</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.9.7
             Just use <a href="org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><code>AntClassLoader</code></a> itself.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/AntFilterReader.html" title="class in org.apache.tools.ant.types">AntFilterReader</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An AntFilterReader is a wrapper class that encloses the classname
 and configuration of a Configurable FilterReader.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Antlib.html" title="class in org.apache.tools.ant.taskdefs">Antlib</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Antlib task.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for tasks that that can be used in antlibs.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ANTLR.html" title="class in org.apache.tools.ant.taskdefs.optional">ANTLR</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Invokes the ANTLR Translator generator on a grammar file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/launch/AntMain.html" title="interface in org.apache.tools.ant.launch">AntMain</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface used to bridge to the actual Main class without any
 messy reflection</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/AntResolver.html" title="class in org.apache.tools.ant.taskdefs.optional.extension.resolvers">AntResolver</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Resolver that just returns s specified location.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sound/AntSoundPlayer.html" title="class in org.apache.tools.ant.taskdefs.optional.sound">AntSoundPlayer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is designed to be used by any AntTask that requires audio output.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AntStructure.html" title="class in org.apache.tools.ant.taskdefs">AntStructure</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a partial DTD for Ant from the currently known tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs">AntStructure.StructurePrinter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Writes the actual structure information.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class contains all the information
 on a particular ant type,
 the classname, adapter and the class
 it should be assignable from.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/AntVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">AntVersion</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Ant version condition.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Context information for the ant processing.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resolver/ApacheCatalog.html" title="class in org.apache.tools.ant.types.resolver">ApacheCatalog</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class extends the Catalog class provided by Norman Walsh's
 resolver library in xml-commons in order to add classpath entity
 and URI resolution.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resolver/ApacheCatalogResolver.html" title="class in org.apache.tools.ant.types.resolver">ApacheCatalogResolver</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class extends the CatalogResolver class provided by Norman
 Walsh's resolver library in xml-commons.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/resources/Appendable.html" title="interface in org.apache.tools.ant.types.resources">Appendable</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to be implemented by "appendable" resources.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/resources/AppendableResourceCollection.html" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface describing a collection of Resources, to which elements can be
 appended.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Arc.html" title="class in org.apache.tools.ant.types.optional.image">Arc</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Draw an arc.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Arc.html" title="class in org.apache.tools.ant.types.optional.imageio">Arc</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Draw an arc.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Arc.ArcType.html" title="class in org.apache.tools.ant.types.optional.imageio">Arc.ArcType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A ArchiveFileSet is a FileSet with extra attributes useful in the
 context of archiving tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Resource representation of an entry inside an archive.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Archives.html" title="class in org.apache.tools.ant.types.resources">Archives</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A resource collection that treats all nested resources as archives
 and returns the contents of the archives as its content.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ArchiveScanner accesses the pattern matching algorithm in DirectoryScanner,
 which are protected methods that can only be accessed by subclassing.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/ArgumentProcessor.html" title="interface in org.apache.tools.ant">ArgumentProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Processor of arguments of the command line.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ArgumentProcessorRegistry.html" title="class in org.apache.tools.ant">ArgumentProcessorRegistry</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The global registry for <a href="org/apache/tools/ant/ArgumentProcessor.html" title="interface in org.apache.tools.ant"><code>ArgumentProcessor</code></a>s.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/AsiExtraField.html" title="class in org.apache.tools.zip">AsiExtraField</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds Unix file permission and UID/GID fields as well as symbolic
 link handling.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Assertions.html" title="class in org.apache.tools.ant.types">Assertions</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The assertion datatype.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Assertions.BaseAssertion.html" title="class in org.apache.tools.ant.types">Assertions.BaseAssertion</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">base class for our assertion elements.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Assertions.DisabledAssertion.html" title="class in org.apache.tools.ant.types">Assertions.DisabledAssertion</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A disabled assertion disables things</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Assertions.EnabledAssertion.html" title="class in org.apache.tools.ant.types">Assertions.EnabledAssertion</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">an enabled assertion enables things</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/windows/Attrib.html" title="class in org.apache.tools.ant.taskdefs.optional.windows">Attrib</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Attrib equivalent for Win32 environments.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/AttributeNamespace.html" title="class in org.apache.tools.ant.attribute">AttributeNamespace</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is used to indicate that the XML namespace (URI)
 can be used to look for namespace attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AttributeNamespaceDef.html" title="class in org.apache.tools.ant.taskdefs">AttributeNamespaceDef</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Definition to allow the URI to be considered for
 Ant attributes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/AugmentReference.html" title="class in org.apache.tools.ant.taskdefs">AugmentReference</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Ant task to dynamically augment a previously declared reference.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Available.html" title="class in org.apache.tools.ant.taskdefs">Available</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Will set the given property if the requested resource is available at
 runtime.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Available.FileDir.html" title="class in org.apache.tools.ant.taskdefs">Available.FileDir</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute covering the file types to be checked for, either
 file or dir.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/Base64Converter.html" title="class in org.apache.tools.ant.util">Base64Converter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">BASE 64 encoding of a String or an array of bytes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Convenience base class for all selectors accessed through ExtendSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for core filter readers.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/BaseIfAttribute.html" title="class in org.apache.tools.ant.attribute">BaseIfAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An abstract class for if/unless attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Basename.html" title="class in org.apache.tools.ant.taskdefs">Basename</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets a property to the base name of a specified file, optionally minus a
 suffix.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Parameterized base class for core filter readers.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/BaseResourceCollectionContainer.html" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionContainer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for ResourceCollections that nest multiple ResourceCollections.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/BaseResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionWrapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for a ResourceCollection that wraps a single nested
 ResourceCollection.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A convenience base class that you can subclass Selectors from.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/BaseSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is the base class for selectors that can contain other selectors.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Baseclass for BatchTest and JUnitTest.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/BasicShape.html" title="class in org.apache.tools.ant.types.optional.image">BasicShape</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Draw a basic shape</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/BasicShape.html" title="class in org.apache.tools.ant.types.optional.imageio">BasicShape</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Draw a basic shape</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Create then run <code>JUnitTest</code>'s based on the list of files
 given by the fileset attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/BCFileSet.html" title="class in org.apache.tools.ant.types.resources">BCFileSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility FileSet that includes directories for backwards-compatibility
 with certain tasks e.g.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/BigProjectLogger.html" title="class in org.apache.tools.ant.listener">BigProjectLogger</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a special logger that is designed to make it easier to work
 with big projects, those that use imports and
 subant to build complex systems.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/BindTargets.html" title="class in org.apache.tools.ant.taskdefs">BindTargets</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple task which bind some targets to some defined extension point</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/testing/BlockFor.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">BlockFor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">BorlandDeploymentTool is dedicated to the Borland Application Server 4.5 and 4.5.1
 This task generates and compiles the stubs and skeletons for all ejb described into the
 Deployment Descriptor, builds the jar file including the support files and verify
 whether the produced jar is valid or not.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/BorlandGenerateClient.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandGenerateClient</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates a Borland Application Server 4.5 client JAR using as
 input the EJB JAR file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/BriefJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BriefJUnitResultFormatter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prints plain text output of the test to a specified Writer.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing an event occurring during a build.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Signals an error condition during a build</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Instances of classes that implement this interface can register
 to be notified when things happened during a build.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface used by Ant to log the build output.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/BuildNumber.html" title="class in org.apache.tools.ant.taskdefs">BuildNumber</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Read, increment, and write a build number in a file
 It will first
 attempt to read a build number from a file, then set the property
 "build.number" to the value that was read in (or 0 if no such value).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/taskdefs/optional/testing/BuildTimeoutException.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">BuildTimeoutException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">This exception is used to indicate timeouts.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/BuiltinNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">BuiltinNative2Ascii</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Encapsulates the built-in Native2Ascii implementation.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/BUnzip2.html" title="class in org.apache.tools.ant.taskdefs">BUnzip2</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Expands a file that has been compressed with the BZIP2
 algorithm.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/BZip2.html" title="class in org.apache.tools.ant.taskdefs">BZip2</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compresses a file with the BZIP2 algorithm.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Base class for both the compress and decompress classes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/BZip2Resource.html" title="class in org.apache.tools.ant.types.resources">BZip2Resource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Bzip2 compressed resource.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/Cab.html" title="class in org.apache.tools.ant.taskdefs.optional">Cab</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Create a CAB archive.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/selectors/modifiedselector/Cache.html" title="interface in org.apache.tools.ant.types.selectors.modifiedselector">Cache</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">A Cache let the user store key-value-pairs in a permanent manner and access
 them.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/CallTarget.html" title="class in org.apache.tools.ant.taskdefs">CallTarget</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Call another target in the same project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2">CBZip2InputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An input stream that decompresses from the BZip2 format (without the file
 header chars) to be read as any other stream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/bzip2/CBZip2OutputStream.html" title="class in org.apache.tools.bzip2">CBZip2OutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An output stream that compresses into the BZip2 format (without the file
 header chars) into another stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCCheckin</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs ClearCase checkin.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCCheckout</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs ClearCase checkout.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCLock</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs a ClearCase Lock command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheck.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheck</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class common to all check commands (checkout, checkin,checkin default task);</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckin.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckin</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs Continuus checkin command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckinDefault.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckinDefault</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs Continuus Checkin Default task command.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckout</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs Continuus checkout command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCreateTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates new Continuus ccm task and sets it as the default.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkattr</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to perform mkattr command to ClearCase.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkbl</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to CreateBaseline command to ClearCase.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkdir</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs ClearCase mkdir.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkelem</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs ClearCase mkelem.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMklabel</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to perform mklabel command to ClearCase.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMklbtype</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to perform mklbtype command to ClearCase.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/CCMReconfigure.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMReconfigure</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task allows to reconfigure a project, recursively or not</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCRmtype</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to perform rmtype command to ClearCase.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUnCheckout</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs ClearCase UnCheckout command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnlock.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUnlock</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs a ClearCase Unlock command.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUpdate</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs a ClearCase Update command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/zip/CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block"><a href="org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip"><code>ZipExtraField</code></a> that knows how to parse central
 directory data.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface indicating that a reader may be chained to another one.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ChainedMapper.html" title="class in org.apache.tools.ant.util">ChainedMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A <code>ContainerMapper</code> that chains the results of the first
 nested <code>FileNameMapper</code>s into sourcefiles for the second,
 the second to the third, and so on, returning the resulting mapped
 filenames from the last nested <code>FileNameMapper</code>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/util/ChainReaderHelper.html" title="class in org.apache.tools.ant.filters.util">ChainReaderHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Process a FilterReader chain.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/ChangeLogTask.html" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Examines the output of cvs log and group related changes together.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/ChangeLogWriter.html" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogWriter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class used to generate an XML changelog.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/CharSet.html" title="class in org.apache.tools.ant.types">CharSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute implementation for Charset to use with encoding/charset attributes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used to create or verify file checksums.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Checksum.FormatElement.html" title="class in org.apache.tools.ant.taskdefs">Checksum.FormatElement</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class for the format attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/ChecksumAlgorithm.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ChecksumAlgorithm</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Computes a 'checksum' for the content of file using
 java.util.zip.CRC32 and java.util.zip.Adler32.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/unix/Chgrp.html" title="class in org.apache.tools.ant.taskdefs.optional.unix">Chgrp</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Chgrp equivalent for unix-like environments.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Chmod.html" title="class in org.apache.tools.ant.taskdefs">Chmod</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Chmod equivalent for unix-like environments.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/unix/Chown.html" title="class in org.apache.tools.ant.taskdefs.optional.unix">Chown</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Chown equivalent for unix-like environments.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/ClassConstants.html" title="class in org.apache.tools.ant.filters">ClassConstants</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Assembles the constants declared in a Java class in
 <code>key1=value1(line separator)key2=value2</code>
 format.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ClassCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ClassCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The constant pool entry which stores class information.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFile.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">ClassFile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A ClassFile object stores information about a Java class.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFileIterator.html" title="interface in org.apache.tools.ant.taskdefs.optional.depend">ClassFileIterator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Iterator interface for iterating over a set of class files</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/depend/ClassfileSet.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A ClassfileSet is a FileSet that enlists all classes that depend on a
 certain set of root classes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/depend/ClassfileSet.ClassRoot.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet.ClassRoot</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Inner class used to contain info about root classes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/ClassFileUtils.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">ClassFileUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class file routines.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Classloader.html" title="class in org.apache.tools.ant.taskdefs">Classloader</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">EXPERIMENTAL
 Create or modifies ClassLoader.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jlink/ClassNameReader.html" title="class in org.apache.tools.ant.taskdefs.optional.jlink">ClassNameReader</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Provides a quick and dirty way to determine the true name of a class
 given just an InputStream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ClasspathUtils.html" title="class in org.apache.tools.ant.util">ClasspathUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Offers some helper methods on the Path structure in ant.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ClasspathUtils.Delegate.html" title="class in org.apache.tools.ant.util">ClasspathUtils.Delegate</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Delegate that helps out any specific ProjectComponent that needs
 dynamic classloading.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A base class for creating tasks for executing commands on ClearCase.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/CloseResources.html" title="class in org.apache.tools.ant.taskdefs">CloseResources</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Not a real task but used during tests.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/CollectionUtils.html" title="class in org.apache.tools.ant.util">CollectionUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/CollectionUtils.EmptyEnumeration.html" title="class in org.apache.tools.ant.util">CollectionUtils.EmptyEnumeration&lt;E&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/ColorMapper.html" title="class in org.apache.tools.ant.types.optional.image">ColorMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/ColorMapper.html" title="class in org.apache.tools.ant.types.optional.imageio">ColorMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">CommandLauncher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher for a particular JVM/OS platform.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/CommandLauncherProxy.html" title="class in org.apache.tools.ant.taskdefs.launcher">CommandLauncherProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher that proxies another command
 launcher.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/CommandLauncherTask.html" title="class in org.apache.tools.ant.taskdefs">CommandLauncherTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task that configures the <a href="org/apache/tools/ant/taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a> to used
 when starting external processes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Commandline objects help handling command lines specifying processes to
 execute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used for nested xml command line definitions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A representation of a Java command line that is
 a composite of 2 <code>Commandline</code>s.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/CommandlineJava.SysProperties.html" title="class in org.apache.tools.ant.types">CommandlineJava.SysProperties</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Specialized Environment class for System properties.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/CommonsLoggingListener.html" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Jakarta Commons Logging listener.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Compare.html" title="class in org.apache.tools.ant.types.resources.selectors">Compare</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceSelector that compares against "control" Resource(s)
 using ResourceComparators.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Comparison.html" title="class in org.apache.tools.ant.types">Comparison</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute for generic comparisons.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/Compatability.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatability</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enum used in (@link Extension) to indicate the compatibility
 of one extension to another.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enum used in (@link Extension) to indicate the compatibility
 of one extension to another.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">The interface that all compiler adapters must adhere to.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapterExtension.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Extension interface for compilers that support source extensions
 other than .java.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/CompilerAdapterFactory.html" title="class in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates the necessary compiler adapter, given basic criteria.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Componentdef.html" title="class in org.apache.tools.ant.taskdefs">Componentdef</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds a component definition to the current project.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Component creation and configuration.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/CompositeMapper.html" title="class in org.apache.tools.ant.util">CompositeMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A <code>ContainerMapper</code> that unites the results of its constituent
 <code>FileNameMapper</code>s into a single set of result filenames.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A compressed resource.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Concat.html" title="class in org.apache.tools.ant.taskdefs">Concat</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class contains the 'concat' task, used to concatenate a series
 of files into a single stream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs">Concat.TextElement</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">sub element points to a file or contains text</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ConcatFileInputStream.html" title="class in org.apache.tools.ant.util">ConcatFileInputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Special <code>InputStream</code> that will
 concatenate the contents of an array of files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/ConcatFilter.html" title="class in org.apache.tools.ant.filters">ConcatFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Concats a file before and/or after the file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ConcatResourceInputStream.html" title="class in org.apache.tools.ant.util">ConcatResourceInputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Special <code>InputStream</code> that will
 concatenate the contents of Resources from a single ResourceCollection.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for conditions to use inside the &lt;condition&gt; task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Baseclass for the &lt;condition&gt; task as well as several
 conditions - ensures that the types of conditions inside the task
 and the "container" conditions are in sync.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ConditionTask.html" title="class in org.apache.tools.ant.taskdefs">ConditionTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to set a property conditionally using &lt;uptodate&gt;, &lt;available&gt;,
 and many other supported conditions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Constant Pool entry which represents a constant value.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The constant pool of a Java class.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An entry in the constant pool.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/Constants.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">Constants</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Constants, like filenames shared between various classes in this package.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/Constants.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">Constants</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Constants used within the junitlauncher task</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ContainerMapper.html" title="class in org.apache.tools.ant.util">ContainerMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A <code>FileNameMapper</code> that contains
 other <code>FileNameMapper</code>s.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Contains.html" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Is one string part of another string?</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files based on a regular expression.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files/resources based on whether they contain a
 particular string.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Content.html" title="class in org.apache.tools.ant.types.resources.comparators">Content</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares Resources by content.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/ContentTransformingResource.html" title="class in org.apache.tools.ant.types.resources">ContentTransformingResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A resource that transforms the content of another resource.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">Continuus</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A base class for creating tasks for executing commands on Continuus 5.1.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Copies a file or directory to a new file
 or directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Copydir.html" title="class in org.apache.tools.ant.taskdefs">Copydir</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">The copydir task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs">Copyfile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">The copyfile task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs">CopyPath</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">this task should have never been released and was
 obsoleted by ResourceCollection support in Copy available since Ant
 1.7.0.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/CustomJUnit4TestAdapterCache.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">CustomJUnit4TestAdapterCache</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Provides a custom implementation of the notifier for a JUnit4TestAdapter
 so that skipped and ignored tests can be reported to the existing
 <code>TestListener</code>s.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/mappers/CutDirsMapper.html" title="class in org.apache.tools.ant.types.mappers">CutDirsMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A mapper that strips of the a configurable number of leading
 directories from a file name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Cvs.html" title="class in org.apache.tools.ant.taskdefs">Cvs</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs operations on a CVS repository.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/CVSEntry.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CVSEntry</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">CVS Entry.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/CVSPass.html" title="class in org.apache.tools.ant.taskdefs">CVSPass</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds an new entry to a CVS password file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/CvsTagDiff.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagDiff</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Examines the output of cvs rdiff between two tags.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/CvsTagEntry.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagEntry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds the information of a line of rdiff</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/CvsUser.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsUser</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a CVS user with a userID and a full name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/cvslib/CvsVersion.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsVersion</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">this task allows to find out the client and the server version of a
 CVS installation

 example usage :
 &lt;cvsversion
 cvsRoot=&quot;:pserver:<EMAIL>:/home/<USER>
 passfile=&quot;c:/programme/cygwin/home/<USER>/.cvspass&quot;
 clientversionproperty=&quot;apacheclient&quot;
 serverversionproperty=&quot;apacheserver&quot;   /&gt;

 the task can be used also in the API by calling its execute method,
 then calling getServerVersion and/or getClientVersion</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for those classes that can appear inside the build file
 as stand alone data types.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Date.html" title="class in org.apache.tools.ant.types.resources.comparators">Date</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares Resources by last modification date.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Date.html" title="class in org.apache.tools.ant.types.resources.selectors">Date</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Date ResourceSelector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that chooses files based on their last modified date.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/DateSelector.TimeComparisons.html" title="class in org.apache.tools.ant.types.selectors">DateSelector.TimeComparisons</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values for time comparison.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/DateUtils.html" title="class in org.apache.tools.ant.util">DateUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper methods to deal with date/time formatting with a specific
 defined format (<a href="https://www.w3.org/TR/NOTE-datetime">ISO8601</a>)
 or a correct pluralization of elapsed time in minutes and seconds.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is the default implementation for the CompilerAdapter interface.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/DefaultDefinitions.html" title="class in org.apache.tools.ant">DefaultDefinitions</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Default definitions.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs">DefaultExcludes</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Alters the default excludes for the <strong>entire</strong> build..</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/DefaultExecutor.html" title="class in org.apache.tools.ant.helper">DefaultExecutor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Default Target executor implementation.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/input/DefaultInputHandler.html" title="class in org.apache.tools.ant.input">DefaultInputHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prompts on System.err, reads input from System.in</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/DefaultJspCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">DefaultJspCompilerAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is the default implementation for the JspCompilerAdapter interface.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Writes build events to a PrintStream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/DefaultNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">DefaultNative2Ascii</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">encapsulates the handling common to different Native2AsciiAdapter
 implementations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is the default implementation for the RmicAdapter interface.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/DefBase.html" title="class in org.apache.tools.ant.taskdefs">DefBase</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for Definitions handling uri and class loading.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Definer.html" title="class in org.apache.tools.ant.taskdefs">Definer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for Taskdef and Typedef - handles all
 the attributes for Typedef.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Definer.Format.html" title="class in org.apache.tools.ant.taskdefs">Definer.Format</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated type for format attribute</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Definer.OnError.html" title="class in org.apache.tools.ant.taskdefs">Definer.OnError</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated type for onError attribute</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/DelegatedResourceComparator.html" title="class in org.apache.tools.ant.types.resources.comparators">DelegatedResourceComparator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Delegates to other ResourceComparators or, if none specified,
 uses Resources' natural ordering.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Delete.html" title="class in org.apache.tools.ant.taskdefs">Delete</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Deletes a file or directory, or set of files defined by a fileset.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs">Deltree</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">The deltree task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant">DemuxInputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Passes input requests to the project object for demultiplexing into
 individual tasks and threads.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/DemuxOutputStream.html" title="class in org.apache.tools.ant">DemuxOutputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Logs content written by a thread and forwards the buffers onto the
 project object which will forward the content to the appropriate
 task.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/Depend.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">Depend</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates a dependency file for a given set of classes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">A dependency analyzer analyzes dependencies between Java classes to
 determine the minimal set of classes which are required by a set of
 &quot;root&quot; classes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/depend/bcel/DependencyVisitor.html" title="class in org.apache.tools.ant.util.depend.bcel">DependencyVisitor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A BCEL visitor implementation to collect class dependency information</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/depend/DependScanner.html" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">DirectoryScanner for finding class dependencies.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files based on whether they are newer than
 a matching file in another directory tree.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/DependSet.html" title="class in org.apache.tools.ant.taskdefs">DependSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Examines and removes out of date target files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files based on the how deep in the directory
 tree they are.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Description.html" title="class in org.apache.tools.ant.types">Description</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Description is used to provide a project-wide description element
 (that is, a description that applies to a buildfile as a whole).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Inner class used by EjbJar to facilitate the parsing of deployment
 descriptors and the capture of appropriate information.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/DeweyDecimal.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">DeweyDecimal</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">use org.apache.tools.ant.util.DeweyDecimal instead.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/DeweyDecimal.html" title="class in org.apache.tools.ant.util">DeweyDecimal</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class to contain version numbers in "Dewey Decimal"
 syntax.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant">Diagnostics</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A little diagnostic helper that output some information that may help
 in support.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs">DiagnosticsTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a task that hands off work to the Diagnostics module.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Difference.html" title="class in org.apache.tools.ant.types.resources">Difference</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection representing the difference between
 two or more nested ResourceCollections.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector selects files against a mapped set of target files, selecting
 all those files which are different.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/DigestAlgorithm.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">DigestAlgorithm</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Computes a 'hashvalue' for the content of file using
 java.security.MessageDigest.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/Directory.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">Directory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A helper object for Scp representing a directory in a file system.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/DirectoryIterator.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">DirectoryIterator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An iterator which iterates through the contents of a java directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class for scanning a directory for files/directories which match certain
 criteria.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Dirname.html" title="class in org.apache.tools.ant.taskdefs">Dirname</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Determines the directory name of the specified file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Subclass as hint for supporting tasks that the included directories
 instead of files should be used.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/dispatch/Dispatchable.html" title="interface in org.apache.tools.ant.dispatch">Dispatchable</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Classes implementing this interface specify the
 name of the parameter that contains the name
 of the task's method to execute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/dispatch/DispatchTask.html" title="class in org.apache.tools.ant.dispatch">DispatchTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Tasks extending this class may contain multiple actions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/dispatch/DispatchUtils.html" title="class in org.apache.tools.ant.dispatch">DispatchUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Determines and Executes the action method for the task.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/DOMElementWriter.html" title="class in org.apache.tools.ant.util">DOMElementWriter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Writes a DOM tree to a given Writer.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/DOMElementWriter.XmlNamespacePolicy.html" title="class in org.apache.tools.ant.util">DOMElementWriter.XmlNamespacePolicy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Whether namespaces should be ignored for elements and attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Some utilities that might be useful when manipulating DOM trees.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.NodeFilter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Filter interface to be applied when iterating over a DOM tree.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.NodeListImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeListImpl</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">custom implementation of a nodelist</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/DOMUtils.html" title="class in org.apache.tools.ant.util">DOMUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Some utility methods for common tasks when building DOM trees in memory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/DoubleCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">DoubleCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The constant pool entry subclass used to represent double constant
 values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Draw.html" title="class in org.apache.tools.ant.types.optional.image">Draw</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Draw.html" title="class in org.apache.tools.ant.types.optional.imageio">Draw</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/optional/image/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.image">DrawOperation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface which represents an Operation which is "drawable", such
 as a Rectangle, Circle or Text.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/optional/imageio/DrawOperation.html" title="interface in org.apache.tools.ant.types.optional.imageio">DrawOperation</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface which represents an Operation which is "drawable", such
 as a Rectangle, Circle or Text.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/DTDLocation.html" title="class in org.apache.tools.ant.types">DTDLocation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class to handle the DTD nested element.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown attributes</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicAttributeNS.html" title="interface in org.apache.tools.ant">DynamicAttributeNS</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant">DynamicConfigurator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown attributes and elements.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown attributes and elements.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/DynamicCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">DynamicCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Dynamic CP Info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown elements.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown elements.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/DynamicObjectAttribute.html" title="interface in org.apache.tools.ant">DynamicObjectAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Enables a task to control unknown attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Ear.html" title="class in org.apache.tools.ant.taskdefs">Ear</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a EAR archive.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Echo.html" title="class in org.apache.tools.ant.taskdefs">Echo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Writes a message to the Ant logging facilities.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Echo.EchoLevel.html" title="class in org.apache.tools.ant.taskdefs">Echo.EchoLevel</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The enumerated values for the level attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/EchoProperties.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Displays all the current properties in the build.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/EchoProperties.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A enumerated type for the format attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/EchoXML.html" title="class in org.apache.tools.ant.taskdefs">EchoXML</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Echo XML.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/EchoXML.NamespacePolicy.html" title="class in org.apache.tools.ant.taskdefs">EchoXML.NamespacePolicy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The interface to implement for deployment tools.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Provides automated EJB JAR file creation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.CMPVersion.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">CMP versions supported
 valid CMP versions are 1.0 and 2.0</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.DTDLocation.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.DTDLocation</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Inner class used to record information about the location of a local DTD</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An EnumeratedAttribute class for handling different EJB jar naming
 schemes</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Ellipse.html" title="class in org.apache.tools.ant.types.optional.image">Ellipse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Draw an ellipse.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Ellipse.html" title="class in org.apache.tools.ant.types.optional.imageio">Ellipse</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Draw an ellipse.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds an email address.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A task to send SMTP email.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/EmailTask.Encoding.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask.Encoding</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerates the encoding constants.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/attribute/EnableAttribute.html" title="interface in org.apache.tools.ant.attribute">EnableAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This interface is used by Ant attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/EnumeratedAttribute.html" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class for attributes that can only take one of a fixed list
 of values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/Enumerations.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">Enumerations</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Environment.html" title="class in org.apache.tools.ant.types">Environment</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wrapper for environment variables.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">representation of a single env value</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/EqualComparator.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">EqualComparator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple implementation of Comparator for use in CacheSelector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Equals.html" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple comparison condition.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/mail/ErrorInQuitException.html" title="class in org.apache.tools.mail">ErrorInQuitException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Specialized IOException that get thrown if SMTP's QUIT command fails.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/EscapeUnicode.html" title="class in org.apache.tools.ant.filters">EscapeUnicode</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This method converts non-latin characters to unicode escapes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/Evaluable.html" title="interface in org.apache.tools.ant">Evaluable&lt;T&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Kind of task attribute that can be evaluated before being assigned</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs">Exec</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Executes a given command if the os platform is appropriate.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/ExecutableSelector.html" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects executable files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs an external program.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs">ExecuteJava</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Execute a Java class.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Executes a given command, supplying a set of files as arguments.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "file", "dir" and "both"
 for the type attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Used by <code>Execute</code> to handle input and output stream of
 subprocesses.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Destroys a process running for too long.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Target executor abstraction.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Exists.html" title="class in org.apache.tools.ant.types.resources.comparators">Exists</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares Resources by existence.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Exists.html" title="class in org.apache.tools.ant.types.resources.selectors">Exists</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Exists ResourceSelector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Exit.html" title="class in org.apache.tools.ant.taskdefs">Exit</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Exits the active build, giving an additional message
 if available.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/ExitException.html" title="class in org.apache.tools.ant">ExitException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Used to report exit status of classes which call System.exit().</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant">ExitStatusException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">BuildException + exit status.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Expand.html" title="class in org.apache.tools.ant.taskdefs">Expand</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Unzip a file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/ExpandProperties.html" title="class in org.apache.tools.ant.filters">ExpandProperties</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Expands Ant properties, if any, in the data.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/selectors/ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">This is the interface to be used by all custom selectors, those that are
 called through the &lt;custom&gt; tag.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that selects files by forwarding the request on to other classes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class that represents either an available "Optional Package"
 (formerly known as "Standard Extension") as described in the manifest
 of a JAR file, or the requirement for such an optional package.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionAdapter.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple class that represents an Extension and conforms to Ants
 patterns.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ExtensionPoint.html" title="class in org.apache.tools.ant">ExtensionPoint</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An extension point build files can provide as a place where other
 build files can add new dependencies.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionResolver.html" title="interface in org.apache.tools.ant.taskdefs.optional.extension">ExtensionResolver</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to locate a File that satisfies extension.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionSet.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The Extension set lists a set of "Optional Packages" /
 "Extensions".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/ExtensionUtil.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtensionUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A set of useful methods relating to extensions.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/ExtraAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">ExtraAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple holder for extra attributes in main section of manifest.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ExtraFieldUtils.html" title="class in org.apache.tools.zip">ExtraFieldUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ZipExtraField related methods</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ExtraFieldUtils.UnparseableExtraField.html" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">"enum" for the possible actions to take if the extra field
 cannot be parsed.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/facade/FacadeTaskHelper.html" title="class in org.apache.tools.ant.util.facade">FacadeTaskHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class for facade implementations - encapsulates treatment of
 explicit implementation choices, magic properties and
 implementation specific command line arguments.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/FailureRecorder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Collects all failing test <i>cases</i> and creates a new JUnit test class containing
 a suite() method which calls these failed tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/FailureRecorder.TestInfos.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder.TestInfos</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TestInfos holds information about a given test for later use.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/FieldRefCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">FieldRefCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A FieldRef CP Info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">FileList represents an explicitly named list of files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FileList.FileName.html" title="class in org.apache.tools.ant.types">FileList.FileName</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Inner class corresponding to the &lt;file&gt; nested element.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to be used by SourceFileScanner.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files based on the filename.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/resources/FileProvider.html" title="interface in org.apache.tools.ant.types.resources">FileProvider</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This is an interface that resources that can provide a file should implement.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/FileResource.html" title="class in org.apache.tools.ant.types.resources">FileResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Resource representation of a File.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/FileResourceIterator.html" title="class in org.apache.tools.ant.types.resources">FileResourceIterator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Iterator of FileResources from filenames.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Files.html" title="class in org.apache.tools.ant.types.resources">Files</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection implementation; like AbstractFileSet with absolute paths.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">An interface used to describe the actions required of any type of
 directory scanner.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">This is the interface to be used by all selectors.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Moved out of MatchingTask to make it a standalone object that could
 be referenced (by scripts for example).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/FilesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares two files for equality based on size and
 content.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/FileSystem.html" title="class in org.apache.tools.ant.types.resources.comparators">FileSystem</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares filesystem Resources.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/FileTokenizer.html" title="class in org.apache.tools.ant.util">FileTokenizer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to read the complete input into a string.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/FileUtils.html" title="class in org.apache.tools.ant.util">FileUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class also encapsulates methods which allow Files to be
 referred to using abstract path names which are translated to native
 system file paths at runtime as well as copying files or setting
 their last modification time.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Filter.html" title="class in org.apache.tools.ant.taskdefs">Filter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets a token filter that is used by the file copy tasks
 to do token substitution.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">FilterChain may contain a chained set of filter readers.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/mappers/FilterMapper.html" title="class in org.apache.tools.ant.types.mappers">FilterMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a FileNameMapper based on a FilterChain.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A set of filters to be applied to something.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FilterSet.Filter.html" title="class in org.apache.tools.ant.types">FilterSet.Filter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Individual filter component of filterset.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FilterSet.OnMissing.html" title="class in org.apache.tools.ant.types">FilterSet.OnMissing</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute to set behavior WRT missing filtersfiles:
 "fail" (default), "warn", "ignore".</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A FilterSetCollection is a collection of filtersets each of which may have
 a different start/end token settings.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/First.html" title="class in org.apache.tools.ant.types.resources">First</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that contains the first <code>count</code> elements of
 another ResourceCollection, a la the UNIX head command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/FirstMatchMapper.html" title="class in org.apache.tools.ant.util">FirstMatchMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A <code>ContainerMapper</code> that returns the results of its
 first constituent <code>FileNameMapper</code>s that matches.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts text source files to local OS formatting conventions, as
 well as repair text files damaged by misconfigured or misguided editors or
 file transfer programs.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "asis", "add" and "remove".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "asis", "cr", "lf", "crlf", "mac", "unix" and "dos.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/FixCrLfFilter.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts text to local OS formatting conventions, as well as repair text
 damaged by misconfigured or misguided editors or file transfer programs.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "asis", "add" and "remove".</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/FixCrLfFilter.CrLf.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "asis", "cr", "lf" and "crlf".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/FlatFileNameMapper.html" title="class in org.apache.tools.ant.util">FlatFileNameMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of FileNameMapper that always returns the source
 file name without any leading directory information.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/FlexInteger.html" title="class in org.apache.tools.ant.types">FlexInteger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class which can be used for Ant task attribute setter methods to allow
 the build file to specify an integer in either decimal, octal, or hexadecimal
 format.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/FloatCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">FloatCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Float CP Info</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ForkDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ForkDefinition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the <code>fork</code> element within test definitions of the
 <code>junitlauncher</code> task</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ForkDefinition.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ForkDefinition.ForkMode</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javah/ForkingJavah.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">ForkingJavah</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This implementation runs the javah executable in a separate process.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/ForkingSunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">ForkingSunRmic</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is an extension of the sun rmic compiler, which forks rather than
 executes it inline.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A wrapper for the implementations of <code>JUnitResultFormatter</code>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.TypeAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement.TypeAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "plain", "xml", "brief" and "failure".</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Basic FTP client.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">an action to perform, one of
 "send", "put", "recv", "get", "del", "delete", "list", "mkdir", "chmod",
 "rmdir"</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPFileProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPFileProxy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">internal class providing a File-like interface to some of the information
 available from the FTP server</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">one of the valid system type keys recognized by the systemTypeKey
 attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">represents one of the valid timestamp adjustment values
 recognized by the <code>timestampGranularity</code> attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.LanguageCode.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated class for languages.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Basic FTP client.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.Action</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">an action to perform, one of
 "send", "put", "recv", "get", "del", "delete", "list", "mkdir", "chmod",
 "rmdir"</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.FTPSystemType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">one of the valid system type keys recognized by the systemTypeKey
 attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.Granularity</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">represents one of the valid timestamp adjustment values
 recognized by the <code>timestampGranularity</code> attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.FTPFileProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPFileProxy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">internal class providing a File-like interface to some of the information
 available from the FTP server</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/depend/bcel/FullAnalyzer.html" title="class in org.apache.tools.ant.util.depend.bcel">FullAnalyzer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An analyzer capable fo traversing all class - class relationships.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/testing/Funtest.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">Funtest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to provide functional testing under Ant, with a fairly complex workflow of:

 
 Conditional execution
 Application to start
 A probe to "waitfor" before running tests
 A tests sequence
 A reporting sequence that runs after the tests have finished
 A "teardown" clause that runs after the rest.
 Automated termination of the program it executes, if a timeout is not met
 Checking of a failure property and automatic raising of a fault
     (with the text in failureText)
 if test shutdown and reporting succeeded
  

 The task is designed to be framework neutral; it will work with JUnit,
  TestNG and other test frameworks That can be
 executed from Ant.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Gcj.html" title="class in org.apache.tools.ant.taskdefs.compilers">Gcj</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the gcj compiler.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javah/Gcjh.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">Gcjh</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapter to the native gcjh compiler.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/GeneralPurposeBit.html" title="class in org.apache.tools.zip">GeneralPurposeBit</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Parser/encoder for the "general purpose bit" field in ZIP's local
 file and central directory headers.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/GenerateKey.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates a key in a keystore.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/GenerateKey.DistinguishedName.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DistinguishedName</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A class corresponding to the dname nested element.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/GenerateKey.DnameParam.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DnameParam</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A DistinguishedName parameter.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A deployment tool which creates generic EJB jars.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/j2ee/GenericHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">GenericHotDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A generic tool for J2EE server hot deployment.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Get.html" title="class in org.apache.tools.ant.taskdefs">Get</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Gets a particular file from a URL source.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Get.Base64Converter.html" title="class in org.apache.tools.ant.taskdefs">Get.Base64Converter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Provide this for Backward Compatibility.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface implemented for reporting
 progress of downloading.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Get.NullProgress.html" title="class in org.apache.tools.ant.taskdefs">Get.NullProgress</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">do nothing with progress info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Get.VerboseProgress.html" title="class in org.apache.tools.ant.taskdefs">Get.VerboseProgress</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">verbose progress system prints to some output stream</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/property/GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to a class (normally PropertyHelper) to get a property.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/GlobPatternMapper.html" title="class in org.apache.tools.ant.util">GlobPatternMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of FileNameMapper that does simple wildcard pattern
 replacements.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/input/GreedyInputHandler.html" title="class in org.apache.tools.ant.input">GreedyInputHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prompts on System.err, reads input from System.in until EOF</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/GUnzip.html" title="class in org.apache.tools.ant.taskdefs">GUnzip</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Expands a file that has been compressed with the GZIP
 algorithm.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/GZip.html" title="class in org.apache.tools.ant.taskdefs">GZip</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compresses a file with the GZIP algorithm.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/GZipResource.html" title="class in org.apache.tools.ant.types.resources">GZipResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A GZip compressed resource.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/HasFreeSpace.html" title="class in org.apache.tools.ant.taskdefs.condition">HasFreeSpace</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">&lt;hasfreespace&gt;</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/HashvalueAlgorithm.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">HashvalueAlgorithm</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Computes a 'hashvalue' for the content of file using String.hashValue().</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/HasMethod.html" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">test for a method</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/Header.html" title="class in org.apache.tools.ant.taskdefs.email">Header</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing a generic key-value header.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/HeadFilter.html" title="class in org.apache.tools.ant.filters">HeadFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reads the first <code>n</code> lines of a stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/HostInfo.html" title="class in org.apache.tools.ant.taskdefs">HostInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets properties to the host provided, or localhost if no information is
 provided.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">An interface for vendor-specific "hot" deployment tools.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition to wait for a HTTP request to succeed.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/IdentityMapper.html" title="class in org.apache.tools.ant.util">IdentityMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of FileNameMapper that always returns the source file name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/IdentityStack.html" title="class in org.apache.tools.ant.util">IdentityStack&lt;E&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Identity Stack.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/IfBlankAttribute.html" title="class in org.apache.tools.ant.attribute">IfBlankAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Check if an attribute is blank or not.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/IfBlankAttribute.Unless.html" title="class in org.apache.tools.ant.attribute">IfBlankAttribute.Unless</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The unless version</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/IfSetAttribute.html" title="class in org.apache.tools.ant.attribute">IfSetAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Check if an attribute value as a property is set or not</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/IfSetAttribute.Unless.html" title="class in org.apache.tools.ant.attribute">IfSetAttribute.Unless</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The unless version</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/IfTrueAttribute.html" title="class in org.apache.tools.ant.attribute">IfTrueAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Check if an attribute value is true or not.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/attribute/IfTrueAttribute.Unless.html" title="class in org.apache.tools.ant.attribute">IfTrueAttribute.Unless</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The unless version</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/IgnoreDependenciesExecutor.html" title="class in org.apache.tools.ant.helper">IgnoreDependenciesExecutor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Target executor implementation that ignores dependencies.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Provides the functionality for TestListeners to be able to be notified of
 the necessary JUnit4 events for test being ignored (@Ignore annotation)
 or skipped (Assume failures).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/IgnoredTestResult.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestResult</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Records ignored and skipped tests reported as part of the execution of
 JUnit 4 tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/image/Image.html" title="class in org.apache.tools.ant.taskdefs.optional.image">Image</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">JAI is not developed any more.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/image/ImageIOTask.html" title="class in org.apache.tools.ant.taskdefs.optional.image">ImageIOTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A MatchingTask which relies on Java ImageIO to read existing image files
 and write the results of AWT image manipulation operations.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/image/ImageIOTask.ImageFormat.html" title="class in org.apache.tools.ant.taskdefs.optional.image">ImageIOTask.ImageFormat</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">defines acceptable image formats.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/ImageOperation.html" title="class in org.apache.tools.ant.types.optional.image">ImageOperation</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/ImageOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">ImageOperation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/types/resources/ImmutableResourceException.html" title="class in org.apache.tools.ant.types.resources">ImmutableResourceException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Exception thrown when an attempt is made to get an OutputStream
 from an immutable Resource.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/facade/ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.util.facade">ImplementationSpecificArgument</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extension of Commandline.Argument with a new attribute that chooses
 a specific implementation of the facade.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ImportTask.html" title="class in org.apache.tools.ant.taskdefs">ImportTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to import another build file into the current project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/InnerClassFilenameFilter.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">InnerClassFilenameFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A filename filter for inner class files of a particular class.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Input.html" title="class in org.apache.tools.ant.taskdefs">Input</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reads an input line from the console.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Input.HandlerType.html" title="class in org.apache.tools.ant.taskdefs">Input.HandlerType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute representing the built-in input handler types:
 "default", "propertyfile", "greedy", "secure" (since Ant 1.8).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Plugin to Ant to handle requests for user input.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/input/InputRequest.html" title="class in org.apache.tools.ant.input">InputRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Encapsulates an input request.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/InstanceOf.html" title="class in org.apache.tools.ant.types.resources.selectors">InstanceOf</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">InstanceOf ResourceSelector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/IntegerCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">IntegerCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Integer CP Info</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/InterfaceMethodRefCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">InterfaceMethodRefCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A InterfaceMethodRef CP Info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Intersect.html" title="class in org.apache.tools.ant.types.resources">Intersect</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection representing the intersection
 of multiple nested ResourceCollections.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class that collects the methods a task or nested element
 holds to set attributes, create nested elements or hold PCDATA
 elements.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">creator - allows use of create/store external
 to IntrospectionHelper.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/InVMExecution.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher">InVMExecution</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used during in-vm (non-forked mode) launching of tests</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/InvokeDynamicCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">InvokeDynamicCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An InvokeDynamic CP Info</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is used to generate iPlanet Application Server (iAS) 6.0 stubs and
 skeletons and build an EJB Jar file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbc</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compiles EJB stubs and skeletons for the iPlanet Application
 Server (iAS).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbcTask.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbcTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compiles EJB stubs and skeletons for the iPlanet Application Server.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsFailure.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFailure</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition to test a return-code for failure.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that tests whether a given string evals to false</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsFileSelected.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a condition that checks to see if a file passes an embedded selector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsLastModified.html" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that makes assertions about the last modified date of a
 resource.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsLastModified.CompareMode.html" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified.CompareMode</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">describes comparison modes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsReachable.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Test for a host being reachable using ICMP "ping" packets &amp; echo operations.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that tests whether a given reference has been defined.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that tests whether a given property has been set.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsSigned.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSigned</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Checks whether a jarfile is signed: if the name of the
 signature is passed, the file is checked for presence of that
 particular signature; otherwise the file is checked for the
 existence of any signature.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that tests whether a given string evals to true</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/JakartaMimeMailer.html" title="class in org.apache.tools.ant.taskdefs.email">JakartaMimeMailer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Uses the JakartaMail classes to send Mime format email.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/JakartaOroMatcher.html" title="class in org.apache.tools.ant.util.regexp">JakartaOroMatcher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of RegexpMatcher for Jakarta-ORO.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/JakartaOroRegexp.html" title="class in org.apache.tools.ant.util.regexp">JakartaOroRegexp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Regular expression implementation using the Jakarta Oro package</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/JakartaRegexpMatcher.html" title="class in org.apache.tools.ant.util.regexp">JakartaRegexpMatcher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of RegexpMatcher for Jakarta-Regexp.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/JakartaRegexpRegexp.html" title="class in org.apache.tools.ant.util.regexp">JakartaRegexpRegexp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Regular expression implementation using the Jakarta Regexp package</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a JAR archive.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The manifest config enumerated type.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Jar.StrictMode.html" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The strict enumerated type.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/JarFileIterator.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">JarFileIterator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A class file iterator which iterates through the contents of a Java jar
 file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibAvailableTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibAvailableTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Checks whether an extension is present in a fileset or an extensionSet.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibDisplayTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibDisplayTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Displays the "Optional Package" and "Package Specification" information
 contained within the specified JARs.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibManifestTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibManifestTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates a manifest that declares all the dependencies.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/JarLibResolveTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibResolveTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Tries to locate a JAR to satisfy an extension and place
 location of JAR into property.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/JarMarker.html" title="class in org.apache.tools.zip">JarMarker</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">If this extra field is added as the very first extra field of the
 archive, Solaris will consider it an executable jar file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/Jasper41Mangler.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">Jasper41Mangler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">this class implements the name mangling rules of the jasper in tomcat4.1.x
 which is likely to remain for some time</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JasperC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JasperC</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the jasper compiler.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Java.html" title="class in org.apache.tools.ant.taskdefs">Java</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Launcher for Java applications.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/Java13CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">Java13CommandLauncher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher for JDK/JRE 1.3 (and higher).</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compiles Java source files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Javac12.html" title="class in org.apache.tools.ant.taskdefs.compilers">Javac12</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">Use <a href="org/apache/tools/ant/taskdefs/compilers/Javac13.html" title="class in org.apache.tools.ant.taskdefs.compilers"><code>Javac13</code></a> instead.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Javac13.html" title="class in org.apache.tools.ant.taskdefs.compilers">Javac13</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the javac compiler for JDK 1.3
 This is primarily a cut-and-paste from the original javac task before it
 was refactored.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JavaCC</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">JavaCC compiler compiler task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/JavacExternal.html" title="class in org.apache.tools.ant.taskdefs.compilers">JavacExternal</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs a compile using javac externally.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/util/JavaClassHelper.html" title="class in org.apache.tools.ant.filters.util">JavaClassHelper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class that filters constants from a Java Class</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/JavaConstantResource.html" title="class in org.apache.tools.ant.types.resources">JavaConstantResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A resource that is a java constant.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javadoc.html" title="class in org.apache.tools.ant.taskdefs">Javadoc</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates Javadoc documentation for a collection
 of source code.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute implementation supporting the Javadoc scoping
 values.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A project aware class used for Javadoc extensions which take a name
 and a path such as doclet and taglet arguments.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An HTML element in the Javadoc.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used to track info about the packages to be javadoc'd</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is used to manage the source files to be processed.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/JavaEnvUtils.html" title="class in org.apache.tools.ant.util">JavaEnvUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A set of helper methods related to locating executables or checking
 conditions of a given Java installation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/Javah.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates JNI header files using javah.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for different backend implementations of the Javah task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javah/JavahAdapterFactory.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">JavahAdapterFactory</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates the JavahAdapter based on the user choice and
 potentially the VM vendor.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/JavaResource.html" title="class in org.apache.tools.ant.types.resources">JavaResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Resource representation of something loadable via a Java classloader.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/JavaVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">JavaVersion</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Java version condition.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/optional/JavaxScriptRunner.html" title="class in org.apache.tools.ant.util.optional">JavaxScriptRunner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is used to run scripts using JSR 223.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/JAXPUtils.html" title="class in org.apache.tools.ant.util">JAXPUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Collection of helper methods that retrieve a ParserFactory or
 Parsers and Readers.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/JbossDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The deployment tool to add the jboss specific deployment descriptor to the ejb jar file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handles JDBC configuration needed by SQL type tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html" title="class in org.apache.tools.ant.taskdefs.optional.jdepend">JDependTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs JDepend tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.jdepend">JDependTask.FormatAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A class for the enumerated attribute format,
 values are xml and text.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/Jdk14RegexpMatcher.html" title="class in org.apache.tools.ant.util.regexp">Jdk14RegexpMatcher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of RegexpMatcher for the built-in regexp matcher of
 JDK 1.4.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/Jdk14RegexpRegexp.html" title="class in org.apache.tools.ant.util.regexp">Jdk14RegexpRegexp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Regular expression implementation using the JDK 1.4 regular expression package</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Jikes.html" title="class in org.apache.tools.ant.taskdefs.compilers">Jikes</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the jikes compiler.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Jikes.html" title="class in org.apache.tools.ant.taskdefs">Jikes</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs">JikesOutputParser</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javacc/JJDoc.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJDoc</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs the JJDoc compiler compiler.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javacc/JJTree.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJTree</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs the JJTree compiler compiler.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jlink/jlink.html" title="class in org.apache.tools.ant.taskdefs.optional.jlink">jlink</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">jlink links together multiple .jar files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jlink/JlinkTask.html" title="class in org.apache.tools.ant.taskdefs.optional.jlink">JlinkTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This task defines objects that can link together various jar and
 zip files.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/modules/Jmod.html" title="class in org.apache.tools.ant.taskdefs.modules">Jmod</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a linkable .jmod file from a modular jar file, and optionally from
 other resource files such as native libraries and documents.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/modules/Jmod.ResolutionWarningReason.html" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningReason</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Permissible reasons for jmod creation to emit warnings.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The deployment tool to add the jonas specific deployment descriptors to the
 ejb JAR file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/j2ee/JonasHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">JonasHotDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Ant wrapper task for the weblogic.deploy tool.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs a JSP compiler.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">static inner class used as a parameter element</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The interface that all jsp compiler adapters must adhere to.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/compilers/JspCompilerAdapterFactory.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapterFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates the necessary compiler adapter, given basic criteria.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This is an interface to the Mangler service that jspc needs to map
 JSP file names to java files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/JspNameMangler.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspNameMangler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a class derived from the Jasper code
 (org.apache.jasper.compiler.CommandLineCompiler) to map from a JSP filename
 to a valid Java classname.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnit4TestMethodAdapter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnit4TestMethodAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapter between JUnit 3.8.x API and JUnit 4.x API for execution of tests
 and listening of events (test start, test finish, test failure, test skipped).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">JUnitLauncherTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Ant <a href="org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant"><code>Task</code></a> responsible for launching the JUnit platform for running tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This Interface describes classes that format the results of a JUnit
 testrun.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs JUnit tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">These are the different forking options</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogOutputStream.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A stream handler for handling the junit task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A log stream handler for junit.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Print summary enumeration values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A value class that contains the result of a test.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Handles the portions of <a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTask</code></a> which need to directly access
 actual JUnit classes, so that junit.jar need not be on Ant's startup classpath.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The interface that JUnitResultFormatter extends.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface that test runners implement.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The interface that SummaryJUnitResultFormatter extends.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirrorImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirrorImpl</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of the part of the junit task which can directly refer to junit.* classes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Run a single JUnit test.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTestRunner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple Testrunner for JUnit that runs all tests of a testsuite.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitVersionHelper.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitVersionHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Work around for some changes to the public JUnit API between
 different JUnit releases.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Jvc.html" title="class in org.apache.tools.ant.taskdefs.compilers">Jvc</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the jvc compiler from microsoft.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javah/Kaffeh.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">Kaffeh</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapter to the native kaffeh compiler.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/KaffeNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">KaffeNative2Ascii</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapter to kaffe.tools.native2ascii.Native2Ascii.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/KaffeRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">KaffeRmic</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the rmic for Kaffe</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/KeepAliveInputStream.html" title="class in org.apache.tools.ant.util">KeepAliveInputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that can be used to wrap <code>System.in</code>
 without getting anxious about any client closing the stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/KeepAliveOutputStream.html" title="class in org.apache.tools.ant.util">KeepAliveOutputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that can be used to wrap <code>System.out</code> and <code>System.err</code>
 without getting anxious about any client closing the stream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs">KeySubst</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">KeySubst is deprecated since Ant 1.1.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Kjc.html" title="class in org.apache.tools.ant.taskdefs.compilers">Kjc</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the Java compiler for KJC.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Last.html" title="class in org.apache.tools.ant.types.resources">Last</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that contains the last <code>count</code> elements of
 another ResourceCollection, a la the UNIX tail command.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/LastModifiedAlgorithm.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">LastModifiedAlgorithm</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Computes a 'timestamp' of file based on the lastModified time of that file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/LaunchDefinition.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">LaunchDefinition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Defines the necessary context for launching the JUnit platform for running
 tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/launch/Launcher.html" title="class in org.apache.tools.ant.launch">Launcher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a launcher for Ant.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/LauncherSupport.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher">LauncherSupport</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Responsible for doing the real work involved in launching the JUnit platform
 and passing it the relevant tests that need to be executed by the JUnit platform.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/launch/LaunchException.html" title="class in org.apache.tools.ant.launch">LaunchException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Signals an error condition during launching</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LayoutPreservingProperties.html" title="class in org.apache.tools.ant.util">LayoutPreservingProperties</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Properties collection which preserves comments and whitespace
 present in the input stream from which it was loaded.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LazyFileOutputStream.html" title="class in org.apache.tools.ant.util">LazyFileOutputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that delays opening the output file until the first bytes
 shall be written or the method <a href="org/apache/tools/ant/util/LazyFileOutputStream.html#open()"><code>open</code></a> has been invoked
 explicitly.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LazyHashtable.html" title="class in org.apache.tools.ant.util">LazyHashtable&lt;K,<wbr>V&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/LazyResourceCollectionWrapper.html" title="class in org.apache.tools.ant.types.resources">LazyResourceCollectionWrapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Resource collection which load underlying resource collection only on demand
 with support for caching</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LeadPipeInputStream.html" title="class in org.apache.tools.ant.util">LeadPipeInputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Special <code>PipedInputStream</code> that will not die
 when the writing <code>Thread</code> is no longer alive.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Length.html" title="class in org.apache.tools.ant.taskdefs">Length</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Gets lengths:  of files/resources, byte size; of strings, length (optionally trimmed).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Length.FileMode.html" title="class in org.apache.tools.ant.taskdefs">Length.FileMode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute operation mode</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Length.When.html" title="class in org.apache.tools.ant.taskdefs">Length.When</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute for the when attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/LibFileSet.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">LibFileSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">LibFileSet represents a fileset containing libraries.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/LineContains.html" title="class in org.apache.tools.ant.filters">LineContains</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Filter which includes only those lines that contain the user-specified
 strings.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/LineContains.Contains.html" title="class in org.apache.tools.ant.filters">LineContains.Contains</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds a contains element</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/LineContainsRegExp.html" title="class in org.apache.tools.ant.filters">LineContainsRegExp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Filter which includes only those lines that contain the user-specified
 regular expression matching strings.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Invokes <a href="org/apache/tools/ant/util/LineOrientedOutputStream.html#processLine(java.lang.String)"><code>processLine</code></a> whenever a full line has
 been written to this stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LineOrientedOutputStreamRedirector.html" title="class in org.apache.tools.ant.util">LineOrientedOutputStreamRedirector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Output stream which buffer and redirect a stream line by line.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LineTokenizer.html" title="class in org.apache.tools.ant.util">LineTokenizer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">class to tokenize the input as lines separated
 by \r (mac style), \r\n (dos/windows style) or \n (unix style)</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/modules/Link.html" title="class in org.apache.tools.ant.taskdefs.modules">Link</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Assembles jmod files into an executable image.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/modules/Link.CompressionLevel.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Possible attribute values for compression level of a linked image:
 
 <code>0</code>
 <code>none</code>
 no compression (default)
 <code>1</code>
 <code>strings</code>
 constant string sharing
 <code>2</code>
 <code>zip</code>
 zip compression
 </div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/modules/Link.Endianness.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Possible values for linked image endianness:
 <code>little</code> and <code>big</code>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/modules/Link.VMType.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Possible values for JVM type in linked image:
 <code>client</code>, <code>server</code>, <code>minimal</code>, or <code>all</code>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LinkedHashtable.html" title="class in org.apache.tools.ant.util">LinkedHashtable&lt;K,<wbr>V&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Subclass of Hashtable that wraps a LinkedHashMap to provide
 predictable iteration order.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ListenerDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ListenerDefinition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the <code>&amp;lt;listener&amp;gt;</code> element within the <code>&amp;lt;junitlauncher&amp;gt;</code>
 task</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/ListenerDefinition.ListenerType.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ListenerDefinition.ListenerType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">defines available listener types.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/LoaderUtils.html" title="class in org.apache.tools.ant.util">LoaderUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ClassLoader utility methods</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/LoadFile.html" title="class in org.apache.tools.ant.taskdefs">LoadFile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Load a file into a property</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/LoadProperties.html" title="class in org.apache.tools.ant.taskdefs">LoadProperties</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Load a file's contents as Ant properties.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/LoadResource.html" title="class in org.apache.tools.ant.taskdefs">LoadResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Load a resource into a property</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Local.html" title="class in org.apache.tools.ant.taskdefs">Local</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to create local properties in the current scope.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Local.Name.html" title="class in org.apache.tools.ant.taskdefs">Local.Name</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Nested <code>name</code> element.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/property/LocalProperties.html" title="class in org.apache.tools.ant.property">LocalProperties</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Thread local class containing local properties.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/property/LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A stack of local property maps.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Stores the location of a piece of text within a file (file name,
 line number and column number).</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/LocationResolver.html" title="class in org.apache.tools.ant.taskdefs.optional.extension.resolvers">LocationResolver</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Resolver that just returns s specified location.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/launch/Locator.html" title="class in org.apache.tools.ant.launch">Locator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The Locator is a utility class which is used to find certain items
 in the environment.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/Log4jListener.html" title="class in org.apache.tools.ant.listener">Log4jListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">Apache Log4j (1) is not developed any more.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/LogLevel.html" title="class in org.apache.tools.ant.types">LogLevel</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The enumerated values for Ant's log level.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/ssh/LogListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for ssh log listeners to implement.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/LogOutputResource.html" title="class in org.apache.tools.ant.types.resources">LogOutputResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Output-only Resource that always appends to Ant's log.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/LogOutputStream.html" title="class in org.apache.tools.ant.taskdefs">LogOutputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Logs each line written to this stream to the log system of ant.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">LogStreamHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Logs standard output and error of a subprocess to the log system of ant.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/LongCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">LongCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Long CP Info</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/MacCommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">MacCommandLauncher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher for Mac that uses a dodgy mechanism to change
 working directory before launching commands.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroDef.html" title="class in org.apache.tools.ant.taskdefs">MacroDef</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Describe class <code>MacroDef</code> here.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An attribute for the MacroDef task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The class corresponding to the sequential nested element.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A nested element for the MacroDef task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A nested text element for the MacroDef task.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroInstance.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The class to be placed in the ant type definition.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MacroInstance.Element.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance.Element</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Embedded element in macro instance</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/MagicNames.html" title="class in org.apache.tools.ant">MagicNames</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Magic names used within Ant.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/Mailer.html" title="class in org.apache.tools.ant.taskdefs.email">Mailer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for the various emailing implementations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Buffers log messages from DefaultLogger, and sends an e-mail with the
  results.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/mail/MailMessage.html" title="class in org.apache.tools.mail">MailMessage</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A class to help send SMTP email.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant">Main</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Command line entry point into Ant.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Majority.html" title="class in org.apache.tools.ant.types.resources.selectors">Majority</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Majority ResourceSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector is here just to shake up your thinking a bit.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs">MakeUrl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This task takes file and turns them into a URL, which it then assigns
 to a property.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds the data of a jar manifest.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An attribute for the manifest.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A manifest section - you can nest attribute elements into sections.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs">ManifestClassPath</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts a Path into a property suitable as a Manifest classpath.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Exception thrown indicating problems in a JAR Manifest</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ManifestTask.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a manifest file for inclusion in a JAR, Ant task wrapper
 around <a href="org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs"><code>Manifest</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask.Mode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class for Manifest's mode attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/MappedResource.html" title="class in org.apache.tools.ant.types.resources">MappedResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A decorator around a different resource that uses a mapper to
 dynamically remap the resource's name.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/MappedResourceCollection.html" title="class in org.apache.tools.ant.types.resources">MappedResourceCollection</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wrapper around a resource collections that maps the names of the
 other collection using a configured mapper.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Element to define a FileNameMapper.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Mapper.MapperType.html" title="class in org.apache.tools.ant.types">Mapper.MapperType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class as Argument to FileNameMapper.setType.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/MappingSelector.html" title="class in org.apache.tools.ant.types.selectors">MappingSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A mapping selector is an abstract class adding mapping support to the base
 selector</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Matches.html" title="class in org.apache.tools.ant.taskdefs.condition">Matches</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple regular expression condition.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is an abstract task that should be used by all those tasks that
 require to include or exclude files based on pattern matching.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/MergingMapper.html" title="class in org.apache.tools.ant.util">MergingMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of FileNameMapper that always returns the same
 target file name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/Message.html" title="class in org.apache.tools.ant.taskdefs.email">Message</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing an email message.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodHandleCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodHandleCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A MethodHandle CP Info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodHandleCPInfo.ReferenceKind.html" title="enum class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodHandleCPInfo.ReferenceKind</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodRefCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A MethodRef CP Info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodTypeCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodTypeCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Method Type CP Info</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/MimeMail.html" title="class in org.apache.tools.ant.taskdefs.optional.net">MimeMail</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.6.x.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/email/MimeMailer.html" title="class in org.apache.tools.ant.taskdefs.email">MimeMailer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">see org.apache.tools.ant.taskdefs.email.JakartaMimeMailer</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs">Mkdir</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a given directory.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector class that uses <i>Algorithm</i>, <i>Cache</i> and <i>Comparator</i>
 for its work.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.AlgorithmName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.AlgorithmName</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The enumerated type for algorithm.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.CacheName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.CacheName</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The enumerated type for cache.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.ComparatorName.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector.ComparatorName</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The enumerated type for algorithm.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ModuleCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ModuleCPInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the module info constant pool entry</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/ModuleVersion.html" title="class in org.apache.tools.ant.types">ModuleVersion</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Element describing the parts of a Java
 <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">module version</a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Move.html" title="class in org.apache.tools.ant.taskdefs">Move</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Moves a file or directory to a new file or directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A base class for creating tasks for executing commands on Visual SourceSafe.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSS.CurrentModUpdated.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extension of EnumeratedAttribute to hold the values for file time stamp.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSS.WritableFiles.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extension of EnumeratedAttribute to hold the values for writable filess.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSADD.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSADD</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs Add commands to Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCHECKIN.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCHECKIN</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs CheckIn commands to Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCHECKOUT.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCHECKOUT</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs CheckOut commands to Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Holds all the constants for the VSS tasks.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCP.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCP</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs CP (Change Project) commands to Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSCREATE.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCREATE</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a new project in Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSGET.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSGET</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Perform Get commands from Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSHISTORY.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSHISTORY</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs History commands to Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSHISTORY.BriefCodediffNofile.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSHISTORY.BriefCodediffNofile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extension of EnumeratedAttribute to hold the values for style.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/vss/MSVSSLABEL.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSLABEL</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs Label commands to Microsoft Visual SourceSafe.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/input/MultipleChoiceInputRequest.html" title="class in org.apache.tools.ant.input">MultipleChoiceInputRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Encapsulates an input request.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/MultiRootFileSet.html" title="class in org.apache.tools.ant.types.resources">MultiRootFileSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Union of file/dirsets that share the same patterns and selectors
 but have different roots.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/ant/types/resources/MultiRootFileSet.SetType.html" title="enum class in org.apache.tools.ant.types.resources">MultiRootFileSet.SetType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">What to return from the set: files, directories or both.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Name.html" title="class in org.apache.tools.ant.types.resources.comparators">Name</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares Resources by name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Name.html" title="class in org.apache.tools.ant.types.resources.selectors">Name</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Name ResourceSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/NameAndTypeCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">NameAndTypeCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A NameAndType CP Info</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/NamedTest.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">NamedTest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">A test that has a name associated with it</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts files from native encodings to ASCII.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/Native2AsciiAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for an adapter to a native2ascii implementation.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/Native2AsciiAdapterFactory.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapterFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates the Native2AsciiAdapter based on the user choice and
 potentially the VM vendor.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/Native2AsciiFilter.html" title="class in org.apache.tools.ant.filters">Native2AsciiFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A filter that performs translations from characters to their
 Unicode-escape sequences and vice-versa.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/Native2AsciiUtils.html" title="class in org.apache.tools.ant.util">Native2AsciiUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Contains helper methods for Ant's built-in implementation of native2ascii.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/NetRexxC.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compiles NetRexx source files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated class corresponding to the trace attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated class corresponding to the verbose attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Nice.html" title="class in org.apache.tools.ant.taskdefs">Nice</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A task to provide "nice-ness" to the current thread, and/or to
 query the current value.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extends DefaultLogger to strip out empty targets.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/optional/NoExitSecurityManager.html" title="class in org.apache.tools.ant.util.optional">NoExitSecurityManager</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is intended as a replacement for the default system manager.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/None.html" title="class in org.apache.tools.ant.types.resources.selectors">None</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">None ResourceSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector has a collection of other selectors.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Not.html" title="class in org.apache.tools.ant.taskdefs.condition">Not</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">&lt;not&gt; condition.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Not.html" title="class in org.apache.tools.ant.types.resources.selectors">Not</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Not ResourceSelector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector has one other selectors whose meaning it inverts.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/NullOutputStream.html" title="class in org.apache.tools.ant.util">NullOutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">OutputStream that completely discards all data written to it.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/property/NullReturn.html" title="class in org.apache.tools.ant.property">NullReturn</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to represent a null and to stop the chain of lookups.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Or.html" title="class in org.apache.tools.ant.taskdefs.condition">Or</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">&lt;or&gt; condition container.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Or.html" title="class in org.apache.tools.ant.types.resources.selectors">Or</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Or ResourceSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/OrionDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">OrionDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The deployment tool to add the orion specific deployment descriptor to the
 ejb jar file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector has a collection of other selectors, any of which have to
 select a file in order for this selector to select it.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Os.html" title="class in org.apache.tools.ant.taskdefs.condition">Os</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that tests the OS type.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/OS2CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">OS2CommandLauncher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher for OS/2 that uses 'cmd.exe' when launching
 commands in directories other than the current working directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/OutErrSummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">OutErrSummaryJUnitResultFormatter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used instead of SummaryJUnitResultFormatter in forked tests if
 withOutAndErr is requested.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/OutputStreamFunneler.html" title="class in org.apache.tools.ant.util">OutputStreamFunneler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Manages a set of <code>OutputStream</code>s to
 write to a single underlying stream, which is
 closed only when the last &quot;funnel&quot;
 has been closed.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/OwnedBySelector.html" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects files based on their owner.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Pack.html" title="class in org.apache.tools.ant.taskdefs">Pack</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract Base class for pack tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/PackageCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">PackageCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the package info (within a module) constant pool entry</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/PackageNameMapper.html" title="class in org.apache.tools.ant.util">PackageNameMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Maps directory name matches into a dotted package name.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Parallel.html" title="class in org.apache.tools.ant.taskdefs">Parallel</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Executes the contained tasks in separate threads, continuing
 once all are completed.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Parallel.TaskList.html" title="class in org.apache.tools.ant.taskdefs">Parallel.TaskList</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class which holds a list of tasks to execute</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Parameter.html" title="class in org.apache.tools.ant.types">Parameter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A parameter is composed of a name, type and value.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Parameterizable objects take generic key value pairs.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/property/ParseNextProperty.html" title="interface in org.apache.tools.ant.property">ParseNextProperty</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Helper for <a href="org/apache/tools/ant/property/PropertyExpander.html" title="interface in org.apache.tools.ant.property"><code>PropertyExpander</code></a> that can be
 used to expand property references to values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/property/ParseProperties.html" title="class in org.apache.tools.ant.property">ParseProperties</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Parse properties using a collection of expanders.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/ParserSupports.html" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Test for the XML parser supporting a particular feature</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Patch.html" title="class in org.apache.tools.ant.taskdefs">Patch</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Patches a file by applying a 'diff' file to it; requires "patch" to be
 on the execution path.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This object represents a path as used by CLASSPATH or PATH
 environment variable.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PathConvert.html" title="class in org.apache.tools.ant.taskdefs">PathConvert</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts path and classpath information to a specific target OS
 format.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PathConvert.TargetOs.html" title="class in org.apache.tools.ant.taskdefs">PathConvert.TargetOs</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An enumeration of supported targets:
 "windows", "unix", "netware", and "os/2".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant">PathTokenizer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Path tokenizer takes a path and returns the components that make up
 that path.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Named collection of include/exclude tags.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/PerlScriptCommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">PerlScriptCommandLauncher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher that uses an auxiliary perl script to launch
 commands in directories other than the current working directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements a security manager meant for usage by tasks that run inside the
 Ant VM.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Permissions.Permission.html" title="class in org.apache.tools.ant.types">Permissions.Permission</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a permission.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/PermissionUtils.html" title="class in org.apache.tools.ant.util">PermissionUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Contains helper methods for dealing with <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/nio/file/attribute/PosixFilePermission.html" title="class or interface in java.nio.file.attribute" class="external-link"><code>PosixFilePermission</code></a> or the traditional Unix mode representation of
 permissions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/ant/util/PermissionUtils.FileType.html" title="enum class in org.apache.tools.ant.util">PermissionUtils.FileType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">The supported types of files, maps to the <code>isFoo</code> methods
 in <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/nio/file/attribute/BasicFileAttributes.html" title="class or interface in java.nio.file.attribute" class="external-link"><code>BasicFileAttributes</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/PlainJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">PlainJUnitResultFormatter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prints plain text output of the test to a specified Writer.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/PosixGroupSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects files based on their POSIX group.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/PosixPermissionsSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects files based on their POSIX permissions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/PrefixLines.html" title="class in org.apache.tools.ant.filters">PrefixLines</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Attaches a prefix to every line.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files based on whether they appear in another
 directory tree.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/PresentSelector.FilePresence.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values for indicating where a file's
 presence is allowed and required.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PreSetDef.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The preset definition task generates a new definition
 based on a current definition with some attributes or
 elements preset.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef.PreSetDefinition</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class contains the unknown element and the object
 that is predefined.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ProcessUtil.html" title="class in org.apache.tools.ant.util">ProcessUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Process Utilities</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/ProfileLogger.html" title="class in org.apache.tools.ant.listener">ProfileLogger</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a special logger that is designed to profile builds.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Central representation of an Ant project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for components of a project, including tasks and data types.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Configures a Project (complete with Targets and Tasks) based on
 a build file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ProjectHelper.OnMissingExtensionPoint.html" title="class in org.apache.tools.ant">ProjectHelper.OnMissingExtensionPoint</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Possible value for target's onMissingExtensionPoint attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sax2 based project reader</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The common superclass for all SAX event handlers used to parse
 the configuration file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.ElementHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.ElementHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handler for all project elements (tasks, data types)</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.MainHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.MainHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The main handler - it handles the &lt;project&gt; tag.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.ProjectHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.ProjectHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handler for the top level "project" element.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.RootHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.RootHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handler for ant processing.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.TargetHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handler for "target" and "extension-point" elements.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/ProjectHelperImpl.html" title="class in org.apache.tools.ant.helper">ProjectHelperImpl</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Original helper.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/ProjectHelperRepository.html" title="class in org.apache.tools.ant">ProjectHelperRepository</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Repository of <a href="org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant"><code>ProjectHelper</code></a> found in the classpath or via
 some System properties.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ProjectHelperTask.html" title="class in org.apache.tools.ant.taskdefs">ProjectHelperTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Task to install project helper into Ant's runtime</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/modifiedselector/PropertiesfileCache.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">PropertiesfileCache</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Use java.util.Properties for storing the values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs">Property</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets a property by name, or set of properties (from file or
 resource) in the project.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/property/PropertyExpander.html" title="interface in org.apache.tools.ant.property">PropertyExpander</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Responsible for locating a property reference inside a String.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Modifies settings in a property file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Instance of this class represents nested elements of
 a task propertyfile.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Operation.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Operation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "+", "-", "="</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Type.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Type</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values "int", "date" and "string".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/PropertyFile.Unit.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Unit</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Borrowed from Tstamp</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/input/PropertyFileInputHandler.html" title="class in org.apache.tools.ant.input">PropertyFileInputHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reads input from a property file, the file name is read from the
 system property ant.input.properties, the prompt is the key for input.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Deals with properties - substitution, dynamic properties, etc.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Marker interface for a PropertyHelper delegate.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/PropertyHelper.PropertyEnumerator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Obtains the names of all known properties.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Looks up a property's value based on its name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Sets or overrides a property.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PropertyHelperTask.html" title="class in org.apache.tools.ant.taskdefs">PropertyHelperTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This task is designed to allow the user to install a different
 PropertyHelper on the current Project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/PropertyOutputStream.html" title="class in org.apache.tools.ant.util">PropertyOutputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link"><code>OutputStream</code></a> that writes an Ant property.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/PropertyResource.html" title="class in org.apache.tools.ant.types.resources">PropertyResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Exposes an Ant property as a Resource.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A set of properties.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/PropertySet.BuiltinPropertySetName.html" title="class in org.apache.tools.ant.types">PropertySet.BuiltinPropertySetName</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used for propertyref's builtin attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/PropertySet.PropertyRef.html" title="class in org.apache.tools.ant.types">PropertySet.PropertyRef</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a nested class containing a reference to some properties
 and optionally a source of properties.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/spi/Provider.html" title="class in org.apache.tools.ant.types.spi">Provider</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ANT Jar-Task SPI extension
 This class corresponds to the nested element
 &lt;provider type="type"&gt; in the &lt;service type=""&gt;
 nested element of the jar task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/java15/ProxyDiagnostics.html" title="class in org.apache.tools.ant.util.java15">ProxyDiagnostics</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class exists to create a string that tells diagnostics about the current
 state of proxy diagnostics.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ProxySetup.html" title="class in org.apache.tools.ant.util">ProxySetup</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Code to do proxy setup.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Copies standard output and error of subprocesses to standard output and
 error of the parent process.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/PumpStreamHandler.ThreadWithPumper.html" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler.ThreadWithPumper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Specialized subclass that allows access to the running StreamPumper.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/pvcs/Pvcs.html" title="class in org.apache.tools.ant.taskdefs.optional.pvcs">Pvcs</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extracts the latest edition of the source code from a PVCS repository.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/pvcs/PvcsProject.html" title="class in org.apache.tools.ant.taskdefs.optional.pvcs">PvcsProject</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">represents a project within the PVCS repository to extract files from.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Quantifier.html" title="class in org.apache.tools.ant.types">Quantifier</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute for quantifier comparisons.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/ReadableSelector.html" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects readable files.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ReaderInputStream.html" title="class in org.apache.tools.ant.util">ReaderInputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapts a <code>Reader</code> as an <code>InputStream</code>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs">Recorder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds a listener to the current build process that records the
 output to a file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Recorder.ActionChoices.html" title="class in org.apache.tools.ant.taskdefs">Recorder.ActionChoices</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A list of possible values for the <code>setAction()</code> method.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs">Recorder.VerbosityLevelChoices</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A list of possible values for the <code>setLoglevel()</code> method.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a class that represents a recorder.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Rectangle.html" title="class in org.apache.tools.ant.types.optional.image">Rectangle</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Rectangle.html" title="class in org.apache.tools.ant.types.optional.imageio">Rectangle</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The Redirector class manages the setup and connection of input and output
 redirection for an Ant project component.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Element representation of a <code>Redirector</code>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to hold a reference to another object in the project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ReflectUtil.html" title="class in org.apache.tools.ant.util">ReflectUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class to handle reflection on java objects.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ReflectWrapper.html" title="class in org.apache.tools.ant.util">ReflectWrapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class to handle reflection on java objects.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/regexp/Regexp.html" title="interface in org.apache.tools.ant.util.regexp">Regexp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface which represents a regular expression, and the operations
 that can be performed on it.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/RegexpFactory.html" title="class in org.apache.tools.ant.util.regexp">RegexpFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Regular expression factory, which will create Regexp objects.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface describing a regular expression matcher.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/RegexpMatcherFactory.html" title="class in org.apache.tools.ant.util.regexp">RegexpMatcherFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple Factory Class that produces an implementation of RegexpMatcher based on the system
 property <code>ant.regexp.regexpimpl</code> and the classes available.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/RegexpPatternMapper.html" title="class in org.apache.tools.ant.util">RegexpPatternMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of FileNameMapper that does regular expression
 replacements.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/regexp/RegexpUtil.html" title="class in org.apache.tools.ant.util.regexp">RegexpUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Regular expression utilities class which handles flag operations.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/RegularExpression.html" title="class in org.apache.tools.ant.types">RegularExpression</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A regular expression datatype.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs">Rename</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">The rename task is deprecated since Ant 1.2.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/RenameExtensions.html" title="class in org.apache.tools.ant.taskdefs.optional">RenameExtensions</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.5.x.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Replace.html" title="class in org.apache.tools.ant.taskdefs">Replace</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Replaces all occurrences of one or more string tokens with given
 values in the indicated files.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ReplaceRegExp.html" title="class in org.apache.tools.ant.taskdefs.optional">ReplaceRegExp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Performs regular expression string replacements in a text
 file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/ReplaceTokens.html" title="class in org.apache.tools.ant.filters">ReplaceTokens</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Replaces tokens in the original input with user-supplied values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/ReplaceTokens.Token.html" title="class in org.apache.tools.ant.filters">ReplaceTokens.Token</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds a token</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/property/ResolvePropertyMap.html" title="class in org.apache.tools.ant.property">ResolvePropertyMap</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to resolve properties in a map.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Describes a "File-like" resource (File, ZipEntry, etc.).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface describing a collection of Resources.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/ResourceComparator.html" title="class in org.apache.tools.ant.types.resources.comparators">ResourceComparator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract Resource Comparator.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/ResourceContains.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceContains</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">&lt;resourcecontains&gt;
 Is a string contained in a resource (file currently)?</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs">ResourceCount</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Count resources from a ResourceCollection, storing to a property or
 writing to the log.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract class that delegates all reading methods of Resource to
 its wrapped resource and deals with reference handling.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/ResourceExists.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceExists</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition that checks whether a given resource exists.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">this interface should be implemented by classes (Scanners) needing
 to deliver information about resources.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/ResourceList.html" title="class in org.apache.tools.ant.types.resources">ResourceList</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reads a resource as text document and creates a resource for each
 line.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/ResourceLocation.html" title="class in org.apache.tools.ant.types">ResourceLocation</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class to handle the <code>&lt;dtd&gt;</code> and
 <code>&lt;entity&gt;</code> nested elements.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generic <a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a>: Either stores nested <a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a>s,
 making no attempt to remove duplicates, or references another <a href="org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types"><code>ResourceCollection</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for a Resource selector.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/ResourceSelectorContainer.html" title="class in org.apache.tools.ant.types.resources.selectors">ResourceSelectorContainer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceSelector container.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/ResourcesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourcesMatch</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares resources for equality based on size and content.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ResourceUtils.html" title="class in org.apache.tools.ant.util">ResourceUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class provides utility methods to process Resources.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/util/ResourceUtils.ReadOnlyTargetFileException.html" title="class in org.apache.tools.ant.util">ResourceUtils.ReadOnlyTargetFileException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/ResourceUtils.ResourceSelectorProvider.html" title="interface in org.apache.tools.ant.util">ResourceUtils.ResourceSelectorProvider</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Restrict.html" title="class in org.apache.tools.ant.types.resources">Restrict</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that allows a number of selectors to be
 applied to a single ResourceCollection for the purposes of
 restricting or narrowing results.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Retry.html" title="class in org.apache.tools.ant.taskdefs">Retry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Retries the nested task a set number of times</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Simple interface for executing a piece of code.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A simple utility class to take a piece of code (that implements
 <code>Retryable</code> interface) and executes that with possibility to
 retry the execution in case of IOException.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Reverse.html" title="class in org.apache.tools.ant.types.resources.comparators">Reverse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reverses another ResourceComparator.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/RExecTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Automates the rexec protocol.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Runs the rmic compiler against classes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/rmic/RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The interface that all rmic adapters must adhere to.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/RmicAdapterFactory.html" title="class in org.apache.tools.ant.taskdefs.rmic">RmicAdapterFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates the necessary rmic adapter, given basic criteria.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Rotate.html" title="class in org.apache.tools.ant.types.optional.image">Rotate</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ImageOperation to rotate an image by a certain degree</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Rotate.html" title="class in org.apache.tools.ant.types.optional.imageio">Rotate</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ImageOperation to rotate an image by a certain degree</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/Rpm.html" title="class in org.apache.tools.ant.taskdefs.optional">Rpm</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Invokes the rpm tool to build a Linux installation file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wrapper class that holds the attributes of an element, its children, and
 any text within it.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Scale.html" title="class in org.apache.tools.ant.types.optional.image">Scale</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Scale.html" title="class in org.apache.tools.ant.types.optional.imageio">Scale</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Scale.ProportionsAttribute.html" title="class in org.apache.tools.ant.types.optional.image">Scale.ProportionsAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated class for proportions attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Scale.ProportionsAttribute.html" title="class in org.apache.tools.ant.types.optional.imageio">Scale.ProportionsAttribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated class for proportions attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Validate XML Schema documents.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">representation of a schema location.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/Scp.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">Scp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Ant task for sending files to remote machine over ssh/scp.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpFromMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A helper object representing an scp download.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpFromMessageBySftp.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessageBySftp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A helper object representing an scp download.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpToMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessage</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class to carry out an upload scp transfer.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/ScpToMessageBySftp.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessageBySftp</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class to carry out an upload by sftp.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/Script.html" title="class in org.apache.tools.ant.taskdefs.optional">Script</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Executes a script.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/ScriptCommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">ScriptCommandLauncher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher that uses an auxiliary script to launch commands
 in directories other than the current working directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/ScriptCondition.html" title="class in org.apache.tools.ant.types.optional">ScriptCondition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A condition that lets you include script.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.html" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDef</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Defines a task using a script.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDef.Attribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing an attribute definition</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.NestedElement.html" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDef.NestedElement</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to represent a nested element definition</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDefBase.html" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDefBase</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The script execution class.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/ScriptFilter.html" title="class in org.apache.tools.ant.types.optional">ScriptFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Most of this is CAP (Cut And Paste) from the Script task
 ScriptFilter class, implements TokenFilter.Filter
 for scripts to use.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ScriptFixBSFPath.html" title="class in org.apache.tools.ant.util">ScriptFixBSFPath</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A class to modify a classloader to
 support BSF language support.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/ant/util/ScriptManager.html" title="enum class in org.apache.tools.ant.util">ScriptManager</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">Script manager <code>enum</code>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/ScriptMapper.html" title="class in org.apache.tools.ant.types.optional">ScriptMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Script support at map time.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/optional/ScriptRunner.html" title="class in org.apache.tools.ant.util.optional">ScriptRunner</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is used to run BSF scripts</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ScriptRunner.html" title="class in org.apache.tools.ant.util">ScriptRunner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">Implementation moved to another location.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ScriptRunnerBase.html" title="class in org.apache.tools.ant.util">ScriptRunnerBase</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a common abstract base case for script runners.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ScriptRunnerCreator.html" title="class in org.apache.tools.ant.util">ScriptRunnerCreator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a helper class used by ScriptRunnerHelper to
 create a ScriptRunner based on a classloader and on a language.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/ScriptRunnerHelper.html" title="class in org.apache.tools.ant.util">ScriptRunnerHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A class to help in creating, setting and getting script runners.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/ScriptSelector.html" title="class in org.apache.tools.ant.types.optional">ScriptSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that lets you run a script with selection logic inline</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/input/SecureInputHandler.html" title="class in org.apache.tools.ant.input">SecureInputHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prompts and requests input.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/SecurityManagerUtil.html" title="class in org.apache.tools.ant.util">SecurityManagerUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This is the interface for selectors that can contain other selectors.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">An interface used to describe the actions required by any type of
 directory scanner that supports Selectors.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SelectorUtils.html" title="class in org.apache.tools.ant.types.selectors">SelectorUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a utility class used by selectors and DirectoryScanner.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This selector just holds one other selector and forwards all
 requests to it.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SendEmail.html" title="class in org.apache.tools.ant.taskdefs">SendEmail</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A task to send SMTP email.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sequential is a container task - it can contain other Ant tasks.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/j2ee/ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">ServerDeploy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Controls hot deployment tools for J2EE servers.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/spi/Service.html" title="class in org.apache.tools.ant.types.spi">Service</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ANT Jar-Task SPI extension</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SetPermissions.html" title="class in org.apache.tools.ant.taskdefs">SetPermissions</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/nio/file/attribute/PosixFilePermission.html" title="class or interface in java.nio.file.attribute" class="external-link"><code>PosixFilePermission</code></a>s for resources.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/ant/taskdefs/SetPermissions.NonPosixMode.html" title="enum class in org.apache.tools.ant.taskdefs">SetPermissions.NonPosixMode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">Options for dealing with file systems that don't support POSIX
 permissions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/SetProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">SetProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets Java's web proxy properties, so that tasks and code run in
 the same JVM can have through-the-firewall access to remote web sites,
 and remote ftp sites.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SignedSelector.html" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that chooses files based on whether they are signed or not.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs">SignJar</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Signs JAR or ZIP files with the javasign command line tool.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/SilentLogger.html" title="class in org.apache.tools.ant.listener">SilentLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A logger which logs nothing but build failure and what task might output</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Displays subproject names like <a href="org/apache/tools/ant/listener/BigProjectLogger.html" title="class in org.apache.tools.ant.listener"><code>BigProjectLogger</code></a>
 but is otherwise as quiet as <a href="org/apache/tools/ant/NoBannerLogger.html" title="class in org.apache.tools.ant"><code>NoBannerLogger</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/helper/SingleCheckExecutor.html" title="class in org.apache.tools.ant.helper">SingleCheckExecutor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">"Single-check" Target executor implementation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/SingleTestClass.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">SingleTestClass</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the single <code>test</code> (class) that's configured to be launched by the <a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>JUnitLauncherTask</code></a></div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Size.html" title="class in org.apache.tools.ant.types.resources.comparators">Size</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares Resources by size.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Size.html" title="class in org.apache.tools.ant.types.resources.selectors">Size</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Size ResourceSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/SizeLimitCollection.html" title="class in org.apache.tools.ant.types.resources">SizeLimitCollection</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that imposes a size limit on another ResourceCollection.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that filters files based on their size.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SizeSelector.ByteUnits.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector.ByteUnits</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values for units.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SizeSelector.SizeComparisons.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector.SizeComparisons</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values for size comparison.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/compilers/Sj.html" title="class in org.apache.tools.ant.taskdefs.compilers">Sj</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the sj compiler.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs">Sleep</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sleep, or pause, for a period of time.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/mail/SmtpResponseReader.html" title="class in org.apache.tools.mail">SmtpResponseReader</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A wrapper around the raw input from the SMTP server that assembles
 multi line responses into a single String.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Condition to wait for a TCP/IP socket to have a listener.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Sort.html" title="class in org.apache.tools.ant.types.resources">Sort</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection that sorts another ResourceCollection.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/SortFilter.html" title="class in org.apache.tools.ant.filters">SortFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">
 Sort a file before and/or after the file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A base class for creating tasks for executing commands on SourceOffSite.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCheckin.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSCheckin</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Commits and unlocks files in Visual SourceSafe via a SourceOffSite server.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSCheckout</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Retrieves and locks files in Visual SourceSafe via a SourceOffSite server.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to hold constants used by the SOS tasks</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sos/SOSGet.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSGet</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Retrieves a read-only copy of the specified project or file
 from Visual SourceSafe via a SourceOffSite server.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSLabel</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Labels Visual SourceSafe files via a SourceOffSite server.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/sound/SoundTask.html" title="class in org.apache.tools.ant.taskdefs.optional.sound">SoundTask</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Plays a sound file at the end of the build, according to whether the build failed or succeeded.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/SourceFileScanner.html" title="class in org.apache.tools.ant.util">SourceFileScanner</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class that collects the functionality of the various
 scanDir methods that have been scattered in several tasks before.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/Specification.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Specification</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class that represents either an available "Optional Package"
 (formerly known as "Standard Extension") as described in the manifest
 of a JAR file, or the requirement for such an optional package.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html" title="class in org.apache.tools.ant.taskdefs.optional.splash">SplashTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a splash screen.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/SplitClassLoader.html" title="class in org.apache.tools.ant.util">SplitClassLoader</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Specialized classloader for tasks that need finer grained control
 over which classes are to be loaded via Ant's classloader and which
 should not even if they are available.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SQLExec.html" title="class in org.apache.tools.ant.taskdefs">SQLExec</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Executes a series of SQL statements on a database using JDBC.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">delimiters we support, "normal" and "row"</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The action a task should perform on an error,
 one of "continue", "stop" and "abort"</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHBase.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHBase</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for Ant tasks using jsch.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHExec.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHExec</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Executes a command on a remote machine via ssh.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHSession.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Establishes an ssh session with a remote machine, optionally
 establishing port forwarding, then executes any nested task(s)
 before closing the session.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHSession.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.NestedSequential</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The class corresponding to the sequential nested element.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ssh/SSHUserInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHUserInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class containing information on an SSH user.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/StandaloneLauncher.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher">StandaloneLauncher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used for launching forked tests from the <a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>JUnitLauncherTask</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Copies all data from an input stream to an output stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/StreamUtils.html" title="class in org.apache.tools.ant.util">StreamUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/StringCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">StringCPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A String Constant Pool Entry.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/StringInputStream.html" title="class in org.apache.tools.ant.filters">StringInputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wraps a String as an InputStream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/StringResource.html" title="class in org.apache.tools.ant.types.resources">StringResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Exposes a string as a Resource.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/StringTokenizer.html" title="class in org.apache.tools.ant.util">StringTokenizer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to tokenize the input as areas separated
 by white space, or by a specified list of
 delim characters.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/StringUtils.html" title="class in org.apache.tools.ant.util">StringUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A set of helper methods related to string manipulation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/StripJavaComments.html" title="class in org.apache.tools.ant.filters">StripJavaComments</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a Java comment and string stripper reader that filters
 those lexical tokens out for purposes of simple Java parsing.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/StripLineBreaks.html" title="class in org.apache.tools.ant.filters">StripLineBreaks</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Filter to flatten the stream to a single line.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/StripLineComments.html" title="class in org.apache.tools.ant.filters">StripLineComments</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This filter strips line comments.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/StripLineComments.Comment.html" title="class in org.apache.tools.ant.filters">StripLineComments.Comment</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The class that holds a comment representation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/SubAnt.html" title="class in org.apache.tools.ant.taskdefs">SubAnt</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Calls a given target for all defined sub-builds.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Instances of classes that implement this interface can register
 to be also notified when things happened during a subbuild.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/Substitution.html" title="class in org.apache.tools.ant.types">Substitution</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A regular expression substitution datatype.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/SuffixLines.html" title="class in org.apache.tools.ant.filters">SuffixLines</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Attaches a suffix to every line.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/SummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">SummaryJUnitResultFormatter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prints short summary output of the test to Ant's logging system.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/javah/SunJavah.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">SunJavah</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapter to com.sun.tools.javah.oldjavah.Main or com.sun.tools.javah.Main.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/native2ascii/SunNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">SunNative2Ascii</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adapter to sun.tools.native2ascii.Main.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/SunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">SunRmic</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the rmic for SUN's JDK.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/SymbolicLinkUtils.html" title="class in org.apache.tools.ant.util">SymbolicLinkUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">Starting Ant 1.10.2, this class is now deprecated in favour
              of the Java <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/nio/file/Files.html" title="class or interface in java.nio.file" class="external-link"><code>Files</code></a> APIs introduced in
              Java 7, for dealing with symbolic links</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/unix/Symlink.html" title="class in org.apache.tools.ant.taskdefs.optional.unix">Symlink</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates, Deletes, Records and Restores Symlinks.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/SymlinkSelector.html" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects symbolic links.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Synchronize a local target directory from the files defined
 in one or more filesets.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Sync.MyCopy.html" title="class in org.apache.tools.ant.taskdefs">Sync.MyCopy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Subclass Copy in order to access it's file/dir maps.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs">Sync.SyncTarget</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Inner class used to hold exclude patterns and selectors to save
 stuff that happens to live in the target directory but should
 not get removed.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TabsToSpaces.html" title="class in org.apache.tools.ant.filters">TabsToSpaces</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts tabs to spaces.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TailFilter.html" title="class in org.apache.tools.ant.filters">TailFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reads the last <code>n</code> lines of a stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Creates a tar archive.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarCompressionMethod</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Valid Modes for Compression attribute to Tar Task</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarFileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a FileSet with the option to specify permissions
 and other attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarLongFileMode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Set of options for long file handling in the task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/tar/TarArchiveSparseEntry.html" title="class in org.apache.tools.tar">TarArchiveSparseEntry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class represents a sparse entry in a Tar archive.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The TarBuffer class implements the tar archive concept
 of a buffered input stream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This interface contains all the definitions used in the package.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class represents an entry in a Tar archive.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A TarFileSet is a FileSet with extra attributes useful in the context of
 Tar/Jar tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to implement a target object with required parameters.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/tar/TarInputStream.html" title="class in org.apache.tools.tar">TarInputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The TarInputStream reads a UNIX tar archive as an InputStream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/tar/TarOutputStream.html" title="class in org.apache.tools.tar">TarOutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The TarOutputStream writes a UNIX tar archive as an OutputStream.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/TarResource.html" title="class in org.apache.tools.ant.types.resources">TarResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Resource representation of an entry in a tar archive.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/TarScanner.html" title="class in org.apache.tools.ant.types">TarScanner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Scans tar archives for resources.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/tar/TarUtils.html" title="class in org.apache.tools.tar">TarUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class provides static utility methods to work with byte streams.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for all tasks.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/TaskAdapter.html" title="class in org.apache.tools.ant">TaskAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Uses introspection to "adapt" an arbitrary Bean which doesn't
 itself extend Task, but still contains an execute method and optionally
 a setProject method.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/TaskConfigurationChecker.html" title="class in org.apache.tools.ant">TaskConfigurationChecker</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper class for the check of the configuration of a given task.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for objects which can contain tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Taskdef.html" title="class in org.apache.tools.ant.taskdefs">Taskdef</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds a task definition to the current project, such that this new task can be
 used in the current project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/TaskLogger.html" title="class in org.apache.tools.ant.util">TaskLogger</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A facade that makes logging nicer to use.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs">TaskOutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.2.x.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/TearDownOnVmCrash.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">TearDownOnVmCrash</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Formatter that doesn't create any output but tries to invoke the
 tearDown method on a testcase if that test was forked and caused a
 timeout or VM crash.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/TeeOutputStream.html" title="class in org.apache.tools.ant.util">TeeOutputStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A simple T-piece to replicate an output stream into two separate streams</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Automates the telnet protocol.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/TempFile.html" title="class in org.apache.tools.ant.taskdefs">TempFile</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This task sets a property to the name of a temporary file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/TestClasses.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestClasses</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a <code>testclasses</code> that's configured to be launched by the <a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>JUnitLauncherTask</code></a></div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/TestDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the configuration details of a test that needs to be launched by the <a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>JUnitLauncherTask</code></a></div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/TestDefinition.ForkedRepresentation.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestDefinition.ForkedRepresentation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestExecutionContext</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">A <a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestExecutionContext</code></a> represents the execution context for a test
 that has been launched by the <a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>JUnitLauncherTask</code></a> and provides any necessary
 contextual information about such tests.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/TestIgnored.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestIgnored</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/TestListenerWrapper.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestListenerWrapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junitlauncher/TestResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestResultFormatter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">A <code>TestExecutionListener</code> which lets implementing classes
 format and write out the test execution results.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/Text.html" title="class in org.apache.tools.ant.types.optional.image">Text</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/Text.html" title="class in org.apache.tools.ant.types.optional.imageio">Text</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/TimeComparison.html" title="class in org.apache.tools.ant.types">TimeComparison</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">EnumeratedAttribute for time comparisons.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for classes that want to be notified by Watchdog.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/listener/TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Like a normal logger, except with timed outputs</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This splits up input into tokens and passes
 the tokens to a sequence of filters.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.ChainableReaderFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract class that converts derived filter classes into
 ChainableReaderFilter's</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">filter to filter tokens matching regular expressions.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.ContainsString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple filter to filter lines contains strings</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Filter to delete characters</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.FileTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.FileTokenizer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">class to read the complete input into a string</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/filters/TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">string filters implement this interface</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Filter remove empty tokens</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">filter to replace regex.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple replace string filter.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.StringTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.StringTokenizer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">class to tokenize the input as areas separated
 by white space, or by a specified list of
 delim characters.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Filter to trim white space</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/TokenizedPath.html" title="class in org.apache.tools.ant.types.selectors">TokenizedPath</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Container for a path that has been split into its components.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/TokenizedPattern.html" title="class in org.apache.tools.ant.types.selectors">TokenizedPattern</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Provides reusable path pattern matching.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/util/Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">input stream tokenizers implement this interface</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Tokens.html" title="class in org.apache.tools.ant.types.resources">Tokens</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection consisting of StringResources gathered from tokenizing
 another ResourceCollection with a Tokenizer implementation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs">Touch</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Touch a file and/or fileset(s) and/or filelist(s);
 corresponds to the Unix touch command.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/Touch.DateFormatFactory.html" title="interface in org.apache.tools.ant.taskdefs">Touch.DateFormatFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/resources/Touchable.html" title="interface in org.apache.tools.ant.types.resources">Touchable</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to be implemented by "touchable" resources;
 that is, those whose modification time can be altered.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Transform.html" title="class in org.apache.tools.ant.taskdefs">Transform</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Has been merged into ExecuteOn, empty class for backwards compatibility.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/image/TransformOperation.html" title="class in org.apache.tools.ant.types.optional.image">TransformOperation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/imageio/TransformOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">TransformOperation</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/i18n/Translate.html" title="class in org.apache.tools.ant.taskdefs.optional.i18n">Translate</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Translates text embedded in files using Resource Bundle files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/TraXLiaison.html" title="class in org.apache.tools.ant.taskdefs.optional">TraXLiaison</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Concrete liaison for XSLT processor implementing TraX.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Truncate.html" title="class in org.apache.tools.ant.taskdefs">Truncate</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Set the length of one or more files, as the intermittently available
 <code>truncate</code> Unix utility/function.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs">Tstamp</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets properties to the current time, or offsets from the current time.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Tstamp.Unit.html" title="class in org.apache.tools.ant.taskdefs">Tstamp.Unit</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">set of valid units to use for time offsets.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/comparators/Type.html" title="class in org.apache.tools.ant.types.resources.comparators">Type</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compares Resources by is-directory status.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Type.html" title="class in org.apache.tools.ant.types.resources.selectors">Type</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Type file/dir ResourceSelector.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/selectors/Type.FileDir.html" title="class in org.apache.tools.ant.types.resources.selectors">Type.FileDir</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implements the type attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant">TypeAdapter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Used to wrap types.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Typedef.html" title="class in org.apache.tools.ant.taskdefs">Typedef</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds a data type definition to the current project.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/TypeFound.html" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">looks for a task or other Ant type that exists.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Selector that selects a certain kind of file: directory or regular.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/TypeSelector.FileType.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector.FileType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Enumerated attribute with the values for types of file</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/UnicodeCommentExtraField.html" title="class in org.apache.tools.zip">UnicodeCommentExtraField</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Info-ZIP Unicode Comment Extra Field (0x6375):</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/UnicodePathExtraField.html" title="class in org.apache.tools.zip">UnicodePathExtraField</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Info-ZIP Unicode Path Extra Field (0x7075):</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/UnicodeUtil.html" title="class in org.apache.tools.ant.util">UnicodeUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Contains one helper method to create a backslash u escape</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/Union.html" title="class in org.apache.tools.ant.types.resources">Union</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">ResourceCollection representing the union of multiple nested ResourceCollections.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/filters/UniqFilter.html" title="class in org.apache.tools.ant.filters">UniqFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Like the Unix uniq(1) command, only returns tokens that are
 different from their ancestor token.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/zip/UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Constants from stat.h on Unix systems.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wrapper class that holds all the information necessary to create a task
 or data type that did not exist when Ant started, or one which
 has had its definition updated to use a different implementation class.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Unpack.html" title="class in org.apache.tools.ant.taskdefs">Unpack</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract Base class for unpack tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/UnPackageNameMapper.html" title="class in org.apache.tools.ant.util">UnPackageNameMapper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Maps dotted package name matches to a directory name.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/UnparseableExtraFieldData.html" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wrapper for extra field data that doesn't conform to the recommended format of header-tag + size + data.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/UnrecognizedExtraField.html" title="class in org.apache.tools.zip">UnrecognizedExtraField</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Simple placeholder for all those extra fields we don't want to deal
 with.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant">UnsupportedAttributeException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Used to report attempts to set an unsupported attribute</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/ant/UnsupportedElementException.html" title="class in org.apache.tools.ant">UnsupportedElementException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Used to report attempts to set an unsupported element
 When the attempt to set the element is made,
 the code does not not know the name of the task/type
 based on a mapping from the classname to the task/type.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/zip/UnsupportedZipFeatureException.html" title="class in org.apache.tools.zip">UnsupportedZipFeatureException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Exception thrown when attempting to read or write data for a zip
 entry that uses ZIP features not supported by this library.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/UnsupportedZipFeatureException.Feature.html" title="class in org.apache.tools.zip">UnsupportedZipFeatureException.Feature</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ZIP Features that may or may not be supported.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Untar.html" title="class in org.apache.tools.ant.taskdefs">Untar</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Untar a file.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Untar.UntarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs">Untar.UntarCompressionMethod</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Valid Modes for Compression attribute to Untar Task</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/xz/Unxz.html" title="class in org.apache.tools.ant.taskdefs.optional.xz">Unxz</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Expands a file that has been compressed with the XZ
 algorithm.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets the given property if the specified target has a timestamp
 greater than all of the source files.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/types/resources/URLProvider.html" title="interface in org.apache.tools.ant.types.resources">URLProvider</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This is an interface that resources that can provide an URL should implement.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/extension/resolvers/URLResolver.html" title="class in org.apache.tools.ant.taskdefs.optional.extension.resolvers">URLResolver</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Resolver that just returns s specified location.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/URLResource.html" title="class in org.apache.tools.ant.types.resources">URLResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Exposes a URL as a Resource.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/depend/constantpool/Utf8CPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">Utf8CPInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A UTF8 Constant Pool Entry.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/UUEncoder.html" title="class in org.apache.tools.ant.util">UUEncoder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">UUEncoding of an input stream placed into an OutputStream.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/VectorSet.html" title="class in org.apache.tools.ant.util">VectorSet&lt;E&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Subclass of Vector that won't store duplicate entries and shows
 HashSet's constant time performance characteristics for the
 contains method.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs">VerifyJar</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">JAR verification task.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/VmsCommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">VmsCommandLauncher</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher for VMS that writes the command to a temporary
 DCL script before launching commands.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/WaitFor.html" title="class in org.apache.tools.ant.taskdefs">WaitFor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wait for an external event to occur.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The enumeration of units:
 millisecond, second, minute, hour, day, week</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/War.html" title="class in org.apache.tools.ant.taskdefs">War</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An extension of &lt;jar&gt; to create a WAR archive.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/Watchdog.html" title="class in org.apache.tools.ant.util">Watchdog</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generalization of <code>ExecuteWatchdog</code></div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/WeakishReference.html" title="class in org.apache.tools.ant.util">WeakishReference</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">deprecated 1.7; will be removed in Ant1.8
             Just use <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ref/WeakReference.html" title="class or interface in java.lang.ref" class="external-link"><code>WeakReference</code></a> directly.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/WeakishReference.HardReference.html" title="class in org.apache.tools.ant.util">WeakishReference.HardReference</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.7.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/optional/WeakishReference12.html" title="class in org.apache.tools.ant.util.optional">WeakishReference12</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">since 1.7.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The weblogic element is used to control the weblogic.ejbc compiler for
    generating WebLogic EJB jars.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/j2ee/WebLogicHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">WebLogicHotDeploymentTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Ant wrapper task for the weblogic.deploy tool.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Deployment tool for WebLogic TOPLink.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">WebSphere deployment tool that augments the ejbjar task.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs">WhichResource</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Find a class or resource on the supplied classpath, or the
 system classpath if none is supplied.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/launcher/WinNTCommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher">WinNTCommandLauncher</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A command launcher for Windows XP/2000/NT that uses 'cmd.exe' when
 launching commands in directories other than the current working
 directory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/jsp/WLJspc.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">WLJspc</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Precompiles JSP's using WebLogic's JSP compiler (weblogic.jspc).</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/WLRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">WLRmic</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The implementation of the rmic for WebLogic</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/WorkerAnt.html" title="class in org.apache.tools.ant.util">WorkerAnt</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A worker ant executes a single task in a background thread.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/selectors/WritableSelector.html" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A selector that selects writable files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/Xalan2TraceSupport.html" title="class in org.apache.tools.ant.taskdefs.optional">Xalan2TraceSupport</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Sets up trace support for a given transformer.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This data type provides a catalog of resource locations (such as
 DTDs and XML entities), based on the <a href="https://oasis-open.org/committees/entity/spec-2001-08-06.html">
 OASIS "Open Catalog" standard</a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/XmlConstants.html" title="class in org.apache.tools.ant.util">XmlConstants</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">XML Parser constants, all kept in one place for ease of reuse</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface groups XML constants.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/util/XMLFragment.html" title="class in org.apache.tools.ant.util">XMLFragment</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Use this class as a nested element if you want to get a literal DOM
 fragment of something nested into your task/type.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/XMLJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLJUnitResultFormatter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Prints XML output of the test to a specified Writer.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/XmlLogger.html" title="class in org.apache.tools.ant">XmlLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates a file in the current directory with
 an XML description of what happened during a build.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XmlProperty.html" title="class in org.apache.tools.ant.taskdefs">XmlProperty</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Loads property values from a valid XML file, generating the
 property names from the file's element and attribute names.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/junit/XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Aggregates all &lt;junit&gt; XML formatter testsuite data under
 a specific directory and transforms the results via XSLT.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Checks XML files are valid (or only well formed).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The class to create to set a feature of the parser.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Parser property.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/rmic/XNewRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">XNewRmic</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Run rmic in a new process with -Xnew set.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/condition/Xor.html" title="class in org.apache.tools.ant.taskdefs.condition">Xor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The <code>Xor</code> condition type to exclusive or operations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Proxy interface for XSLT processors.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Extended Proxy interface for XSLT processors.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Extends Proxy interface for XSLT processors.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Extends Proxy interface for XSLT processors: adds support for XSLT parameters
 of various types (not only String)</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface to log messages for XSLT</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for a class that one can set an XSLTLogger on.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Processes a set of XML documents via XSLT.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The factory element to configure a transformer factory</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.Factory.Attribute.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Attribute</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A JAXP factory attribute.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.Factory.Feature.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Feature</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A feature for the TraX factory.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Specify how the result tree should be output as specified
 in the <a href="https://www.w3.org/TR/xslt#output">
 specification</a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The Param inner class used to store XSL parameters</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.ParamType.html" title="enum class in org.apache.tools.ant.taskdefs">XSLTProcess.ParamType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">Enum for types of the parameter expression.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/ant/taskdefs/optional/XSLTTraceSupport.html" title="interface in org.apache.tools.ant.taskdefs.optional">XSLTTraceSupport</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Sets up trace support for a given transformer.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/optional/xz/Xz.html" title="class in org.apache.tools.ant.taskdefs.optional.xz">Xz</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Compresses a file with the XZ algorithm.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/optional/xz/XzResource.html" title="class in org.apache.tools.ant.types.optional.xz">XzResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A XZ compressed resource.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Create a Zip file.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds the up-to-date status and the out-of-date resources of
 the original archive.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Possible behaviors when a duplicate file is added:
 "add", "preserve" or "fail"</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Policy for creation of Unicode extra fields: never, always or
 not-encodeable.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Possible behaviors when there are no matching files for the task:
 "fail", "skip", or "create".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/taskdefs/Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The choices for Zip64 extensions.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/Zip64ExtendedInformationExtraField.html" title="class in org.apache.tools.zip">Zip64ExtendedInformationExtraField</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds size and other extended information for entries that use Zip64
 features.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="org/apache/tools/zip/Zip64Mode.html" title="enum class in org.apache.tools.zip">Zip64Mode</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">The different modes <a href="org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip"><code>ZipOutputStream</code></a> can operate in.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="org/apache/tools/zip/Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Exception thrown when attempting to write data that requires Zip64
 support to an archive and <a href="org/apache/tools/zip/ZipOutputStream.html#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>UseZip64</code></a> has been set to <a href="org/apache/tools/zip/Zip64Mode.html#Never"><code>Never</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class that represents an eight byte integer with conversion
 rules for the big endian byte order of ZIP files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/zip/ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">An interface for encoders that do a pretty encoding of ZIP
 filenames.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipEncodingHelper.html" title="class in org.apache.tools.zip">ZipEncodingHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Static helper functions for robustly encoding filenames in zip files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Extension that adds better handling of extra fields and provides
 access to the internal and external file attributes.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">General format of extra field data.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipFile.html" title="class in org.apache.tools.zip">ZipFile</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Replacement for <code>java.util.ZipFile</code>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A ZipFileSet is a FileSet with extra attributes useful in the context of
 Zip/Jar tasks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class that represents a four byte integer with conversion
 rules for the big endian byte order of ZIP files.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Reimplementation of <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>java.util.zip.ZipOutputStream</code></a> that does handle the extended
 functionality of this package, especially internal/external file
 attributes and extra fields with different layouts for local file
 data and central directory entries.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipOutputStream.UnicodeExtraFieldPolicy.html" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">enum that represents the possible policies for creating Unicode
 extra fields.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/resources/ZipResource.html" title="class in org.apache.tools.ant.types.resources">ZipResource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Resource representation of an entry in a zipfile.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/ant/types/ZipScanner.html" title="class in org.apache.tools.ant.types">ZipScanner</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Scans zip archives for resources.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class that represents a two byte integer with conversion
 rules for the big endian byte order of ZIP files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/apache/tools/zip/ZipUtil.html" title="class in org.apache.tools.zip">ZipUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for handling DOS and Java time conversions.</div>
</div>
</div>
</div>
</div>
</main>
</body>
</html>
