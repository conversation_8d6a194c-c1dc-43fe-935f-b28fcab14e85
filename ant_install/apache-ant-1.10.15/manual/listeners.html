<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Listeners &amp; Loggers</title>
</head>

<body>
<h1>Listeners &amp; Loggers</h1>

<h2 id="Overview">Overview</h2>

<p>Apache Ant has two related features to allow the build process to be monitored: listeners and
loggers.</p>

<h3 id="Listeners">Listeners</h3>

<p>A listener is alerted of the following events:</p>

<ul>
  <li>build started</li>
  <li>build finished</li>
  <li>target started</li>
  <li>target finished</li>
  <li>task started</li>
  <li>task finished</li>
  <li>message logged</li>
</ul>

<p>These are used internally for various recording and housekeeping operations, however new
listeners may registered on the command line through the <kbd>-listener</kbd> argument.</p>

<h3 id="Loggers">Loggers</h3>

<p>Loggers extend the capabilities of listeners and add the following features:</p>

<ul>
  <li>Receives a handle to the standard output and error print streams and therefore can log
    information to the console or the <kbd>-logfile</kbd> specified file.</li>
  <li>Logging level (<kbd>-quiet</kbd>, <kbd>-verbose</kbd>, <kbd>-debug</kbd>) aware</li>
  <li>Emacs-mode aware</li>
</ul>

<h2 id="builtin">Built-in Listeners/Loggers</h2>

<table>
  <tr>
    <th scope="col">Classname</th>
    <th scope="col">Description</th>
    <th scope="col">Type</th>
  </tr>
  <tr>
    <td><code><a href="#DefaultLogger">org.apache.tools.ant.DefaultLogger</a></code></td>
    <td>The logger used implicitly unless overridden with the <kbd>-logger</kbd> command-line
      switch.</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#NoBannerLogger">org.apache.tools.ant.NoBannerLogger</a></code></td>
    <td>This logger omits output of empty target output.</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#MailLogger">org.apache.tools.ant.listener.MailLogger</a></code></td>
    <td>Extends DefaultLogger such that output is still generated the same, and when the build is
      finished an e-mail can be sent.</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#AnsiColorLogger">org.apache.tools.ant.listener.AnsiColorLogger</a></code></td>
    <td>Colorifies the build output.</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#Log4jListener">org.apache.tools.ant.listener.Log4jListener</a></code></td>
    <td>Passes events to Apache Log4j for highly customizable
      logging.<br/><em><u>Deprecated</u></em>: Apache Log4j (1.x) is not developed any more. Last
      release is 1.2.17 from 26 May 2012 and contains vulnerability issues.</td>
    <td>BuildListener</td>
  </tr>
  <tr>
    <td><code><a href="#XmlLogger">org.apache.tools.ant.XmlLogger</a></code></td>
    <td>Writes the build information to an XML file.</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#TimestampedLogger">org.apache.tools.ant.TimestampedLogger</a></code></td>
    <td>Prints the time that a build finished</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#BigProjectLogger">org.apache.tools.ant.listener.BigProjectLogger</a></code></td>
    <td>Prints the project name every target</td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#SimpleBigProjectLogger">org.apache.tools.ant.listener.SimpleBigProjectLogger</a></code></td>
    <td>Prints the project name for subprojects only, otherwise like NoBannerLogger <em>Since Ant
    1.8.1</em></td>
    <td>BuildLogger</td>
  </tr>
  <tr>
    <td><code><a href="#ProfileLogger">org.apache.tools.ant.listener.ProfileLogger</a></code></td>
    <td>The default logger, with start times, end times and durations added for each task and
      target.</td>
    <td>BuildLogger</td>
  </tr>
</table>

<h3 id="DefaultLogger">DefaultLogger</h3>
<p>Simply run Ant normally, or:</p>

<pre class="input">ant -logger org.apache.tools.ant.DefaultLogger</pre>

<h3 id="NoBannerLogger">NoBannerLogger</h3>
<p>Removes output of empty target output.</p>

<pre class="input">ant -logger org.apache.tools.ant.NoBannerLogger</pre>

<h3 id="MailLogger">MailLogger</h3>
<p>The MailLogger captures all output logged through DefaultLogger (standard Ant output) and will
send success and failure messages to unique e-mail lists, with control for turning off success or
failure messages individually.</p>

<p>Properties controlling the operation of MailLogger:</p>
<table>
  <tr>
    <th scope="col">Property</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td><code>MailLogger.mailhost</code></td>
    <td>Mail server to use</td>
    <td>No; default <q>localhost</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.port</code></td>
    <td>SMTP Port for the Mail server</td>
    <td>No; default <q>25</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.user</code></td>
    <td>user name for SMTP auth</td>
    <td>Yes, if SMTP auth is required on your SMTP server<br/> the email message will be then sent
      using MIME and requires JavaMail</td>
  </tr>
  <tr>
    <td><code>MailLogger.password</code></td>
    <td>password for SMTP auth</td>
    <td>Yes, if SMTP auth is required on your SMTP server<br/> the email message will be then sent
      using MIME and requires JavaMail</td>
  </tr>
  <tr>
    <td><code>MailLogger.ssl</code></td>
    <td>on or true if SSL is needed<br/>This feature requires JavaMail</td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.from</code></td>
    <td>Mail <q>from</q> address</td>
    <td>Yes, if mail needs to be sent</td>
  </tr>
  <tr>
    <td><code>MailLogger.replyto</code></td>
    <td>Mail <q>replyto</q> address(es), comma-separated</td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.failure.notify</code></td>
    <td>Send build failure e-mails?</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.success.notify</code></td>
    <td>Send build success e-mails?</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.failure.to</code></td>
    <td>Address(es) to send failure messages to, comma-separated</td>
    <td>Yes, if failure mail is to be sent</td>
  </tr>
  <tr>
    <td><code>MailLogger.success.to</code></td>
    <td>Address(es) to send success messages to, comma-separated</td>
    <td>Yes, if success mail is to be sent</td>
  </tr>
  <tr>
    <td><code>MailLogger.failure.cc</code></td>
    <td>Address(es) to send failure messages to carbon copy (cc), comma-separated</td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.success.cc</code></td>
    <td>Address(es) to send success messages to carbon copy (cc), comma-separated</td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.failure.bcc</code></td>
    <td>Address(es) to send failure messages to blind carbon copy (bcc), comma-separated</td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.success.bcc</code></td>
    <td>Address(es) to send success messages to blind carbon copy (bcc), comma-separated</td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.failure.subject</code></td>
    <td>Subject of failed build</td>
    <td>No; default <q>Build Failure</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.success.subject</code></td>
    <td>Subject of successful build</td>
    <td>No; default <q>Build Success</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.failure.body</code></td>
    <td>Fixed body of the email for a failed build.  <em>Since Ant 1.8.0</em></td>
    <td>No; default is to send the full log output</td>
  </tr>
  <tr>
    <td><code>MailLogger.success.body</code></td>
    <td>Fixed body of the email for a successful build.  <em>Since Ant 1.8.0</em></td>
    <td>No; default is to send the full log output</td>
  </tr>
  <tr>
    <td><code>MailLogger.mimeType</code></td>
    <td>MIME-Type of the message.  <em>Since Ant 1.8.0</em></td>
    <td>No; default is <q>text/plain</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.charset</code></td>
    <td>Character set of the message.  <em>Since Ant 1.8.0</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td><code>MailLogger.starttls.enable</code></td>
    <td>on or true if <code>STARTTLS</code> should be supported (requires JavaMail).  <em>Since Ant
      1.8.0</em></td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td><code>MailLogger.properties.file</code></td>
    <td>Filename of properties file that will override other values.</td>
    <td>No</td>
  </tr>
</table>

<pre class="input">ant -logger org.apache.tools.ant.listener.MailLogger</pre>

<h3 id="AnsiColorLogger">AnsiColorLogger</h3>

<p>The AnsiColorLogger adds color to the standard Ant output by prefixing and suffixing ANSI color
code escape sequences to it.  It is just an extension of <a href="#DefaultLogger">DefaultLogger</a>
and hence provides all features that DefaultLogger does.</p>
<p>AnsiColorLogger differentiates the output by assigning different colors depending upon the type
of the message.</p>
<p>If used with the <kbd>-logfile</kbd> option, the output file will contain all the necessary
escape codes to display the text in colorized mode when displayed in the console using applications
like <code>cat</code>, <code>more</code>, etc.</p>
<p>This is designed to work on terminals that support ANSI color codes.  It works on XTerm, ETerm,
Win9x Console (with <code>ANSI.SYS</code> loaded.), etc.</p>
<p><strong>Note</strong>: It doesn't work on WinNT and successors, even when
a <code>COMMAND.COM</code> console loaded with <code>ANSI.SYS</code> is used.</p>
<p>If the user wishes to override the default colors with custom ones, a file containing zero or
more of the custom color key-value pairs must be created.  The recognized keys and their default
values are shown below:</p>
<pre>
AnsiColorLogger.ERROR_COLOR=2;31
AnsiColorLogger.WARNING_COLOR=2;35
AnsiColorLogger.INFO_COLOR=2;36
AnsiColorLogger.VERBOSE_COLOR=2;32
AnsiColorLogger.DEBUG_COLOR=2;34</pre>
<p>Each key takes as value a color combination defined as <q>Attribute;Foreground;Background</q>.
In the above example, background value has not been used.</p>
<p>This file must be specified as the value of a system variable
named <code>ant.logger.defaults</code> and passed as an argument using the <kbd>-D</kbd> option to
the <kbd>java</kbd> command that invokes the Ant application. An easy way to achieve this is to
add <kbd>-Dant.logger.defaults=</kbd><samp class="input">/path/to/your/file</samp> to
the <code>ANT_OPTS</code> environment variable. Ant's launching script recognizes this flag and will
pass it to the <kbd>java</kbd> command appropriately.</p>
<p>Format:</p>
<pre>
AnsiColorLogger.*=Attribute;Foreground;Background

Attribute is one of the following:
0 &rarr; Reset All Attributes (return to normal mode)
1 &rarr; Bright (Usually turns on BOLD)
2 &rarr; Dim
3 &rarr; Underline
5 &rarr; link
7 &rarr; Reverse
8 &rarr; Hidden

Foreground is one of the following:
30 &rarr; Black
31 &rarr; Red
32 &rarr; Green
33 &rarr; Yellow
34 &rarr; Blue
35 &rarr; Magenta
36 &rarr; Cyan
37 &rarr; White

Background is one of the following:
40 &rarr; Black
41 &rarr; Red
42 &rarr; Green
43 &rarr; Yellow
44 &rarr; Blue
45 &rarr; Magenta
46 &rarr; Cyan
47 &rarr; White</pre>

<pre class="input">ant -logger org.apache.tools.ant.listener.AnsiColorLogger</pre>

<h3 id="Log4jListener">Log4jListener</h3>
<p><em><u>Deprecated</u></em>: Apache Log4j (1) is not developed any more. Last release is 1.2.17
from 26 May 2012 and contains vulnerability issues.</p>
<p>Passes build events to Log4j, using the full classname's of the generator of each build event as
the category:</p>
<ul>
  <li>build started / build finished&mdash;<code class="code">org.apache.tools.ant.Project</code></li>
  <li>target started / target finished&mdash;<code class="code">org.apache.tools.ant.Target</code></li>
  <li>task started / task finished&mdash;the fully qualified classname of the task</li>
  <li>message logged&mdash;the classname of one of the above, so if a task logs a message, its
    classname is the category used, and so on.</li>
</ul>
<p>All start events are logged as INFO. Finish events are either logged as INFO or ERROR depending
on whether the build failed during that stage. Message events are logged according to their Ant
logging level, mapping directly to a corresponding Log4j level.</p>

<pre class="input">ant -listener org.apache.tools.ant.listener.Log4jListener</pre>

<p>To use Log4j you will need the Log4j JAR file and a <samp>log4j.properties</samp> configuration
file.  Both should be placed somewhere in your Ant classpath. If the <samp>log4j.properties</samp>
is in your project root folder you can add this with <kbd>-lib</kbd> option:</p>

<pre class="input">ant -listener org.apache.tools.ant.listener.Log4jListener -lib .</pre>

<p>If, for example, you wanted to capture the same information output to the console by the
DefaultLogger and send it to a file named <samp>build.log</samp>, you could use the following
configuration:</p>

<pre>
log4j.rootLogger=ERROR, LogFile
log4j.logger.org.apache.tools.ant.Project=INFO
log4j.logger.org.apache.tools.ant.Target=INFO
log4j.logger.org.apache.tools.ant.taskdefs=INFO
log4j.logger.org.apache.tools.ant.taskdefs.Echo=WARN

log4j.appender.LogFile=org.apache.log4j.FileAppender
log4j.appender.LogFile.layout=org.apache.log4j.PatternLayout
log4j.appender.LogFile.layout.ConversionPattern=[%6r] %8c{1} : %m%n
log4j.appender.LogFile.file=build.log</pre>

<p>For more information about configuring Log4J see <a href="https://logging.apache.org/log4j/1.2/"
target="_top">its documentation page</a>.</p>

<h4>Using the Log4j 1.2 Bridge</h4>
<p>You could use the <a href="https://logging.apache.org/log4j/2.x/log4j-1.2-api/index.html"
target="_top">Log4j Bridge</a> if your application is written against the Log4j (1.x) API, but you
want to use the Log4j 2.x runtime.  For using the bridge with Ant you have to add</p>
<ul>
  <li><samp>log4j-1.2-api-${log4j.version}.jar</samp></li>
  <li><samp>log4j-api-${log4j.version}.jar</samp></li>
  <li><samp>log4j-core-${log4j.version}.jar</samp></li>
  <li><samp>log4j2.xml</samp></li>
</ul>
<p>to your classpath, e.g. via the <kbd>-lib</kbd> option.  (For using the bridge, Ant
1.9.10/1.10.2 or higher is required.)  Translating the 1.x properties file into the 2.x XML syntax
would result in</p>
<pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;Configuration status=&quot;WARN&quot;&gt;
  &lt;Appenders&gt;
    &lt;File name=&quot;file&quot; fileName=&quot;build.log&quot;&gt;
      &lt;PatternLayout&gt;
        &lt;Pattern&gt;[%6r] %8c{1} : %m%n&lt;/Pattern&gt;
      &lt;/PatternLayout&gt;
    &lt;/File&gt;
  &lt;/Appenders&gt;
  &lt;Loggers&gt;
    &lt;Root level=&quot;ERROR&quot;&gt;
      &lt;AppenderRef ref=&quot;file&quot; level=&quot;DEBUG&quot;/&gt;
    &lt;/Root&gt;
    &lt;Logger name=&quot;org.apache.tools.ant.Project&quot; level=&quot;INFO&quot;/&gt;
    &lt;Logger name=&quot;org.apache.tools.ant.Project&quot; level=&quot;INFO&quot;/&gt;
    &lt;Logger name=&quot;org.apache.tools.ant.taskdefs&quot; level=&quot;INFO&quot;/&gt;
    &lt;Logger name=&quot;org.apache.tools.ant.taskdefs.Echo&quot; level=&quot;WARN&quot;/&gt;
  &lt;/Loggers&gt;
&lt;/Configuration&gt;</pre>

<h3 id="XmlLogger">XmlLogger</h3>
<p>Writes all build information out to an XML file named <samp>log.xml</samp>, or the value of
the <code>XmlLogger.file</code> property if present, when used as a listener. When used as a logger,
it writes all output to either the console or to the value of <kbd>-logfile</kbd>. Whether used as
a listener or logger, the output is not generated until the build is complete, as it buffers the
information in order to provide timing information for task, targets, and the project.</p>
<p>By default the XML file creates a reference to an XSLT file <samp>log.xsl</samp> in the current
directory; look in <samp>ANT_HOME/etc</samp> for one of these. You can set the
property <code>ant.XmlLogger.stylesheet.uri</code> to provide a URI to a style sheet. This can be a
relative or absolute file path, or an HTTP URL. If you set the property to the empty
string, <q></q>, no XSLT transform is declared at all.</p>

<pre class="input">ant -listener org.apache.tools.ant.XmlLogger
ant -logger org.apache.tools.ant.XmlLogger -verbose -logfile build_log.xml</pre>

<h3 id="TimestampedLogger">TimestampedLogger</h3>
<p>Acts like the default logger, except that the final success/failure message also includes the
time that the build completed. For example:</p>
<pre class="output">BUILD SUCCESSFUL - at 16/08/05 16:24</pre>
<p>To use this listener, use the command:</p>

<pre class="input">ant -logger org.apache.tools.ant.listener.TimestampedLogger</pre>

<h3 id="BigProjectLogger">BigProjectLogger</h3>
<p>This logger is designed to make examining the logs of a big build easier, especially those run
under continuous integration tools. It</p>
<ol>
  <li>When entering a child project, prints its name and directory</li>
  <li>When exiting a child project, prints its name</li>
  <li>Includes the name of the project when printing a target</li>
  <li>Omits logging the names of all targets that have no direct task output</li>
  <li>Includes the build finished timestamp of the TimeStamp logger</li>
</ol>
<p>This is useful when using <code>&lt;subant&gt;</code> to build a large project from many smaller
projects&mdash;the output shows which particular project is building. Here is an example in which
"clean" is being called on all a number of child projects, only some of which perform work:</p>
<pre class="output">
======================================================================
Entering project "xunit"
In /home/<USER>/components/xunit
======================================================================

xunit.clean:
   [delete] Deleting directory /home/<USER>/components/xunit/build
   [delete] Deleting directory /home/<USER>/components/xunit/dist

======================================================================
Exiting project "xunit"
======================================================================

======================================================================
Entering project "junit"
In /home/<USER>/components/junit
======================================================================

======================================================================
Exiting project "junit"
======================================================================</pre>

<p>The entry and exit messages are very verbose in this example, but in a big project compiling or
testing many child components, the messages are reduced to becoming clear delimiters of where
different projects are in charge&mdash;or, more importantly, which project is failing.</p>
<p>To use this listener, use the command:</p>
<pre class="input">ant -logger org.apache.tools.ant.listener.BigProjectLogger</pre>

<h3 id="SimpleBigProjectLogger">SimpleBigProjectLogger</h3>
<p><em>Since Ant 1.8.1</em></p>
<p>Like <code>BigProjectLogger</code>, project-qualified target names are printed, useful for big
builds with subprojects. Otherwise it is as quiet as <code>NoBannerLogger</code>:</p>
<pre class="output">
Buildfile: /sources/myapp/build.xml

myapp-lib.compile:
Created dir: /sources/myapp/lib/build/classes
Compiling 1 source file to /sources/myapp/lib/build/classes

myapp-lib.jar:
Building jar: /sources/myapp/lib/build/lib.jar

myapp.compile:
Created dir: /sources/myapp/build/classes
Compiling 2 source files to /sources/myapp/build/classes

myapp.jar:
Building jar: /sources/myapp/build/myapp.jar

BUILD SUCCESSFUL
Total time: 1 second</pre>
<p>To use this listener, use the command:</p>
<pre class="input">ant -logger org.apache.tools.ant.listener.SimpleBigProjectLogger</pre>

<h3 id="ProfileLogger">ProfileLogger</h3>
<!-- This is the 'since' as described in the Loggers JavaDoc -->
<p><em>Since Ant 1.8.0</em></p>
<p>This logger stores the time needed for executing a task, target and the whole build and prints
these information. The output contains a timestamp when entering the build, target or task and a
timestamp and the needed time when exiting.</p>
<h4>Example</h4>
Having that buildfile
<pre>
&lt;project&gt;
    &lt;target name=&quot;aTarget&quot;&gt;
        &lt;echo&gt;echo-task&lt;/echo&gt;
        &lt;zip destfile=&quot;my.zip&quot;&gt;
            &lt;fileset dir=&quot;${ant.home}&quot;/&gt;
        &lt;/zip&gt;
    &lt;/target&gt;
    &lt;target name=&quot;anotherTarget&quot; depends=&quot;aTarget&quot;&gt;
        &lt;echo&gt;another-echo-task&lt;/echo&gt;
    &lt;/target&gt;
&lt;/project&gt;</pre>
<p>and executing with <kbd>ant -logger org.apache.tools.ant.listener.ProfileLogger
anotherTarget</kbd> gives that output (with other timestamps and duration of course ;-):</p>
<pre class="output">
Buildfile: ...\build.xml

Target aTarget: started Thu Jan 22 09:01:00 CET 2009

echo: started Thu Jan 22 09:01:00 CET 2009
     [echo] echo-task

echo: finished Thu Jan 22 09:01:00 CET 2009 (250ms)

zip: started Thu Jan 22 09:01:00 CET 2009
      [zip] Building zip: ...\my.zip

zip: finished Thu Jan 22 09:01:01 CET 2009 (1313ms)

Target aTarget: finished Thu Jan 22 09:01:01 CET 2009 (1719ms)

Target anotherTarget: started Thu Jan 22 09:01:01 CET 2009

echo: started Thu Jan 22 09:01:01 CET 2009
     [echo] another-echo-task

echo: finished Thu Jan 22 09:01:01 CET 2009 (0ms)

Target anotherTarget: finished Thu Jan 22 09:01:01 CET 2009 (0ms)

BUILD SUCCESSFUL
Total time: 2 seconds</pre>

<h2 id="dev">Writing your own</h2>

<p>See the <a href="develop.html#buildevents">Build Events</a> section for developers.</p>

<p>Notes:</p>

<ul>
  <li>
    A listener or logger should not write to standard output or error in
    the <code class="code">messageLogged()</code> method; Ant captures these internally and it will
    trigger an infinite loop.
  </li>
  <li>
    Logging is synchronous; all listeners and loggers are called one after the other, with the build
    blocking until the output is processed. Slow logging means a slow build.
  </li>
  <li>When a build is started, and <code class="code">BuildListener.buildStarted(BuildEvent
    event)</code> is called, the project is not fully functional. The build has started, yes, and
    the <code class="code">event.getProject()</code> method call returns the Project instance, but
    that project is initialized with JVM and Ant properties, nor has it parsed the build file
    yet. You cannot call <code class="code">Project.getProperty()</code> for property lookup, or
    <code class="code">Project.getName()</code> to get the project name (it will return null).
  </li>
  <li>
    Classes that implement <code class="code">org.apache.tools.ant.SubBuildListener</code> receive
    notifications when child projects start and stop.
  </li>
</ul>

</body>
</html>
