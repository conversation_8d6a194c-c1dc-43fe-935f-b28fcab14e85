<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Directory-based Tasks</title>
</head>

<body>

<h2 id="directorybasedtasks">Directory-based Tasks</h2>
<p>Some tasks use directory trees for the actions they perform.
For example, the <a href="Tasks/javac.html">javac</a> task, which
compiles a directory tree with <samp>.java</samp> files into
<samp>.class</samp> files, is one of these directory-based tasks. Because
some of these tasks do so much work with a directory tree, the task itself
can act as an implicit <a href="Types/fileset.html">FileSet</a>.</p>

<p>Whether the fileset is implicit or not, it can often be very useful to
work on a subset of the directory tree. This section describes how you can
select a subset of such a directory tree when using one of these
directory-based tasks.</p>

<p>Apache Ant gives you two ways to create a subset of files in a
fileset, both of which can be used at the same time:</p>
<ul>
  <li>Only include files and directories that match any
    <var>include</var> patterns and do not match any
    <var>exclude</var> patterns in a given
    <a href="Types/patternset.html">PatternSet</a>.</li>
  <li>Select files based on selection criteria defined by a collection of
    <a href="Types/selectors.html">selector</a> nested elements.</li>
</ul>
<h3 id="patternset">Patternset</h3>

<p>We said that Directory-based tasks can sometimes act as an
implicit <a href="Types/fileset.html"><code>&lt;fileset&gt;</code></a>,
but in addition to that, a FileSet acts as an
implicit <a href="Types/patternset.html"><code>&lt;patternset&gt;</code></a>.</p>

<p>The inclusion and exclusion elements of the implicit PatternSet can be
specified inside the directory-based task (or explicit fileset) via
either:</p>
<ul>
  <li>the attributes <var>includes</var> and
    <var>excludes</var>.</li>
  <li>nested elements <code>&lt;include&gt;</code> and
    <code>&lt;exclude&gt;</code>.</li>
  <li>external files specified with the attributes
    <var>includesfile</var> and <var>excludesfile</var>.</li>
  <li>external files specified with the nested elements
    <code>&lt;includesfile&gt;</code> and <code>&lt;excludesfile&gt;</code>.
  </li>
</ul>
<p>
When dealing with an external file, each line of the file
is taken as a pattern that is added to the list of include or exclude
patterns.</p>

<p>When both inclusion and exclusion are used, only files/directories that
match at least one of the include patterns and don't match any of the
exclude patterns are used. If no include pattern is given, all files
are assumed to match the include pattern (with the possible exception of
the default excludes).</p>

<h4 id="patterns">Patterns</h4>

<p>As described earlier, patterns are used for the inclusion and exclusion
of files. These patterns look very much like the patterns used in DOS and
UNIX:</p>

<p><q>*</q> matches zero or more characters, <q>?</q> matches one character.</p>

<p>In general, patterns are considered relative paths, relative to a
task dependent base directory (the <var>dir</var> attribute in the
case of <code>&lt;fileset&gt;</code>).  Only files found below that base
directory are considered.  So while a pattern
like <samp>../foo.java</samp> is possible, it will not match
anything when applied since the base directory's parent is never
scanned for files.</p>

<h5>Examples</h5>
<p>
<samp>*.java</samp>&nbsp;&nbsp;matches&nbsp;&nbsp;<samp>.java</samp>,
<samp>x.java</samp> and <samp>FooBar.java</samp>, but
not <samp>FooBar.xml</samp> (does not end with <samp>.java</samp>).</p>

<p>
<samp>?.java</samp>&nbsp;&nbsp;matches&nbsp;&nbsp;<samp>x.java</samp>,
<samp>A.java</samp>, but not <samp>.java</samp> or <samp>xyz.java</samp>
(both don't have one character before <samp>.java</samp>).</p>

<p>Combinations of <q>*</q>'s and <q>?</q>'s are allowed.</p>

<p>Matching is done per-directory. This means that first the first directory in
the pattern is matched against the first directory in the path to match. Then
the second directory is matched, and so on. For example, when we have
the pattern <samp>/?abc/*/*.java</samp>
and the path <samp>/xabc/foobar/test.java</samp>,
the first <samp>?abc</samp> is matched with <samp>xabc</samp>,
then <samp>*</samp> is matched with <samp>foobar</samp>,
and finally <samp>*.java</samp> is matched with <samp>test.java</samp>.
They all match, so the path matches the pattern.</p>

<p>To make things a bit more flexible, we add one extra feature, which makes it
possible to match multiple directory levels. This can be used to match a
complete directory tree, or a file anywhere in the directory tree.
To do this, <q>**</q> must be used as the name of a
directory. When <samp>**</samp> is used as the name of a directory in
the pattern, it matches zero or more directories. For
example: <samp>/test/**</samp> matches all files/directories
under <samp>/test/</samp>, such as <samp>/test/x.java</samp>,
or <samp>/test/foo/bar/xyz.html</samp>, but not <samp>/xyz.xml</samp>.</p>

<p>There is one &quot;shorthand&quot;: if a pattern ends with <q>/</q>
or <q>\</q>, then <q>**</q> is appended. For
example, <samp>mypackage/test/</samp> is interpreted as if it
were <samp>mypackage/test/**</samp>.</p>

<h5>Example patterns</h5>
<table>
  <tr>
    <td>Example</td>
    <td>Explanation</td>
  </tr>
  <tr>
    <td><samp>**/CVS/*</samp></td>
    <td>Matches all files in <samp>CVS</samp>
      directories that can be located
      anywhere in the directory tree.<br/>
      Matches:
<pre>
CVS/Repository
org/apache/CVS/Entries
org/apache/jakarta/tools/ant/CVS/Entries</pre>
But not:
<pre>org/apache/CVS/foo/bar/Entries</pre>
(<samp>foo/bar/</samp> part does not match)
    </td>
  </tr>
  <tr>
    <td><samp>org/apache/jakarta/**</samp></td>
    <td>Matches all files in the <samp>org/apache/jakarta</samp>
      directory tree.<br/>
      Matches:
<pre>
org/apache/jakarta/tools/ant/docs/index.html
org/apache/jakarta/test.xml
</pre>
But not:
<pre>org/apache/xyz.java</pre>
(<code>jakarta/</code> part is missing).</td>
  </tr>
  <tr>
    <td><samp>org/apache/**/CVS/*</samp></td>
    <td>Matches all files in <samp>CVS</samp> directories
      that are located anywhere in the directory tree under
      <samp>org/apache</samp>.<br/>
      Matches:
<pre>
org/apache/CVS/Entries
org/apache/jakarta/tools/ant/CVS/Entries</pre>
But not:
<pre>org/apache/CVS/foo/bar/Entries</pre>
(<samp>foo/bar/</samp> part does not match)</td>
  </tr>
  <tr>
    <td><samp>**/test/**</samp></td>
    <td>Matches all files that have a <samp>test</samp>
        element in their path, including <samp>test</samp> as a filename.</td>
  </tr>
</table>
<p>When these patterns are used in inclusion and exclusion, you have a powerful
way to select just the files you want.</p>

<h3 id="selectors">Selectors</h3>
<p>The <a href="Types/fileset.html"><code>&lt;fileset&gt;</code></a>,
whether implicit or explicit in the directory-based task, also acts as
an <a href="Types/selectors.html#andselect"><code>&lt;and&gt;</code></a>
selector container. This can be used to create arbitrarily complicated
selection criteria for the files the task should work with. See
the <a href="Types/selectors.html">Selector</a> documentation for
more information.</p>

<h3 id="tasklist">Standard Tasks/FileSets</h3>
<p>Many of the standard tasks in ant take one or more filesets which follow
the rules given here. This list, a subset of those, is a list of standard ant
tasks that can act as an implicit fileset:</p>
<ul>
  <li><a href="Tasks/checksum.html"><code>&lt;checksum&gt;</code></a></li>
  <li><a href="Tasks/copydir.html"><code>&lt;copydir&gt;</code></a> (<em>deprecated</em>)</li>
  <li><a href="Tasks/delete.html"><code>&lt;delete&gt;</code></a></li>
  <li><a href="Tasks/dependset.html"><code>&lt;dependset&gt;</code></a></li>
  <li><a href="Tasks/fixcrlf.html"><code>&lt;fixcrlf&gt;</code></a></li>
  <li><a href="Tasks/javac.html"><code>&lt;javac&gt;</code></a></li>
  <li><a href="Tasks/replace.html"><code>&lt;replace&gt;</code></a></li>
  <li><a href="Tasks/rmic.html"><code>&lt;rmic&gt;</code></a></li>
  <li><a href="Tasks/style.html"><code>&lt;xslt&gt;</code> (aka <code>&lt;style&gt;</code>)</a></li>
  <li><a href="Tasks/tar.html"><code>&lt;tar&gt;</code></a></li>
  <li><a href="Tasks/zip.html"><code>&lt;zip&gt;</code></a></li>
  <li><a href="Tasks/ejb.html#ddcreator"><code>&lt;ddcreator&gt;</code></a></li>
  <li><a href="Tasks/ejb.html#ejbjar"><code>&lt;ejbjar&gt;</code></a></li>
  <li><a href="Tasks/ejb.html#ejbc"><code>&lt;ejbc&gt;</code></a></li>
  <li><a href="Tasks/cab.html"><code>&lt;cab&gt;</code></a></li>
  <li><a href="Tasks/native2ascii.html"><code>&lt;native2ascii&gt;</code></a></li>
  <li><a href="Tasks/netrexxc.html"><code>&lt;netrexxc&gt;</code></a></li>
  <li><a href="Tasks/renameextensions.html"><code>&lt;renameextensions&gt;</code></a></li>
  <li><a href="Tasks/depend.html"><code>&lt;depend&gt;</code></a></li>
  <li><a href="Tasks/translate.html"><code>&lt;translate&gt;</code></a></li>
  <li><a href="Tasks/image.html"><code>&lt;image&gt;</code></a></li>
  <li><a href="Tasks/jlink.html"><code>&lt;jlink&gt;</code></a> (<em>deprecated</em>)</li>
  <li><a href="Tasks/jspc.html"><code>&lt;jspc&gt;</code></a> (<em>deprecated</em>)</li>
  <li><a href="Tasks/wljspc.html"><code>&lt;wljspc&gt;</code></a></li>
</ul>

<h3 id="examples">Examples</h3>
<pre>
&lt;copy todir=&quot;${dist}&quot;&gt;
  &lt;fileset dir=&quot;${src}&quot;
           includes=&quot;**/images/*&quot;
           excludes=&quot;**/*.gif&quot;/&gt;
&lt;/copy&gt;</pre>
<p>This copies all files in directories called <samp>images</samp> that are
located in the directory tree defined by <samp>${src}</samp> to the
destination directory defined by <samp>${dist}</samp>,
but excludes all <samp>*.gif</samp> files from the copy.</p>
<pre>
&lt;copy todir=&quot;${dist}&quot;&gt;
  &lt;fileset dir=&quot;${src}&quot;&gt;
    &lt;include name=&quot;**/images/*&quot;/&gt;
    &lt;exclude name=&quot;**/*.gif&quot;/&gt;
  &lt;/fileset&gt;
&lt;/copy&gt;</pre>
<p>The same as the example above, but expressed using nested elements.</p>

<pre>
&lt;delete dir=&quot;${dist}&quot;&gt;
    &lt;include name=&quot;**/images/*&quot;/&gt;
    &lt;exclude name=&quot;**/*.gif&quot;/&gt;
&lt;/delete&gt;
</pre>
<p>Deleting the original set of files, the <code>delete</code> task can act
as an implicit fileset.</p>

<h3 id="defaultexcludes">Default Excludes</h3>
<p>There are a set of definitions that are excluded by default from all
directory-based tasks. <em>Since Ant 1.8.1</em> they are:</p>
<pre>
**/*~
**/#*#
**/.#*
**/%*%
**/._*
**/CVS
**/CVS/**
**/.cvsignore
**/SCCS
**/SCCS/**
**/vssver.scc
**/.svn
**/.svn/**
**/.DS_Store</pre>
<p><em>Since Ant 1.8.2</em>, additional default excludes are:</p>
<pre>
**/.git
**/.git/**
**/.gitattributes
**/.gitignore
**/.gitmodules
**/.hg
**/.hg/**
**/.hgignore
**/.hgsub
**/.hgsubstate
**/.hgtags
**/.bzr
**/.bzr/**
**/.bzrignore</pre>
<p>If you do not want these default excludes applied, you may disable
them with the <var>defaultexcludes</var>=<q>no</q> attribute.</p>

<p>This is the default list; note that you can modify the list of
default excludes by using
the <a href="Tasks/defaultexcludes.html">defaultexcludes</a> task.</p>

</body>
</html>
