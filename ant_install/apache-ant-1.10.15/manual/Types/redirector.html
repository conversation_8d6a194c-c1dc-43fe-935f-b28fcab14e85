<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Redirector Type</title>
</head>

<body>

<h2 id="redirector">I/O redirection</h2>
<p><em>Since Apache Ant 1.6.2</em></p>
<p>For many tasks, input and output can be defined in a fairly straightforward fashion.
The <a href="../Tasks/exec.html">exec</a> task, used to execute an external process, stands as a
very basic example.  The executed process may accept input, produce output, or do either or both
depending upon various circumstances.  Output may be classified as <em>output</em> or
as <em>error output</em>.  The <code>&lt;redirector&gt;</code> type provides a concrete means of
redirecting input and output featuring the use of <a href="./mapper.html">File Mapper</a>s to
specify source (input) and destination (output/error) files.</p>
<p>The <code>&lt;redirector&gt;</code> element accepts the following attributes:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>output</td>
    <td>Name of a file to which output should be written.  If the error stream is not also
      redirected to a file or property, it will appear in this output.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>error</td>
    <td>The file to which the standard error of the command should be redirected.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>logError</td>
    <td>This attribute is used when you wish to see error output in Ant's log and you are
      redirecting output to a file/property. The error output will not be included in the output
      file/property.</td>
    <td>No; ignored if <var>error</var> or <var>errorProperty</var>
    is set</td>
  </tr>
  <tr>
    <td>append</td>
    <td>Whether output and error files should be appended to rather than overwritten.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>createemptyfiles</td>
    <td>Whether output and error files should be created even when empty.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>outputproperty</td>
    <td>The name of a property in which the output of the command should be stored. Unless the
      error stream is redirected to a separate file or stream, this property will include the
      error output.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>errorproperty</td>
    <td>The name of a property in which the standard error of the command should be stored.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>input</td>
    <td>A file from which the executed command's standard input is taken. This attribute is
      mutually exclusive with the <var>inputstring</var> attribute.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>inputstring</td>
    <td>A string which serves as the input stream for the executed command. This attribute is
      mutually exclusive with the <var>input</var> attribute.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>inputencoding</td>
    <td>The input encoding.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>outputencoding</td>
    <td>The output encoding.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>errorencoding</td>
    <td>The error encoding.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>alwayslog</td>
    <td>Always send to the log in addition to any other destination. <em>Since Ant 1.6.3</em></td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>loginputstring</td>
    <td>Controls the display of <var>inputstring</var>'s value in log messages. Set
      to <q>false</q> when sending sensitive data (e.g. passwords) to external
      processes. <em>Since Ant 1.6.3</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>binaryOutput</td>
    <td>When set to <q>true</q> Ant will not try to split the output into lines&mdash;which it
      will usually do in order to separate error from normal output.  This setting will not
      prevent binary output from getting corrupted if you also specify filter chains.
      <em>Since Ant 1.9.4</em>
    </td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>discardOutput</td>
    <td>Whether output should completely be discarded. This setting is
      incompatible with any setting that redirects output to files or
      properties.<br/>
      If you set this to <q>true</q> error output will be discared as
      well unless you redirect error output to files, properties or
      enable <q>logError</q>.
      <em>Since Ant 1.10.10</em>
    </td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>discardError</td>
    <td>Whether error output should completely be discarded. This
      setting is incompatible with any setting that redirects error
      output to files or properties as well as <q>logError</q>.
      <em>Since Ant 1.10.10</em>
    </td>
    <td>No; defaults to <q>false</q></td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>inputmapper</h4>
<p>A single <a href="./mapper.html">File Mapper</a> used to redirect process input.  Multiple
mapping results should concatenate all mapped files as input.  Mapping will ordinarily be
performed on a task-specified sourcefile; consult the documentation of the individual task for
more details.  A nested <code>&lt;inputmapper&gt;</code> is not compatible with either of the
<var>input</var> or <var>inputstring</var> attributes.</p>
<h4>outputmapper</h4>
<p>A single <a href="./mapper.html">File Mapper</a> used to redirect process output.  Mapping
will ordinarily be performed on a task-specified sourcefile; consult the documentation of the
individual task for more details.  A nested <code>&lt;outputmapper&gt;</code> is not compatible
with the <var>output</var> attribute.</p>
<h4>errormapper</h4>
<p>A single <a href="./mapper.html">File Mapper</a> used to redirect error output.  Mapping will
ordinarily be performed on a task-specified sourcefile; consult the documentation of the
individual task for more details.  A nested <code>&lt;errormapper&gt;</code> is not compatible
with the <var>error</var> attribute.</p>
<h4>inputfilterchain</h4>
<p>A <a href="./filterchain.html">FilterChain</a> can be applied to the process input.</p>
<h4>outputfilterchain</h4>
<p>A <a href="./filterchain.html">FilterChain</a> can be applied to the process output.</p>
<h4>errorfilterchain</h4>
<p>A <a href="./filterchain.html">FilterChain</a> can be applied to the error output.</p>
<h3>Usage</h3>
<p>Tasks known to support I/O redirection:</p>
<ul>
<li><a href="../Tasks/exec.html">Exec</a></li>
<li><a href="../Tasks/apply.html">Apply</a></li>
<li><a href="../Tasks/java.html">Java</a></li>
</ul>
<p>The expected behavior of a <code>&lt;redirector&gt;</code> is to a great degree dependent on
the supporting task.  Any possible points of confusion should be noted at the task level.</p>

</body>
</html>
