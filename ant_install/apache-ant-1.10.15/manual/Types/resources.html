<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Resources and Resource Collections</title>
</head>

<body>

<h2 id="resource">Resources</h2>
<p>
A file-like entity can be abstracted to the concept of a <em>resource</em>.  In addition to
providing access to file-like attributes, a resource implementation should, when possible,
provide the means to read content from and/or write content to the underlying entity. Although
the resource concept was introduced in Apache Ant 1.5.2, resources are available for explicit
use <em>since Ant 1.7</em>.
</p>

<h3>Built-in resource types</h3>
The built-in resource types are:
<ul>
  <li><a href="#basic">resource</a>&mdash;a basic resource.</li>
  <li><a href="#bzip2resource">bzip2resource</a>&mdash;a BZip2 compressed resource.</li>
  <li><a href="#file">file</a>&mdash;a file.</li>
  <li><a href="#gzipresource">gzipresource</a>&mdash;a GZip compressed resource.</li>
  <li><a href="#javaresource">javaresource</a>&mdash;a resource loadable via a Java
        classloader.</li>
  <li><a href="#javaconstant">javaconstant</a>&mdash;a constant in a class loadable via a Java
        classloader.</li>
  <li><a href="#propertyresource">propertyresource</a>&mdash;an Ant property.</li>
  <li><a href="#string">string</a>&mdash;a text string.</li>
  <li><a href="#tarentry">tarentry</a>&mdash;an entry in a tar file.</li>
  <li><a href="#url">url</a>&mdash;a URL.</li>
  <li><a href="#xzresource">xzresource</a>&mdash;an XZ compressed resource.</li>
  <li><a href="#zipentry">zipentry</a>&mdash;an entry in a zip file.</li>
</ul>

<h4 id="basic">resource</h4>

<p>
A basic resource. Other resource types derive from this basic type; as such all its attributes
are available, though in most cases irrelevant attributes will be ignored. This and all resource
implementations are also usable as single-element <a href="#collection">resource
collections</a>.
</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of this resource</td>
    <td>No</td>
  </tr>
  <tr>
    <td>exists</td>
    <td>Whether this resource exists</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>lastmodified</td>
    <td>The last modification time of this resource</td>
    <td>No</td>
  </tr>
  <tr>
    <td>directory</td>
    <td>Whether this resource is directory-like</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>size</td>
    <td>The size of this resource</td>
    <td>No</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>resource</code>
      a <a href="../using.html#references">reference</a> to
      a <code>resource</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h4 id="file">file</h4>

<p>Represents a file accessible via local filesystem conventions.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>The file represented by this resource</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>basedir</td>
    <td>The base directory of this resource.  When this attribute is set, attempts to access the
      name of the resource will yield a path relative to this location.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>file</code>
      a <a href="../using.html#references">reference</a> to
      a <code>file</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h4 id="javaresource">javaresource</h4>

<p>Represents a resource loadable via a Java classloader.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the resource.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>classpath</td>
    <td>the classpath to use when looking up a resource.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>the classpath to use when looking up a resource, given
      as <a href="../using.html#references">reference</a> to a <code>&lt;path&gt;</code> defined
      elsewhere.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>loaderRef</td>
    <td>the name of the loader that is used to load the resource, constructed from the specified
      classpath.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>parentFirst</td>
    <td>Whether to consult the parent classloader first&mdash;the parent classloader most likely
      is the system classloader&mdash;when using a nested classpath.<br/>
      <em>Since Ant 1.8.0</em></td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>javaresource</code>
      a <a href="../using.html#references">reference</a> to
      a <code>javaresource</code> defined elsewhere. If specified no
      other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<p>The classpath can also be specified as nested classpath element,
where <code>&lt;classpath&gt;</code> is a <a href="../using.html#path">path-like
structure</a>.</p>

<h4 id="javaconstant">javaconstant</h4>
<p>Loads the value of a Java constant. As a specialisation
of <a href="#javaresource">javaresource</a> all of its attributes and nested elements are
supported. A constant must be specified as <code>public static</code> otherwise it could not be
loaded.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the resource. Must be specified as full qualified
      field name.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>javaconstant</code>
      a <a href="../using.html#references">reference</a> to
      a <code>javaconstant</code> defined elsewhere. If specified no
      other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Examples</h5>
<p>This loads the value of the constant <code>VERSION</code> of the <code>org.acme.Main</code>
class into the <var>version</var>-property. The classpath for finding that class is provided via
nested <code>classpath</code> element.</p>
<pre>
&lt;loadresource property=&quot;version&quot;&gt;
  &lt;javaconstant name=&quot;org.acme.Main.VERSION&quot;&gt;
    &lt;classpath&gt;
      &lt;pathelement location=&quot;${acme.lib.dir}&quot;/&gt;
    &lt;/classpath&gt;
  &lt;/javaconstant&gt;
&lt;/loadresource&gt;</pre>

<p>Create a new file <samp>c:/temp/org.apache.tools.ant.Main.DEFAULT_BUILD_FILENAME</samp> with
the content of that constant (<q>build.xml</q>).</p>
<pre>
&lt;copy todir=&quot;c:/temp&quot;&gt;
  &lt;javaconstant name=&quot;org.apache.tools.ant.Main.DEFAULT_BUILD_FILENAME&quot;/&gt;
&lt;/copy&gt;</pre>

<h4 id="zipentry">zipentry</h4>

<p>Represents an entry in a ZIP archive. The archive can be specified using the archive
attribute or a nested single-element resource collection.  <code>zipentry</code> only supports
file system resources as nested elements.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>zipfile</td>
    <td>The zip file containing this resource</td>
    <td rowspan="2">Yes, unless a nested resource collection has been
    specified</td>
  </tr>
  <tr>
    <td>archive</td>
    <td class="left">alias of <var>zipfile</var></td>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the archived resource</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the zipfile</td>
    <td>No; defaults to default JVM character encoding</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>propertyresource</code>
      a <a href="../using.html#references">reference</a> to
      a <code>propertyresource</code> defined elsewhere. If specified
      no other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h4 id="tarentry">tarentry</h4>

<p>Represents an entry in a TAR archive.  The archive can be specified using the archive
attribute or a nested single-element resource collection.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>archive</td>
    <td>The tar archive containing this resource</td>
    <td>Yes, unless a nested resource
    collection has been specified</td>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the archived resource</td>
    <td>Yes</td>
  </tr>
</table>

<h4 id="gzipresource">gzipresource</h4>

<p>This is not a stand-alone resource, but a wrapper around another resource providing
compression of the resource's contents on the fly.  A single element resource collection must be
specified as a nested element.</p>

<h4 id="bzip2resource">bzip2resource</h4>

<p>This is not a stand-alone resource, but a wrapper around another resource providing
compression of the resource's contents on the fly.  A single element resource collection must be
specified as a nested element.</p>

<h4 id="xzresource">xzresource</h4>

<p>This is not a stand-alone resource, but a wrapper around another resource providing
compression of the resource's contents on the fly.  A single element resource collection must be
specified as a nested element.</p>

<p>XZ compression support has been added <em>since Apache Ant 1.10.1</em> and depends on
external libraries not included in the Ant
distribution. See <a href="../install.html#librarydependencies">Library Dependencies</a> for
more information.</p>

<h4 id="url">url</h4>

<p>Represents a URL.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>url</td>
    <td>The URL to expose</td>
    <td rowspan="3">Exactly one of these</td>
  </tr>
  <tr>
    <td>file</td>
    <td class="left">The file to expose as a <code>file:</code> URL</td>
  </tr>
  <tr>
    <td>baseUrl</td>
    <td class="left">The base URL which must be combined
    with <var>relativePath</var></td>
 </tr>
  <tr>
    <td>relativePath</td>
    <td>Relative path that defines the URL when combined with
      <var>baseUrl</var></td>
    <td>If using <var>baseUrl</var></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>url</code>
      a <a href="../using.html#references">reference</a> to
      an <code>url</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h4 id="string">string</h4>

<p>Represents a Java String. It can be written to, but only once, after which it will be an
error to write to again.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>value</td>
    <td>The value of this resource</td>
    <td>No</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>string</code>
      a <a href="../using.html#references">reference</a> to
      a <code>string</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<p>The resource also supports nested text, which can only be supplied if the <var>value</var>
attribute is unset:</p>
<pre>
&lt;string&gt;
    self.log("Ant version =${ant.version}");
&lt;/string&gt;</pre>

<h4 id="propertyresource">propertyresource</h4>

<p>Represents an Ant property.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The property name</td>
    <td>Yes</td>
  </tr>
</table>

<hr/>
<h2 id="collection">Resource Collections</h2>
<p>
A resource collection is an abstraction of an entity that groups together a number
of <a href="#resource">resources</a>. Several of Ant's "legacy" datatypes have been modified to
behave as resource collections:
</p>
<ul>
  <li><a href="fileset.html">fileset</a>,
    <a href="dirset.html">dirset</a>,
    <a href="multirootfileset.html">multirootfileset</a>,
    <a href="filelist.html">filelist</a>, and
    <a href="../using.html#path">path</a>
    (and derivative types) expose <a href="#file">file</a> resources
  </li>
  <li><a href="tarfileset.html">tarfileset</a>
    can expose <a href="#file">file</a> or <a href="#tarentry">tarentry</a>
    resources depending on configuration
  </li>
  <li><a href="zipfileset.html">zipfileset</a>
    can expose <a href="#file">file</a> or <a href="#zipentry">zipentry</a>
    resources depending on configuration
  </li>
  <li><a href="propertyset.html">propertyset</a>
    exposes <a href="#propertyresource">property</a> resources
  </li>
</ul>
<p>Strangely, some tasks can even legitimately behave as resource
collections:</p>
<ul>
  <li><a href="../Tasks/concat.html">concat</a> exposes a concatenated
    resource, and adds e.g.  <a href="filterchain.html">filtering</a>
    to Ant's resource-related capabilities.
  </li>
</ul>
<h3>Additional built-in resource collections</h3>
The additional built-in resource collections are:
<ul>
  <li><a href="#resources">resources</a>&mdash;generic resource collection</li>
  <li><a href="#files">files</a>&mdash;collection of files similar
  to <a href="fileset.html">fileset</a></li>
  <li><a href="#restrict">restrict</a>&mdash;restrict a resource collection to include only
  resources meeting specified criteria</li>
  <li><a href="#sort">sort</a>&mdash;sorted resource collection</li>
  <li><a href="#first">first</a>&mdash;first <em>n</em> resources from a nested collection</li>
  <li><a href="#last">last</a>&mdash;last <em>n</em> resources from a nested collection</li>
  <li><a href="#allbutfirst">allbutfirst</a>&mdash;all except the first <em>n</em> resources
    from a nested collection</li>
  <li><a href="#allbutlast">allbutlast</a>&mdash;all except the last <em>n</em> resources from a
    nested collection</li>
  <li><a href="#tokens">tokens</a>&mdash;<a href="#string">string</a> tokens gathered from a
    nested collection</li>
  <li><a href="#union">union</a>&mdash;set union of nested resource collections</li>
  <li><a href="#intersect">intersect</a>&mdash;set intersection of nested resource
    collections</li>
  <li><a href="#difference">difference</a>&mdash;set difference of nested resource
    collections</li>
  <li><a href="#mappedresources">mappedresources</a>&mdash;generic resource collection wrapper
    that maps the names of the nested resources using a <a href="mapper.html">mapper</a>.</li>
  <li><a href="#archives">archives</a>&mdash;wraps around different resource collections and
    treats the nested resources as ZIP or TAR archives that will be extracted on the fly.</li>
  <li><a href="#resourcelist">resourcelist</a>&mdash;a collection of resources whose names have
    been read from another resource.</li>
</ul>

<h4 id="resources">resources</h4>
<p>
A generic resource collection, designed for use
with <a href="../using.html#references">references</a>.  For example, if a third-party Ant task
generates a resource collection of an unknown type, it can still be accessed via
a <code>&lt;resources&gt;</code> collection. The secondary use of this collection type is as a
container of other resource collections, preserving the order of nested collections as well as
duplicate resources (contrast with <a href="#union">union</a>).
</p>
  <table class="attr">
    <tr>
      <th scope="col">Attribute</th>
      <th scope="col">Description</th>
      <th scope="col">Required</th>
    </tr>
    <tr>
      <td>cache</td>
      <td>Whether to cache results.  <em>since Ant 1.8.0</em></td>
      <td>No; default <q>false</q></td>
    </tr>
    <tr>
      <td>refid</td>
      <td>Makes this <code>resourcecollection</code>
        a <a href="../using.html#references">reference</a> to
        a <code>resourcecollection</code> defined elsewhere. If
        specified no other attributes or nested elements are
        allowed.</td>
      <td>No</td>
    </tr>
  </table>

<h4 id="files">files</h4>
<p>A group of files. These files are matched by <strong>absolute</strong> patterns taken from a
number of <a href="patternset.html">PatternSets</a>.  These can be specified as
nested <code>&lt;patternset&gt;</code> elements. In addition, <code>&lt;files&gt;</code> holds
an implicit PatternSet and supports the
nested <code>&lt;include&gt;</code>, <code>&lt;includesfile&gt;</code>, <code>&lt;exclude&gt;</code>
and <code>&lt;excludesfile&gt;</code> elements of PatternSet directly, as well as PatternSet's
attributes.
</p>
<p><a href="selectors.html">File Selectors</a> are available as nested elements. A file must be
selected by all selectors in order to be included; <code>&lt;files&gt;</code> is thus equivalent
to an <code>&lt;and&gt;</code> file selector container.
</p>
<p><strong>More simply put</strong>, this type is equivalent
to <a href="fileset.html">fileset</a> with no base directory.  <strong>Please note</strong> that
without a base directory, filesystem scanning is based entirely on include and exclude patterns.
A <a href="selectors.html#filenameselect">filename</a> (or any) selector can <em>only</em>
influence the scanning process <em>after</em> the file has been included based on pattern-based
selection.
</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>includes</td>
    <td>Comma- or space-separated list of patterns of files that must be included</td>
    <td rowspan="2">At least one of these</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td class="left">Name of a file; each line of this file is taken to be an include
    pattern.</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>Comma- or space-separated list of patterns of files that must be excluded</td>
    <td rowspan="2">No; defaults to default excludes or none if <var>defaultexcludes</var>
    is <q>no</q></td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td class="left">Name of a file; each line of this file is taken to be an exclude
    pattern.</td>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>Whether <a href="../dirtasks.html#defaultexcludes">default excludes</a> should be
      used</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>casesensitive</td>
    <td>Whether patterns are case-sensitive</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr id="followsymlinks">
    <td>followsymlinks</td>
    <td>Whether to follow symbolic links (see note <a href="#symlink">below</a>)</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>files</code>
      a <a href="../using.html#references">reference</a> to
      a <code>files</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<p id="symlink"><a href="#followsymlinks"><strong>Note</strong></a>: All files/directories for which
the canonical path is different from its path are considered symbolic links.  On Unix systems this
usually means the file really is a symbolic link but it may lead to false results on other
platforms.</p>

<h4 id="restrict">restrict</h4>
<p>Restricts a nested resource collection using resource selectors:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling
      may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>restrict</code>
      a <a href="../using.html#references">reference</a> to
      a <code>restrict</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>
<p>Nested resource selectors are used to "narrow down" the included resources, combined via a
logical <em>AND</em>. These are patterned after <a href="selectors.html">file selectors</a> but
are, unsurprisingly, targeted to resources.  Several built-in resource selectors are available
in the
internal <a href="antlib.html">antlib</a> <code>org.apache.tools.ant.types.resources.selectors</code>:</p>

<ul>
  <li><a href="#rsel.name">name</a>&mdash;select resources by name.</li>
  <li><a href="#rsel.exists">exists</a>&mdash;select existing resources.</li>
  <li><a href="#rsel.date">date</a>&mdash;select resources by date.</li>
  <li><a href="#rsel.type">type</a>&mdash;select resources by type.</li>
  <li><a href="#rsel.size">size</a>&mdash;select resources by size.</li>
  <li><a href="#rsel.instanceof">instanceof</a>&mdash;select resources by class or Ant
    datatype.</li>
  <li><a href="#rsel.and">and</a>&mdash;<q>and</q> nested resource selectors.</li>
  <li><a href="#rsel.or">or</a>&mdash;<q>or</q> nested resource selectors.</li>
  <li><a href="#rsel.not">not</a>&mdash;<q>not</q> a nested resource selector.</li>
  <li><a href="#rsel.none">none</a>&mdash;select resources selected by no nested resource
    selectors.</li>
  <li><a href="#rsel.majority">majority</a>&mdash;select resources selected by a majority of
    nested resource selectors.</li>
  <li><a href="selectors.html#modified">modified</a>&mdash;select resources which content has
    changed.</li>
  <li><a href="selectors.html#containsselect">contains</a>&mdash;select resources containing a
    particular text string.</li>
  <li><a href="selectors.html#regexpselect">containsregexp</a>&mdash;select resources whose
    contents match a particular regular expression.</li>
  <li><a href="#rsel.compare">compare</a>&mdash;select resources based on comparison to other
    resources.</li>
  <li><a href="selectors.html#readable">readable</a>&mdash;select files (resources must be
    files) if they are readable.</li>
  <li><a href="selectors.html#writable">writable</a>&mdash;select files (resources must be
    files) if they are writable.</li>
</ul>

<h5 id="rsel.name">name</h5>
<p>Selects resources by name.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name pattern to test using standard Ant patterns.</td>
    <td rowspan="2">Exactly one of the two</td>
  </tr>
  <tr>
    <td>regex</td>
    <td class="left">The regular expression matching files to select.</td>
  </tr>
  <tr>
    <td>casesensitive</td>
    <td>Whether name comparisons are case-sensitive</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>handledirsep</td>
    <td>
      If this is specified, the mapper will treat a \ character in a resource name or name
      attribute as a / for the purposes of matching.  This attribute can be <q>true</q>
      or <q>false</q>.
      <em>Since Ant 1.8.0</em>
    <td>No; default <q>false</q></td>
  </tr>
</table>

<h5 id="rsel.exists">exists</h5>
<p>Selects existing resources.</p>

<h5 id="rsel.date">date</h5>
<p>Selects resources by date.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>millis</td>
    <td>The comparison date/time in ms since January 1, 1970</td>
    <td rowspan="2">One of these</td>
  </tr>
  <tr>
    <td>datetime</td>
    <td class="left">The formatted comparison date/time</td>
  </tr>
  <tr>
    <td>pattern</td>
    <td>SimpleDateFormat-compatible pattern for use with the <code>datetime</code> attribute
    using the current locale</td>
    <td>No; default is <q>MM/dd/yyyy hh:mm a</q> using the US locale</td>
  </tr>
  <tr>
    <td>granularity</td>
    <td>The number of milliseconds leeway to use when comparing file modification times. This is
    needed because not every file system supports tracking the last modified time to the
    millisecond level.</td>
    <td>No; default varies by platform: FAT filesystems = 2 sec; Unix = 1 sec; NTFS = 1 ms.</td>
  </tr>
  <tr>
    <td>when</td>
    <td>One of <q>before</q>, <q>after</q>, <q>equal</q></td>
    <td>No; default <q>equal</q></td>
  </tr>
</table>

<h5 id="rsel.type">type</h5>
<p>Selects resources by type (file or directory).</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>type</td>
    <td>One of <q>file</q>, <q>dir</q>, <q>any</q> (<em>since Ant 1.8</em>)</td>
    <td>Yes</td>
  </tr>
</table>

<h5 id="rsel.size">size</h5>
<p>Selects resources by size.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>size</td>
    <td>The size to compare</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>when</td>
    <td>One
    of <q>equal</q>, <q>eq</q>, <q>greater</q>, <q>gt</q>, <q>less</q>, <q>lt</q>, <q>ge</q>
    (greater or equal), <q>ne</q> (not equal), <q>le</q> (less or equal)</td>
    <td>No; default <q>equal</q></td>
  </tr>
</table>

<h5 id="rsel.instanceof">instanceof</h5>
<p>Selects resources by type.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>class</td>
    <td>The class of which the resource must be an instance</td>
    <td rowspan="2">One of these</td>
  </tr>
  <tr>
    <td>type</td>
    <td class="left">The Ant type that must be assignable from the resource</td>
  </tr>
  <tr>
    <td>uri</td>
    <td>The URI in which <var>type</var> must be defined</td>
    <td>No</td>
  </tr>
</table>

<h5 id="rsel.and">and</h5>
<p>Selects a resource if it is selected by all nested resource selectors.</p>

<h5 id="rsel.or">or</h5>
<p>Selects a resource if it is selected by at least one nested resource selector.</p>

<h5 id="rsel.not">not</h5>
<p>Negates the selection result of the single nested resource selector allowed.</p>

<h5 id="rsel.none">none</h5>
<p>Selects a resource if it is selected by no nested resource selectors.</p>

<h5 id="rsel.majority">majority</h5>
<p>Selects a resource if it is selected by the majority of nested resource selectors.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>allowtie</td>
    <td>Whether a tie (when there is an even number of nested resource selectors) is considered
      a majority</td>
    <td>No; default <q>true</q></td>
  </tr>
</table>

<h5 id="rsel.compare">compare</h5>
<p>Selects a resource based on its comparison to one or more "control" resources using
nested <a href="#rcmp">resource comparators</a>.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>when</td>
    <td>Comparison
    (<q>equal</q>/<q>eq</q>, <q>greater</q>/<q>gt</q>, <q>less</q>/<q>lt</q>, <q>le</q> (less or
    equal), <q>ge</q> (greater or equal), <q>ne</q> (not equal).</td>
    <td>No; default <q>equal</q></td>
  </tr>
  <tr>
    <td>against</td>
    <td>Quantifier (<q>all</q>/<q>each</q>/<q>every</q>, <q>any</q>/<q>some</q>,
    (exactly) <q>one</q>, <q>most</q>/<q>majority</q>, <q>none</q>.</td>
    <td>No; default <q>all</q></td>
  </tr>
</table>
<h6>Parameters specified as nested elements</h6>
<p>The resources against which comparisons will be made must be specified using the nested
&lt;control&gt; element, which denotes a <a href="#resources">resources</a> collection.</p>
<h6>Examples</h6>
<p>Assuming the namespace settings</p>
<pre>
rsel="antlib:org.apache.tools.ant.types.resources.selectors"
rcmp="antlib:org.apache.tools.ant.types.resources.comparators"</pre>
  <pre>
&lt;restrict&gt;
  &lt;fileset dir="src" includes="a,b,c,d,e,f,g"/&gt;
  &lt;rsel:compare when="le" against="all"&gt;
    &lt;control&gt;
      &lt;resource name="d"/&gt;
    &lt;/control&gt;
    &lt;rcmp:name/&gt;
  &lt;/rsel:compare&gt;
&lt;/restrict&gt;</pre>
<p>Selects files <samp>a</samp>, <samp>b</samp>, <samp>c</samp>, and <samp>d</samp>.</p>
<pre>
&lt;project rsel=&quot;antlib:org.apache.tools.ant.types.resources.selectors&quot;&gt;
    &lt;macrodef name=&quot;copyFromPath&quot;&gt;
        &lt;attribute name=&quot;todir&quot;/&gt;
        &lt;attribute name=&quot;refid&quot;/&gt;
        &lt;element name=&quot;nested-resource-selectors&quot; optional=&quot;yes&quot; implicit=&quot;true&quot;/&gt;
        &lt;sequential&gt;
            &lt;mkdir dir=&quot;@{todir}&quot; taskname=&quot;copyFromPath&quot;/&gt;
            &lt;copy todir=&quot;@{todir}&quot; taskname=&quot;copyFromPath&quot;&gt;
                &lt;restrict&gt;
                    &lt;path refid=&quot;@{refid}&quot;/&gt;
                    &lt;rsel:or&gt;
                        &lt;nested-resource-selectors/&gt;
                    &lt;/rsel:or&gt;
                &lt;/restrict&gt;
                &lt;flattenmapper/&gt;
            &lt;/copy&gt;
        &lt;/sequential&gt;
    &lt;/macrodef&gt;
    &lt;copyFromPath refid=&quot;classpath&quot; todir=&quot;todir&quot;&gt;
        &lt;rsel:name name=&quot;log4j.properties&quot;/&gt;
        &lt;rsel:name name=&quot;default.properties&quot;/&gt;
    &lt;/copyFromPath&gt;
&lt;/project&gt;</pre>
<p>Creates the <samp>todir</samp> directory and copies (if present) the
files <samp>log4j.properties</samp> and <samp>default.properties</samp> from the classpath
(already used while compiling).</p>

<pre>
&lt;project&gt;
    &lt;filelist id=&quot;allfiles&quot; dir=&quot;${ant.home}/bin&quot; files=&quot;ant.cmd,foo.txt,ant.bat,bar.txt,ant&quot;/&gt;
    &lt;restrict id=&quot;missingfiles&quot;&gt;
        &lt;filelist refid=&quot;allfiles&quot;/&gt;
        &lt;rsel:not xmlns:rsel=&quot;antlib:org.apache.tools.ant.types.resources.selectors&quot;&gt;
            &lt;rsel:exists/&gt;
        &lt;/rsel:not&gt;
    &lt;/restrict&gt;
    &lt;echo&gt;These files are missed: ${toString:missingfiles}&lt;/echo&gt;
&lt;/project&gt;</pre>
<p>The resource collection <q>allfiles</q> defines a list of files which are expected. The
restrict <q>missingfiles</q> uses the <code>&lt;not&gt;&lt;exists&gt;</code> selector for
getting all files which are not present. Finally we use
the <code>toString:</code> <a href="../using.html#pathshortcut">pathshortcut</a> for getting
them in a readable form: <code>[echo] These files are missed: ....foo.txt;....bar.txt</code></p>

<h4 id="sort">sort</h4>

<p>Sorts a nested resource collection according to the resources' natural order, or by one or
more nested <a href="#rcmp">resource comparators</a>:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>sort</code>
      a <a href="../using.html#references">reference</a> to
      a <code>sort</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>
<p>The sort can be controlled and customized by specifying one or more resource
comparators. Resources can be sorted according to multiple criteria; the first specified is the
"outermost", while the last specified is the "innermost". Several built-in resource comparators
are available in the
internal <a href="antlib.html">antlib</a> <code>org.apache.tools.ant.types.resources.comparators</code>:</p>
<h5 id="rcmp">Resource Comparators</h5>
<ul>
  <li><a href="#rcmp.name">name</a>&mdash;sort resources by name</li>
  <li><a href="#rcmp.exists">exists</a>&mdash;sort resources by existence</li>
  <li><a href="#rcmp.date">date</a>&mdash;sort resources by date</li>
  <li><a href="#rcmp.type">type</a>&mdash;sort resources by type</li>
  <li><a href="#rcmp.size">size</a>&mdash;sort resources by size</li>
  <li><a href="#rcmp.content">content</a>&mdash;sort resources by content</li>
  <li><a href="#rcmp.reverse">reverse</a>&mdash;reverse the natural sort order, or that of a
    single nested resource comparator</li>
</ul>

<h6 id="rcmp.name">name</h6>
<p>Sort resources by name.</p>

<h6 id="rcmp.exists">exists</h6>
<p>Sort resources by existence.  Not existing is considered "less than" existing.</p>

<h6 id="rcmp.date">date</h6>
<p>Sort resources by date.</p>

<h6 id="rcmp.type">type</h6>
<p>Sort resources by type (file or directory).  Because directories contain files, they are
considered "greater".</p>

<h6 id="rcmp.size">size</h6>
<p>Sort resources by size.</p>

<h6 id="rcmp.content">content</h6>
<p>Sort resources by content.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>binary</td>
    <td>Whether content should be compared in binary mode.  If <q>false</q>, content will be
    compared without regard to platform-specific line-ending conventions.</td>
    <td>No; default <q>true</q></td>
  </tr>
</table>

<h6 id="rcmp.reverse">reverse</h6>
<p>Reverse the natural sort order, or that of a single nested comparator.</p>

<h5>Examples</h5>
<pre>
&lt;property name=&quot;eol&quot; value=&quot;${line.separator}&quot;/&gt;
&lt;pathconvert property=&quot;sorted&quot; pathsep=&quot;${eol}&quot;&gt;
  &lt;sort&gt;
    &lt;tokens&gt;
      &lt;string value=&quot;foo bar etc baz&quot;/&gt;
      &lt;stringtokenizer/&gt;
    &lt;/tokens&gt;
  &lt;/sort&gt;
&lt;/pathconvert&gt;</pre>
<p>The resource of type string <q>foo bar etc baz</q> is split into four tokens by
the <code>stringtokenizer</code>. These tokens are sorted and there <code>sorted</code> gets the
value of <q>bar baz etc foo</q>.</p>

<pre>
&lt;sort&gt;
  &lt;fileset dir=&quot;foo&quot;/&gt;
  &lt;reverse xmlns=&quot;antlib:org.apache.tools.ant.types.resources.comparators&quot;&gt;
    &lt;date/&gt;
  &lt;/reverse&gt;
&lt;/sort&gt;</pre>
<p>This takes all files from <samp>foo</samp> and sorts them by modification date in reverse
order.  Because the resource comparators used (<code>&lt;reverse&gt;</code>
and <code>&lt;date&gt;</code>) are in an internal antlib their namespace must be set explicitly.
</p>

<h4 id="first">first</h4>
<p>Includes the first <var>count</var> resources from a nested resource collection.  This can be
used in conjunction with the <a href="#sort">sort</a> collection, for example, to select the
first few oldest, largest, etc. resources from a larger collection.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>count</td>
    <td>The number of resources to include</td>
    <td>No; default <q>1</q></td>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>first</code>
      a <a href="../using.html#references">reference</a> to
      a <code>first</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>

<h4 id="last">last</h4>
<p><em>Since Ant 1.7.1</em></p>
<p>Includes the last <var>count</var> resources from a nested resource collection.  This can be
used in conjunction with the <a href="#sort">sort</a> collection, for example, to select the
last few oldest, largest, etc. resources from a larger collection.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>count</td>
    <td>The number of resources to include</td>
    <td>No; default <q>1</q></td>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>last</code>
      a <a href="../using.html#references">reference</a> to
      a <code>last</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>

<h4 id="allbutfirst">allbutfirst</h4>
<p><em>Since Ant 1.9.5</em></p>
<p>Includes all elements except for the first <var>count</var> resources from a nested resource
collection.  This can be used in conjunction with the <a href="#sort">sort</a> collection, for
example, to select all but the first few oldest, largest, etc. resources from a larger
collection.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>count</td>
    <td>The number of resources to exclude</td>
    <td>No; default <q>1</q></td>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>allbutfirst</code>
      a <a href="../using.html#references">reference</a> to
      an <code>allbutfirst</code> defined elsewhere. If specified no
      other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>

<h4 id="allbutlast">allbutlast</h4>
<p><em>Since Ant 1.9.5</em></p>
<p>Includes all elements except for the last <var>count</var> resources from a nested resource
collection.  This can be used in conjunction with the <a href="#sort">sort</a> collection, for
example, to select all but the last few oldest, largest, etc. resources from a larger
collection.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>count</td>
    <td>The number of resources to exclude</td>
    <td>No; default <q>1</q></td>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>allbutlast</code>
      a <a href="../using.html#references">reference</a> to
      an <code>allbutlast</code> defined elsewhere. If specified no
      other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>

<h4 id="tokens">tokens</h4>
<p>Includes the <a href="#string">string</a> tokens gathered from a nested resource collection. Uses
the same tokenizers supported by
the <a href="filterchain.html#tokenfilter">TokenFilter</a>. Imaginative use of this resource
collection can implement equivalents for such Unix functions as <kbd>sort</kbd>, <kbd>grep
-c</kbd>, <kbd>wc</kbd> and <kbd>wc -l</kbd>.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the nested resources</td>
    <td>No; default is default JVM character encoding</td>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>tokens</code>
      a <a href="../using.html#references">reference</a> to
      a <code>tokens</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h5>Parameters specified as nested elements</h5>
<ul>
  <li>A single resource collection is required.</li>
  <li>One nested tokenizer may be specified.  If omitted,
    a <a href="filterchain.html#linetokenizer">LineTokenizer</a> will be used.
  </li>
</ul>
<h5>Examples</h5>
<pre>
&lt;concat&gt;
  &lt;union&gt;
    &lt;sort&gt;
      &lt;tokens&gt;
        &lt;resources refid="input"/&gt;
        &lt;linetokenizer includedelims="true"/&gt;
      &lt;/tokens&gt;
    &lt;/sort&gt;
  &lt;/union&gt;
&lt;/concat&gt;</pre>
<p>Implements Unix <kbd>sort -u</kbd> against resource collection <q>input</q>.</p>

<h4 id="setlogic">Set operations</h4>
<p>The following resource collections implement set operations:</p>
<ul>
  <li><a href="#union">union</a></li>
  <li><a href="#intersect">intersect</a></li>
  <li><a href="#difference">difference</a></li>
</ul>

<h4 id="union">union</h4>
<p>Union of nested resource collections.</p>

<p>The only supported attribute is <code>refid</code> which makes this
  <code>union</code>
  a <a href="../using.html#references">reference</a> to
  an <code>union</code> defined elsewhere. If specified no other
  attributes or nested elements are allowed.</p>

<h4 id="intersect">intersect</h4>
<p>Intersection of nested resource collections.</p>

<p>The only supported attribute is <code>refid</code> which makes this
  <code>intersect</code>
  a <a href="../using.html#references">reference</a> to
  an <code>intersect</code> defined elsewhere. If specified no other
  attributes or nested elements are allowed.</p>

<h4 id="difference">difference</h4>
<p>Difference of nested resource collections.</p>

<p>The following attributes apply to all set-operation resource collections:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>difference</code>
      a <a href="../using.html#references">reference</a> to
      a <code>difference</code> defined elsewhere. If specified no
      other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h5>Examples</h5>
<pre>
&lt;resources id=&quot;A&quot;&gt;
    &lt;string value=&quot;a&quot;/&gt;
    &lt;string value=&quot;b&quot;/&gt;
&lt;/resources&gt;
&lt;resources id=&quot;B&quot;&gt;
    &lt;string value=&quot;b&quot;/&gt;
    &lt;string value=&quot;c&quot;/&gt;
&lt;/resources&gt;
&lt;union id=&quot;union&quot;&gt;&lt;resources refid=&quot;A&quot;/&gt;&lt;resources refid=&quot;B&quot;/&gt;&lt;/union&gt;
&lt;intersect id=&quot;intersect&quot;&gt;&lt;resources refid=&quot;A&quot;/&gt;&lt;resources refid=&quot;B&quot;/&gt;&lt;/intersect&gt;
&lt;difference id=&quot;difference&quot;&gt;&lt;resources refid=&quot;A&quot;/&gt;&lt;resources refid=&quot;B&quot;/&gt;&lt;/difference&gt;
&lt;echo&gt;
  A: ${toString:A}                    = a;b
  B: ${toString:B}                    = b;c

  union     : ${toString:union}       = a;b;c
  intersect : ${toString:intersect}   = b
  difference: ${toString:difference}  = a;c
&lt;/echo&gt;</pre>

<h4 id="mappedresources">mappedresources</h4>

<p><em>Since Ant 1.8.0</em></p>

<p>Wraps another resource collection and maps the names of the nested resources using
a <a href="mapper.html">mapper</a>.</p>

<p>Even if <var>mappedresources</var> wraps a resource collection that consists of file-system
based resources, <var>mappedresources</var> will not appear to be file-system based.  This means
you can't use <var>mappedresources</var> with tasks that only allow file-system based
resources.</p>

<h5>Parameters specified as attributes</h5>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; enabling may improve performance.  <em>Since Ant
      1.8.1</em></td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>enablemultiplemappings</td>
    <td>If <q>true</q> the collection will use all the mappings for a given source
      path. If <q>false</q> it will only process the first resource.
      <em>since Ant 1.8.1</em></td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>mappedresources</code>
      a <a href="../using.html#references">reference</a> to
      a <code>mappedresources</code> defined elsewhere. If specified
      no other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h5>Parameters specified as nested elements</h5>
<p>A single resource collection is required.</p>
<p>A single <a href="mapper.html">mapper</a> can be used to map names.  If no mapper has been
given (which doesn't make any sense, honestly), an identity mapper will be used.</p>

<h5>Examples</h5>

<p>Copies all files from a given directory to a target directory adding <samp>.bak</samp> as an
extension.  Note this could be done with a <code>mapper</code> nested into <code>copy</code>
directly as well.</p>

<pre>
&lt;copy todir="${target}"&gt;
  &lt;mappedresources&gt;
    &lt;fileset dir="${src}"/&gt;
    &lt;globmapper from="*" to="*.bak"/&gt;
  &lt;/mappedresources&gt;
&lt;/copy&gt;</pre>

<p>Creates a WAR archive adding all <code>CLASSPATH</code> entries that are files to
the <samp>WEB-INF/lib</samp> directory without keeping their files-system structure.</p>

<pre>
&lt;war destfile="${output}"&gt;
  &lt;mappedresources&gt;
    &lt;restrict&gt;
      &lt;path path="${java.class.path}"/&gt;
      &lt;type type="file"/&gt;
    &lt;/restrict&gt;
    &lt;chainedmapper&gt;
      &lt;flattenmapper/&gt;
      &lt;globmapper from="*" to="WEB-INF/lib/*"/&gt;
    &lt;/chainedmapper&gt;
  &lt;/mappedresources&gt;
&lt;/war&gt;</pre>

<h4 id="archives">archives</h4>

<p><em>Since Ant 1.8.0</em></p>

<p>This resource collection accepts an arbitrary number of nested resources and assumes that all
those resources must be either ZIP or TAR archives.  The resources returned
by <code>&lt;archives&gt;</code> are the contents of the nested archives.</p>

<p>This resource collection is a generalization
of <a href="../Tasks/zip.html#zipgroupfileset">zipgroupfileset</a> which is only supported by
the zip family of tasks.</p>

<p>The only supported attribute is <code>refid</code> which makes this
  <code>archives</code>
  a <a href="../using.html#references">reference</a> to
  an <code>archives</code> defined elsewhere. If specified no other
  attributes or nested elements are allowed.</p>

<h5>Parameters specified as nested elements</h5>

<p><code>&lt;archives&gt;</code> has two nested elements <code>&lt;zips&gt;</code>
and <code>&lt;tars&gt;</code> that are <a href="#union">unions</a> themselves, i.e. they accept
arbitrary many resource(collection)s as nested elements.</p>

<p>The nested resources of <code>&lt;zips&gt;</code> are treated as ZIP archives, the nested
resources of <code>&lt;tars&gt;</code> as TAR archives.</p>

<h5>Examples</h5>

<p>Copies all files from all jars that are on the classpath to <samp>${target}</samp>.</p>

<pre>
&lt;copy todir="${target}"&gt;
  &lt;archives&gt;
    &lt;zips&gt;
      &lt;restrict&gt;
        &lt;path path="${java.class.path}"/&gt;
        &lt;name name="*.jar"/&gt;
      &lt;/restrict&gt;
    &lt;/zips&gt;
  &lt;/archives&gt;
&lt;/copy&gt;</pre>

<h4 id="resourcelist">resourcelist</h4>

<p><em>Since Ant 1.8.0</em></p>

<p>This resource collection accepts an arbitrary number of nested resources, reads those
resources and returns a resource for each line read.</p>

<p>If the line contains a colon, Ant will try to use it as an URL and if that fails (or the line
doesn't contain a colon) will return a file resource with the line's content as its name.</p>

<p>Properties will be expanded for each line.  If the property expansion yields a resource
object rather than a string (for example because of custom property helpers), the resources will
be returned directly.</p>

<p><code>&lt;resourcelist&gt;</code> is a generalization
of <a href="filelist.html"><code>&lt;filelist&gt;</code></a>.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the nested resources</td>
    <td>No; default is default JVM character encoding</td>
  </tr>
  <tr>
    <td>basedir</td>
    <td>Base directory that is used to resolve relative file names
      against. Is also used to provide a base directory to the
      FileResources created by this resource collection. <em>Since Ant
      1.10.4</em>
    </td>
    <td>No</td>
  </tr>
  <tr>
    <td>preserveduplicates</td>
    <td>Makes this <code>resourcelist</code> return all resources as
      many times as they are specified. Otherwise
      <code>resourcelist</code> will only return each resource, in the
      order they first appear. <em>Since Ant 1.10.10</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>resourcelist</code>
      a <a href="../using.html#references">reference</a> to
      a <code>resourcelist</code> defined elsewhere. If specified no
      other attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>

<h5>Parameters specified as nested elements</h5>

<p><code>&lt;resourcelist&gt;</code> accepts arbitrary many resource(collection)s as nested
elements.</p>

<p>In addition <code>&lt;resourcelist&gt;</code> supports
nested <code>&lt;filterchain&gt;</code> elements that can be used to filter/modify the read
resources before their lines get expanded.  Such a nested element corresponds to
a <a href="filterchain.html">filterchain</a>.</p>

<h5>Examples</h5>

<p>The following example copies a file from the first URL of several alternatives that can
actually be reached.  It assumes that the file <samp>mirrors.txt</samp> looks like</p>

<pre>
    mirrors.txt:
https://best.mirror.example.org/
http://second.best.mirror.example.org/mirror/of/best/
https://yet.another.mirror/
https://the.original.site/</pre>

<pre>
&lt;copy todir="${target}"&gt;
  &lt;first&gt;
    &lt;restrict&gt;
      &lt;resourcelist&gt;
        &lt;file file="mirrors.txt"/&gt;
      &lt;/resourcelist&gt;
      &lt;exists/&gt;
    &lt;/restrict&gt;
  &lt;/first&gt;
&lt;/copy&gt;</pre>

</body>
</html>
