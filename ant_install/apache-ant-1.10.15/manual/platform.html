<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Platform Issues</title>
</head>
<body>

<h1>Platform Issues</h1>

<h2>Java versions</h2>
<h3>Java 5</h3>
<p>
You may need a bigger stack than default, especially if you are using the built in XSLT engine. We
recommend you use Apache Xalan; indeed, some tasks (JUnit report in XML, for example) may not work
against the shipping XSL engine.
</p>

<h2>Unix and Linux</h2>
<ul>
<li>You should use a GNU version of <kbd>tar</kbd> to untar the Apache Ant source tree, if you have
downloaded this as a tar file. If you get weird errors about missing files, this is the
problem.</li>
<li>Ant does not preserve file permissions when a file is copied, moved or archived, because Java
does not let it read or write the permissions.  Use <code>&lt;chmod&gt;</code> to set permissions,
and when creating a tar archive, use the <var>mode</var> attribute
of <code>&lt;tarfileset&gt;</code> to set the permissions in the tar file,
or <code>&lt;apply&gt;</code> the real <kbd>tar</kbd> program.</li>
<li>Ant is not symbolic link aware in moves, deletes and when recursing down a tree of directories
to build up a list of files. Unexpected things can happen.</li>
<li>Linux on IA-64: apparently you need a larger heap than the default one (64M) to compile big
projects. If you get out of heap errors, either increase the heap or use a
forking <code>&lt;javac&gt;</code>. Better yet, use <kbd>jikes</kbd> for extra compilation
speed.</li>
</ul>

<h2>Microsoft Windows</h2>
<p>
Windows 9x (win95, win98, win98SE and winME) are not supported <em>since Ant 1.7</em>.
</p>
<p>
The Ant team has retired support for these products because they are outdated and can expose
customers to security risks. We recommend that customers who are still running Windows 98 or Windows
ME upgrade to a newer, more secure operating system, as soon as possible.
</p>
<p>
Customers who upgrade to Linux report improved security, richer functionality, and increased
productivity.
</p>

<h2>Microsoft Windows 2K, XP and Server 2K03</h2>
<p>
Windows 9x (win95, win98, win98SE and winME) has a batch file system which does not work fully with
long file names, so we recommend that Ant and JDK are installed into directories without spaces, and
with 8.3 filenames.  The Perl and Python launcher scripts do not suffer from this limitation.
</p>
<p>
All versions of Windows are usually case insensitive, although mounted file systems (Unix drives,
ClearCase views) can be case sensitive underneath, confusing patternsets.
</p>
<p>
Ant can often not delete a directory which is open in an Explorer window.  There is nothing we can
do about this short of spawning a program to kill the shell before deleting directories.  Nor can
files that are in use be overwritten.
</p>
<p>
Finally, if any Ant task fails with an <code>error=2</code>, it means that whatever native program
Ant is trying to run, it is not on the <code>Path</code>.
</p>

<h2>Microsoft Windows Vista</h2>
<p>
There are reports of problems with Windows Vista security bringing up dialog boxes asking if the
user wants to run an untrusted executable during an Ant run, such as when
the <code>&lt;signjar&gt;</code> task runs the <kbd>jarsigner.exe</kbd> program. This is beyond
Ant's control, and stems from the OS trying to provide some illusion of security by being reluctant
to run unsigned native executables.  The latest Java versions appear to resolve this problem by
having signed binaries.
</p>

<h2>Cygwin</h2>
<p>
Cygwin is not an operating system; rather it is an application suite running under Windows and
providing some UNIX like functionality. Sun has not created any specific Java Development Kit or
Java Runtime Environment for cygwin. See this link: <a href="http://www.inonit.com/cygwin/faq/"
target="_top">http://www.inonit.com/cygwin/faq/</a>.  Only Windows path names are supported by JDK
and JRE tools under Windows or cygwin. Relative path names such as <samp>src/org/apache/tools</samp>
are supported, but Java tools do not understand <samp>/cygdrive/c</samp> to mean <samp>c:\</samp>.
</p>
<p>
The utility <kbd>cygpath</kbd> (used industrially in the <kbd>ant</kbd> script to support
cygwin) can convert cygwin path names to Windows.  You can use the <code>&lt;exec&gt;</code> task in
Ant to convert cygwin paths to Windows path, for instance like that:
</p>
<pre>
&lt;property name=&quot;some.cygwin.path&quot; value=&quot;/cygdrive/h/somepath&quot;/&gt;
&lt;exec executable=&quot;cygpath&quot; outputproperty=&quot;windows.pathname&quot;&gt;
   &lt;arg value=&quot;--windows&quot;/&gt;
   &lt;arg value=&quot;${some.cygwin.path}&quot;/&gt;
&lt;/exec&gt;
&lt;echo message=&quot;${windows.pathname}&quot;/&gt;
</pre>
<p>
We get lots of support calls from Cygwin users. Either it is incredibly popular, or it is
trouble. If you do use it, remember that Java is a Windows application, so Ant is running in a
Windows process, not a Cygwin one. This will save us having to mark your bug reports as invalid.
</p>

<h2>Apple MacOS X/macOS</h2>
<p>
MacOS X a.k.a. macOS is the first of the Apple platforms that Ant supports completely; it is treated
like any other Unix.
</p>

<h2>Other platforms</h2>
<p>
Support for other platforms is not guaranteed to be complete, as certain techniques to hide platform
details from build files need to be written and tested on every particular platform. Contributions
in this area are welcome.
</p>

</body>
</html>
