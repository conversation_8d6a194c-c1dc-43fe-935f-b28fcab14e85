<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css"/>
<title>Apache Ant User Manual</title>
<base target="mainFrame"/>
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Using Apache Ant</h3>
<ul class="inlinelist">
  <li><a href="using.html#buildfile">Writing a Simple Buildfile</a></li>
  <li class="indent"><a href="using.html#projects">Projects</a></li>
  <li class="indent"><a href="targets.html#targets">Targets</a></li>
  <li class="indent"><a href="using.html#tasks">Tasks</a></li>
  <li class="indent"><a href="using.html#properties">Properties</a></li>
  <li class="indent"><a href="properties.html#built-in-props">Built-in Properties</a></li>
  <li class="indent"><a href="properties.html#propertyHelper">Property Helpers</a></li>
  <li class="indent"><a href="using.html#example">Example Buildfile</a></li>
  <li class="indent"><a href="using.html#filters">Token Filters</a></li>
  <li class="indent"><a href="using.html#path">Path-like Structures</a></li>
  <li class="indent"><a href="using.html#arg">Command-line Arguments</a></li>
  <li class="indent"><a href="using.html#references">References</a></li>
  <li class="indent"><a href="using.html#external-tasks">Use of external tasks</a></li>
</ul>
</body>
</html>
