<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css"/>
<title>Apache Ant User Manual</title>
<base target="mainFrame"/>
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Concepts</h3>
<ul class="inlinelist">
<li><a href="targets.html">Targets and Extension-Points</a></li>
<li><a href="properties.html">Properties and PropertyHelpers</a></li>
<li><a href="clonevm.html">ant.build.clonevm</a></li>
<li><a href="sysclasspath.html">build.sysclasspath</a></li>
<li><a href="javacprops.html">Ant properties controlling javac</a></li>
<li><a href="Tasks/common.html">Common Attributes</a></li>
<li><a href="ifunless.html">If and Unless Attributes</a></li>
</ul>

<h3>List of Types</h3>
<ul class="inlinelist">
<li><a href="Types/classfileset.html">Class Fileset</a></li>
<li><a href="Types/description.html">Description Type</a></li>
<li><a href="dirtasks.html">Directory-based Tasks</a></li>
<li><a href="Types/dirset.html">DirSet</a></li>
<li><a href="Types/extension.html">Extension Package</a></li>
<li><a href="Types/extensionset.html">Set of Extension Packages</a></li>
<li><a href="Types/filelist.html">FileList</a></li>
<li><a href="Types/fileset.html">FileSet</a></li>
<li><a href="Types/mapper.html">File Mappers</a></li>
<li><a href="Types/filterchain.html">FilterChains and FilterReaders</a></li>
<li><a href="Types/filterset.html">FilterSet</a></li>
<li><a href="Types/multirootfileset.html">MultiRootFileSet</a></li>
<li><a href="Types/patternset.html">PatternSet</a></li>
<li><a href="using.html#path">Path-like Structures</a></li>
<li><a href="Types/permissions.html">Permissions</a></li>
<li><a href="Types/propertyset.html">PropertySet</a></li>
<li><a href="Types/redirector.html">I/O Redirectors</a></li>
<li><a href="Types/regexp.html">Regexp</a></li>
<li><a href="Types/resources.html">Resources</a></li>
<li><a href="Types/resources.html#collection">Resource Collections</a></li>
<li><a href="Types/selectors.html">Selectors</a></li>
<li><a href="Types/tarfileset.html">TarFileSet</a></li>
<li><a href="Types/xmlcatalog.html">XMLCatalog</a></li>
<li><a href="Types/zipfileset.html">ZipFileSet</a></li>
</ul>

<h3>Namespace</h3>
<ul class="inlinelist">
<li><a href="Types/namespace.html">Namespace Support</a></li>
</ul>

<h3>Antlib</h3>
<ul class="inlinelist">
<li><a href="Types/antlib.html">Antlib</a></li>
<li><a href="Types/antlib.html#antlibnamespace">Antlib namespace</a></li>
<li><a href="Types/antlib.html#currentnamespace">Current namespace</a></li>
</ul>

<h3>Custom Components</h3>
<ul class="inlinelist">
<li><a href="Types/custom-programming.html">Custom Components</a></li>
<li><a href="Types/custom-programming.html#customconditions">Conditions</a></li>
<li><a href="Types/custom-programming.html#customselectors">Selectors</a></li>
<li><a href="Types/custom-programming.html#filterreaders">FilterReaders</a></li>
</ul>

</body>
</html>
