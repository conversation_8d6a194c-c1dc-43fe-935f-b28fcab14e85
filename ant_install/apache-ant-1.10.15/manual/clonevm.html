<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>ant.build.clonevm</title>
</head>

<body>

<h2 id="clonevm">ant.build.clonevm</h2>

<p><em>Since Apache Ant 1.7</em></p>

<p>The value of the <code>ant.build.clonevm</code> system property
controls how Ant instruments forked JVMs.  The <a href="Tasks/java.html">java</a>
and <a href="Tasks/junit.html">junit</a> tasks support <var>clonevm</var>
attributes to control JVMs on a task-by-task basis while the system
property applies to all forked JVMs.</p>

<p>If the value of the property is <q>true</q>, then all system properties of
the forked JVM will be the same as those of the JVM running Ant. In
addition, if you set <code>ant.build.clonevm</code> to <q>true</q>
and <a href="sysclasspath.html">build.sysclasspath</a> has not been
set, the <var>bootclasspath</var> of forked JVMs gets constructed as
if <code>build.sysclasspath</code> had the value <q>last</q>.</p>

<p>Note that this has to be a system property, so it cannot be
specified on the Ant command line.  Use the <code>ANT_OPTS</code>
environment variable instead.</p>

</body>
</html>
