<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Rename Task</title>
</head>

<body>

<h2 id="rename">Rename</h2>
<h3><em><u>Deprecated</u></em></h3>
<p><em>This task has been <u>deprecated</u>.  Use the <code>Move</code> task instead.</em></p>
<h3>Description</h3>
<p>Renames a given file.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>src</td>
    <td>file to rename.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>dest</td>
    <td>new name of the file.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>Enable replacing of existing file.</td>
    <td>No; default is <q>on</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Rename the file <samp>foo.jar</samp> to <samp>${name}-${version}.jar</samp>
(assuming <code>name</code> and <code>version</code> being predefined properties). If a file
named <samp>${name}-${version}.jar</samp> already exists, it will be removed prior to
renaming <samp>foo.jar</samp>.</p>
<pre>&lt;rename src=&quot;foo.jar&quot; dest=&quot;${name}-${version}.jar&quot;/&gt;</pre>

</body>
</html>
