<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>GUnzip/BUnzip2 Task</title>
</head>

<body>

<h2 id="unpack">GUnzip/BUnzip2/UnXZ</h2>
<h3>Description</h3>
<p>Expands a resource packed using GZip, BZip2 or XZ.</p>

<p>If <var>dest</var> is a directory the name of the destination file is the same as <var>src</var>
(with the <q>.gz</q>, <q>.bz2</q> or <q>.xz</q> extension removed if present). If <var>dest</var> is
omitted, the parent dir of <var>src</var> is taken. The file is only expanded if the source resource
is newer than the destination file, or when the destination file does not exist.</p>

<p>XZ compression support has been added <em>since Apache Ant 1.10.1</em> and depends on external
libraries not included in the Ant distribution.
See <a href="../install.html#librarydependencies">Library Dependencies</a> for more information.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>src</td>
    <td>the file to expand.</td>
    <td>Yes, or a nested resource collection</td>
  </tr>
  <tr>
    <td>dest</td>
    <td>the destination file or directory.</td>
    <td>No</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>any <a href="../Types/resources.html">resource</a> or single element resource collection</h4>

<p>The specified resource will be used as <var>src</var>.</p>

<h3>Examples</h3>
<p>Expand <samp>test.tar.gz</samp> to <samp>test.tar</samp></p>
<pre>&lt;gunzip src=&quot;test.tar.gz&quot;/&gt;</pre>

<p>Expand <samp>test.tar.bz2</samp> to <samp>test.tar</samp></p>
<pre>&lt;bunzip2 src=&quot;test.tar.bz2&quot;/&gt;</pre>

<p>Expand <samp>test.tar.xz</samp> to <samp>test.tar</samp></p>
<pre>&lt;unxz src=&quot;test.tar.xz&quot;/&gt;</pre>

<p>Expand <samp>test.tar.gz</samp> to <samp>test2.tar</samp></p>
<pre>&lt;gunzip src=&quot;test.tar.gz&quot; dest=&quot;test2.tar&quot;/&gt;</pre>

<p>Expand <samp>test.tar.gz</samp> to <samp>subdir/test.tar</samp> (assuming <samp>subdir</samp> is
a directory).</p>
<pre>&lt;gunzip src=&quot;test.tar.gz&quot; dest=&quot;subdir&quot;/&gt;</pre>

<p>Download <samp>https://example.org/archive.tar.gz</samp> and expand it to <samp>archive.tar</samp>
in the project's <var>basedir</var> on the fly.</p>
<pre>
&lt;gunzip dest=&quot;.&quot;&gt;
  &lt;url url="https://example.org/archive.tar.gz"/&gt;
&lt;/gunzip&gt;
</pre>

<h3>Related tasks</h3>

<p>The following</p>
<pre>&lt;gunzip src="some-archive.gz" dest="some-dest-dir"/&gt;</pre>
<p>is identical to</p>
<pre>
&lt;copy todir="some-dest-dir"&gt;
  &lt;gzipresource&gt;
    &lt;file file="some-archive.gz"/&gt;
  &lt;/gzipresource&gt;
  &lt;mapper type="glob" from="*.gz" to="*"/&gt;
&lt;/copy&gt;</pre>

<p>The same is also true for <code>&lt;bunzip2&gt;</code> and <code>&lt;bzip2resource&gt;</code>
or <code>&lt;unxz&gt;</code> and <code>&lt;xzresource&gt;</code>.  <code>&lt;copy&gt;</code> offers
additional features like <a href="../Types/filterchain.html">filtering files</a> on the fly,
allowing a file to be mapped to multiple destinations, preserving the last modified time or a
configurable file system timestamp granularity.</p>

</body>
</html>
