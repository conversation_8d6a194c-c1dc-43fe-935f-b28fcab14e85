<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>XmlProperty Task</title>
</head>

<body>

<h2 id="xmlproperty">XmlProperty</h2>
<h3>Description</h3>
<p>Loads property values from a well-formed xml file. There are no other restrictions than
"well-formed". You can choose the layout you want. For example this XML property file:</p>
<pre>
  &lt;root&gt;
    &lt;properties&gt;
      &lt;foo&gt;bar&lt;/foo&gt;
    &lt;/properties&gt;
  &lt;/root&gt;</pre>
<p>is roughly equivalent to this Java property file:</p>
<pre>root.properties.foo = bar</pre>

<p>By default, this load does <em>no</em> processing of the input.  In particular, unlike
the <a href="property.html">Property task</a>, property references (i.e., <samp>${foo}</samp>) are
not resolved.</p>
<h3 id="semanticAttributes">Semantic attributes</h3>
<p>Input processing can be enabled by using the <var>semanticAttributes</var> attribute.  If this
attribute is set to <q>true</q> (its default is <q>false</q>), the following processing occurs as
the input XML file is loaded:/p>
<ul>
  <li>Property references are resolved.</li>
  <li>The following attributes are treated differently:
    <ul>
      <li><var>id</var>: The property is associated with the given id value.</li>
      <li><var>location</var>: The property is treated as a file location</li>
      <li><var>refid</var>: The property is set to the value of the referenced property.</li>
      <li><var>value</var>: The property is set to the value indicated.</li>
    </ul>
  </li>
  <li><a href="../using.html#path">path-like structures</a> can be defined by use of the following
    attributes:
    <ul>
      <li><var>pathid</var>: The given <var>id</var> is used to identify a path.  The nested XML tag
        name is ignored.  Child elements can be used (XML tag names are ignored) to identify
        elements of the path.</li>
    </ul>
  </li>
</ul>
<p>For example, with semantic attribute processing enabled, this XML property file:</p>
<pre>
&lt;root&gt;
  &lt;properties&gt;
    &lt;foo location="bar"/&gt;
    &lt;quux&gt;${root.properties.foo}&lt;/quux&gt;
  &lt;/properties&gt;
&lt;/root&gt;</pre>
<p>is roughly equivalent to the following fragments in a <samp>build.xml</samp> file:</p>
<pre>
&lt;property name="root.properties.foo" location="bar"/&gt;
&lt;property name="root.properties.quux" value="${root.properties.foo}"/&gt;</pre>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>The XML file to parse.</td>
    <td>Yes, or a nested resource collection</td>
  </tr>
  <tr>
    <td>prefix</td>
    <td>The prefix to prepend to each property.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>keepRoot</td>
    <td>Keep the XML root tag as the first value in the property name.</td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>validate</td>
    <td>Validate the input file (e.g. by a DTD). Otherwise the XML must only be well-formed.</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>collapseAttributes</td>
    <td>Treat attributes as nested elements.</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>semanticAttributes</td>
    <td>Enable special handling of certain attribute names.  See
      the <a href="#semanticAttributes">Semantic Attributes</a> section for more information.</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>includeSemanticAttribute</td>
    <td>Include the semantic attribute name as part of the property name.  Ignored
      if <var>semanticAttributes</var> is not set to <q>true</q>.  See
      the <a href="#semanticAttributes">Semantic Attributes</a> section for more information.</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>rootDirectory</td>
    <td>The directory to use for resolving file references. Ignored if <var>semanticAttributes</var>
      is not set to <q>true</q>.</td>
    <td>No; default is <var>basedir</var></td>
  </tr>
  <tr>
    <td>delimiter</td>
    <td>Delimiter for splitting multiple values.<br/><em>since Apache Ant 1.7.1</em></td>
    <td>No; defaults to <q>,</q> (comma)</td>
  </tr>
</table>

<h3 id="nested">Parameters specified as nested elements</h3>
<h4>xmlcatalog</h4>
<p>The <a href="../Types/xmlcatalog.html"><code>&lt;xmlcatalog&gt;</code></a> element is used to
perform entity resolution.</p>

<h4>any <a href="../Types/resources.html">resource</a> or single element resource collection</h4>

<p>The specified resource will be used as input.</p>

<h3 id="examples">Examples</h3>

<h4>Non-semantic attributes</h4>

<p>Here is an example XML file that does not have any semantic attributes.</p>

<pre>
&lt;root-tag myattr="true"&gt;
  &lt;inner-tag someattr="val"&gt;Text&lt;/inner-tag&gt;
  &lt;a2&gt;&lt;a3&gt;&lt;a4&gt;false&lt;/a4&gt;&lt;/a3&gt;&lt;/a2&gt;
&lt;/root-tag&gt;</pre>

<h5>default loading</h5>
<p>This entry in a build file:</p>
<pre>&lt;xmlproperty file="somefile.xml"/&gt;</pre>
<p>is equivalent to the following properties:</p>
<pre>
root-tag(myattr)=true
root-tag.inner-tag=Text
root-tag.inner-tag(someattr)=val
root-tag.a2.a3.a4=false</pre>

<h5><var>collapseAttributes</var>=<q>false</q></h5>
<p>This entry in a build file:</p>
<pre>&lt;xmlproperty file="somefile.xml" collapseAttributes="true"/&gt;</pre>
<p>is equivalent to the following properties:</p>
<pre>
root-tag.myattr=true
root-tag.inner-tag=Text
root-tag.inner-tag.someatt=val
root-tag.a2.a3.a4=false</pre>

<h5><var>keepRoot</var>=<q>false</q></h5>
<p>This entry in a build file:</p>
<pre>&lt;xmlproperty file="somefile.xml" keepRoot="false"/&gt;</pre>
<p>is equivalent to the following properties:</p>
<pre>
inner-tag=Text
inner-tag(someattr)=val
a2.a3.a4=false</pre>

<h4>Semantic attributes</h4>

<p>Here is an example XML file that has semantic attributes.</p>
<pre>
&lt;root-tag&gt;
  &lt;version value="0.0.1"/&gt;
  &lt;build folder="build"&gt;
    &lt;classes id="build.classes" location="${build.folder}/classes"/&gt;
    &lt;reference refid="build.classes"/&gt;
  &lt;/build&gt;
  &lt;compile&gt;
    &lt;classpath pathid="compile.classpath"&gt;
      &lt;pathelement location="${build.classes}"/&gt;
    &lt;/classpath&gt;
  &lt;/compile&gt;
  &lt;run-time&gt;
    &lt;jars&gt;*.jar&lt;/jars&gt;
    &lt;classpath pathid="run-time.classpath"&gt;
      &lt;path refid="compile.classpath"/&gt;
      &lt;pathelement path="${run-time.jars}"/&gt;
    &lt;/classpath&gt;
  &lt;/run-time&gt;
&lt;/root-tag&gt;</pre>

<h5>default loading (<var>semanticAttributes</var>=<q>true</q>)</h5>
<p>This entry in a build file:</p>
<pre>
&lt;xmlproperty file="somefile.xml" keepRoot="false"
             semanticAttributes="true"/&gt;</pre>
<p>is equivalent to the following entries in a build file:</p>
<pre>
&lt;property name="version" value="0.0.1"/&gt;
&lt;property name="build.folder" value="build"/&gt;
&lt;property name="build.classes" location="${build.folder}/classes" id="build.classes"/&gt;
&lt;property name="build.reference" refid="build.classes"/&gt;

&lt;property name="run-time.jars" value="*.jar"/&gt;

&lt;path id="compile.classpath"&gt;
  &lt;pathelement location="${build.classes}"/&gt;
&lt;/path&gt;

&lt;path id="run-time.classpath"&gt;
  &lt;path refid="compile.classpath"/&gt;
  &lt;pathelement path="${run-time.jars}"/&gt;
&lt;/path&gt;</pre>

<h5><var>includeSemanticAttribute</var>=<q>true</q></h5>
<p>This entry in a build file:</p>
<pre>
&lt;xmlproperty file="somefile.xml"
             semanticAttributes="true" keepRoot="false"
             includeSemanticAttribute="true"/&gt;</pre>
<p>is equivalent to the following entries in a build file:</p>
<pre>
&lt;property name="version.value" value="0.0.1"/&gt;
&lt;property name="build.folder" value="build"/&gt;
&lt;property name="build.classes.location" location="${build.folder}/classes"/&gt;
&lt;property name="build.reference.refid" refid="build.classes"/&gt;

&lt;property name="run-time.jars" value="*.jar"/&gt;

&lt;path id="compile.classpath"&gt;
  &lt;pathelement location="${build.classes}"/&gt;
&lt;/path&gt;

&lt;path id="run-time.classpath"&gt;
  &lt;path refid="compile.classpath"/&gt;
  &lt;pathelement path="${run-time.jars}"/&gt;
&lt;/path&gt;</pre>

</body>
</html>
