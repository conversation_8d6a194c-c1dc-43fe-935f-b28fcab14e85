<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>CVSPass Task</title>
</head>

<body>

<h2 id="cvs">cvspass</h2>
<h3>Description</h3>
<p>Adds entries to a <samp>.cvspass</samp> file. Adding entries to this file has the same affect as
a <kbd>cvs login</kbd> command.</p>

<p><strong>CVSNT Note</strong>: CVSNT prefers users to store the passwords inside the registry.  If
the task doesn't seem to work for you, the most likely reason is that CVSNT ignores
your <samp>.cvspass</samp> file
completely. See <a href="https://issues.apache.org/bugzilla/show_bug.cgi?id=21657#c5"
target="_top">bugzilla report 21657</a> for recommended workarounds.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>cvsroot</td>
    <td>the CVS repository to add an entry for.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>password</td>
    <td>Password to be added to the password file.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>passfile</td>
    <td>Password file to add the entry to.</td>
    <td>No; default is <q>~/.cvspass</q></td>
  </tr>
</table>
<h3>Examples</h3>
<p>Add an entry into the <samp>~/.cvspass</samp> password file.</p>
<pre>
&lt;cvspass cvsroot=&quot;:pserver:<EMAIL>:/home/<USER>
         password=&quot;anoncvs&quot;/&gt;</pre>

</body>
</html>
