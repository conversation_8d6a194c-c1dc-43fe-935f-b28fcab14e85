<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JJTree Task</title>
</head>
<body>

<h2 id="jjtree">JJTree</h2>

<h3>Description</h3>
<p>Invokes the <a href="https://javacc.org/" target="_top">JJTree</a> preprocessor for the JavaCC
compiler compiler. It inserts parse tree building actions at various places in the JavaCC source
that it generates. The output of JJ<PERSON><PERSON> is run through JavaCC to create the parser.</p>
<p>To use the <code>jjtree</code> task, set the <var>target</var> attribute to the name of the
JJTree grammar file to process. You also need to specify the directory containing the JavaCC
installation using the <var>javacchome</var> attribute, so that Ant can find the JavaCC
classes. Optionally, you can also set the <var>outputdirectory</var> to write the generated JavaCC
grammar and node files to a specific directory.  Otherwise <code>jjtree</code> writes the generated
JavaCC grammar and node files to the directory containing the JJTree grammar file. As an extra
option, you can also set the <var>outputfile</var> to write the generated JavaCC grammar file to a
specific (directory and) file.  Otherwise <code>jjtree</code> writes the generated JavaCC grammar
file as the JJTree grammar file with a suffix <samp>.jj</samp>.</p>
<p>This task only invokes JJTree if the grammar file is newer than the generated JavaCC file.</p>

<h3>Parameters</h3>

<table class="attr">
<tr>
  <th scope="col">Attribute</th>
  <th scope="col">Description</th>
  <th scope="col">Required</th>
</tr>

<tr>
  <td>target</td>
  <td>The JJTree grammar file to process.</td>
  <td>Yes</td>
</tr>

<tr>
  <td>javacchome</td>
  <td>The directory containing the JavaCC distribution.</td>
  <td>Yes</td>
</tr>

<tr>
  <td>outputdirectory</td>
  <td>The directory to write the generated JavaCC grammar and node files to.  If not set, the files
    are written to the directory containing the grammar file.</td>
  <td>No</td>
</tr>

<tr>
  <td>outputfile</td>
  <td>The file to write the generated JavaCC grammar file to. If not set, the file is written with
    the same name as the JJTree grammar file but with a the suffix <code>.jj</code>.  This is a
    filename relative to <var>outputdirectory</var> if specified, the project's basedir.</td>
  <td>No</td>
</tr>

<tr>
  <td>buildnodefiles</td>
  <td>Sets the BUILD_NODE_FILES grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>multi</td>
  <td>Sets the MULTI grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>nodedefaultvoid</td>
  <td>Sets the NODE_DEFAULT_VOID grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>nodefactory</td>
  <td>Sets the NODE_FACTORY grammar option. This is boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>nodescopehook</td>
  <td>Sets the NODE_SCOPE_HOOK grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>nodeusesparser</td>
  <td>Sets the NODE_USES_PARSER grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>static</td>
  <td>Sets the STATIC grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>visitor</td>
  <td>Sets the VISITOR grammar option. This is a boolean option.</td>
  <td>No</td>
</tr>

<tr>
  <td>nodepackage</td>
  <td>Sets the NODE_PACKAGE grammar option. This is a string option.</td>
  <td>No</td>
</tr>

<tr>
  <td>visitorexception</td>
  <td>Sets the VISITOR_EXCEPTION grammar option. This is a string option.</td>
  <td>No</td>
</tr>

<tr>
  <td>nodeprefix</td>
  <td>Sets the NODE_PREFIX grammar option. This is a string option.</td>
  <td>No</td>
</tr>

<tr>
  <td>maxmemory</td>
  <td>Max amount of memory to allocate to the forked JVM.  <em>since Ant 1.8.3</em></td>
  <td>No</td>
</tr>
</table>

<h3>Example</h3>

<p>Invoke JJTree on grammar file <samp>src/Parser.jjt</samp>, writing the generated grammar
file, <samp>Parser.jj</samp>, to <samp>build/src</samp>. The grammar option NODE_USES_PARSER is set
to <q>true</q> when invoking JJTree.</p>
<pre>
&lt;jjtree target="src/Parser.jjt"
        outputdirectory="build/src"
        javacchome="c:/program files/JavaCC"
        nodeusesparser="true"/&gt;</pre>

<h3>Comparison of output locations between command line JJTree and different Ant <code>taskdef</code>
versions</h3>

<table>
  <thead class="no-bold">
<tr>
  <th scope="col"><strong>Command line JJTree options</strong> and <em>generated files</em> (working directory: <samp>/tmp</samp>)</th>
  <th scope="col"><strong>Ant 1.5.3</strong> versus command line</th>
  <th scope="col"><strong>Ant 1.6</strong> versus command line</th>
</tr>
  </thead>
  <tbody>
<tr>
  <td><pre><b>jjtree  grammar.jjt</b>
    /tmp/grammar.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree  relative/grammar.jjt</b>
    /tmp/grammar.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td><pre>
/tmp/relative/grammar.jj
/tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree /tmp/absolute/grammar.jjt</b>
    /tmp/grammar.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td><pre>
/tmp/absolute/grammar.jj
/tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_DIRECTORY:relative grammar.jjt</b>
    /tmp/relative/grammar.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_DIRECTORY:relative relative/grammar.jjt</b>
    /tmp/relative/grammar.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_DIRECTORY:relative /tmp/absolute/grammar.jjt</b>
    /tmp/relative/grammar.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_DIRECTORY:/tmp/absolute/ grammar.jjt</b>
    /tmp/absolute/grammar.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_DIRECTORY:/tmp/absolute/ relative/grammar.jjt</b>
    /tmp/absolute/grammar.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_DIRECTORY:/tmp/absolute/ /tmp/absolute/grammar.jjt</b>
    /tmp/absolute/grammar.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Same</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj grammar.jjt</b>
    /tmp/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj relative/grammar.jjt</b>
    /tmp/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj /tmp/absolute/grammar.jjt</b>
    /tmp/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj -OUTPUT_DIRECTORY:relative grammar.jjt</b>
    /tmp/relative/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj -OUTPUT_DIRECTORY:relative relative/grammar.jjt</b>
    /tmp/relative/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj -OUTPUT_DIRECTORY:relative /tmp/absolute/grammar.jjt</b>
    /tmp/relative/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ grammar.jjt</b>
    /tmp/absolute/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ relative/grammar.jjt</b>
    /tmp/absolute/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ /tmp/absolute/grammar.jjt</b>
    /tmp/absolute/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj relative/grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj /tmp/absolute/grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj -OUTPUT_DIRECTORY:relative grammar.jjt</b>
    /tmp/relative/subdir/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj -OUTPUT_DIRECTORY:relative relative/grammar.jjt</b>
    /tmp/relative/subdir/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj -OUTPUT_DIRECTORY:relative /tmp/absolute/grammar.jjt</b>
    /tmp/relative/subdir/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ grammar.jjt</b>
    /tmp/absolute/subdir/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ relative/grammar.jjt</b>
    /tmp/absolute/subdir/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:subdir/output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ /tmp/absolute/grammar.jjt</b>
    /tmp/absolute/subdir/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj relative/grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj /tmp/absolute/grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr id="footnote-1-back">
  <td><pre><b>jjtree -OUTPUT_FILE:<i>D:</i>/tmp/subdir/output.jj grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Not Supported<a href="#footnote-1"><sup>*</sup></a></td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:<i>D:</i>/tmp/subdir/output.jj relative/grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Not Supported<a href="#footnote-1"><sup>*</sup></a></td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:<i>D:</i>/tmp/subdir/output.jj /tmp/absolute/grammar.jjt</b>
    /tmp/subdir/output.jj
    /tmp/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Not Supported<a href="#footnote-1"><sup>*</sup></a></td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj -OUTPUT_DIRECTORY:relative grammar.jjt</b>
    /tmp/relative/tmp/subdir/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj -OUTPUT_DIRECTORY:relative relative/grammar.jjt</b>
    /tmp/relative/tmp/subdir/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj -OUTPUT_DIRECTORY:relative /tmp/absolute/grammar.jjt</b>
    /tmp/relative/tmp/subdir/output.jj
    /tmp/relative/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ grammar.jjt</b>
    /tmp/absolute/tmp/subdir/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ relative/grammar.jjt</b>
    /tmp/absolute/tmp/subdir/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
<tr>
  <td><pre><b>jjtree -OUTPUT_FILE:/tmp/subdir/output.jj -OUTPUT_DIRECTORY:/tmp/absolute/ /tmp/absolute/grammar.jjt</b>
    /tmp/absolute/tmp/subdir/output.jj
    /tmp/absolute/&lt;generated&gt;.java</pre>
  </td>
  <td>Not Supported</td>
  <td>Same</td>
</tr>
  </tbody>
</table>

<p id="footnote-1"><a href="#footnote-1-back"><strong>Note</strong></a>: When running JJTree with
the Ant <code>taskdef jjtree</code> the option <kbd>-OUTPUT_DIRECTORY</kbd> must always be set,
because the project's <var>basedir</var> and the Ant working directory might differ. So even if you
don't specify the <var>outputdirectory</var> for <code>taskdef jjtree</code>, JJTree will be called
with the <kbd>-OUTPUT_DIRECTORY</kbd> set to the project's <var>basedir</var>.  But when
the <kbd>-OUTPUT_DIRECTORY</kbd> is set, the <kbd>-OUTPUT_FILE</kbd> setting is handled as if
relative to this <kbd>-OUTPUT_DIRECTORY</kbd>. Thus when the <kbd>-OUTPUT_FILE</kbd> is absolute
or contains a drive letter we have a problem.  Therefore absolute <var>outputfile</var>s (when
the <var>outputdirectory</var> isn't specified) are made relative to the default directory.  And for
this reason <var>outputfile</var>s that contain a drive letter can't be supported.</p>

<p>By the way: specifying a drive letter in the <kbd>-OUTPUT_FILE</kbd> when
the <kbd>-OUTPUT_DIRECTORY</kbd> is set, also results in strange behavior when running JJTree from
the command line.</p>

</body>
</html>
