<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>GZip/BZip2/XZ Tasks</title>
</head>

<body>

<h2 id="pack">GZip/BZip2/XZ</h2>
<h3>Description</h3>
<p>Packs a resource using the GZip, BZip2 or XZ algorithm.  The output file is only generated if it
doesn't exist or the source resource is newer.</p>

<p>XZ compression support has been added <em>since Apache Ant 1.10.1</em> and depends on external
libraries not included in the Ant distribution.
See <a href="../install.html#librarydependencies">Library Dependencies</a> for more information.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>src</td>
    <td>the file to gzip/bzip/xz.</td>
    <td>Yes, or a nested resource collection</td>
  </tr>
  <tr>
    <td>destfile</td>
    <td>the destination file to create.</td>
    <td rowspan="2">Exactly one of the two</td>
  </tr>
  <tr>
    <td>zipfile</td>
    <td class="left"><em><u>deprecated</u></em> old name of <var>destfile</var>.</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>any <a href="../Types/resources.html">resource</a> or single element resource collection</h4>
<p><em>Since Apache Ant 1.7</em></p>
<p>The specified resource will be used as <var>src</var>.</p>

<h3>Examples</h3>
<pre>&lt;gzip src=&quot;test.tar&quot; destfile=&quot;test.tar.gz&quot;/&gt;</pre>
<pre>&lt;bzip2 src=&quot;test.tar&quot; destfile=&quot;test.tar.bz2&quot;/&gt;</pre>
<pre>&lt;xz src=&quot;test.tar&quot; destfile=&quot;test.tar.xz&quot;/&gt;</pre>

<p>Download <samp>https://example.org/archive.tar</samp> and compress it
to <samp>archive.tar.gz</samp> in the project's <var>basedir</var> on the fly.</p>
<pre>
&lt;gzip destfile=&quot;archive.tar.gz&quot;&gt;
  &lt;url url="https://example.org/archive.tar"/&gt;
&lt;/gzip&gt;</pre>

</body>
</html>
