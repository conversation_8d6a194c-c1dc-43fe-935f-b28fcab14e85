<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Copyfile Task</title>
</head>

<body>

<h2 id="copyfile">Copyfile</h2>
<h3><em><u>Deprecated</u></em></h3>
<p><em>This task has been <em>deprecated</em>.  Use the <code>Copy</code> task instead.</em></p>
<h3>Description</h3>
<p>Copies a file from the source to the destination. The file is only copied if the source file is
newer than the destination file, or when the destination file does not exist.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>src</td>
    <td>the filename of the file to copy.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>dest</td>
    <td>the filename of the file where to copy to.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>filtering</td>
    <td>indicates whether token filtering should take place during the copy</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>forceoverwrite</td>
    <td>overwrite existing files even if the destination files are newer.</td>
    <td>No; default is <q>false</q></td>
  </tr>
</table>
<h3>Examples</h3>
<pre>
&lt;copyfile src=&quot;test.java&quot; dest=&quot;subdir/test.java&quot;/&gt;
&lt;copyfile src=&quot;${src}/index.html&quot; dest=&quot;${dist}/help/index.html&quot;/&gt;</pre>

</body>
</html>
