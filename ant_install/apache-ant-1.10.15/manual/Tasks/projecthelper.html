<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ProjectHelper Task</title>
</head>

<body>

<h2>ProjectHelper</h2>
<p><em>Since Ant 1.8.2</em></p>
<h3>Description</h3>
<p>This task is provided for the purpose of allowing the user to install a different ProjectHelper
at run time.</p>
<p>The helpers will be added after all the already registered helpers, but before the default one
(<code>ProjectHelper2</code>)</p>
<p>See the description of Apache Ant's <a href="../projecthelper.html">Project Helper</a> for more
information.</p>

<h3>Parameters specified as nested elements</h3>

<p>You may specify many configured <code class="code">org.apache.tools.ant.ProjectHelper</code>
instances.</p>

<h3>Example</h3>

<p>Install a custom ProjectHelper implementation (assuming <code class="code">MyProjectHelper
extends ProjectHelper</code>):</p>

<pre>
&lt;typedef classname="org.example.MyProjectHelper"
         name="myprojecthelper"/>
&lt;projecthelper>
  &lt;myprojecthelper/>
&lt;/projecthelper>
</pre>

</body>
</html>
