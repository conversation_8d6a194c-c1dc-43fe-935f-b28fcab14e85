<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>SignJar Task</title>
</head>

<body>

<h2 id="signjar">SignJar</h2>
<h3>Description</h3>
<p>Signing a jar allows users to authenticate the publisher.</p>
<p>Signs JAR files with
the <a href="https://docs.oracle.com/javase/8/docs/technotes/tools/windows/jarsigner.html"
target="_top"><kbd>jarsigner</kbd></a> command line tool.  It will take a named file in
the <var>jar</var> attribute, and an optional <var>destDir</var> or <var>signedJar</var>
attribute. Nested paths are also supported; here only an (optional) <var>destDir</var> is
allowed. If a destination directory or explicit JAR file name is not provided, JARs are signed in
place.</p>
<p>Dependency rules</p>
<ul>
  <li>Nonexistent destination JARs are created/signed</li>
  <li>Out of date destination JARs are created/signed</li>
  <li>If a destination file and a source file are the same, and <var>lazy</var> is <q>true</q>, the
    JAR is only signed if it does not contain a signature by this alias.</li>
  <li>If a destination file and a source file are the same, and <var>lazy</var> is <q>false</q>, the
    JAR is signed.</li>
</ul>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>jar</td>
    <td>the jar file to sign</td>
    <td>Yes, unless nested paths have been used</td>
  </tr>
  <tr>
    <td>alias</td>
    <td>the alias to sign under</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>storepass</td>
    <td>password for keystore integrity. Ant will not use
    the <code>-storepass</code> command line argument but send the
    password to jarsigner when it prompts for it.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>keystore</td>
    <td>keystore location</td>
    <td>No</td>
  </tr>
  <tr>
    <td>storetype</td>
    <td>keystore type</td>
    <td>No</td>
  </tr>
  <tr>
    <td>keypass</td>
    <td>password for private key (if different)</td>
    <td>No</td>
  </tr>
  <tr>
    <td>sigfile</td>
    <td>name of <samp>.SF</samp>/<samp>.DSA</samp> file</td>
    <td>No</td>
  </tr>
  <tr>
    <td>signedjar</td>
    <td>name of signed JAR file. This can only be set when the <var>jar</var> attribute is set.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>verbose</td>
    <td>(<q>true|false</q>) verbose output when signing</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>strict</td>
    <td>(<q>true|false</q>) strict checking when signing.<br/><em>since Ant 1.9.1</em>.</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>internalsf</td>
    <td>(<q>true|false</q>) include the <samp>.SF</samp> file inside the signature block</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>sectionsonly</td>
    <td>(<q>true|false</q>) don't compute hash of entire manifest</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>lazy</td>
    <td>flag to control whether the presence of a signature file means a JAR is signed. This is only
      used when the target JAR matches the source JAR</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>maxmemory</td>
    <td>Specifies the maximum memory the <kbd>jarsigner</kbd> JVM will use. Specified in the style
      of standard Java memory specs (e.g. <q>128m</q> = 128 MBytes)</td>
    <td>No</td>
  </tr>
  <tr>
    <td>preservelastmodified</td>
    <td>Give the signed files the same last modified time as the original jar files.</td>
    <td>No; default <q>false</q>.</td>
  </tr>
  <tr>
    <td>tsaurl</td>
    <td>URL for a timestamp authority for timestamped JAR files in Java 5+</td>
    <td>No</td>
  </tr>
  <tr>
    <td>tsacert</td>
    <td>alias in the keystore for a timestamp authority for timestamped JAR files in Java 5+</td>
    <td>No</td>
  </tr>
  <tr>
    <td>tsaproxyhost</td>
    <td>proxy host to be used when connecting to TSA server</td>
    <td>No</td>
  </tr>
  <tr>
    <td>tsaproxyport</td>
    <td>proxy port to be used when connecting to TSA server</td>
    <td>No</td>
  </tr>
  <tr>
    <td>executable</td>
    <td>Specify a particular <kbd>jarsigner</kbd> executable to use in place of the default binary
      (found in the same JDK as Apache Ant is running in).<br/>Must support the same command line
      options as the Sun JDK <kbd>jarsigner</kbd> command.  <em>since Ant 1.8.0</em>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>force</td>
    <td>Whether to force signing of the jar file even if it doesn't seem to be out of date or
      already signed.  <em>since Ant 1.8.0</em>.</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>sigalg</td>
    <td>name of signature algorithm</td>
    <td>No</td>
  </tr>
  <tr>
    <td>digestalg</td>
    <td>name of digest algorithm</td>
    <td>No</td>
  </tr>
  <tr>
    <td>tsadigestalg</td>
    <td>name of TSA digest algorithm. <em>since Ant 1.10.2</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>providername</td>
    <td>name of a cryptographic service provider's name
      when listed in the security properties file.
      <em>since Ant 1.10.6</em>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>providerclass</td>
    <td>name of a cryptographic service provider's master
      class file when the service provider is not listed in the security
      properties file.
      <em>since Ant 1.10.6</em>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>providerarg</td>
    <td>Represents an optional string input argument for
      the constructor of provider_class_name. Ignored
      if <code>providerclass</code> is not set.
      <em>since Ant 1.10.6</em>.</td>
    <td>No</td>
  </tr>
</table>
<h3>Parameters as nested elements</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>path</td>
    <td>path of JAR files to sign. <em>since Ant 1.7</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>fileset</td>
    <td>fileset of JAR files to sign.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>mapper</td>
    <td>A mapper to rename jar files during signing</td>
    <td>No, and only one can be supplied</td>
  </tr>
  <tr>
    <td>sysproperty</td>
    <td>JVM system properties, with the syntax of Ant <a href="exec.html#env">environment
      variables</a></td>
    <td>No, and only one can be supplied</td>
  </tr>
  <tr>
    <td>arg</td>
    <td>Use this to specify a <kbd>keytool</kbd>
    <a href="../using.html#arg">command line argument</a> not
    explicitly supported via an attribute.
    <em>since Ant 1.10.6</em>.</td>
    <td>No</td>
  </tr>
</table>

<h3>Examples</h3>
<p>For instructions on generating a code signing certificate, see
the <a href="https://docs.oracle.com/javase/8/docs/technotes/tools/windows/keytool.html"
target="_top">keytool documentation</a> and/or instructions from your certificate authority.</p>

<p>Sign the <samp>ant.jar</samp> with alias <q>apache-group</q> accessing the keystore and private
key via <q>secret</q> password.</p>
<pre>
&lt;signjar jar=&quot;${dist}/lib/ant.jar&quot;
         alias=&quot;apache-group&quot; storepass=&quot;secret&quot;/&gt;</pre>

<p>Sign all JAR files matching the <samp>dist/**/*.jar</samp> pattern, copying them to the
directory <samp>signed</samp> afterwards. The flatten mapper means that they will all be copied to
this directory, not to subdirectories.</p>
<pre>
&lt;signjar destDir="signed"
         alias="testonly" keystore="testkeystore"
         storepass="apacheant"
         preservelastmodified="true"&gt;
  &lt;path&gt;
    &lt;fileset dir="dist" includes="**/*.jar"/&gt;
  &lt;/path&gt;
  &lt;flattenmapper/&gt;
&lt;/signjar&gt;</pre>

<p>Sign all the JAR files in <samp>dist/**/*.jar</samp> <em>in-situ</em>. Lazy signing is used, so
the files will only be signed if they are not already signed.</p>
<pre>
&lt;signjar alias="testonly" keystore="testkeystore"
         storepass="apacheant"
         lazy="true"&gt;
  &lt;path&gt;
    &lt;fileset dir="dist" includes="**/*.jar"/&gt;
  &lt;/path&gt;
&lt;/signjar&gt;</pre>

<p>Sign all the JAR files in <samp>dist/**/*.jar</samp> using the digest algorithm SHA1 and the
signature algorithm MD5withRSA. This is especially useful when you want to use the JDK
7 <kbd>jarsigner</kbd> (which uses SHA256 and SHA256withRSA as default) to create signed jars that
will be deployed on platforms not supporting SHA256 and SHA256withRSA.</p>
<pre>
&lt;signjar alias="testonly" keystore="testkeystore"
         storepass="apacheant"
         sigalg="MD5withRSA"
         digestalg="SHA1"&gt;
  &lt;path&gt;
    &lt;fileset dir="dist" includes="**/*.jar"/&gt;
  &lt;/path&gt;
&lt;/signjar&gt;</pre>

<h3>About timestamp signing</h3>

<p>Timestamps record the date and time that a signature took place, allowing the signature to be
verified as of that point in time.  With trusted timestamping, users can verify that signing
occurred before a certificate's expiration or revocation. Without this timestamp, users can only
verify the signature as of their current date.</p>

<p>Timestamped JAR files were introduced in Java 5; they are supported <em>since Ant
1.7</em>. Unauthenticated proxies can be used to access TSAs <em>since Ant 1.9.5</em>.</p>

<p>Common public timestamp authorities include</p>
<ul>
  <li>http://timestamp.verisign.com</li>
  <li>http://tsa.starfieldtech.com</li>
  <li>https://timestamp.geotrust.com/tsa</li>
  <li>Others (see your certificate authority)</li>
</ul>

</body>
</html>
