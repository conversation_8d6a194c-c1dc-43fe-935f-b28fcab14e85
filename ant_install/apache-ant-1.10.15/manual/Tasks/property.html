<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Property Task</title>
</head>

<body>

<h2 id="property">Property</h2>
<h3>Description</h3>
<p>Sets a <a href="../using.html#properties">property</a> (by name and value), or set of properties
(from file or resource) in the project.  Properties are case sensitive.</p>
<p>Properties are immutable: whoever sets a property first freezes it for the rest of the build;
they are most definitely not variables.</p>
<p>There are seven ways to set properties:</p>
<ul>
  <li>By supplying both the <var>name</var> and one of <var>value</var> or <var>location</var>
    attributes.</li>
  <li>By supplying the <var>name</var> and nested text.</li>
  <li>By supplying both the <var>name</var> and <var>refid</var> attributes.</li>
  <li>By setting the <var>file</var> attribute with the filename of the property file to load. This
    property file has the format as defined by the file used in the
    class <code class="code">java.util.Properties</code>, with the same rules about how
    non-ISO-8859-1 characters must be escaped.</li>
  <li>By setting the <var>url</var> attribute with the URL from which to load the properties. This
    URL must be directed to a file that has the format as defined by the file used in the
    class <code class="code">java.util.Properties</code>.</li>
  <li>By setting the <var>resource</var> attribute with the resource name of the property file to
    load. A resource is a property file on the current classpath, or on the specified
    classpath.</li>
  <li>By setting the <var>environment</var> attribute with a prefix to use.  Properties will be
    defined for every environment variable by prefixing the supplied name and a period to the name
    of the variable.</li>
  <li>By setting the <var>runtime</var> attribute with a prefix to use.
    Properties <code>prefix.availableProcessors</code>, <code>prefix.freeMemory</code>, <code>prefix.totalMemory</code>
    and <code>prefix.maxMemory</code> will be defined with values returned by the
    corresponding methods of
    the <a href="https://docs.oracle.com/javase/10/docs/api/java/lang/Runtime.html">Runtime</a>
    class.</li>
</ul>
<p>Although combinations of these ways are possible, only one should be used at a time. Problems
might occur with the order in which properties are set, for instance.</p>
<p>The value part of the properties being set might contain references to other properties. These
references are resolved at the time these properties are set.  This also holds for properties loaded
from a property file.</p>
<p>A list of predefined properties can be
found <a href="../properties.html#built-in-props">here</a>.</p>
<p><em>Since Apache Ant 1.8.0</em>, it is possible to load properties defined in XML according
to <a href="https://java.sun.com/dtd/properties.dtd" target="_top">Sun DTD</a>, when running on Java
5+. For this the name of the file, resource or url has to end with <samp>.xml</samp>.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>the name of the property to set.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>value</td>
    <td>the value of the property.</td>
    <td rowspan="3">One of these or nested text, when the <var>name</var> attribute is set</td>
  </tr>
  <tr>
    <td>location</td>
    <td class="left">Sets the property to the absolute filename of the given file. If the value of
      this attribute is an absolute path, it is left unchanged (with <q>/</q> and <q>\</q>
      characters converted to the current platforms conventions). Otherwise it is taken as a path
      relative to the project's <var>basedir</var> and expanded.</td>
  </tr>
  <tr>
    <td>refid</td>
    <td class="left"><a href="../using.html#references">Reference</a> to an object defined
      elsewhere. Only yields reasonable results for references
      to <a href="../using.html#path">path-like structures</a> or properties.</td>
  </tr>
  <tr>
    <td>resource</td>
    <td>the name of the classpath resource containing properties settings in properties file
      format.</td>
    <td rowspan="5">One of these, <strong>unless</strong> the <var>name</var> attribute is set</td>
  </tr>
  <tr>
    <td>file</td>
    <td class="left">the location of the properties file to load.</td>
  </tr>
  <tr>
    <td>url</td>
    <td class="left">a URL containing properties-format settings.</td>
  </tr>
  <tr>
    <td>environment</td>
    <td class="left">the prefix to use when retrieving environment variables. Thus if you
      specify <var>environment</var>=<q>myenv</q> you will be able to access OS-specific environment
      variables via property names <code>myenv.PATH</code> or <code>myenv.TERM</code>. Note that if
      you supply a property <var>name</var> with a final <q>.</q> it will not be doubled;
      i.e. <var>environment</var>=<q>myenv.</q> will still allow access of environment variables
      through <code>myenv.PATH</code> and <code>myenv.TERM</code>. This functionality is currently
      only implemented on <a href="#notes-env">select platforms</a>. Feel free to send patches to
      increase the number of platforms on which this functionality is supported ;-).<br/>Note also
      that properties are case-sensitive, even if the environment variables on your operating system
      are not; e.g. Windows 2000 or later system path variable is set to an Ant property
      named <code>env.Path</code> rather than <code>env.PATH</code>.</td>
  </tr>
  <tr>
    <td>runtime</td>
    <td class="left">the prefix to use when retrieving runtime properties. Thus if you
    specify <var>runtime</var>=<q>myrt</q> you will be able to access runtime values corresponding
    to methods in
    the <a href="https://docs.oracle.com/javase/10/docs/api/java/lang/Runtime.html">Runtime</a>
    class via property
    names <code>myrt.availableProcessors</code>, <code>myrt.maxMemory</code>, <code>myrt.totalMemory</code>
    or <code>myrt.freeMemory</code>. Note that if you supply a property name with a final <q>.</q>
    it will not be doubled; i.e. <var>runtime</var>=<q>myrt.</q> will still allow access of runtime
    properties as e.g. <code>myrt.maxMemory</code>.<br>  Note also that the property values are
    snapshots taken at the point in time when the <code>property</code> has been executed. <em>Since
    Ant 1.10.4</em>
    </td>
  </tr>
  <tr>
    <td>classpath</td>
    <td>the classpath to use when looking up a resource.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>the classpath to use when looking up a resource, given
      as <a href="../using.html#references">reference</a> to a <code>&lt;path&gt;</code> defined
      elsewhere..</td>
    <td>No</td>
  </tr>
  <tr>
    <td>prefix</td>
    <td>Prefix to apply to properties loaded using <var>file</var>, <var>resource</var>,
      or <var>url</var>.  A <q>.</q> is appended to the prefix if not specified.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>prefixValues</td>
    <td>Whether to apply the prefix when expanding the right hand side of properties loaded
      using <var>file</var>, <var>resource</var>, or <var>url</var>.  <em>Since Ant 1.8.2</em></td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>relative</td>
    <td>If set to <q>true</q> the relative path to <var>basedir</var> is set.  <em>Since Ant
      1.8.0</em></td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>basedir</td>
    <td>The <var>basedir</var> to calculate the relative path from.  <em>Since Ant 1.8.0</em></td>
    <td>No; default is project's <var>basedir</var></td>
  </tr>
</table>

<h4>OpenVMS Users</h4>
<p>With the <var>environment</var> attribute this task will load all defined logicals on an OpenVMS
system.  Logicals with multiple equivalence names get mapped to a property whose value is a comma
separated list of all equivalence names.  If a logical is defined in multiple tables, only the most
local definition is available (the table priority order being PROCESS, JOB, GROUP, SYSTEM).</p>

<h4>Any OS except OpenVMS</h4>
<p><em>Since Ant 1.8.2</em>, if Ant detects it is running on a Java 5 or newer, Ant will
use <code class="code">System.getenv()</code> rather than its own OS dependent native
implementation.  For some OSes this causes minor differences when compared to older versions of Ant.
For a full list see <a href="https://issues.apache.org/bugzilla/show_bug.cgi?id=49366"
target="_top">Bugzilla Issue 49366</a>.  In particular:</p>
<ul>
  <li>On Windows, Ant will now return additional "environment variables" that correspond to the
    drive specific current working directories when Ant is run from the command line.  The keys of
    these variables starts with an equals sign.</li>
  <li>Some users reported that some Cygwin specific variables (in particular <code>PROMPT</code>)
    were no longer present.</li>
  <li>On OS/2, Ant no longer returns the <code>BEGINLIBPATH</code> variable.</li>
</ul>

<h3>Parameters specified as nested elements</h3>
<h4>classpath</h4>
<p><code>Property</code>'s <var>classpath</var> attribute is
a <a href="../using.html#path">path-like structure</a> and can also be set via a
nested <code>classpath</code> element.</p>

<h3>Examples</h3>
<p>Set the property <code>foo.dist</code> to the value <q>dist</q>.</p>
<pre>&lt;property name=&quot;foo.dist&quot; value=&quot;dist&quot;/&gt;</pre>

<p>Set the property <code>foo.dist</code> to the value <q>dist</q>.</p>
<pre>&lt;property name=&quot;foo.dist&quot;&gt;dist&lt;/property&gt;</pre>

<p>Read a set of properties from a file called <samp>foo.properties</samp>.</p>
<pre>&lt;property file=&quot;foo.properties&quot;/&gt;</pre>

<p>Read a set of properties from the
address <samp>https://www.mysite.com/bla/props/foo.properties</samp>.</p>
<pre>&lt;property url=&quot;https://www.mysite.com/bla/props/foo.properties&quot;/&gt;</pre>

<p>Read a set of properties from a resource called <samp>foo.properties</samp>.</p>
<pre>&lt;property resource=&quot;foo.properties&quot;/&gt;</pre>

<p>Note that you can reference a global properties file for all of your Ant builds using the
following:</p>
<pre>&lt;property file=&quot;${user.home}/.ant-global.properties&quot;/&gt;</pre>
<p>since the <code>user.home</code> property is defined by JVM to be your home directory. Where
the <code>user.home</code> property resolves to in the file system depends on the operating system
version and the JVM implementation.  On Unix based systems, this will map to the user's home
directory. On modern Windows variants, this will most likely resolve to the user's directory in
the <samp>Documents and Settings</samp> or <samp>Users</samp> folder. Older Windows variants such as
Windows 98/ME are less predictable, as are other operating system/JVM combinations.</p>

<p>Read the system environment variables and stores them in properties, prefixed
with <q>env</q>. Note that this only works on <em>select</em> operating systems. Two of the values
are shown being echoed.</p>
<pre>
&lt;property environment=&quot;env&quot;/&gt;
&lt;echo message=&quot;Number of Processors = ${env.NUMBER_OF_PROCESSORS}&quot;/&gt;
&lt;echo message=&quot;ANT_HOME is set to = ${env.ANT_HOME}&quot;/&gt;</pre>

<p>This buildfile uses the properties defined in <samp>build.properties</samp>. Regarding to the
environment variable <code>STAGE</code> some or all values could be overwritten,
e.g. having <code>STAGE=test</code> and a <samp>test.properties</samp> you have special values for
that (like another name for the test server). Finally all these values could be overwritten by
personal settings with a file per user.</p>
<pre>
&lt;property environment=&quot;env&quot;/&gt;
&lt;property file=&quot;${user.name}.properties&quot;/&gt;
&lt;property file=&quot;${env.STAGE}.properties&quot;/&gt;
&lt;property file=&quot;build.properties&quot;/&gt;</pre>

<p>Store the relative path in <code>foo</code>: <samp>../my/file.txt</samp></p>
<pre>&lt;property name=&quot;foo&quot; location=&quot;my/file.txt&quot; relative=&quot;true&quot; basedir=&quot;..&quot;/&gt;</pre>

<p>Store the relative path in <code>foo</code>: <samp>cvs/my/file.txt</samp></p>
<pre>&lt;property name=&quot;foo&quot; location=&quot;my/file.txt&quot; relative=&quot;true&quot; basedir=&quot;cvs&quot;/&gt;</pre>

<h3>Property files</h3>

<p>As stated, this task will load in a properties file stored in the file system, or as a resource
on a classpath. Here are some interesting facts about this feature</p>
<ol>
  <li>If the file is not there, nothing is printed except at <kbd>-verbose</kbd> log level. This
    lets you have optional configuration files for every project, that team members can customize.
  <li>The rules for this format
    match <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Properties.html#load-java.io.InputStream-"
    target="_top">java.util.Properties</a>.</li>
  <li>Trailing spaces are not stripped. It may have been what you wanted.</li>
  <li>Want unusual characters? Escape them <code>\u0456</code> or <code>\&quot;</code> style.</li>
  <li>Ant Properties are expanded in the file</li>
  <li>If you want to expand properties defined inside the same file and you use
    the <var>prefix</var> attribute of the task, you must use the same prefix when expanding the
    properties or set <var>prefixValues</var> to <q>true</q>.</li>
</ol>
<p>In-file property expansion is very cool. Learn to use it.</p>
<p>Example:</p>
<pre>
build.compiler=jikes
deploy.server=lucky
deploy.port=8443
deploy.url=https://${deploy.server}:${deploy.port}/</pre>

<h3 id="notes-env">Notes about environment variables</h3>
<p>
  When Ant started to support setting properties from environment
  variables it ran on Java 1.2 where <code class="code">System.getEnv</code> didn't
  work. So we decided to start a command in a new process which prints
  the environment variables, analyzes the output and creates the
  properties. With Ant 1.9.0 when we started to require Java 5 we switched back
  to <code class="code">System.getEnv</code> even though it returned different results on some
  platforms. The commands described below are still used on OpenVMS or
  if <code class="code">System.getEnv</code> throws an exception.
</p>
<p>
  There are commands for the following operating systems implemented in
  <a href="https://gitbox.apache.org/repos/asf?p=ant.git;a=blob;f=src/main/org/apache/tools/ant/taskdefs/Execute.java;h=2f29256ed8ee964d78718fd0d7929659008482e6;hb=HEAD">
  Execute.java</a> (method <code class="code">getProcEnvCommand()</code>):
  <table>
    <tr>
      <th scope="col">OS</th>
      <th scope="col">command</th>
    </tr>
    <tr>
      <td>os/2</td>
      <td><code>cmd /c set</code></td>
    </tr>
    <tr>
      <td colspan="2">windows</td>
    </tr>
    <tr>
      <td>* win9x</td>
      <td><code>command.com /c set</code></td>
    </tr>
    <tr>
      <td>* other</td>
      <td><code>cmd /c set</code></td>
    </tr>
    <tr>
      <td>z/os</td>
      <td><code>/bin/env</code> <strong>OR</strong> <code>/usr/bin/env</code> <strong>OR</strong> <code>env</code> (<em>depending on read rights</em>)</td>
    </tr>
    <tr>
      <td>unix</td>
      <td><code>/bin/env</code> <strong>OR</strong> <code>/usr/bin/env</code> <strong>OR</strong> <code>env</code> (<em>depending on read rights</em>)</td>
    </tr>
    <tr>
      <td>netware</td>
      <td><code>env</code></td>
    </tr>
    <tr>
      <td>os/400</td>
      <td><code>env</code></td>
    </tr>
    <tr>
      <td>openvms</td>
      <td><code>show logical</code></td>
    </tr>
  </table>

</body>
</html>
