<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Chmod Task</title>
</head>

<body>

<h2 id="chmod">Chmod</h2>
<h3>Description</h3>
<p>Changes the permissions of a file or all files inside specified directories. Right now it has
effect only under Unix or NonStop Kernel (Tandem).  The permissions are also UNIX style, like the
argument for the <kbd>chmod</kbd> command.</p>
<p>See the section on <a href="../dirtasks.html#directorybasedtasks">directory based tasks</a>, on
how the inclusion/exclusion of files works, and how to write patterns.</p>

<p>This task holds an implicit <a href="../Types/fileset.html">FileSet</a> and supports all of
FileSet's attributes and nested elements directly. More sets can be specified using
nested <code>&lt;fileset&gt;</code> or <code>&lt;dirset&gt;</code> (<em>since Apache Ant 1.6</em>)
elements.</p>

<p><em>Since Ant 1.6</em>, this task also supports
nested <a href="../Types/filelist.html">filelist</a>s.</p>

<p><em>Since Ant 1.7</em>, this task supports
arbitrary <a href="../Types/resources.html#collection">resource collections</a> as nested
elements.</p>

<p>By default this task will use a single invocation of the underlying <kbd>chmod</kbd> command.
If you are working on a large number of files this may result in a command line that is too long for
your operating system.  If you encounter such problems, you should set the <var>maxparallel</var>
attribute of this task to a non-zero value.  The number to use highly depends on the length of your
file names (the depth of your directory tree) and your operating system, so you'll have to
experiment a little.  POSIX recommends command line length limits of at least 4096 characters, this
may give you an approximation for the number you could use as initial value for these
experiments.</p>

<p>By default this task won't do anything unless it detects it is running on a Unix system.  If you
know for sure that you have a <kbd>chmod</kbd> executable on your <code>PATH</code> that is
command line compatible with the Unix command, you can use the task's <var>os</var> attribute and
set its value to your current OS.</p>

<p>See the <a href="setpermissions.html">setpermissions</a> task for a platform independent
alternative.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>the file or single directory of which the permissions must be changed.</td>
    <td rowspan="2">Exactly one of the two, unless
      nested <code>&lt;fileset|filelist|dirset&gt;</code> elements are specified</td>
  </tr>
  <tr>
    <td>dir</td>
    <td class="left">the directory which holds the files whose permissions must be
      changed.<br/><strong>Note</strong>: for backwards compatibility reasons <code>&lt;chmod
      dir="some-dir"/&gt;</code> will only change the permissions on <samp>some-dir</samp> but not
      recurse into it, unless you also specify any patterns.</td>
  </tr>
  <tr>
    <td>perm</td>
    <td>the new permissions.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>includes</td>
    <td>comma- or space-separated list of patterns of files that must be included.</td>
    <td>No; defaults to all (<q>**</q>)</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>comma- or space-separated list of patterns of files that must be excluded.</td>
    <td>No; defaults to default excludes or none if <var>defaultexcludes</var> is <q>no</q></td>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>indicates whether default excludes should be used or not (<q>yes|no</q>).</td>
    <td>No; defaults to <q>yes</q></td>
  </tr>
  <tr>
    <td>parallel</td>
    <td>process all specified files using a single <kbd>chmod</kbd> command.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>type</td>
    <td>One of <q>file</q>, <q>dir</q> or <q>both</q>. If set to <q>file</q>, only the permissions
      of plain files are going to be changed. If set to <q>dir</q>, only the directories are
      considered.<br/><strong>Note</strong>: The type attribute does not apply to
      nested <code>dirset</code>s&mdash;<code>dirset</code>s always implicitly assume type to
      be <q>dir</q>.</td>
    <td>No; default is <q>file</q></td>
  </tr>
  <tr>
    <td>maxparallel</td>
    <td>Limit the amount of parallelism by passing at most this many sourcefiles at once.  Set it to
      negative integer for unlimited.  <em>Since Ant 1.6</em>.</td>
    <td>No; defaults to unlimited</td>
  </tr>
  <tr>
    <td>verbose</td>
    <td>Whether to print a summary after execution or not.  <em>Since Ant 1.6</em>.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>os</td>
    <td>list of Operating Systems on which the command may be executed.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>osfamily</td>
    <td>OS family as used in the <a href="conditions.html#os">&lt;os&gt;</a> condition.</td>
    <td>No; defaults to <q>unix</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Make the <samp>start.sh</samp> file readable and executable for anyone on a UNIX system.</p>
<pre>&lt;chmod file=&quot;${dist}/start.sh&quot; perm=&quot;ugo+rx&quot;/&gt;</pre>

<p>Make the <samp>start.sh</samp> file readable, writable and executable only for the owner on a
UNIX system.</p>
<pre>&lt;chmod file=&quot;${dist}/start.sh&quot; perm=&quot;700&quot;/&gt;</pre>

<p>Make all <samp>.sh</samp> files below <samp>${dist}/bin</samp> readable and executable for anyone
on a UNIX system.</p>
<pre>
&lt;chmod dir=&quot;${dist}/bin&quot; perm=&quot;ugo+rx&quot;
       includes=&quot;**/*.sh&quot;/&gt;</pre>

<p>Make all files below <samp>shared/sources1</samp> (except those below any directory
named <samp>trial</samp>) writable for members of the same group on a UNIX system. In addition, all
files belonging to a FileSet with <var>id</var> <samp>other.shared.sources</samp> get the same
permissions.</p>
<pre>
&lt;chmod perm=&quot;g+w&quot;&gt;
  &lt;fileset dir=&quot;shared/sources1&quot;&gt;
    &lt;exclude name=&quot;**/trial/**&quot;/&gt;
  &lt;/fileset&gt;
  &lt;fileset refid=&quot;other.shared.sources&quot;/&gt;
&lt;/chmod&gt;</pre>

<p>Keep non-owners from touching <code>cgi</code> scripts, files with a <samp>.old</samp> extension
or directories beginning with <samp>private_</samp>. A directory ending in <samp>.old</samp> or a
file beginning with <samp>private_</samp> would remain unaffected.</p>
<pre>
&lt;chmod perm=&quot;go-rwx&quot; type=&quot;file&quot;&gt;
  &lt;fileset dir=&quot;/web&quot;&gt;
    &lt;include name=&quot;**/*.cgi&quot;/&gt;
    &lt;include name=&quot;**/*.old&quot;/&gt;
  &lt;/fileset&gt;
  &lt;dirset dir=&quot;/web&quot;&gt;
    &lt;include name=&quot;**/private_*&quot;/&gt;
  &lt;/dirset&gt;
&lt;/chmod&gt;</pre>

<h3>Note on <var>maxparallel</var> attribute</h3>
<p>Some shells have a limit of the number of characters that a command line may contain.  This
maximum limit varies from shell to shell and from operating system to operating system.  If one has
a large number of files to change mode on, consider using the <var>maxparallel</var> attribute. For
example when using AIX and the limit is reached, the system responds with a warning: <code>"Warning:
UNIXProcess.forkAndExec native error: The parameter or environment lists are too long"</code>. A
value of about 300 seems to result in a command line that is acceptable.</p>
</body>
</html>
