<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Unzip Task</title>
</head>

<body>

<h2 id="unzip">Unjar/Untar/Unwar/Unzip</h2>
<h3>Description</h3>
<p>Unzips a zip-, war-, or jar file.</p>
<p><a href="../Types/patternset.html">PatternSet</a>s are used to select files to
extract <em>from</em> the archive.  If no patternset is used, all files are extracted.</p>

<p><a href="../Types/resources.html#collection">resource collections</a> may be used to select
archived files to perform unarchival upon.  Only file system based resource collections are
supported by <code>Unjar</code>/<code>Unwar</code>/<code>Unzip</code>, this
includes <a href="../Types/fileset.html">fileset</a>, <a href="../Types/filelist.html">filelist</a>, <a href="../using.html#path">path</a>,
and <a href="../Types/resources.html#files">files</a>. <code>Untar</code> supports arbitrary
resource collections.  Prior to Apache Ant 1.7 only <code>fileset</code> has been supported as a
nested element.</p>

<p>You can define filename transformations by using a
nested <a href="../Types/mapper.html">mapper</a> element.  The default mapper is the
<a href="../Types/mapper.html#identity-mapper">identity mapper</a>.</p>
<p>File permissions will not be restored on extracted files.</p>
<p>The <code>untar</code> task recognizes the long pathname entries used by GNU tar.<p>

<p><strong>Please note</strong> that different ZIP tools handle timestamps differently when it comes
to applying timezone offset calculations of files.  Some ZIP libraries will store the timestamps as
they've been read from the filesystem while others will modify the timestamps both when reading and
writing the files to make all timestamps use the same timezone.  A ZIP archive created by one
library may extract files with "wrong timestamps" when extracted by another library.</p>

<p>Ant's ZIP classes use the same algorithm as the InfoZIP tools and zlib (timestamps get adjusted),
Windows' "compressed folders" function and WinZIP don't change the timestamps.  This means that
using the <code>unzip</code> task on files created by Windows' compressed folders function may
create files with timestamps that are "wrong", the same is true if you use Windows' functions to
extract an Ant generated ZIP archive.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>src</td>
    <td>archive file to expand.</td>
    <td>Yes, unless filesets are used</td>
  </tr>
  <tr>
    <td>dest</td>
    <td>directory where to store the expanded files.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>overwrite</td>
    <td>Overwrite files, even if they are newer than the corresponding entries in the archive
      (<q>true|false</q>).</td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>compression</td>
    <td><strong>Note</strong>: This attribute is only available for the <code>untar</code> task.<br/>
      compression method.  Allowable values are <q>none</q>, <q>gzip</q>, <q>xz</q>
      and <q>bzip2</q>.</td>
    <td>No; default is <q>none</q></td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The character encoding that has been used for filenames inside the zip file.  For a list of
      possible values see
      the <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html"
      target="_top">Supported Encodings</a>.<br/>  Use the magic value <q>native-encoding</q> for
      default JVM character encoding.<br/>  See also the <a href="zip.html#encoding">discussion in
      the zip task page</a></td>
    <td>No; defaults to <q>UTF8</q> for <code>unzip</code> and default JVM character encoding
      for <code>untar</code> task</td>
  </tr>
  <tr>
    <td>failOnEmptyArchive</td>
    <td>whether trying to extract an empty archive is an error. <em>since Ant 1.8.0</em></td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>stripAbsolutePathSpec</td>
    <td>whether Ant should remove leading <q>/</q> or <q>\</q> characters from the extracted file
      name before extracting it.  Note that this changes the entry name before
      applying <code>include</code>/<code>exclude</code> patterns and before using the nested
      mappers (if any).  <em>since Ant 1.8.0</em></td>
    <td>No; defaults to <q>true</q> <em>since Ant 1.10.4</em>
      (used to default to <q>false</q> prior to that)</td>
  </tr>
  <tr>
    <td>scanForUnicodeExtraFields</td>
    <td><strong>Note</strong>: This attribute is not available for the <code>untar</code> task.<br/>
      If the archive contains Unicode extra fields then use them to set the file names, ignoring the
      specified encoding.<br/>See also the <a href="zip.html#encoding">discussion in the zip task
      page</a></td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>allowFilesToEscapeDest</td>
    <td>Whether to allow the extracted file or directory to be outside of the dest
      directory.  <em>since Ant 1.10.4</em></td>
    <td>No, defaults to <q>false</q> unless <var>stripAbsolutePathSpec</var> is <q>false</q> and the
      entry's name starts with a leading path spec.</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>&lt;unzip src=&quot;${tomcat_src}/tools-src.zip&quot; dest=&quot;${tools.home}&quot;/&gt;</pre>
<pre>
&lt;gunzip src=&quot;tools.tar.gz&quot;/&gt;
&lt;untar src=&quot;tools.tar&quot; dest=&quot;${tools.home}&quot;/&gt;</pre>
<pre>
&lt;unzip src=&quot;${tomcat_src}/tools-src.zip&quot;
       dest=&quot;${tools.home}&quot;&gt;
    &lt;patternset&gt;
        &lt;include name=&quot;**/*.java&quot;/&gt;
        &lt;exclude name=&quot;**/Test*.java&quot;/&gt;
    &lt;/patternset&gt;
&lt;/unzip&gt;</pre>
<pre>
&lt;unzip dest=&quot;${tools.home}&quot;&gt;
    &lt;patternset&gt;
        &lt;include name=&quot;**/*.java&quot;/&gt;
        &lt;exclude name=&quot;**/Test*.java&quot;/&gt;
    &lt;/patternset&gt;
    &lt;fileset dir=&quot;.&quot;&gt;
        &lt;include name=&quot;**/*.zip&quot;/&gt;
        &lt;exclude name=&quot;**/tmp*.zip&quot;/&gt;
    &lt;/fileset&gt;
&lt;/unzip&gt;</pre>
<pre>
&lt;unzip src=&quot;apache-ant-bin.zip&quot; dest=&quot;${tools.home}&quot;&gt;
    &lt;patternset&gt;
        &lt;include name=&quot;apache-ant/lib/ant.jar&quot;/&gt;
    &lt;/patternset&gt;
    &lt;mapper type=&quot;flatten&quot;/&gt;
&lt;/unzip&gt;</pre>

<p>Extract all images from <samp>ant.jar</samp> which are stored in the <samp>images</samp>
  directory of the jar file (or somewhere under it). While extracting the directory structure
  (<samp>images</samp>) will be preserved.</p>
<pre>
&lt;unzip src=&quot;${ant.home}/lib/ant.jar&quot; dest=&quot;...&quot;&gt;
  &lt;patternset&gt;
    &lt;include name=&quot;images/&quot;/&gt;
  &lt;/patternset&gt;
&lt;/unzip&gt;</pre>

<p>Extract two files, <samp>ant_logo_large.gif</samp> and <samp>LICENSE.txt</samp>,
  from <samp>ant.jar</samp>. More exactly: extract all files with these names from anywhere in the
  source file. While extracting the directory structure will be preserved.</p>
<pre>
&lt;unzip src=&quot;${ant.home}/lib/ant.jar&quot; dest=&quot;...&quot;&gt;
  &lt;patternset&gt;
    &lt;include name=&quot;**/ant_logo_large.gif&quot;/&gt;
    &lt;include name=&quot;**/LICENSE.txt&quot;/&gt;
  &lt;/patternset&gt;
&lt;/unzip&gt;</pre>

<h3>Related tasks</h3>

<p>The task</p>

<pre>
&lt;unzip src="some-archive" dest="some-dir"&gt;
  &lt;patternset&gt;
    &lt;include name="some-pattern"/&gt;
  &lt;/patternset&gt;
  &lt;mapper type=&quot;some-mapper&quot;/&gt;
&lt;/unzip&gt;</pre>

<p>is identical to</p>

<pre>
&lt;copy todir="some-dir" preservelastmodified="true"&gt;
  &lt;zipfileset src="some-archive"&gt;
    &lt;patternset&gt;
      &lt;include name="some-pattern"/&gt;
    &lt;/patternset&gt;
  &lt;/zipfileset&gt;
  &lt;mapper type=&quot;some-mapper&quot;/&gt;
&lt;/copy&gt;</pre>

<p>The same is also true for <code>&lt;untar&gt;</code>
and <code>&lt;tarfileset&gt;</code>.  <code>&lt;copy&gt;</code> offers additional features
like <a href="../Types/filterchain.html">filtering files</a> on the fly, allowing a file to be
mapped to multiple destinations or a configurable file system timestamp granularity.</p>

<p>&quot;Delete&quot; files from a zipfile.</p>

<pre>&lt;zip destfile=&quot;new.jar&quot;&gt;
  &lt;zipfileset src=&quot;old.jar&quot;&gt;
    &lt;exclude name=&quot;do/not/include/this/class&quot;/&gt;
  &lt;/zipfileset&gt;
&lt;/zip&gt;</pre>

</body>
</html>
