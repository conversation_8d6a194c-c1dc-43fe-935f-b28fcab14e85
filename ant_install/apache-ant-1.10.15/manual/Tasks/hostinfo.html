<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>HostInfo Task</title>
</head>

<body>

<h2 id="hostinfo">HostInfo</h2>

<h3>Description</h3>
<p>Sets the <code>NAME</code>, <code>DOMAIN</code>, <code>ADDR4</code>, and <code>ADDR6</code>
properties in the current project.</p>
<p>
The <code>NAME</code> contains the host part of the canonical name of the host.<br/>  If the host is
not found, the host will contain the name as provided to the task, or <code>localhost</code> if no
host was provided, and no name for the local host was found.<br/>The <code>DOMAIN</code> contains
the domain part of the canonical name of the host.<br/>If the host is not found, the domain will
contain the domain as provided to the task, or <code>localdomain</code> if no host / domain was
provided.<br/>The <code>ADDR4</code> contains the IPv4 address of the host with the widest
meaning.<br/>If no IPv4 address is found and a host has been provided the
address <code>0.0.0.0</code> is returned, when no host was provided the
address <code>127.0.0.1</code> is returned.<br/>The <code>ADDR6</code> contains the IPv6 address of
the host with the widest meaning.<br/>If no IPv6 address is found and a host has been provided the
address <code>::</code> is returned, when no host was provided the address <code>::1</code> is
returned.</p>

<p>These properties can be used in the build-file, for instance, to create host-stamped filenames,
or used to replace placeholder tags inside documents to indicate, for example, the host where the
build was performed on.  The best place for this task is probably in an initialization target.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>prefix</td>
    <td>Prefix used for all properties set.</td>
    <td>No; defaults to no prefix</td>
  </tr>
  <tr>
    <td>host</td>
    <td>The host to retrieve the information for.</td>
    <td>No; default is to retrieve information for the host the task is running on</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Set the <code>NAME</code>, <code>DOMAIN</code>, <code>ADDR4</code>, and <code>ADDR6</code> for
the local host, using the most &quot;global&quot; address available.</p>
<pre>&lt;hostinfo/&gt;</pre>

<p>Set the properties <code>remotehost.NAME</code>
to <samp>eos</samp>, <code>remotehost.DOMAIN</code>
to <samp>apache.org</samp>, <code>remotehost.ADDR4</code> to <samp>**************</samp>
and <code>remotehost.ADDR6</code> to <samp>::</samp> for the host with the
name <samp>www.apache.org</samp> (provided the canonical name and IP addresses do not change).</p>
<pre>&lt;hostinfo prefix=&quot;remotehost&quot; host=&quot;www.apache.org&quot;/&gt;</pre>

</body>
</html>
