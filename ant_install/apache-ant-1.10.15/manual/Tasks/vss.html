<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Microsoft Visual SourceSafe (VSS) Tasks</title>
</head>
<body>
<h1>Microsoft Visual SourceSafe Tasks User Manual</h1>
<p>by</p>
<ul>
  <li><PERSON></li>
  <li><PERSON></li>
  <li><PERSON>lazs <PERSON>je<PERSON> 2</li>
  <li><a href="mailto:<PERSON><EMAIL>"><PERSON>_<PERSON>@bmc.com</a></li>
  <li><PERSON>l (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
  <li>Phillip Wells</li>
  <li>Jon Skeet (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
  <li>Nigel Magnay (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
  <li>Gary S. Weaver</li>
  <li>Jesse Stockall</li>
</ul>
<hr/>
<h2>Contents</h2>
<ul>
  <li><a href="#intro">Introduction</a></li>
  <li><a href="#tasks">The Tasks</a></li>
</ul>

<br/>
<h2 id="intro">Introduction</h2>
<p>These tasks provide an interface to
the <a href="https://msdn.microsoft.com/en-us/library/3h0544kx(v=vs.80).aspx"
target="_top">Microsoft Visual SourceSafe</a> SCM.
The <code class="code">org.apache.tools.ant.taskdefs.optional.vss</code> package consists of a
simple framework to support VSS functionality as well as some Apache Ant tasks encapsulating
frequently used VSS commands.  Although it is possible to use these commands on the desktop, they
were primarily intended to be used by automated build systems.</p>
<p>If you get a <code class="output">CreateProcess error=2</code> when running these, it means
that <kbd>ss.exe</kbd> was not found. Check to see if you can run it from the command line&mdash;you
may need to alter your path, or set the <var>ssdir</var> property.</p>
<h2 id="tasks">The Tasks</h2>

<table>
  <tr><th scope="col">Task</th><th scope="col">Description</th></tr>
  <tr>
    <td><a href="#vssget">vssget</a></td>
    <td>Retrieves a copy of the specified VSS file(s).</td>
  </tr>
  <tr>
    <td><a href="#vsslabel">vsslabel</a></td>
    <td>Assigns a label to the specified version or current version of a file or project.</td>
  </tr>
  <tr>
    <td><a href="#vsshistory">vsshistory</a></td>
    <td>Shows the history of a file or project in VSS.</td>
  </tr>
  <tr>
    <td><a href="#vsscheckin">vsscheckin</a></td>
    <td>Updates VSS with changes made to a checked out file, and unlocks the VSS master copy.</td>
  </tr>
  <tr>
    <td><a href="#vsscheckout">vsscheckout</a></td>
    <td>Copies a file from the current project to the current folder, for the purpose of editing.</td>
  </tr>
  <tr>
    <td><a href="#vssadd">vssadd</a></td>
    <td>Adds a new file into the VSS Archive</td>
  </tr>
  <tr>
    <td><a href="#vsscp">vsscp</a></td>
    <td>Change the current project being used in VSS</td>
  </tr>
  <tr>
    <td><a href="#vsscreate">vsscreate</a></td>
    <td>Creates a project in VSS.</td>
  </tr>
</table>

<hr/>
<h2>Task Descriptions</h2>

<!-- VSSGET -->

<h2 id="vssget">VssGet</h2>
<h3>Description</h3>
<p>Task to perform GET commands to Microsoft Visual SourceSafe.</p>
<p>If you specify two or more attributes from version, date and label only one will be used in the
order version, date, label.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>vsspath</td>
    <td>SourceSafe path which specifies the project/file(s) you wish to perform the action on.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username[,password]</q>&mdash;The username and password needed to get access to VSS. Note
      that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang if you
      leave the password out and VSS does not accept login without a password.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>localpath</td>
    <td>Override the working directory and get to the specified path</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>writable</td>
    <td><q>true|false</q></td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>recursive</td>
    <td><q>true|false</q>. Note however that in the SourceSafe UI there is a setting accessed
      via <q>Tools/Options/GeneralTab</q> called <q>Act on projects recursively</q>.  If this
      setting is checked, then the <var>recursive</var> attribute is effectively ignored, and get
      will always be done recursively</td>
    <td>No; default <q>false</q></td>
  </tr>
  <tr>
    <td>version</td>
    <td>a version number to get</td>
    <td rowspan="3">No; only one of these may be used</td>
  </tr>
  <tr>
    <td>date</td>
    <td class="left">a date stamp to get at</td>
  </tr>
  <tr>
    <td>label</td>
    <td class="left">a label to get for</td>
  </tr>
  <tr>
    <td>quiet</td>
    <td>suppress output</td>
    <td>No; defaults to <q>off</q></td>
  </tr>
  <tr>
    <td>autoresponse</td>
    <td>What to respond with (sets the <kbd>-I</kbd> option). By default, <kbd>-I-</kbd> is
      used; values of <q>Y</q> or <q>N</q> will be appended to this.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>writablefiles</td>
    <td>Behavior when local files are writable. Valid options are: <q>replace</q>, <q>skip</q>
      and <q>fail</q>; <q>skip</q> implies <var>failonerror</var>=<q>false</q></td>
    <td>No; defaults to <q>fail</q></td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q></td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>filetimestamp</td>
    <td>Set the behavior for timestamps of local files. Valid options
      are <q>current</q>, <q>modified</q>, or <q>updated</q>.</td>
    <td>No; defaults to <q>current</q></td>
  </tr>
</table>
<p>Note that only one of version, date or label should be specified</p>
<h3>Examples</h3>

<p>Perform a get on the VSS Project <samp>$/source/myproject</samp> using the username <q>me</q> and
the password <q>mypassword</q>. That will recursively get the files which are
labeled <q>Release1</q> and write them to the local directory <samp>C:\mysrc\myproject</samp>. The
local files will be writable.</p>

<pre>
&lt;vssget localPath=&quot;C:\mysrc\myproject&quot;
        recursive=&quot;true&quot;
        label=&quot;Release1&quot;
        login=&quot;me,mypassword&quot;
        vsspath=&quot;$/source/aProject&quot;
        writable=&quot;true&quot;/&gt;</pre>

<hr/>
<!-- VSSLABEL -->
<h2 id="vsslabel">VssLabel</h2>
<h3>Description</h3>
<p>Task to perform LABEL commands to Microsoft Visual SourceSafe.</p>
<p>Assigns a label to the specified version or current version of a file or project.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>vsspath</td>
    <td>SourceSafe path which specifies the project/file(s) you wish to perform the action on.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username[,password]</q>&mdash;The username and password needed to get access to VSS. Note
      that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang if you
      leave the password out and VSS does not accept login without a password.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>label</td>
    <td>A label to apply to the hierarchy</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>version</td>
    <td>An existing file or project version to label. By default the current version is
      labeled.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>The comment to use for this label. Empty or <q>-</q> for no comment.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>autoresponse</td>
    <td>What to respond with (sets the <kbd>-I</kbd> option). By default, <kbd>-I-</kbd> is
      used; values of <q>Y</q> or <q>N</q> will be appended to this.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Label the current version of the VSS project <samp>$/source/aProject</samp> with the
label <q>Release1</q> using the username <q>me</q> and the password <q>mypassword</q>.</p>

<pre>
&lt;vsslabel vsspath=&quot;$/source/aProject&quot;
          login=&quot;me,mypassword&quot;
          label=&quot;Release1&quot;/&gt;</pre>

<p>Label version 4 of the VSS file <samp>$/source/aProject/myfile.txt</samp> with the
label <q>1.03.004</q>. If this version already has a label, the operation (and the build) will
fail.</p>

<pre>
&lt;vsslabel vsspath=&quot;$/source/aProject/myfile.txt&quot;
          version=&quot;4&quot;
          label=&quot;1.03.004&quot;/&gt;</pre>

<hr/>
<!-- VSSHISTORY -->
<h2 id="vsshistory">VssHistory</h2>
<h3>Description</h3>
Task to perform HISTORY commands to Microsoft Visual SourceSafe.
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>vsspath</td>
    <td>SourceSafe path which specifies the project/file(s) you wish to perform the action on.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username[,password]</q>&mdash;The username and password needed to get access to VSS. Note
      that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang if you
      leave the password out and VSS does not accept login without a password.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>fromDate</td>
    <td>Start date for comparison</td>
    <td>See below</td>
  </tr>
  <tr>
    <td>toDate</td>
    <td>End date for comparison</td>
    <td>See below</td>
  </tr>
  <tr>
    <td>dateFormat</td>
    <td>Format of dates in <var>fromDate</var> and <var>toDate</var>. Used when calculating dates
      with the <var>numdays</var> attribute. This string uses the formatting rules of
      SimpleDateFormat.</td>
    <td>No; defaults to <code>DateFormat.SHORT</code></td>
  </tr>
  <tr>
    <td>fromLabel</td>
    <td>Start label for comparison</td>
    <td>No</td>
  </tr>
  <tr>
    <td>toLabel</td>
    <td>Start label for comparison</td>
    <td>No</td>
  </tr>
  <tr>
    <td>numdays</td>
    <td>The number of days for comparison.</td>
    <td>See below</td>
  </tr>
  <tr>
    <td>output</td>
    <td>File to write the diff.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>recursive</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>style</td>
    <td><q>brief</q>, <q>codediff</q>, <q>default</q> or <q>nofile</q>.</td>
    <td>No; defaults to <q>default</q></td>
  </tr>
  <tr>
    <td>user</td>
    <td>Name the user whose changes we would like to see</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q></td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>

<h4>Specifying the time-frame</h4>
<p>There are different ways to specify what time-frame you wish to evaluate:</p>
<ul>
  <li>Changes between two dates: Specify both <var>fromDate</var> and <var>toDate</var></li>
  <li>Changes before a date: Specify <var>toDate</var></li>
  <li>Changes after a date: Specify <var>fromDate</var></li>
  <li>Changes X Days before a date: Specify <var>toDate</var> and (negative!) <var>numDays</var></li>
  <li>Changes X Days after a date: Specify <var>fromDate</var> and <var>numDays</var></li>
</ul>

<h3>Examples</h3>

<p>Show all changes between <q>Release1</q> and <q>Release2</q>.</p>

<pre>
&lt;vsshistory vsspath=&quot;$/myProject&quot; recursive=&quot;true&quot;
            fromLabel=&quot;Release1&quot;
            toLabel=&quot;Release2&quot;/&gt;</pre>

<p>Show all changes between January 1st 2001 and March 31st 2001 (in Germany, date must be specified
according to your locale).</p>

<pre>
&lt;vsshistory vsspath=&quot;$/myProject&quot; recursive=&quot;true&quot;
            fromDate=&quot;01.01.2001&quot;
            toDate=&quot;31.03.2001&quot;/&gt;</pre>

<p>Shows all changes in the last fortnight.</p>

<pre>
&lt;tstamp&gt;
  &lt;format property=&quot;to.tstamp&quot; pattern=&quot;M-d-yy;h:mma&quot;/&gt;
&lt;/tstamp&gt;

&lt;vsshistory vsspath=&quot;$/myProject&quot; recursive=&quot;true&quot;
            numDays=&quot;-14&quot;
            dateFormat=&quot;M-d-yy;h:mma&quot;
            toDate=&quot;${to.tstamp}&quot;/&gt;</pre>

<hr/>
<!-- VSSCHECKIN -->
<h2 id="vsscheckin">VssCheckin</h2>
<h3>Description</h3>
<p>Task to perform CHECKIN commands to Microsoft Visual SourceSafe.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
     <td>vsspath</td>
     <td>SourceSafe path which specifies the project/file(s) you wish to perform the action on.</td>
     <td>Yes</td>
  </tr>
  <tr>
     <td>login</td>
     <td><q>username[,password]</q>&mdash;The username and password needed to get access to
       VSS. Note that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang
       if you leave the password out and VSS does not accept login without a password.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>localpath</td>
     <td>Override the working directory and get to the specified path</td>
     <td>No</td>
  </tr>
  <tr>
     <td>ssdir</td>
     <td>directory where <kbd>ss.exe</kbd> resides.</td>
     <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>writable</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>recursive</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Comment to use for the files that where checked in.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>autoresponse</td>
    <td><q>Y</q>, <q>N</q> or empty. Specify how to reply to questions from VSS.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Check in the file(s) named <samp>test*</samp> in the project <samp>$/test</samp> using the local
directory <samp>D:\build</samp>.</p>

<pre>
&lt;vsscheckin vsspath=&quot;$/test/test*&quot;
            localpath=&quot;D:\build\&quot;
            comment=&quot;Modified by automatic build&quot;/&gt;
</pre>

<hr/>
<!-- VSSCHECKOUT -->
<h2 id="vsscheckout">VssCheckout</h2>
<h3>Description</h3>
<p>Task to perform CHECKOUT commands to Microsoft Visual SourceSafe.</p>
<p>If you specify two or more attributes from <var>version</var>, <var>date</var>
and <var>label</var> only one will be used in the
order <var>version</var>, <var>date</var>, <var>label</var>.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>vsspath</td>
    <td>SourceSafe path which specifies the project/file(s) you wish to perform the action on.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username[,password]</q>&mdash;The username and password needed to get access to VSS. Note
      that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang if you
      leave the password out and VSS does not accept login without a password.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>localpath</td>
    <td>Override the working directory and get to the specified path</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>writable</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>recursive</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>version</td>
    <td>a version number to get</td>
    <td rowspan="3">No; only one of these may be used</td>
  </tr>
  <tr>
    <td>date</td>
    <td class="left">a date stamp to get at</td>
  </tr>
  <tr>
    <td>label</td>
    <td class="left">a label to get for</td>
  </tr>
  <tr>
    <td>writablefiles</td>
    <td>Behavior when local files are writable. Valid options are: <q>replace</q>, <q>skip</q>
      and <q>fail</q>; <q>skip</q> implies <var>failonerror</var>=<q>false</q></td>
    <td>No; defaults to <q>fail</q></td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>filetimestamp</td>
    <td>Set the behavior for timestamps of local files. Valid options
      are <q>current</q>, <q>modified</q>, or <q>updated</q>.</td>
    <td>No; defaults to <q>current</q></td>
  </tr>
  <tr>
    <td>getlocalcopy</td>
    <td>Set the behavior to retrieve local copies of the files.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Check out the project <samp>$/test</samp> recursively to the directory <samp>D:\build</samp>.</p>

<pre>
&lt;vsscheckout vsspath=&quot;$/test&quot;
             localpath=&quot;D:\build&quot;
             recursive=&quot;true&quot;
             login=&quot;me,mypass&quot;/&gt;</pre>

<hr/>
<!-- VSSADD -->
<h2 id="vssadd">VssAdd</h2>
<h3>Description</h3>
Task to perform ADD commands to Microsoft Visual SourceSafe.
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>localpath</td>
    <td>Specify the local file(s) to add to VSS</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username[,password]</q>&mdash;The username and password needed to get access to VSS. Note
      that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang if you
      leave the password out and VSS does not accept login without a password.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>writable</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>recursive</td>
    <td><q>true|false</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Comment to use for the files that where checked in.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>autoresponse</td>
    <td><q>Y</q>, <q>N</q> or empty. Specify how to reply to questions from VSS.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Add the file named <samp>build.00012.zip</samp> into the project current working directory
(see <code>vsscp</code>).</p>

<pre>
&lt;vssadd localpath=&quot;D:\build\build.00012.zip&quot;
        comment=&quot;Added by automatic build&quot;/&gt;</pre>

<hr/>
<!-- VSSCP -->
<h2 id="vsscp">VssCp</h2>
<h3>Description</h3>
<p>Task to perform CP (Change Project) commands to Microsoft Visual SourceSafe.</p>
<p>This task is typically used before a <code>VssAdd</code> in order to set the target project</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>vsspath</td>
    <td>SourceSafe path which specifies the project you wish to make the current project.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username[,password]</q>&mdash;The username and password needed to get access to VSS. Note
      that you may need to specify both (if you have a password)&mdash;Ant/VSS will hang if you
      leave the password out and VSS does not accept login without a password.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>serverPath</td>
    <td>directory where <samp>srcsafe.ini</samp> resides.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if <kbd>ss.exe</kbd> exits with a return code <q>100</q>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Set the current VSS project to <samp>$/Projects/ant</samp>.</p>

<pre>&lt;vsscp vsspath=&quot;$/Projects/ant&quot;/&gt;</pre>

<hr/>
<!-- VSSCREATE -->
<h2 id="vsscreate">VssCreate</h2>
<h3>Description</h3>
<p>Task to perform CREATE commands to Microsoft Visual Source Safe.</p>
<p>Creates a new project in VSS.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>login</td>
    <td><q>username,password</q></td>
    <td>No</td>
  </tr>
  <tr>
    <td>vsspath</td>
    <td>SourceSafe path of project to be created</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>ssdir</td>
    <td>directory where <kbd>ss.exe</kbd> resides.</td>
    <td>No; by default expected to be in <code>PATH</code></td>
  </tr>
  <tr>
    <td>quiet</td>
    <td>suppress output</td>
    <td>No; defaults to <q>off</q></td>
  </tr>
  <tr>
    <td>failOnError</td>
    <td>fail if there is an error creating the project.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>autoresponse</td>
    <td>What to respond with (sets the <kbd>-I</kbd> option). By default, <kbd>-I-</kbd> is
      used; values of <q>Y</q> or <q>N</q> will be appended to this.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>The comment to use for this label. Empty or <q>-</q> for no comment.</td>
    <td>No</td>
  </tr>
</table>
<h3>Examples</h3>

<p>Create the VSS Project <samp>$/existingProject/newProject</samp>.</p>

<pre>&lt;vsscreate vsspath=&quot;$/existingProject/newProject&quot;/&gt;</pre>

</body>
</html>
