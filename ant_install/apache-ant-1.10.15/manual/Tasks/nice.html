<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Nice Task</title>
</head>

<body>

<h2 id="echo">Nice</h2>
<h3>Description</h3>
<p>Provide &quot;nice-ness&quot; to the current thread and/or query the current value.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>currentpriority</td>
    <td>name of the property whose value should be set to the current &quot;nice-ness&quot;
      level.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>newpriority</td>
    <td>value to which the &quot;nice-ness&quot; level should be set.  Must be a valid Java Thread
      priority.</td>
    <td>No</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Set the Thread priority to 10 (highest).</p>
<pre>&lt;nice newpriority=&quot;10&quot;/&gt;</pre>

<p>Store the current Thread priority in the user property <code>priority</code>.</p>
<pre>&lt;nice currentpriority=&quot;priority&quot;/&gt;</pre>

<p>Set the current Thread priority to 1 (lowest), storing the original priority in the user
property <code>currentpriority</code>. This can be used to set the priority back to its original
value later.</p>
<pre>&lt;nice currentpriority=&quot;currentpriority&quot; newpriority=&quot;1&quot;/&gt;</pre>

</body>
</html>
