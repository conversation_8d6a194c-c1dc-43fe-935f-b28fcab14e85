<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>DefaultExcludes Task</title>
</head>

<body>

<h2 id="defaultexcludes">DefaultExcludes</h2>

<p><em>Since Apache Ant 1.6</em></p>

<h3>Description</h3>
<p>Alters the default excludes for all subsequent processing in the build, and prints out the
current default excludes if desired.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>echo</td>
    <td>whether or not to print out the default excludes</td>
    <td><q>true</q> required if no other attribute specified; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>default</td>
    <td>go back to hard wired default excludes</td>
    <td><q>true</q> required if no other attribute is specified</td>
  </tr>
  <tr>
    <td>add</td>
    <td>the pattern to add to the default excludes</td>
    <td>if no other attribute is specified</td>
  </tr>
  <tr>
    <td>remove</td>
    <td>remove the specified pattern from the default excludes</td>
    <td>if no other attribute is specified</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Print out the default excludes</p>

<pre>&lt;defaultexcludes echo=&quot;true&quot;/&gt;</pre>

<p>Print out the default excludes and exclude all <samp>*.bak</samp> files in <strong>all</strong>
further processing</p>

<pre>&lt;defaultexcludes echo=&quot;true&quot; add=&quot;**/*.bak&quot;/&gt;</pre>

<p>Silently allow several fileset based tasks to operate on emacs backup files and then restore
normal behavior</p>

<pre>
&lt;defaultexcludes remove=&quot;**/*~&quot;/&gt;

(do several fileset based tasks here)

&lt;defaultexcludes default=&quot;true&quot;/&gt;</pre>

<h3>Notes</h3>
<p>By default the pattern <samp>**/.svn</samp> and <samp>**/.svn/**</samp> are set as default
excludes. Since version 1.3, Subversion supports
the <a href="https://subversion.apache.org/docs/release-notes/1.3.html#_svn-hack"
target="_top">&quot;_svn hack&quot;</a>.  That means, that the svn-libraries evaluate environment
variables and use <samp>.svn</samp> or <samp>_svn</samp> directory regarding to that value. We had
chosen not to evaluate environment variables to get a more reliable build. Instead you have to
change the settings by yourself by changing the exclude patterns:</p>
<pre>
&lt;defaultexcludes remove=&quot;**/.svn&quot;/&gt;
&lt;defaultexcludes remove=&quot;**/.svn/**&quot;/&gt;
&lt;defaultexcludes add=&quot;**/_svn&quot;/&gt;
&lt;defaultexcludes add=&quot;**/_svn/**&quot;/&gt;
</pre>

</body>
</html>
