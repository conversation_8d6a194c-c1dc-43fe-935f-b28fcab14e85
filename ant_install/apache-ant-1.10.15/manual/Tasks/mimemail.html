<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>MimeMail Task</title>
</head>

<body>

<h2 id="mimemail">MimeMail</h2>

<h3><em><u>Deprecated</u></em></h3>
<p><em>This task has been <u>deprecated</u>.  Use the <a href="../Tasks/mail.html">mail</a> task instead.</em></p>

<h3>Description</h3>
<p>Sends SMTP mail with MIME
attachments.  <a href="https://eclipse-ee4j.github.io/mail/"
target="_top">Jakarta Mail</a>
and <a href="https://eclipse-ee4j.github.io/jaf/" target="_top">Jakarta
Activation</a> are required for this task. The Java
Activation Framework is part of the standard class library for Java 6
to Java 10 but the <code>java.activation</code> module has been
deprecated in Java 9 and must be enabled explicitly when running on
Java 10. Alternatively it can be provided as an external
library. Javamail has become Jakarta Mail and the Java activation
Framework has become Jakarta Activation and both are part of the
Jakarta effort at Eclipse.</p>
<p>Multiple files can be attached using <a href="../Types/fileset.html">FileSets.</a></p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>message</td>
    <td>The message body</td>
    <td rowspan="2">Exactly one of these, or a nested fileset</td>
  </tr>
  <tr>
    <td>messageFile</td>
    <td class="left">A filename to read and used as the message body</td>
  </tr>
  <tr>
    <td>messageMimeType</td>
    <td>MIME type to use for <var>message</var> or <var>messageFile</var> when attached.</td>
    <td>No; defaults to <q>text/plain</q></td>
  </tr>
  <tr>
    <td>tolist</td>
    <td>Comma-separated list of <code>To:</code> recipients</td>
    <td rowspan="3">Yes, at least one of these</td>
  </tr>
  <tr>
    <td>cclist</td>
    <td class="left">Comma-separated list of <code>CC:</code> recipients</td>
  </tr>
  <tr>
    <td>bcclist</td>
    <td class="left">Comma-separated list of <code>BCC:</code> recipients</td>
  </tr>
  <tr>
    <td>mailhost</td>
    <td>Host name of the mail server.</td>
    <td>No; default to <q>localhost</q></td>
  </tr>
  <tr>
    <td>subject</td>
    <td>Email subject line.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>from</td>
    <td>Email address of sender.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if an error occurs sending the e-mail.</td>
    <td>No; default to <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>
<p>Send a single HTML file as the body of a message</p>
<pre>
&lt;mimemail messageMimeType=&quot;text/html&quot; messageFile=&quot;overview-summary.html&quot;
          tolist=&quot;you&quot; subject=&quot;JUnit Test Results: ${TODAY}&quot; from=&quot;me&quot;/&gt;</pre>

<p>Send all files in a directory as attachments</p>
<pre>
&lt;mimemail message=&quot;See attached files&quot; tolist=&quot;you&quot; subject=&quot;Attachments&quot; from=&quot;me&quot;&gt;
    &lt;fileset dir=&quot;.&quot;&gt;
       &lt;include name=&quot;dist/*.*&quot;/&gt;
    &lt;/fileset&gt;
&lt;/mimemail&gt;</pre>

</body>
</html>
