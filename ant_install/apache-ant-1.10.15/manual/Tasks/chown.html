<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Chown Task</title>
</head>

<body>

<h2 id="chown">Chown</h2>
<p><em>Since Apache Ant 1.6</em>.</p>
<h3>Description</h3>

<p>Changes the owner of a file or all files inside specified directories. Right now it has effect
only under Unix.  The owner attribute is equivalent to the corresponding argument for
the <kbd>chown</kbd> command.</p>

<p><a href="../Types/fileset.html">FileSet</a>s, <a href="../Types/dirset.html">DirSet</a>s
or <a href="../Types/filelist.html">FileList</a>s can be specified using
nested <code>&lt;fileset&gt;</code>, <code>&lt;dirset&gt;</code> and <code>&lt;filelist&gt;</code>
elements.</p>

<p><em>Since Ant 1.7</em>, this task supports
arbitrary <a href="../Types/resources.html#collection">resource collections</a> as nested
elements.</p>

<p>By default this task will use a single invocation of the underlying <kbd>chown</kbd> command.
If you are working on a large number of files this may result in a command line that is too long for
your operating system.  If you encounter such problems, you should set the <var>maxparallel</var>
attribute of this task to a non-zero value.  The number to use highly depends on the length of your
file names (the depth of your directory tree) and your operating system, so you'll have to
experiment a little.  POSIX recommends command line length limits of at least 4096 characters, this
may give you an approximation for the number you could use as initial value for these
experiments.</p>

<p>By default this task won't do anything unless it detects it is running on a Unix system.  If you
know for sure that you have a <kbd>chown</kbd> executable on your <code>PATH</code> that is command
line compatible with the Unix command, you can use the task's <var>os</var> attribute and set its
value to your current OS.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>the file or directory of which the owner must be
    changed.</td>
    <td>Yes, unless nested <code>&lt;fileset|filelist|dirset&gt;</code> elements are specified</td>
  </tr>
  <tr>
    <td>owner</td>
    <td>the new owner.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>parallel</td>
    <td>process all specified files using a single <kbd>chown</kbd> command.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>type</td>
    <td>One of <q>file</q>, <q>dir</q> or <q>both</q>. If set to <q>file</q>, only the owner of
      plain files are going to be changed. If set to <q>dir</q>, only the directories are
      considered.<br/><strong>Note</strong>: The <var>type</var> attribute does not apply to
      nested <code>dirset</code>s&mdash;<code>dirset</code>s always implicitly assume type to
      be <q>dir</q>.</td>
    <td>No; default is <q>file</q></td>
  </tr>
  <tr>
    <td>maxparallel</td>
    <td>Limit the amount of parallelism by passing at most this many sourcefiles at once.  Set it to
      negative integer for unlimited.</td>
    <td>No; defaults to unlimited</td>
  </tr>
  <tr>
    <td>verbose</td>
    <td>Whether to print a summary after execution or not.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>os</td>
    <td>list of Operating Systems on which the command may be executed.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>osfamily</td>
    <td>OS family as used in the <a href="../Tasks/conditions.html#os">&lt;os&gt;</a>
      condition.</td>
    <td>No; defaults to <q>unix</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Make the <samp>start.sh</samp> file belong to <samp>coderjoe</samp> on a UNIX system.</p>
<pre>&lt;chown file="${dist}/start.sh" owner="coderjoe"/&gt;</pre>

<p>Make all <samp>.sh</samp> files below <samp>${dist}/bin</samp> belong to <samp>coderjoe</samp> on
a UNIX system.</p>
<pre>
&lt;chown owner="coderjoe"&gt;
  &lt;fileset dir="${dist}/bin" includes="**/*.sh"/&gt;
&lt;/chown&gt;</pre>

<p>Make all files below <samp>shared/sources1</samp> (except those below any directory
named <samp>trial</samp>) belong to <samp>coderjoe</samp> on a UNIX system. In addition, all files
belonging to a FileSet with <var>id</var>=<code>other.shared.sources</code> get the same owner.</p>
<pre>
&lt;chown owner="coderjoe"&gt;
  &lt;fileset dir="shared/sources1"&gt;
    &lt;exclude name="**/trial/**"/&gt;
  &lt;/fileset&gt;
  &lt;fileset refid="other.shared.sources"/&gt;
&lt;/chown&gt;</pre>

<p>Make <samp>cgi</samp> scripts, files with a <samp>.old</samp> extension or directories beginning
with <samp>private_</samp> belong to the user named <samp>webadmin</samp>. A directory ending
in <samp>.old</samp> or a file beginning with <samp>private_</samp> would remain unaffected.</p>
<pre>
&lt;chown owner="webadmin" type="file"&gt;
  &lt;fileset dir="/web"&gt;
    &lt;include name="**/*.cgi"/&gt;
    &lt;include name="**/*.old"/&gt;
  &lt;/fileset&gt;
  &lt;dirset dir="/web"&gt;
    &lt;include name="**/private_*"/&gt;
  &lt;/dirset&gt;
&lt;/chmod&gt;</pre>

</body>
</html>
