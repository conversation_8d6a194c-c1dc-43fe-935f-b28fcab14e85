<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
  <title>Include Task</title>
</head>
<body>
  <h2 id="include">Include</h2>
  <p><em>Since Apache Ant 1.8.0</em></p>
  <h3>Description</h3>
  <p>Include another build file into the current project.</p>

  <p><strong>Note</strong> this task heavily relies on the ProjectHelper implementation and doesn't
    really perform any work of its own.  If you have configured Ant to use a ProjectHelper other
    than <PERSON><PERSON>'s default, this task may or may not work.</p>

  <p>On execution it will read another Ant file into the same project rewriting the included
    target <var>name</var>s and <var>depends</var> lists.  This is different
    from <a href="https://ant.apache.org/faq.html#xml-entity-include" target="_top">Entity Includes
    as explained in the Ant FAQ</a> insofar as the target names get prefixed by the included
    project's <var>name</var> or <var>as</var> attribute and do not appear as if the file was
    contained in the including file.</p>

  <p>The <code>include</code> task may only be used as a top-level task. This means that
    it may not be used in a target.</p>

  <p>There are two further functional aspects that pertain to this task and that are not possible
    with entity includes:</p>
  <ul>
    <li>target rewriting</li>
    <li>special properties</li>
  </ul>
  <h4>Target rewriting</h4>

  <p>Any target in the included file will be renamed to <q>prefix.name</q> where <q>name</q> is the
    original target's name and <q>prefix</q> is either the value of the <var>as</var> attribute or
    the <var>name</var> attribute of the <code>project</code> tag of the included file.</p>

  <p>The <var>depends</var> attribute of all included targets is rewritten so that all target names
    are prefixed as well.  This makes the included file self-contained.</p>

  <p>Note that prefixes nest, so if a build file includes a file with prefix <q>q</q> and the
    included file includes another file with prefix <q>b</q>, then the targets of that last build
    file will be prefixed by <q>a.b.</q>.</p>

  <p><code>&lt;import&gt;</code> contribute to the prefix as well, but only if their <var>as</var>
    attribute has been specified.

  <h4>Special properties</h4>

  <p>Included files are treated as they are present in the main buildfile. This makes it easy to
    understand, but it makes it impossible for them to reference files and resources relative to
    their path.  Because of this, for every included file, Ant adds a property that contains the
    path to the included buildfile. With this path, the included buildfile can keep resources and be
    able to reference them relative to its position.</p>

  <p>So if I include for example a <samp>docsbuild.xml</samp> file named <q>builddocs</q>, I can get
    its path as <code>ant.file.builddocs</code>, similarly to the <code>ant.file</code> property of
    the main buildfile.</p>

  <p>Note that <q>builddocs</q> is not the filename, but the <var>name</var> attribute present in
    the included <code>project</code> tag.</p>
  <p>If the included file does not have a <var>name</var> attribute,
    the <code>ant.file.<i>projectname</i></code> property will not be set.</p>

  <p>If you need to know whether the current build file's source has been a file or an URL you can
    consult the property <code>ant.file.type.<i>projectname</i></code> (using the same example as
    above <code>ant.file.type.builddocs</code>) which either have the value <q>file</q>
    or <q>url</q>.</p>

  <h4>Resolving files against the included file</h4>

  <p>Suppose your main build file called <samp>including.xml</samp> includes a build
    file <samp>included.xml</samp>, located anywhere on the file system,
    and <samp>included.xml</samp> reads a set of properties
    from <samp>included.properties</samp>:</p>

  <pre>
&lt;!-- including.xml --&gt;
&lt;project name="including" basedir="." default="..."&gt;
  &lt;include file="${path_to_included}/included.xml"/&gt;
&lt;/project&gt;

&lt;!-- included.xml --&gt;
&lt;project name="included" basedir="." default="..."&gt;
  &lt;property file="included.properties"/&gt;
&lt;/project&gt;</pre>

  <p>This snippet however will resolve <samp>included.properties</samp> against
    the <var>basedir</var> of <samp>including.xml</samp>, because the <var>basedir</var>
    of <samp>included.xml</samp> is ignored by Ant. The right way to
    use <samp>included.properties</samp> is:</p>

  <pre>
&lt;!-- included.xml --&gt;
&lt;project name="included" basedir="." default="..."&gt;
  &lt;dirname property="included.basedir" file="${ant.file.included}"/&gt;
  &lt;property file="${included.basedir}/included.properties"/&gt;
&lt;/project&gt;</pre>

  <p>As explained above <code>ant.file.included</code> stores the path of the build script, that
    defines the project called <q>included</q>, (in short it stores the path
    to <samp>included.xml</samp>) and <a href="dirname.html"><code>&lt;dirname&gt;</code></a> takes
    its directory. This technique also allows <samp>included.xml</samp> to be used as a standalone
    file (without being included in other project).</p>

  <p>The above description only works for included files that actually are included from files and
    not from URLs.  For files included from URLs using resources relative to the included file
    requires you to use tasks that can work on non-file resources in the first place.  To create a
    relative resource you'd use something like:</p>

  <pre>
&lt;loadproperties&gt;
  &lt;url baseUrl="${ant.file.included}"
       relativePath="included.properties"/&gt;
&lt;/loadproperties&gt;</pre>

<h3>Parameters</h3>
<table class="attr">
  <tbody>
    <tr>
      <th scope="col">Attribute</th>
      <th scope="col">Description</th>
      <th scope="col">Required</th>
    </tr>
    <tr>
      <td>file</td>
      <td>The file to include. If this is a relative file name, the file name will be resolved
        relative to the <em>including</em> file. <strong>Note</strong>, this is unlike most other
        ant file attributes, where relative files are resolved relative to ${basedir}.</td>
      <td>Yes or a nested resource collection</td>
    </tr>
    <tr>
      <td>optional</td>
      <td>If <q>true</q>, do not stop the build if the file does not exist.</td>
      <td>No; default is <q>false</q></td>
    </tr>
    <tr>
      <td>as</td>
      <td>Specifies the prefix prepended to the target names.</td>
      <td>Yes, if the included file's <code>project</code> tag doesn't specify a <var>name</var>
        attribute (which is otherwise taken as default)</td>
    </tr>
    <tr>
      <td>prefixSeparator</td>
      <td>Specifies the separator to be used between the prefix and the target name.</td>
      <td>No; defaults to <q>.</q></td>
    </tr>
  </tbody>
</table>

<h3>Parameters specified as nested elements</h3>

<h4>any <a href="../Types/resources.html">resource</a> or resource collection</h4>

<p>The specified resources will be included.</p>

<h3>Examples</h3>
<pre>&lt;include file=&quot;../common-targets.xml&quot;/&gt;</pre>

<p>Includes targets from the <samp>common-targets.xml</samp> file that is in a parent directory.</p>

<pre>&lt;include file=&quot;${deploy-platform}.xml&quot;/&gt;</pre>

<p>Includes the project defined by the property deploy-platform</p>

<pre>
&lt;include&gt;
  &lt;javaresource name="common/targets.xml"&gt;
    &lt;classpath location="common.jar"/&gt;
  &lt;/javaresource&gt;
&lt;/include&gt;</pre>

<p>Includes targets from the <samp>targets.xml</samp> file that is inside the
directory <samp>common</samp> inside the jar file <samp>common.jar</samp>.</p>

<h3>How is <a href="import.html">&lt;import&gt;</a> different from &lt;include&gt;?</h3>

<p>The short version: Use <code>import</code> if you intend to override a target, otherwise
use <code>include</code>.</p>

<p>When <code>import</code> is used, the imported targets are available by up to two names: their
"normal" name without any prefix and potentially with a prefixed name (the value of
the <var>as</var> attribute or the imported project's <var>name</var> attribute, if any).</p>

<p>When <code>include</code> is used, the included targets are only available in the prefixed form.</p>

<p>When <code>import</code> is used, the imported target's <var>depends</var> attribute remains
unchanged, i.e. it uses "normal" names and allows you to override targets in the dependency
list.</p>

<p>When <code>include</code> is used, the included targets cannot be overridden and
their <var>depends</var> attributes are rewritten so that prefixed names are used.  This allows
writers of the included file to control which target is invoked as part of the dependencies.</p>

<p>It is possible to <code>include</code> the same file more than once by using different prefixes;
it is not possible to <code>import</code> the same file more than once.</p>

<h4>Examples</h4>

<p><samp>nested.xml</samp> shall be:</p>

<pre>
&lt;project&gt;
  &lt;target name="setUp"&gt;
    &lt;property name="prop" value="in nested"/&gt;
  &lt;/target&gt;

  &lt;target name="echo" depends="setUp"&gt;
    &lt;echo&gt;prop has the value ${prop}&lt;/echo&gt;
  &lt;/target&gt;
&lt;/project&gt;</pre>

<p>When using <code>import</code> like in</p>

<pre>
&lt;project default="test"&gt;
  &lt;target name="setUp"&gt;
    &lt;property name="prop" value="in importing"/&gt;
  &lt;/target&gt;

  &lt;import file="nested.xml" as="nested"/&gt;

  &lt;target name="test" depends="nested.echo"/&gt;
&lt;/project&gt;</pre>

<p>Running the build file will emit:</p>

<pre class="output">
setUp:

nested.echo:
     [echo] prop has the value in importing

test:

</pre>

<p>When using <code>include</code> like in</p>

<pre>
&lt;project default="test"&gt;
  &lt;target name="setUp"&gt;
    &lt;property name="prop" value="in importing"/&gt;
  &lt;/target&gt;

  &lt;include file="nested.xml" as="nested"/&gt;

  &lt;target name="test" depends="nested.echo"/&gt;
&lt;/project&gt;</pre>

<p>Running the target build file will emit:</p>

<pre class="output">
nested.setUp:

nested.echo:
     [echo] prop has the value in nested

test:

</pre>

<p>and there won't be any target named <q>echo</q> on the including build file.</p>

</body>
</html>
