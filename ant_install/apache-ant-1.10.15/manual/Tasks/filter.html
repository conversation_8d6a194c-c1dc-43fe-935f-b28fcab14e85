<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Filter Task</title>
</head>

<body>

<h2 id="filter">Filter</h2>
<h3>Description</h3>
<p>Sets a token filter for this project or reads a properties file as multiple token filter
definitions and sets them.  Token filters are used by all tasks that perform file copying operations
through the Project commodity methods. See the
warning <a href="../using.html#filters"><em>here</em></a> before using.</p>
<p>The task uses <q>@</q> as token separator. Token strings cannot contain separator characters;
incorrect tokens are ignored.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>token</td>
    <td>the token string without the separator chars (<q>@</q>)</td>
    <td rowspan="3">Yes; either both <var>token</var> and <var>value</var>, or
      only <var>filtersfile</var></td>
  </tr>
  <tr>
    <td>value</td>
    <td class="left">the string that should be put to replace the token when the file is copied</td>
  </tr>
  <tr>
    <td>filtersfile</td>
    <td class="left">The file from which the filters must be read. This file must be a formatted as
      a property file.</td>
  </tr>
</table>
<h3>Examples</h3>

<p>Copy recursively all the files from the <samp>src.dir</samp> directory into
the <samp>dest.dir</samp> directory replacing all the occurrences of the string <code>@year@</code>
with <q>2000</q>.</p>
<pre>
&lt;filter token=&quot;year&quot; value=&quot;2000&quot;/&gt;
&lt;copy todir=&quot;${dest.dir}&quot; filtering=&quot;true&quot;&gt;
  &lt;fileset dir=&quot;${src.dir}&quot;/&gt;
&lt;/copy&gt;</pre>

<p>Read all property entries from the <samp>deploy_env.properties</samp> file and set these as
filters.</p>
<pre>&lt;filter filtersfile=&quot;deploy_env.properties&quot;/&gt;</pre>

</body>
</html>
