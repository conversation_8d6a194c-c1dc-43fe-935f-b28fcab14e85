<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>RenameExtensions Task</title>
</head>

<body>

<h2 id="renameexts">RenameExtensions</h2>
<h3><em><u>Deprecated</u></em></h3>
<p><em>This task has been <u>deprecated</u>.  Use the <a href="../Tasks/move.html">move</a> task
with a <a href="../Types/mapper.html#glob-mapper">glob mapper</a> instead.</em></p>
<h3>Description</h3>
<p>Renames files in the <var>srcDir</var> directory ending with the <var>fromExtension</var> string
so that they end with the <var>toExtension</var> string. Files are only replaced
if <var>replace</var> is <q>true</q>.</p>
<p>See the section on <a href="../dirtasks.html#directorybasedtasks">directory based tasks</a>, on
how the inclusion/exclusion of files works, and how to write patterns.  This task forms an
implicit <a href="../Types/fileset.html">FileSet</a> and supports most attributes
of <code>&lt;fileset&gt;</code> (<var>dir</var> becomes <var>srcDir</var>) as well as the
nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code>
and <code>&lt;patternset&gt;</code> elements.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>indicates whether default excludes should be used or not (<q>yes|no</q>).</td>
    <td>No; defaults to <q>yes</q></td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>comma- or space-separated list of patterns of files that must be excluded.</td>
    <td>No; defaults to default excludes or none if <var>defaultexcludes</var> is <q>no</q></td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td>name of a file. Each line of this file is taken to be an exclude pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>fromExtension</td>
    <td>The string that files must end in to be renamed</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>includes</td>
    <td>comma- or space-separated list of patterns of files that must be included.</td>
    <td>No; defaults to all (<q>**</q>)</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td>name of a file. Each line of this file is taken to be an include pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>Whether the file being renamed to should be replaced if it already exists</td>
    <td>No</td>
  </tr>
  <tr>
    <td>srcDir</td>
    <td>The starting directory for files to search in</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>toExtension</td>
    <td>The string that renamed files will end with on completion</td>
    <td>Yes</td>
  </tr>
</table>
<h3>Examples</h3>

<pre>
&lt;renameext srcDir=&quot;/source/project1&quot;
           includes=&quot;**&quot;
           excludes=&quot;**/samples/*&quot;
           fromExtension=&quot;.java.keep&quot;
           toExtension=&quot;.java&quot;
           replace=&quot;true&quot;/&gt;</pre>

</body>
</html>
