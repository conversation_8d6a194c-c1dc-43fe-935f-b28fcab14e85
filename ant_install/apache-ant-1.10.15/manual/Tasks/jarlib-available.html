<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>jarlib-available Task</title>
</head>

<body>

<h2 id="jarlib-available">jarlib-available</h2>
<h3>Description</h3>
<p>Check whether an extension is present in a <code>fileset</code> or an <code>extensionSet</code>.
If the extension is present then a property is set.</p>

<p>Note that this task works with extensions as defined by the "Optional Package" specification.
For more information about optional packages, see the document <em>Optional Package Versioning</em>
in the documentation bundle for your Java Standard Edition package, in
file <samp>guide/extensions/versioning.html</samp> or the
online <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/extensions/versioning.html"
target="_top">Extension and ExtensionSet documentation</a> for further details.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The name of property to set if extensions is available</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>file</td>
    <td>The file to check for extension</td>
    <td>Yes, unless a nested
    <code>&lt;extensionSet&gt;</code> or <code>&lt;fileset&gt;</code> is specified</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>extension</h4>
<p><a href="../Types/extension.html">Extension</a> the extension to search for.</p>

<h4>fileset</h4>
<p><a href="../Types/fileset.html">FileSet</a>s are used to select sets of files to check for
extension.</p>

<h4>extensionSet</h4>
<p><a href="../Types/extensionset.html">ExtensionSet</a>s is the set of extensions to search for
extension in.</p>

<h3>Examples</h3>
<p>Search for extension in single file</p>
<pre>
&lt;jarlib-available property=&quot;myext.present&quot; file=&quot;myfile.jar&quot;&gt;
  &lt;extension extensionName=&quot;org.apache.tools.ant&quot;
             specificationVersion=&quot;1.4.9&quot;
             specificationVendor=&quot;Apache Software Foundation&quot;/&gt;
&lt;/jarlib-available&gt;
</pre>

<p>Search for extension in single file referencing external Extension</p>
<pre>
&lt;extension id=&quot;myext&quot;
           extensionName=&quot;org.apache.tools.ant&quot;
           specificationVersion=&quot;1.4.9&quot;
           specificationVendor=&quot;Apache Software Foundation&quot;/&gt;

&lt;jarlib-available property=&quot;myext.present&quot; file=&quot;myfile.jar&quot;&gt;
  &lt;extension refid=&quot;myext&quot;/&gt;
&lt;/jarlib-available&gt;</pre>

<p>Search for extension in fileset</p>
<pre>
&lt;extension id=&quot;myext&quot;
           extensionName=&quot;org.apache.tools.ant&quot;
           specificationVersion=&quot;1.4.9&quot;
           specificationVendor=&quot;Apache Software Foundation&quot;/&gt;

&lt;jarlib-available property=&quot;myext.present&quot;&gt;
  &lt;extension refid=&quot;myext&quot;/&gt;
  &lt;fileset dir="lib"&gt;
    &lt;include name="*.jar"/&gt;
  &lt;/fileset&gt;
&lt;/jarlib-available&gt;</pre>

<p>Search for extension in extensionSet</p>
<pre>
&lt;extension id=&quot;myext&quot;
           extensionName=&quot;org.apache.tools.ant&quot;
           specificationVersion=&quot;1.4.9&quot;
           specificationVendor=&quot;Apache Software Foundation&quot;/&gt;

&lt;jarlib-available property=&quot;myext.present&quot;&gt;
  &lt;extension refid=&quot;myext&quot;/&gt;
  &lt;extensionSet id=&quot;exts3&quot;&gt;
    &lt;libfileset includeUrl=&quot;false&quot;
                includeImpl=&quot;true&quot;
                dir=&quot;lib&quot;&gt;
      &lt;include name=&quot;*.jar&quot;/&gt;
    &lt;/libfileset&gt;
  &lt;/extensionSet&gt;
&lt;/jarlib-available&gt;</pre>

</body>
</html>
