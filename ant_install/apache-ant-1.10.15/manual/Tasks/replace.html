<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Replace Task</title>
</head>

<body>

<h2 id="replace">Replace</h2>
<h3>Description</h3>
<p><code>Replace</code> is a directory based task for replacing the occurrence of a given string
with another string in selected file.</p>
<p>If you want to replace a text that crosses line boundaries, you must use a
nested <code>&lt;replacetoken&gt;</code> element.</p>

<p>The output file is only written if it differs from the existing file.  This prevents spurious
rebuilds based on unchanged files which have been regenerated by this task.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>file for which the token should be replaced.</td>
    <td rowspan="2">Exactly one of the two</td>
  </tr>
  <tr>
    <td>dir</td>
    <td class="left">The base directory to use when replacing a token in multiple files.</td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the files upon which replace operates.</td>
    <td>No; defaults to default JVM character encoding</td>
  </tr>
  <tr>
    <td>token</td>
    <td>the token which must be replaced.</td>
    <td>Yes, unless a nested <code>replacetoken</code> element or the <var>replacefilterfile</var>
      attribute is used.</td>
  </tr>
  <tr>
    <td>value</td>
    <td>the new value for the token.</td>
    <td>No; defaults to empty string (<q></q>)</td>
  </tr>
  <tr>
    <td>summary</td>
    <td>Indicates whether a summary of the replace operation should be produced, detailing how many
      token occurrences and files were processed</td>
    <td>No; defaults to no summary</td>
  </tr>
  <tr>
    <td>propertyFile</td>
    <td>valid property file from which properties specified using
      nested <code>&lt;replacefilter&gt;</code> elements are drawn.</td>
    <td>Yes, only if <var>property</var> attribute of <code>&lt;replacefilter&gt;</code> is
      used.</td>
  </tr>
  <tr>
    <td>replacefilterfile</td>
    <td>valid property file.  Each property will be treated as a <code>replacefilter</code>
      where <var>token</var> is the name of the property and <var>value</var> is the property's
      value.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>includes</td>
    <td>comma- or space-separated list of patterns of files that must be included.</td>
    <td>No; defaults to all (<q>**</q>)</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td>name of a file. Each line of this file is taken to be an include pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>comma- or space-separated list of patterns of files that must be excluded.</td>
    <td>No; defaults to default excludes or none if <var>defaultexcludes</var> is <q>no</q></td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td>name of a file. Each line of this file is taken to be an exclude pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>indicates whether default excludes should be used or not (<q>yes|no</q>).</td>
    <td>No; defaults to <q>yes</q></td>
  </tr>
  <tr>
    <td>preserveLastModified</td>
    <td>Keep the file timestamp(s) even if the file(s) is(are) modified.  <em>since Apache Ant
      1.8.0</em>.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>failOnNoReplacements</td>
    <td>Whether to fail the build if the task didn't do anything.  <em>since Ant 1.8.0</em>.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
</table>
<h3>Examples</h3>
<pre>&lt;replace file=&quot;${src}/index.html&quot; token=&quot;@@@&quot; value=&quot;wombat&quot;/&gt;</pre>
<p>replaces occurrences of the string <q>@@@</q> with the string <q>wombat</q>, in the
file <samp>${src}/index.html</samp>.</p>
<h3>Parameters specified as nested elements</h3>
<p>This task forms an implicit <a href="../Types/fileset.html">FileSet</a> and supports most
attributes of <code>&lt;fileset&gt;</code> as well as the
nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code>
and <code>&lt;patternset&gt;</code> elements.</p>
<p><em>Since Ant 1.8.0</em>, this task supports any filesystem
based <a href="../Types/resources.html#collection">resource collections</a> as nested elements.</p>
<h4>replacetoken and replacevalue</h4>
<p>If either the text you want to replace or the replacement text cross line boundaries, you can use
nested elements to specify them.</p>
<p>The elements support attributes:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>expandProperties</td>
    <td>Whether to expand properties in the nested text.  <em>Since Ant 1.8.0</em>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>
<pre>
&lt;replace dir=&quot;${src}&quot; value=&quot;wombat&quot;&gt;
  &lt;include name=&quot;**/*.html&quot;/&gt;
  &lt;replacetoken&gt;&lt;![CDATA[multi line
token]]&gt;&lt;/replacetoken&gt;
&lt;/replace&gt;</pre>
<p>replaces occurrences of the string <q>multi line<i>\n</i>token</q> with the string <q>wombat</q>,
in all HTML files in the directory <samp>${src}</samp>. Here, <q><i>\n</i></q> is the platform
specific line separator.</p>
<pre>
&lt;replace file=&quot;${src}/index.html&quot;&gt;
  &lt;replacetoken&gt;&lt;![CDATA[two line
token]]&gt;&lt;/replacetoken&gt;
  &lt;replacevalue&gt;&lt;![CDATA[two line
token]]&gt;&lt;/replacevalue&gt;
&lt;/replace&gt;</pre>
<h4>replacefilter</h4>
<p>In addition to allowing for multiple replacements, optional
nested <code>&lt;replacefilter&gt;</code> elements allow replacement values to be extracted from a
property file. The name of this file is specified using the <code>&lt;replace&gt;</code>
attribute <var>propertyFile</var>.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>token</td>
    <td>The string to search for.</td>
    <td>Yes, unless a nested <code>replacetoken</code>
      is specified</td>
  </tr>
  <tr>
    <td>value</td>
    <td>The replacement string.</td>
    <td rowspan="2">Either may be specified, but not both. Both can be omitted, if desired.</td>
  </tr>
  <tr>
    <td>property</td>
    <td class="left">Name of the property whose value is to serve as the replacement value.</td>
  </tr>
</table>
<p><em>Since Ant 1.8.0</em>, <var>token</var> and <var>value</var> can be specified as nested
elements just like in the task itself.</p>
<p>If neither <var>value</var> nor <var>property</var> is used, the value provided using
the <code>&lt;replace&gt;</code> attribute <var>value</var> and/or
the <code>&lt;replacevalue&gt;</code> element is used. If no value was specified using either of
these options, the token is replaced with an empty string.</p>
<h3>Examples</h3>
<p>In file <samp>configure.sh</samp>, replace all instances of <q>@token1@</q>
with <q>defaultvalue</q>, all instances of <q>@token2@</q> with <q>value2</q>, and all instances
of <q>@token3@</q> with the value of the property <code>property.key</code>, as it appears in
property file <samp>src/name.properties</samp>.</p>
<pre>
&lt;replace file=&quot;configure.sh&quot;
         value=&quot;defaultvalue&quot;
         propertyFile=&quot;src/name.properties&quot;&gt;
  &lt;replacefilter token=&quot;@token1@&quot;/&gt;
  &lt;replacefilter token=&quot;@token2@&quot;
                 value=&quot;value2&quot;/&gt;
  &lt;replacefilter token=&quot;@token3@&quot;
                 property=&quot;property.key&quot;/&gt;
  &lt;replacefilter&gt;
    &lt;replacetoken&gt;@token4@&lt;/replacetoken&gt;
    &lt;replacevalue&gt;value4&lt;/replacevalue&gt;
  &lt;/replacefilter&gt;
&lt;/replace&gt;</pre>
<p><strong>Note</strong>: It is possible to use either
the <var>token</var>/<code>&lt;replacetoken&gt;</code>
and <var>value</var>/<code>&lt;replacevalue&gt;</code> attributes/elements, the
nested <code>replacefilter</code> elements, or both in the same operation.</p>

</body>
</html>
