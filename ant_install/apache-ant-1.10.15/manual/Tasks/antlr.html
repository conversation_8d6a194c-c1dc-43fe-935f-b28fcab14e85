<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ANTLR Task</title>
</head>

<body>

<h2 id="antlr">ANTLR</h2>
<h3>Description</h3>
<p>Invokes the <a href="https://www.antlr.org/" target="_top">ANTLR</a> Translator generator on a
grammar file.</p>
<p>To use the ANTLR task, set the <var>target</var> attribute to the name of the grammar file to
process.  Optionally, you can also set the <var>outputdirectory</var> to write the generated file to
a specific directory.  Otherwise ANTLR writes the generated files to the directory containing the
grammar file.</p>
<p>This task only invokes ANTLR if the grammar file (or the supergrammar specified by
the <var>glib</var> attribute) is newer than the generated files.</p>
<p><strong>Note</strong>: This task depends on external libraries not included in the Apache Ant
distribution. See <a href="../install.html#librarydependencies">Library Dependencies</a> for more
information.</p>
<p>Antlr 2.7.2 Note: <em>You will need <samp>antlrall.jar</samp> that can be created by
the <q>antlr-all.jar</q> target of the Makefile provided with the download.</em></p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>target</td>
    <td>The grammar file to process.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>outputdirectory</td>
    <td>The directory to write the generated files to.</td>
    <td>No; defaults to the directory containing the grammar file</td>
  </tr>
  <tr>
    <td>glib</td>
    <td>An optional super grammar file that the target grammar overrides.  This feature is only
      needed for advanced vocabularies.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>debug</td>
    <td>When set to <q>yes</q>, this flag adds code to the generated parser that will launch the
      ParseView debugger upon invocation.<br/>  Note: ParseView is a separate component that needs
      to be installed or your grammar will have compilation errors.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <tr>
    <td>html</td>
    <td>Emit an HTML version of the grammar with hyperlinked actions if set to <q>yes</q>.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <tr>
    <td>diagnostic</td>
    <td>Generate a text file with debugging information based on the target grammar if set
      to <q>yes</q>.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <tr>
    <td>trace</td>
    <td>Force <strong>all</strong> rules to call traceIn/traceOut if set to <q>yes</q>.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <tr>
    <td>traceParser</td>
    <td>Only force parser rules to call traceIn/traceOut if set to <q>yes</q>.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <tr>
    <td>traceLexer</td>
    <td>Only force lexer rules to call traceIn/traceOut if set to <q>yes</q>.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <tr>
    <td>traceTreeWalker</td>
    <td>Only force tree walker rules to call traceIn/traceOut if set to <q>yes</q>.</td>
    <td>No; default is <q>no</q></td>
  </tr>
  <!--tr>
    <td>fork</td>
    <td>Run ANTLR in a separate JVM.</td>
    <td>No; default is <q>off</q></td>
  </tr-->
  <tr>
    <td>dir</td>
    <td>The directory to invoke JVM in.<!--(ignored if fork is disabled)--></td>
    <td>No</td>
  </tr>
</table>

<h3 id="nested">Parameters specified as nested elements</h3>

<p>The task supports a nested <code>&lt;classpath&gt;</code> element, that represents
a <a href="../using.html#path">path-like structure</a>. It is given as a convenience if you have to
specify the original ANTLR directory. In most cases, dropping the appropriate ANTLR jar in the
normal Ant lib repository will be enough.</p>

<h4>jvmarg</h4>

<p><!--If fork is enabled, -->Additional parameters may be passed to the new JVM via
nested <code>&lt;jvmarg&gt;</code> attributes, for example:</p>

<pre>
&lt;antlr target="..."&gt;
  &lt;jvmarg value=&quot;-Dfoo=bar&quot;/&gt;
  ...
&lt;/antlr&gt;</pre>

<p>would run ANTLR in a JVM without JIT.</p>

<p><code>&lt;jvmarg&gt;</code> allows all attributes described
in <a href="../using.html#arg">Command line arguments</a>.</p>

<h3>Example</h3>
<p>Invoke ANTLR on grammar file <samp>etc/java.g</samp>, writing the generated files
to <samp>build/src</samp>:</p>
<pre>
&lt;antlr
    target=&quot;etc/java.g&quot;
    outputdirectory=&quot;build/src&quot;/&gt;</pre>

</body>
</html>
