<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JLink Task</title>
</head>
<body>

<h2 id="jlink">Jlink</h2>
<h3><em><u>Deprecated</u></em></h3>
<p><em>This task has been <u>deprecated</u>. Use a <a href="../Types/zipfileset.html">zipfileset</a>
or <a href="../Tasks/zip.html#zipgroupfileset">zipgroupfileset</a> with
the <a href="../Tasks/jar.html">Jar task</a> or <a href="../Tasks/zip.html">Zip task</a>
instead.  For a task based on the JDK's jlink tool, see
<a href="link.html">Link</a>.</em></p>

<h3>Description</h3>
<p><strong>For a task based on the JDK's jlink tool, see
<a href="link.html">Link</a>.  This task is for something else
entirely.</strong></p>

<p>Links entries from sub-builds and libraries.</p>

<p>The <code>jlink</code> task can be used to build jar and zip files, similar to
the <code>jar</code> task.  However, <code>jlink</code> provides options for controlling the way
entries from input files are added to the output file. Specifically, capabilities for merging
entries from multiple zip or jar files is available.</p>

<p>If a mergefile is specified directly (eg. at the top level of a <code>mergefiles</code>
pathelement) <em>and</em> the mergefile ends in <samp>.zip</samp> or <samp>.jar</samp>, entries in
the mergefile will be merged into the <var>outfile</var>. A file with any other extension will be
added to the output file, even if it is specified in the <code>mergefiles</code> element.
Directories specified in either the <code>mergefiles</code> or <code>addfiles</code> element are
added to the output file as you would expect: all files in subdirectories are recursively added to
the output file with appropriate prefixes in the output file (without merging).</p>

<p>In the case where duplicate entries and/or files are found among the files to be merged or
added, <code>jlink</code> merges or adds the first entry and ignores all subsequent entries.</p>

<p><code>jlink</code> ignores <samp>META-INF</samp> directories in <var>mergefiles</var>. Users
should supply their own manifest information for the output file.</p>

<p>It is possible to refine the set of files that are being jlinked.  This can be done with
the <var>includes</var>, <var>includesfile</var>, <var>excludes</var>, <var>excludesfile</var>,
and <var>defaultexcludes</var> attributes on the <code>addfiles</code> and <code>mergefiles</code>
nested elements. With the <var>includes</var> or <var>includesfile</var> attribute you specify the
files you want to have included by using patterns.  The <var>exclude</var>
or <var>excludesfile</var> attribute is used to specify the files you want to have excluded. This is
also done with patterns. And finally with the <var>defaultexcludes</var> attribute, you can specify
whether you want to use default exclusions or not. See the section
on <a href="../dirtasks.html#directorybasedtasks">directory based tasks</a>, on how the
inclusion/exclusion of files works, and how to write patterns. The patterns are relative to
the <em>base</em> directory.</p>

<h3>Parameters</h3>
<table class="attr">
   <tr>
     <th scope="col">Attribute</th>
     <th scope="col">Description</th>
     <th scope="col">Required</th>
   </tr>
   <tr>
     <td>outfile</td>
     <td>the path of the output file.</td>
     <td>Yes</td>
   </tr>
   <tr>
     <td>compress</td>
     <td>whether or not the output should be compressed. <q>true</q>, <q>yes</q>, or <q>on</q>
       result in compressed output.</td>
     <td>No; defaults to uncompressed (inflated) output</td>
   </tr>
   <tr>
     <td>mergefiles</td>
     <td>files to be merged into the output, if possible.</td>
     <td rowspan="2">Exactly one of the two</td>
   </tr>
   <tr>
     <td>addfiles</td>
     <td class="left">files to be added to the output.</td>
   </tr>
</table>

<h3>Examples</h3>

<p>The following will merge the entries in <samp>mergefoo.jar</samp> and <samp>mergebar.jar</samp>
into <samp>out.jar</samp>.  <samp>mac.jar</samp> and <samp>pc.jar</samp> will be added as single
entries to <samp>out.jar</samp>.</p>
<pre>
&lt;jlink compress=&quot;false&quot; outfile=&quot;out.jar&quot;&gt;
   &lt;mergefiles&gt;
     &lt;pathelement path=&quot;${build.dir}/mergefoo.jar&quot;/&gt;
     &lt;pathelement path=&quot;${build.dir}/mergebar.jar&quot;/&gt;
   &lt;/mergefiles&gt;
   &lt;addfiles&gt;
     &lt;pathelement path=&quot;${build.dir}/mac.jar&quot;/&gt;
     &lt;pathelement path=&quot;${build.dir}/pc.zip&quot;/&gt;
   &lt;/addfiles&gt;
&lt;/jlink&gt;
</pre>

<p><strong>Non-deprecated alternative to the above:</strong></p>
<pre>
&lt;jar compress=&quot;false&quot; destfile=&quot;out.jar&quot;&gt;
  &lt;zipgroupfileset dir=&quot;${build.dir}&quot;&gt;
    &lt;include name=&quot;mergefoo.jar&quot;/&gt;
    &lt;include name=&quot;mergebar.jar&quot;/&gt;
  &lt;/zipgroupfileset&gt;
  &lt;fileset dir=&quot;${build.dir}&quot;&gt;
    &lt;include name=&quot;mac.jar&quot;/&gt;
    &lt;include name=&quot;pc.jar&quot;/&gt;
  &lt;/fileset&gt;
&lt;/jar&gt;
</pre>

<p>Suppose the file <samp>foo.jar</samp> contains two entries: <samp>bar.class</samp>
and <samp>barnone/myClass.zip</samp>.  Suppose the path for file <samp>foo.jar</samp>
is <samp>build/tempbuild/foo.jar</samp>. The following example will provide the
entry <samp>tempbuild/foo.jar</samp> in the <samp>out.jar</samp>.</p>
<pre>
&lt;jlink compress=&quot;false&quot; outfile=&quot;out.jar&quot;&gt;
   &lt;mergefiles&gt;
     &lt;pathelement path=&quot;build/tempbuild&quot;/&gt;
   &lt;/mergefiles&gt;
&lt;/jlink&gt;</pre>

<p>However, the next example would result in two top-level entries in <samp>out.jar</samp>,
namely <samp>bar.class</samp> and <samp>barnone/myClass.zip</samp></p>
<pre>
&lt;jlink compress=&quot;false&quot; outfile=&quot;out.jar&quot;&gt;
   &lt;mergefiles&gt;
     &lt;pathelement path=&quot;build/tempbuild/foo.jar&quot;/&gt;
   &lt;/mergefiles&gt;
&lt;/jlink&gt;</pre>

</body>
</html>
