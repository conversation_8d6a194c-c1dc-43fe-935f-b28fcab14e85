<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Mkdir Task</title>
</head>

<body>

<h2 id="mkdir">Mkdir</h2>
<h3>Description</h3>
<p>Creates a directory. Also non-existent parent directories are created, when necessary. Does
nothing if the directory already exists.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>dir</td>
    <td>the directory to create.</td>
    <td>Yes</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Create a directory <code>${dist}</code>.</p>
<pre>&lt;mkdir dir=&quot;${dist}&quot;/&gt;</pre>

<p>Create a directory <code>${dist}/lib</code>.</p>
<pre>&lt;mkdir dir=&quot;${dist}/lib&quot;/&gt;</pre>

</body>
</html>
