<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>EchoXML Task</title>
</head>

<body>

<h2>EchoXML</h2>
<p><em>Since Apache Ant 1.7</em></p>
<h3>Description</h3>
<p>Echo nested XML to the console or a file.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>The file to receive the XML.</td>
    <td>No; by default nested XML is echoed to the log</td>
  </tr>
  <tr>
    <td>append</td>
    <td>Whether to append <var>file</var>, if specified.</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>namespacePolicy</td>
    <td>Sets the namespace policy as defined
      by <code>org.apache.tools.ant.util.DOMElementWriter.XmlNamespacePolicy</code>.  Valid values
      are <q>ignore</q>, <q>elementsOnly</q>, or <q>all</q>.  <em>Since Apache Ant 1.8</em></td>
    <td>No; default <q>ignore</q></td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<p>Nested XML content is required.</p>

<h3>Examples</h3>

<p>Create an Ant buildfile, <samp>subbuild.xml</samp>.</p>
<pre>&lt;echoxml file=&quot;subbuild.xml&quot;&gt;
  &lt;project default=&quot;foo&quot;&gt;
    &lt;target name=&quot;foo&quot;&gt;
      &lt;echo&gt;foo&lt;/echo&gt;
    &lt;/target&gt;
  &lt;/project&gt;
&lt;/echoxml&gt;</pre>

</body>
</html>
