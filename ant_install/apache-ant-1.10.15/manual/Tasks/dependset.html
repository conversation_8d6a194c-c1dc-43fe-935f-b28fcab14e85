<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>DependSet Task</title>
</head>

<body>

<h2>DependSet</h2>

A task to manage arbitrary dependencies between resources.

<h3>Description</h3>

<p>The <code>dependset</code> task compares a set of sources with a set of target files.  If any of
the sources has been modified more recently than any of the target files, all of the target files
are removed.</p>
<p>Sources and target files are specified via
nested <a href="../Types/resources.html#collection">resource collections</a>; sources can be
resources of any type, while targets are restricted to files only. At least one set of sources and
one set of targets is required.</p>
<p>Use a FileSet when you want to use wildcard include or exclude patterns and don't care about
missing files.  Use a FileList when you want to consider the non-existence of a file as if it were
out of date.  If there are any non-existing files in any source or target FileList, all target files
will be removed.</p>
<p>DependSet is useful to capture dependencies that are not or cannot be determined algorithmically.
For example, the <code>&lt;style&gt;</code> task only compares the source XML file and XSLT
stylesheet against the target file to determined whether to restyle the source.
Using <code>dependset</code> you can extend this dependency checking to include a DTD or XSD file as
well as other stylesheets imported by the main stylesheet.</p>

<h3>Parameters</h3>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>verbose</td>
    <td>Makes the task list all deleted targets files and the reason why they get deleted.</td>
    <td>No</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>

<h4>sources</h4>

<p>The <code>&lt;sources&gt;</code> element is a <a href="../Types/resources.html#union">Union</a>
into which arbitrary resource collections can be nested. <em>Since Apache Ant 1.7</em>
</p>

<h4>srcfileset</h4>

<p>The nested <code>&lt;srcfileset&gt;</code> element specifies
a <a href="../Types/fileset.html">FileSet</a>. All files included in this fileset will be compared
against all files included in all of the <code>&lt;targetfileset&gt;</code> filesets
and <code>&lt;targetfilelist&gt;</code> filelists.  Multiple <code>&lt;srcfileset&gt;</code>
filesets may be specified.</p>

<h4>srcfilelist</h4>

<p>The nested <code>&lt;srcfilelist&gt;</code> element specifies
a <a href="../Types/filelist.html">FileList</a>. All files included in this filelist will be
compared against all files included in all of the <code>&lt;targetfileset&gt;</code> filesets
and <code>&lt;targetfilelist&gt;</code> filelists.  Multiple <code>&lt;srcfilelist&gt;</code>
filelists may be specified.</p>

<h4>targets</h4>

<p><em>Since Ant 1.7</em></p>

<p>The <code>&lt;targets&gt;</code> element is a <a href="../using.html#path">Path</a> and thus can
include any filesystem-based resource.</p>

<h4>targetfileset</h4>

<p>The nested <code>&lt;targetfileset&gt;</code> element specifies
a <a href="../Types/fileset.html">FileSet</a>.  All files included in this fileset will be compared
against all files included in all of the <code>&lt;srcfileset&gt;</code> filesets
and <code>&lt;sourcefilelist&gt;</code> filelists, and if any are older, they are all deleted.
Multiple <code>&lt;targetfileset&gt;</code> filesets may be specified.</p>

<h4>targetfilelist</h4>

<p>The nested <code>&lt;targetfilelist&gt;</code> element specifies
a <a href="../Types/filelist.html">FileList</a>.  All files included in this filelist will be
compared against all files included in all of the <code>&lt;srcfileset&gt;</code> filesets
and <code>&lt;sourcefilelist&gt;</code> filelists, and if any are older, they are all deleted.
Multiple <code>&lt;targetfilelist&gt;</code> filelists may be specified.</p>

<h3>Examples</h3>

<p>Remove derived HTML files in the ${output.dir} directory if they are out-of-date with respect
to:</p>
<ol>
    <li>the DTD of their source XML files</li>
    <li>a common DTD (imported by the main DTD)</li>
    <li>a subordinate XSLT stylesheet (imported by the main stylesheet), or</li>
    <li>the buildfile</li>
</ol>
<pre>
&lt;dependset&gt;
  &lt;srcfilelist
      dir   = &quot;${dtd.dir}&quot;
      files = &quot;paper.dtd,common.dtd&quot;/&gt;
  &lt;srcfilelist
      dir   = &quot;${xsl.dir}&quot;
      files = &quot;common.xsl&quot;/&gt;
  &lt;srcfilelist
      dir   = &quot;${basedir}&quot;
      files = &quot;build.xml&quot;/&gt;
  &lt;targetfileset
      dir      = &quot;${output.dir}&quot;
      includes = &quot;**/*.html&quot;/&gt;
&lt;/dependset&gt;</pre>

<p>If any of the sources in the above example does not exist, all target files will also be
removed. To ignore missing sources instead, use filesets instead of filelists for the sources.</p>

</body>
</html>
