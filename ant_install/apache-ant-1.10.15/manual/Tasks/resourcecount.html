<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ResourceCount Task</title>
</head>

<body>

<h2>ResourceCount</h2>
<p><em>Since Apache Ant 1.7</em></p>
<h3>Description</h3>
<p>Display or set a property containing the size of a
nested <a href="../Types/resources.html#collection">resource collection</a>.  Can also be used as
a <code>condition</code>.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col" rowspan="2">Attribute</th>
    <th scope="col" rowspan="2">Description</th>
    <th scope="col" colspan="2">Required</th>
  </tr>
  <tr>
    <th scope="col">Task</th>
    <th scope="col">Condition</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The property to set.</td>
    <td class="center">No; by default, output value to the log</td>
    <td>Ignored</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>A <a href="../using.html#references">reference</a> to a resource collection.</td>
    <td colspan="2">Yes, unless a nested resource collection is supplied</td>
  </tr>
  <tr>
    <td>count</td>
    <td>Comparison count.</td>
    <td class="center" rowspan="2">Ignored</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>when</td>
    <td>Comparison
    type: <q>equal</q>, <q>eq</q>, <q>greater</q>, <q>gt</q>, <q>less</q>, <q>lt</q>, <q>ge</q>
    (greater or equal), <q>ne</q> (not equal), <q>le</q> (less or equal).</td>
    <td>No; default is <q>equal</q></td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<h4>resource collection</h4>
<p>A single <a href="../Types/resources.html#collection">resource collection</a> should be specified
via a nested element or the <var>refid</var> attribute.</p>

<h3>Examples</h3>
<p>Store the number of resources in the specified filelist (two files) in the property
named <samp>count.foo</samp>.</p>
<pre>
&lt;resourcecount property=&quot;count.foo&quot;&gt;
  &lt;filelist dir=&quot;.&quot; files=&quot;foo,bar&quot;/&gt;
&lt;/resourcecount&gt;</pre>

<p>Store the number of lines of the current buildfile in the
property <code>file.lines</code>. Requires Ant 1.7.1+ as <code>&lt;concat&gt;</code> has to be a
resource.</p>
<pre>
&lt;project&gt;
  &lt;property name=&quot;file&quot; value=&quot;${ant.file}&quot;/&gt;
  &lt;resourcecount property=&quot;file.lines&quot;&gt;
    &lt;tokens&gt;
      &lt;concat&gt;
        &lt;filterchain&gt;
          &lt;tokenfilter&gt;
            &lt;linetokenizer/&gt;
          &lt;/tokenfilter&gt;
        &lt;/filterchain&gt;
        &lt;fileset file=&quot;${file}&quot;/&gt;
      &lt;/concat&gt;
    &lt;/tokens&gt;
  &lt;/resourcecount&gt;
  &lt;echo&gt;The file '${file}' has ${file.lines} lines.&lt;/echo&gt;
&lt;/project&gt;</pre>

</body>
</html>
