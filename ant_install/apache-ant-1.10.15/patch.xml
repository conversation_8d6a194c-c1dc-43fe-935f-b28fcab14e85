<?xml version="1.0"?>

<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!--
  =======================================================================
   Use Apache Ant to generate a patch file for Apache Ant.
  =======================================================================
-->
<project name="create-patch" default="patchpackage" basedir=".">
    <property environment="env"/>
    <property name="patch.package" value="patch.tar.gz"/>
    <property name="patch.file" value="patch.txt"/>

    <condition property="git.found">
        <or>
            <available file="git" filepath="${env.PATH}"/>
            <available file="git.exe" filepath="${env.PATH}"/>
            <available file="git.exe" filepath="${env.Path}"/>
        </or>
    </condition>

    <target name="createpatch">
        <fail unless="git.found"
              message="You need a version of git to create the patch"/>
        <exec executable="git" output="${patch.file}">
            <arg value="diff"/>
        </exec>
    </target>

    <target name="patchpackage" depends="createpatch">
        <gzip src="${patch.file}" destfile="${patch.file}.gz"/>
    </target>
</project>
