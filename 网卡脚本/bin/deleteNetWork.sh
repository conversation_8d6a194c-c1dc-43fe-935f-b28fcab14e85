#! /bin/sh
export LANG=C
NET_PATH=/etc/network/interfaces
NET_PATH_BAK=/etc/network/interfaces_del
waNGKA=$1
isAll=$2

isVip=$(echo $waNGKA | grep ":")

let i=0;
let j=4;
let flag=false;
cp $NET_PATH /etc/network/interfaces.bak_del
while read -r line
do
	if [[ "$flag" = true ]] && [[ "$i" -le "$j" ]];then
		let i++
		continue
	else
		let i=0;
		flag=false;
	fi
	ethN=$(echo $line | grep "$waNGKA")
	if [[ -n "$ethN" ]];then
		if [[ -n "$isVip" ]] && [[ "$isAll" = "0" ]];then
			vip=$(echo $line | grep ":")
			if [[ -n "$vip" ]];then
				flag=true
			else
				flag=false
			fi
		else
			flag=true
		fi
		if [[ "$flag" = true ]];then 
			echo isAuto = $(echo $line | grep "auto")
			if [[ -n "$isAuto" ]];then
				j=4;
			else
				j=3;
			fi
		fi
	fi
	echo $flag	
	if [[ "$flag" = false ]];then
		echo $line >>$NET_PATH_BAK
	fi	
done < $NET_PATH
echo "disable $waNGKA" >>$NET_PATH_BAK
echo "iface $waNGKA inet manual" >>$NET_PATH_BAK
echo "" >>$NET_PATH_BAK
cp $NET_PATH_BAK $NET_PATH
rm -rf $NET_PATH_BAK