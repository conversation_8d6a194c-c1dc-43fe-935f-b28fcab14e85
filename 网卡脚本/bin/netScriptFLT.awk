#!/bin/sh
eth_config=$1

function repacle_value(){
    key=$1
    value=`cat $eth_config | grep -E $2 | cut -d "=" -f2 | sed 's/\"//g'`
    isAppend=$3
    if [ $isAppend == "true" ];then
        echo "\"$key\":\"$value\","
    else
        echo "\"$key\":\"$value\""
    fi
}


repacle_value ethip "ipaddr|IPADDR" true
repacle_value ethmask "netmask|NETMASK" true
repacle_value gateway "gateway|GATEWAY" true
repacle_value ethname "ethname|ETHNAME" false
