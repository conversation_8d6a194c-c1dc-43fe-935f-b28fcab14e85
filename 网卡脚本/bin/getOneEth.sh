#!/bin/bash

ETHNAME=$1

INTERFACE_CONFIG=$(cat /etc/network/interfaces)

ETHIP=$(echo "$INTERFACE_CONFIG" | grep -w "iface $ETHNAME inet static" -A3 | grep -w "address" | awk '{print $2}')
ETHMASK=$(echo "$INTERFACE_CONFIG" | grep -w "iface $ETHNAME inet static" -A3 | grep -w "netmask" | awk '{print $2}')
GATEWAY=$(echo "$INTERFACE_CONFIG" | grep -w "iface $ETHNAME inet static" -A3 | grep -w "gateway" | awk '{print $2}')

echo "\"ethip\":\"$ETHIP\","
echo "\"ethmask\":\"$ETHMASK\","
echo "\"gateway\":\"$GATEWAY\","
echo "\"ethname\":\"$ETHNAME\""