#! /bin/sh

#�����������ƣ���Ҫ�޸�
patch_name="sg-v6.1.7_cncs-r05-test"
#�ò�����������������������ƣ���������������������÷ֺŸ�������"p01;p02"�����û����������Ϊ��
relate_patch=""

#�������汾��Ӧ������汾������ͨ������Ҫ�޸�
soft_version="6.1.7_cncs"
soft_release="r05"
#�汾��Ӧ�Ĳ���ϵͳ�ںˣ�ͨ������Ҫ�޸�
kernel_version="4.19.90-20.hzsj.ky10S.aarch64"
#��������Ҫ������������ͨ������Ҫ�޸� 0�����Ͷ����� 1�����ն����� ,������߶���Ҫ��������Ϊ��
network=""

result="result:success"

fileName=/var/unimas/patch/history_info.json
patchFile=/opt/unimas/patch.version

#������ϵͳ�ں˰汾
function check_kernel(){
    ret=`uname -a | grep -E $kernel_version | wc -l`
    if [ $ret -eq 0 ] ;then
       result="result:failed-0"
       echo -ne "�������汾�뵱ǰ��װ����ϵͳ�ں˰汾[$kernel_version]�������޷���װ�ò��������飡">>result.failed
    fi
}

#�������汾
function check_soft_version(){
    ret=`cat /boot/cfcard| grep version | grep $soft_version | wc -l`
    if [ $ret -eq 0 ] ;then
       result="result:failed-0"
       echo -ne "��������汾��[$soft_version]�뵱ǰ��װ����汾�������޷���װ�ò��������飡">>result.failed
    fi
    
    if [ -f /opt/unimas/version ]
    then
        ret=`cat /opt/unimas/version | grep release | grep -E $soft_release | wc -l`
        if [ $ret -eq 0 ] ;then
           result="result:failed-0"
           echo -ne "�������汾[$soft_version]��С�汾�ţ�[$soft_release]���뵱ǰ��װ����汾�������޷���װ�ò��������飡">>result.failed
        fi
    fi
}

#��������ķ���
function check_network(){
    if [ ! -z $network ] && [ $network != "" ] ;then
        ret=`cat /boot/cfcard |grep network |awk -F = '{print $2}'`
        if [ -z $ret ] ;then
           result="result:failed-0"
           echo -ne "�÷�����Ҫ�����ò����� ">>result.failed
        else
           if [ $ret != $network ] ;then
              result="result:failed-0"
              echo -ne "�÷�����Ҫ�����ò����� ">>result.failed
           fi
        fi
    fi
}


#��鲹��������ʷ
function check_update_history(){
    if [ -f $fileName ] ;then
       chmod 755 $fileName
       ret=`grep -i $patch_name $fileName |wc -l`
       if [ $ret -gt 0 ] ;then
           result="result:failed-0"
           echo -ne "��ǰϵͳ��ͨ��ҳ���������ò�����������Ҫ�ظ�������">>result.failed
           return 
       fi
    fi
    if [ -f $patchFile ] ;then
       ret=`grep -i $patch_name $patchFile |wc -l`
       if [ $ret -gt 0 ] ;then
           result="result:failed-0"
           echo -ne "��ǰϵͳ���ֶ���̨�������ò�����������Ҫ�ظ�������">>result.failed
           return
       fi
    fi
    check_relate_patch
}

function check_relate_patch() {
    value=""
    prefix=${patch_name%-*}
    let count=0
    if [ ! -z $relate_patch ]
    then
       arr=(${relate_patch//;/ })
       for str in ${arr[@]}
       do
         if [ ! -z $str ] && [ $str != "" ]
         then
            ret=0
            if [ -f $patchFile ]
            then
               ret=`grep -i $prefix-$str $patchFile |wc -l`
            fi
            ret1=0
            if [ -f $fileName ]
            then
               ret1=`grep -i $prefix-$str $fileName |wc -l`
            fi
            if [ $ret -eq 0 ] && [ $ret1 -eq 0 ]
            then
               value=$value"[$prefix-$str] "
               let count++
            fi
         fi
       done
       if [ $count -gt 0 ]
       then
           result="result:failed-0"
           echo -ne "�����ò���ǰ������������������ $value��">>result.failed
       fi
    fi
}

check_kernel
check_soft_version
check_network
check_update_history
echo $result
