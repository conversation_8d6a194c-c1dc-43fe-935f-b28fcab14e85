#! /bin/sh
bak_suffix=".test"

result=success
network=0
productType=2

copyFile(){
    destFile=$1
    srcFile=$2
    srcMd5="src"
    destMd5="dest"
    if [ -f $srcFile ]
    then 
        srcMd5=`md5sum $srcFile|awk '{print $1}'`
        destMd5=`md5sum $destFile|awk '{print $1}'`
	if [ $srcMd5 != $destMd5 ]
        then
            mv $srcFile $srcFile$bak_suffix
            cp $destFile $srcFile
        fi
    else
        cp $destFile $srcFile
    fi
}

main(){
   echo "[INFO] [sg-v6.1.7_cncs-r05-test] patching started....."
   network=`cat /boot/cfcard |grep network |awk -F = '{print $2}'`
   productType=`cat /boot/cfcard |grep productType |awk -F = '{print $2}'`
   
   echo -e "`date` sg-v6.1.7_cncs-r05-test is a test patch!!!">>/tmp/patch.version
   
	 if [ ! -f /boot/hasinstall.sh ];then
	     echo -e "`date` [��������] sg-v6.1.7_cncs-r05-test">>/opt/unimas/patch.version
	 fi   
   echo "[INFO] [sg-v6.1.7_cncs-r05-test] patching over.....! "
   echo "result:$result"
}

main
