<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>SSL证书设置指南</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
<link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3>SSL/TLS证书配置指南</h3>
            </div>
            <div class="card-body">
                <h4>服务器证书设置</h4>
                <ol>
                    <li>将 <code>keystore.p12</code> 文件放置在 <code>src/main/resources</code> 目录下</li>
                    <li>确保 <code>application.properties</code> 文件中的密码与证书密码匹配</li>
                    <li>重启应用程序以应用更改</li>
                </ol>
                
                <h4>客户端证书导入</h4>
                <ol>
                    <li>确保您有 <code>admin.p12</code> 证书文件</li>
                    <li>在Chrome浏览器中：
                        <ul>
                            <li>打开设置 → 隐私设置和安全性 → 安全 → 管理证书</li>
                            <li>在"个人"选项卡中点击"导入"</li>
                            <li>选择您的 <code>admin.p12</code> 文件并提供密码</li>
                        </ul>
                    </li>
                    <li>在Firefox浏览器中：
                        <ul>
                            <li>打开设置 → 隐私与安全 → 证书 → 查看证书</li>
                            <li>在"您的证书"选项卡中点击"导入"</li>
                            <li>选择您的 <code>admin.p12</code> 文件并提供密码</li>
                        </ul>
                    </li>
                </ol>
                
                <h4>测试配置</h4>
                <ol>
                    <li>访问 <a href="https://localhost:8443">https://localhost:8443</a></li>
                    <li>浏览器应该会询问您选择证书</li>
                    <li>选择已导入的 <code>admin</code> 证书</li>
                    <li>如果配置正确，您应该能够成功登录系统</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <strong>注意：</strong> 如果访问时浏览器显示证书错误，您可以临时选择"继续访问"，或者在浏览器中添加例外。在生产环境中，您应该使用受信任的证书颁发机构签发的证书。
                </div>
            </div>
        </div>
    </div>
</body>
</html>