<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务审计日志</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <style>
        .table-container {
            overflow-x: auto;
        }
        
        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .status-badge {
            font-size: 0.85em;
        }
        
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div th:replace="fragments/navigation :: navbar('audit')"></div>
    
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-journal-text"></i> 服务审计日志</h2>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bi bi-funnel"></i> 筛选
                </button>
            </div>
        </div>
        
        <!-- 数据库错误提示 -->
        <div class="alert alert-danger" th:if="${databaseError}" role="alert">
            <h4 class="alert-heading"><i class="bi bi-exclamation-triangle-fill"></i> 数据库连接错误</h4>
            <p>系统无法连接到数据库，请检查数据库服务是否正常运行。</p>
            <hr>
            <p class="mb-0" th:text="${errorMessage}">错误详情</p>
            <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="window.location.reload()">
                <i class="bi bi-arrow-clockwise"></i> 重试连接
            </button>
        </div>
        
        <div class="collapse show filter-form" id="filterCollapse">
            <form th:action="@{/audit/search}" method="get">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="beginTime" class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="beginTime" name="beginTime" th:value="${beginTime}">
                    </div>
                    <div class="col-md-3">
                        <label for="endTime" class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="endTime" name="endTime" th:value="${endTime}">
                    </div>
                    <div class="col-md-3">
                        <label for="serviceId" class="form-label">服务名称</label>
                        <select class="form-select" id="serviceId" name="serviceId">
                            <option value="">所有服务</option>
                            <option th:each="service : ${services}" 
                                    th:value="${service.id}" 
                                    th:text="${service.name}"
                                    th:selected="${service.id == selectedServiceId}"></option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="clientIp" class="form-label">客户端IP</label>
                        <input type="text" class="form-control" id="clientIp" name="clientIp" 
                               th:value="${clientIp}" placeholder="不填写则查询所有IP" autocomplete="off">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <a href="/audit" class="btn btn-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>客户端IP</th>
                        <th>流量大小</th>
                        <th>时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${#lists.isEmpty(auditLogs) && (param.serviceId != null || param.clientIp != null || param.beginTime != null || param.endTime != null)}">
                        <td colspan="4" class="text-center">没有审计记录</td>
                    </tr>
                    <tr th:each="log : ${auditLogs}">
                        <td th:text="${log.serviceName}"></td>
                        <td th:text="${log.clientIp}"></td>
                        <td class="flow-size" th:data-bytes="${log.dataSize}" th:text="${log.dataSize != null ? log.dataSize : '-'}"></td>
                        <td th:text="${#temporals.format(log.beginTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页控件 -->
        <div class="pagination-container" th:if="${totalPages > 0}">
            <div>
                显示 <span th:text="${currentPage * pageSize + 1}"></span> - 
                <span th:text="${(currentPage * pageSize) + #lists.size(auditLogs)}"></span> 
                共 <span th:text="${totalItems}"></span> 条记录
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=0, size=${pageSize})}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${currentPage - 1}, size=${pageSize})}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:each="i: ${#numbers.sequence(0, totalPages - 1)}" 
                        th:if="${i >= currentPage - 2 and i <= currentPage + 2}"
                        th:classappend="${i == currentPage ? 'active' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${i}, size=${pageSize})}" th:text="${i + 1}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${currentPage + 1}, size=${pageSize})}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${totalPages - 1}, size=${pageSize})}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
    
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        // 格式化流量大小的函数
        function updateFlowShow(bytes) {
            if (bytes < 1024) {
                return bytes + "B";
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(2) + "KB";
            } else if (bytes < (1024 * 1024 * 1024)) {
                return (bytes / (1024 * 1024)).toFixed(2) + "MB";
            } else {
                return (bytes / (1024 * 1024 * 1024)).toFixed(2) + "GB";
            }
        }
        
        $(document).ready(function() {
            // 格式化流量大小显示
            $('.flow-size').each(function() {
                const bytes = $(this).attr('data-bytes');
                if (bytes && bytes !== '-') {
                    $(this).text(updateFlowShow(parseInt(bytes)));
                }
            });
            
            // 格式化日期和时间输入
            $("#beginTime, #endTime").on("change", function() {
                const val = $(this).val();
                if (val) {
                    // 确保日期时间格式符合ISO标准
                    try {
                        const date = new Date(val);
                        const isoString = date.toISOString().slice(0, 16);
                        $(this).val(isoString);
                    } catch (e) {
                        console.error("Invalid date format", e);
                    }
                }
            });
            
            // 初始化筛选表单的折叠状态
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('serviceId') || urlParams.has('clientIp') || 
                urlParams.has('beginTime') || urlParams.has('endTime')) {
                $('#filterCollapse').addClass('show');
            }
        });
    </script>
</body>
</html>