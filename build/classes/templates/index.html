<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/default}">
<head>
    <meta charset="UTF-8">
    <title>系统首页</title>
    <style>
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .health-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .health-good {
            background-color: #28a745;
        }
        .health-warning {
            background-color: #ffc107;
        }
        .health-danger {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="container">
            <h2 class="mb-4">系统状态概览</h2>
            
            <!-- 系统健康状态卡片 -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card status-card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-server me-2"></i>系统运行状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <span class="health-indicator health-good"></span>
                                <h6 class="mb-0">系统正常运行中</h6>
                            </div>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>运行时间:</span>
                                    <span th:text="${healthInfo.uptime}">3天12小时45分</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>启动时间:</span>
                                    <span th:text="${healthInfo.startTime}">2023-01-01 08:00:00</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <div class="card status-card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-memory me-2"></i>资源使用情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label>CPU负载</label>
                                <div class="progress" style="height: 20px;">
                                    <div th:with="cpuLoad=${#strings.replace(healthInfo.cpuLoad, '%', '')}"
                                         th:class="${'progress-bar ' + (#numbers.parseFloat(cpuLoad) > 80 ? 'bg-danger' : (#numbers.parseFloat(cpuLoad) > 60 ? 'bg-warning' : 'bg-success'))}"
                                         th:style="'width:' + ${healthInfo.cpuLoad} + ';'"
                                         th:text="${healthInfo.cpuLoad}">
                                        40%
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label>内存使用率</label>
                                <div class="progress" style="height: 20px;">
                                    <div th:with="memUsage=${#strings.replace(healthInfo.memoryUsage, '%', '')}"
                                         th:class="${'progress-bar ' + (#numbers.parseFloat(memUsage) > 80 ? 'bg-danger' : (#numbers.parseFloat(memUsage) > 60 ? 'bg-warning' : 'bg-success'))}"
                                         th:style="'width:' + ${healthInfo.memoryUsage} + ';'"
                                         th:text="${healthInfo.memoryUsage}">
                                        60%
                                    </div>
                                </div>
                                <small class="text-muted" th:text="${healthInfo.usedMemory + ' / ' + healthInfo.totalMemory}">
                                    512MB / 1024MB
                                </small>
                            </div>
                            <div>
                                <label>磁盘空间</label>
                                <div class="progress" style="height: 20px;">
                                    <div th:with="diskUsage=${healthInfo.diskUsagePercent}"
                                         th:class="${'progress-bar ' + (diskUsage > 80 ? 'bg-danger' : (diskUsage > 60 ? 'bg-warning' : 'bg-success'))}"
                                         th:style="'width:' + ${diskUsage} + '%'"
                                         th:text="${diskUsage + '%'}">
                                        62%
                                    </div>
                                </div>
                                <small class="text-muted" th:text="${healthInfo.freeDiskSpace + ' 可用 / ' + healthInfo.totalDiskSpace}">
                                    3 GB可用 / 80 GB
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <div class="card status-card h-100">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>管理平台配置</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="health-indicator health-warning"></span>
                                    <h6 class="mb-0">状态上报功能已禁用</h6>
                                </div>
                            </div>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>管理平台地址:</span>
                                    <span th:text="${managementConfig.ipAddress + ':' + managementConfig.port}">127.0.0.1:8000</span>
                                </li>
                                <li class="list-group-item">
                                    <span>注意: 系统已禁用状态上报功能，仅保存管理平台配置信息。</span>
                                </li>
                            </ul>
                            <div class="mt-3">
                                <a th:href="@{/management}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-cog"></i> 管理平台设置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 其他系统信息 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <tbody>
                                        <tr>
                                            <th style="width: 30%">操作系统</th>
                                            <td th:text="${healthInfo.osName + ' ' + healthInfo.osVersion}">Linux 5.10.0</td>
                                        </tr>
                                        <tr>
                                            <th>处理器数量</th>
                                            <td th:text="${healthInfo.availableProcessors}">4</td>
                                        </tr>
                                        <tr>
                                            <th>Java版本</th>
                                            <td th:text="${healthInfo.javaVersion}">17.0.2</td>
                                        </tr>
                                        <tr>
                                            <th>Java路径</th>
                                            <td th:text="${healthInfo.javaHome}">/usr/lib/jvm/java-17</td>
                                        </tr>
                                        <tr>
                                            <th>磁盘空间</th>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div th:with="diskUsage=${healthInfo.diskUsagePercent}"
                                                         th:class="${'progress-bar ' + (diskUsage > 80 ? 'bg-danger' : (diskUsage > 60 ? 'bg-warning' : 'bg-success'))}"
                                                         th:style="'width:' + ${diskUsage} + '%'"
                                                         th:text="${diskUsage + '%'}">
                                                        62%
                                                    </div>
                                                </div>
                                                <small class="text-muted" th:text="${healthInfo.totalDiskSpace + ' 总空间 (' + healthInfo.freeDiskSpace + ' 可用)'}">
                                                    80GB 总空间 (3GB 可用)
                                                </small>
                                                <small class="text-muted d-block" th:if="${healthInfo.rootPath != null}" th:text="'位置: ' + ${healthInfo.rootPath}">
                                                    位置: /var/unimas9
                                                </small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 