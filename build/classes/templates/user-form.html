<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${user.id == null ? '新增用' : '编辑用户'} + ' - 安全隔离单向输出模块'"></title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <style>
        .is-invalid-custom {
            border-color: #dc3545;
        }
        .invalid-feedback-custom {
            display: none;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        .invalid-feedback-visible {
            display: block;
        }
    </style>
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('users')"></div>

    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="bi bi-ethernet"></i> <span th:text="${user.id == null ? '新增用户' : '编辑用户'}"></span></h2>
            </div>
        </div>
        
        <div class="alert alert-danger" th:if="${error}" th:text="${error}"></div>
        
        <form th:action="@{/admin/users/save}" method="post" id="userForm">
            <input type="hidden" name="id" th:value="${user.id}" />
            <input type="hidden" name="isNew" th:value="${user.id == null}" />
            
            <!-- 设置默认值 -->
            <input type="hidden" name="role" value="USER" th:value="${user.role != null ? user.role : 'USER'}" />
            <input type="hidden" name="active" value="true" th:value="${user.active != null ? user.active : true}" />
            
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card mb-4">
                        <div class="card-header">基本信息</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       th:value="${user.username}" required
                                       th:readonly="${user.id != null}"
                                       pattern="[a-zA-Z0-9]+"
                                       placeholder="请输入用户名" autocomplete="off">
                                <div class="form-text">用户名将用于登录系统，创建后不可修改，只允许输入数字和字母</div>
                                <div class="invalid-feedback">用户名只能包含字母和数字</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dn" class="form-label">DN <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dn" name="dn" 
                                       th:value="${user.dn}" required
                                       placeholder="请输入DN" autocomplete="off">
                                <div class="form-text">必须包含"cn="或"CN="部分，如CN=admin或包含",cn="</div>
                                <div class="invalid-feedback">DN格式不正确，必须包含cn或CN部分</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <span th:text="${user.id == null ? '密码 *' : '密码 (留空表示不修改)'}"></span>
                                    <span class="text-danger" th:if="${user.id == null}">*</span>
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       th:required="${user.id == null}"
                                       placeholder="请输入密码" autocomplete="off">
                                <div class="form-text" th:if="${user.id != null}">如果不修改密码，请留空此字段</div>
                                <div class="form-text" th:if="${user.id == null}">请设置安全的密码，必须包含字母、数字和特殊字符</div>
                                <div class="invalid-feedback">密码必须同时包含字母、数字和特殊字符</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <span th:text="${user.id == null ? '确认密码 *' : '确认密码 (留空表示不修改)'}"></span>
                                    <span class="text-danger" th:if="${user.id == null}">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                                       th:required="${user.id == null}"
                                       placeholder="请再次输入密码" autocomplete="off">
                                <div class="invalid-feedback">两次输入的密码不一致</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="passwordExpiryDays" class="form-label">密码有效期 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="passwordExpiryDays" name="passwordExpiryDays"
                                           th:value="${user.passwordExpiryDays != null ? user.passwordExpiryDays : '30'}"
                                           th:readonly="${user.username != null && (user.username.equalsIgnoreCase('admin') || user.role == 'ADMIN')}"
                                           required pattern="^[1-9][0-9]{0,2}$" maxlength="3"
                                           placeholder="密码有效期">
                                    <span class="input-group-text">天</span>
                                </div>
                                <div class="form-text" th:if="${user.username != null && (user.username.equalsIgnoreCase('admin') || user.role == 'ADMIN')}">
                                    管理员用户密码有效期为永久，不可修改
                                </div>
                                <div class="form-text" th:unless="${user.username != null && (user.username.equalsIgnoreCase('admin') || user.role == 'ADMIN')}">
                                    请输入大于0的数字，最大长度为3位
                                </div>
                                <div class="invalid-feedback">请输入1-999之间的数字</div>
                            </div>
                            
                            <div class="mb-3" th:if="${user.id != null && !(user.username != null && (user.username.equalsIgnoreCase('admin') || user.role == 'ADMIN'))}">
                                <label class="form-label">密码有效期重置</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="resetPasswordExpiry" name="resetPasswordExpiry" value="true">
                                    <label class="form-check-label" for="resetPasswordExpiry">
                                        强制重置密码有效期（从今天开始重新计算）
                                    </label>
                                </div>
                                <div class="form-text" th:if="${user.passwordModifiedAt != null}">
                                    <span>当前密码设置时间: </span>
                                    <span th:text="${#temporals.format(user.passwordModifiedAt, 'yyyy-MM-dd HH:mm:ss')}">2023-01-01 08:00:00</span>
                                </div>
                                <div class="form-text" th:if="${user.passwordModifiedAt != null && user.passwordExpiryDays != null}">
                                    <div th:with="expiryDate=${user.passwordModifiedAt.plusDays(user.passwordExpiryDays)},
                                                 now=${T(java.time.LocalDateTime).now()},
                                                 isExpired=${now.isAfter(expiryDate) || now.isEqual(expiryDate)},
                                                 daysLeft=${T(java.time.temporal.ChronoUnit).DAYS.between(now, expiryDate)}">
                                        <span>密码到期时间: </span>
                                        <span th:text="${#temporals.format(expiryDate, 'yyyy-MM-dd HH:mm:ss')}">2023-02-01 08:00:00</span>
                                        <span th:if="${isExpired}" class="text-danger"> (已过期)</span>
                                        <span th:if="${!isExpired}" th:text="${' (剩余 ' + daysLeft + ' 天)'}"> (剩余25天)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12 d-flex justify-content-between">
                    <a href="/admin/users/" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="bi bi-save"></i> 保存
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 用户名验证（仅允许数字和字母）
            $('#username').on('input', function() {
                const value = $(this).val();
                const isValid = /^[a-zA-Z0-9]*$/.test(value);
                
                if (!isValid && value.length > 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // DN验证
            $('#dn').on('input', function() {
                const value = $(this).val();
                // 使用相同的更新后的正则表达式
                const dnRegex = /.*((^|,)\s*(cn|CN)\s*=).*/;
                const isValid = dnRegex.test(value);
                
                if (!isValid && value.length > 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // 密码验证（必须同时包含字母、数字和特殊字符）
            $('#password').on('input', function() {
                if ($(this).val().length > 0) {
                    validatePassword();
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // 确认密码验证
            $('#confirmPassword').on('input', function() {
                if ($(this).val().length > 0) {
                    validateConfirmPassword();
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // 密码有效期验证
            $('#passwordExpiryDays').on('input', function() {
                const value = $(this).val();
                const isValid = /^[1-9][0-9]{0,2}$/.test(value);
                
                if (!isValid && value.length > 0) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // 表单提交前验证
            $('#userForm').on('submit', function(e) {
                let isValid = true;
                
                // 验证用户名
                const username = $('#username').val();
                if (!/^[a-zA-Z0-9]+$/.test(username) && username.length > 0) {
                    $('#username').addClass('is-invalid');
                    isValid = false;
                }
                
                // 验证DN
                const dn = $('#dn').val();
                const dnRegex = /.*((^|,)\s*(cn|CN)\s*=).*/;
                if (!dnRegex.test(dn) && dn.length > 0) {
                    $('#dn').addClass('is-invalid');
                    isValid = false;
                }
                
                // 验证密码
                const password = $('#password').val();
                if (password.length > 0) {
                    if (!validatePassword()) {
                        isValid = false;
                    }
                    
                    // 验证确认密码
                    if (!validateConfirmPassword()) {
                        isValid = false;
                    }
                }
                
                // 验证密码有效期
                const passwordExpiryDays = $('#passwordExpiryDays').val();
                if (!/^[1-9][0-9]{0,2}$/.test(passwordExpiryDays)) {
                    $('#passwordExpiryDays').addClass('is-invalid');
                    isValid = false;
                }
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
            
            function validatePassword() {
                const password = $('#password').val();
                const hasLetter = /[a-zA-Z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
                const isValid = hasLetter && hasNumber && hasSpecial;
                
                if (!isValid) {
                    $('#password').addClass('is-invalid');
                    return false;
                } else {
                    $('#password').removeClass('is-invalid');
                    return true;
                }
            }
            
            function validateConfirmPassword() {
                const password = $('#password').val();
                const confirmPassword = $('#confirmPassword').val();
                const isValid = password === confirmPassword;
                
                if (!isValid) {
                    $('#confirmPassword').addClass('is-invalid');
                    return false;
                } else {
                    $('#confirmPassword').removeClass('is-invalid');
                    return true;
                }
            }
        });
    </script>
</body>
</html>