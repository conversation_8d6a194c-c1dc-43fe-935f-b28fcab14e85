<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="autocomplete" content="off">
    <title layout:title-pattern="$CONTENT_TITLE - $LAYOUT_TITLE">安全隔离单向输出模块管理系统</title>
    
    <!-- CSS 引用 -->
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
    
    <!-- 额外引用 -->
    <th:block layout:fragment="css"></th:block>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-shield-lock me-2"></i>安全隔离单向输出模块管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTop">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarTop">
                <ul class="navbar-nav ms-auto">
                    <!-- 时间显示 -->
                    <li class="nav-item">
                        <span class="nav-link">
                            <i class="bi bi-clock me-1"></i><span id="currentTime"></span>
                        </span>
                    </li>
                    
                    <!-- 用户菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span th:text="${session.username != null ? session.username : '管理员'}">管理员</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <div th:replace="fragments/navigation :: sidebar-menu('home')"></div>
                </div>
            </nav>
            
            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div layout:fragment="content">
                    <!-- 页面内容将在这里渲染 -->
                </div>
                
                <!-- 页脚 -->
                <footer class="mt-5 text-center text-muted">
                    <p>&copy; 2023-2025 安全隔离单向输出模块管理系统</p>
                </footer>
            </main>
        </div>
    </div>
    
    <!-- JavaScript 引用 -->
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script src="/js/main.js"></script>
    
    <!-- 时钟脚本 -->
    <script>
        function updateTime() {
            var now = new Date();
            var timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            document.getElementById('currentTime').textContent = timeString;
            setTimeout(updateTime, 1000);
        }
        
        $(document).ready(function() {
            updateTime();
            
            // 侧边栏折叠功能
            $('.dropdown-toggle').click(function(e) {
                e.preventDefault();
                $(this).next('.dropdown-menu').toggleClass('show');
            });
        });
    </script>
    
    <!-- 额外脚本 -->
    <th:block layout:fragment="scripts"></th:block>
</body>
</html>