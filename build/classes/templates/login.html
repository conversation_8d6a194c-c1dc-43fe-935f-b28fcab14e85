<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>登录 - 安全隔离单向输出模块</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <script th:src="@{/js/crypto-js.min.js}"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo i {
            font-size: 50px;
            color: #0d6efd;
        }
        .login-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        }
        .version-info {
            text-align: center;
            margin-top: 20px;
            color: #6c757d;
            font-size: 0.8rem;
        }
        .password-expired-alert {
            background-color: #fff3cd;
            border-color: #ffecb5;
            color: #664d03;
        }
    </style>
</head>
<body>
    <div class="container login-container">
        <div class="login-logo">
            <i class="bi bi-diagram-3"></i>
            <h2>安全隔离单向输出模块</h2>
            <p class="text-muted">请登录以继续访问</p>
        </div>
        
        <div class="login-form">
            <!-- 密码过期错误 -->
            <div th:if="${param.error != null and param.error[0] == 'password_expired'}" class="alert alert-warning password-expired-alert">
                <i class="bi bi-exclamation-triangle-fill"></i> 
                <span th:text="${session.PASSWORD_EXPIRED_MESSAGE != null ? session.PASSWORD_EXPIRED_MESSAGE : '密码已过期，请联系管理员重置密码'}">密码已过期</span>
                <div class="mt-2 small">
                    <i class="bi bi-info-circle"></i> 您的密码已过期，请联系系统管理员重置密码有效期。
                </div>
                <div class="mt-2 small">
                    <i class="bi bi-clock-history"></i> 系统时间: <span th:text="${#temporals.format(T(java.time.LocalDateTime).now(), 'yyyy-MM-dd HH:mm:ss')}">2025-04-16 11:34:55</span>
                </div>
            </div>
            
            <!-- 其他登录错误 -->
            <div th:if="${param.error != null and param.error[0] != 'password_expired'}" class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill"></i> 
                <span th:if="${session.SPRING_SECURITY_LAST_EXCEPTION != null and session.SPRING_SECURITY_LAST_EXCEPTION.message == '账户已被禁用'}">
                    账户已被禁用，请联系管理员
                </span>
                <span th:unless="${session.SPRING_SECURITY_LAST_EXCEPTION != null and session.SPRING_SECURITY_LAST_EXCEPTION.message == '账户已被禁用'}">
                    用户名或密码不正确
                </span>
            </div>
            
            <!-- 退出成功 -->
            <div th:if="${param.logout}" class="alert alert-success">
                <i class="bi bi-check-circle-fill"></i> 您已成功退出
            </div>
            
            <form th:action="@{/login}" method="post" id="loginForm" onsubmit="return encryptPassword()">
                <!-- CSRF令牌会由Thymeleaf自动添加 -->
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                        <input type="text" class="form-control" id="username" name="username" required autofocus autocomplete="off">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-key"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required autocomplete="off">
                        <input type="hidden" id="encryptedPassword" name="encryptedPassword">
                    </div>
                </div>
                
                <input type="hidden" name="successUrl" value="/goto-home" />
                
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </button>
            </form>
            
        </div>
        
        <div class="version-info">
            <p>版本: <span>1.0.0</span></p>
            <p>© 2025 安全隔离单向输出模块 版权所有</p>
        </div>
    </div>

    <script>
        // 密码加密函数
        function encryptPassword() {
            // 获取明文密码
            var plainPassword = document.getElementById('password').value;
            if (!plainPassword) return true; // 如果没有密码，不做处理
            
            try {
                // 使用MD5加密密码
                var md5Password = CryptoJS.MD5(plainPassword).toString();
                
                // 将加密后的密码填入隐藏字段
                document.getElementById('encryptedPassword').value = md5Password;
                
                // 清空原始密码字段，不提交原始密码
                document.getElementById('password').value = '';
                document.getElementById('password').disabled = true;
                
                console.log("密码已MD5加密");
                return true;
            } catch (error) {
                console.error("密码加密失败:", error);
                alert("登录过程中出现错误，请刷新页面后重试");
                return false;
            }
        }

        // 如果登录表单已提交且URL中没有错误参数，则尝试跳转
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const loginForm = document.querySelector('form');
            
            // 登录表单提交时设置标记
            if (loginForm) {
                loginForm.addEventListener('submit', function() {
                    localStorage.setItem('login_submitted', 'true');
                });
            }
            
            // 检查是否刚提交过登录表单且不含错误参数
            if (localStorage.getItem('login_submitted') === 'true' && !urlParams.has('error')) {
                console.log('检测到登录提交，尝试跳转到首页');
                localStorage.removeItem('login_submitted');
                
                // 等待一段时间后，如果页面没有变化，强制跳转到首页
                setTimeout(function() {
                    if (window.location.pathname === '/login') {
                        console.log('强制跳转到首页');
                        window.location.href = '/index';
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html> 