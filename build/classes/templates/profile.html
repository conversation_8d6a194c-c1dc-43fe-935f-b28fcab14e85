<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>个人资料</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
</head>
<body>
    <div th:replace="fragments/navigation :: navbar(activeTab='profile')"></div>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">个人资料</h5>
                    </div>
                    <div class="card-body">
                        <!-- 显示成功消息 -->
                        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <span th:text="${success}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <!-- 显示错误消息 -->
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <span th:text="${error}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <form th:action="@{/profile/update}" method="post" id="profileForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" th:value="${user.username}" readonly autocomplete="off">
                            </div>
                            <div class="mb-3">
                                <label for="role" class="form-label">用户类型</label>
                                <input type="text" class="form-control" id="role" th:value="${user.role == 'ADMIN' ? '管理员' : '普通用户'}" readonly autocomplete="off">
                            </div>
                            <div class="mb-3">
                                <label for="lastLoginIp" class="form-label">上次登录IP</label>
                                <input type="text" class="form-control" id="lastLoginIp" th:value="${lastLoginIp != null ? lastLoginIp : '未记录'}" readonly autocomplete="off">
                            </div>
                            <div class="mb-3">
                                <label for="currentLoginIp" class="form-label">本次登录IP</label>
                                <input type="text" class="form-control" id="currentLoginIp" th:value="${currentLoginIp != null ? currentLoginIp : '未记录'}" readonly autocomplete="off">
                            </div>
                            <div class="mb-3">
                                <label for="passwordExpiry" class="form-label">密码有效期</label>
                                <div th:if="${user.role == 'ADMIN' || user.username.equalsIgnoreCase('admin')}">
                                    <input type="text" class="form-control" id="passwordExpiry" value="永久" readonly autocomplete="off">
                                </div>
                                <div th:unless="${user.role == 'ADMIN' || user.username.equalsIgnoreCase('admin')}" 
                                     th:with="expiryDate=${user.passwordModifiedAt.plusDays(user.passwordExpiryDays)},
                                              now=${T(java.time.LocalDateTime).now()},
                                              isExpired=${now.isAfter(expiryDate) || now.isEqual(expiryDate)},
                                              daysLeft=${T(java.time.temporal.ChronoUnit).DAYS.between(now, expiryDate)}">
                                    <input type="text" class="form-control" id="passwordExpiry" 
                                           th:value="${isExpired ? '已过期' : ('剩余 ' + daysLeft + ' 天')}" 
                                           th:class="${isExpired ? 'form-control bg-danger text-white' : (daysLeft <= 7 ? 'form-control bg-warning' : 'form-control')}"
                                           readonly autocomplete="off">
                                </div>
                            </div>
                            <hr>
                            <h6>修改密码</h6>
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="currentPassword" name="currentPassword" autocomplete="off">
                                <input type="hidden" id="encryptedCurrentPassword" name="encryptedCurrentPassword">
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="newPassword" name="newPassword" autocomplete="off">
                                <input type="hidden" id="encryptedNewPassword" name="encryptedNewPassword">
                            </div>
                            <button type="submit" class="btn btn-primary" id="submitBtn">保存更改</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script src="https://cdn.jsdelivr.net/npm/crypto-js@4.1.1/crypto-js.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // MD5加密函数
            function md5(str) {
                return CryptoJS.MD5(str).toString();
            }
            
            // 处理表单提交
            document.getElementById('profileForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                
                // 如果两个密码都为空，显示错误消息
                if (!currentPassword && !newPassword) {
                    alert('请输入密码信息');
                    return;
                }
                
                // 如果只填写了一个密码，显示错误消息
                if ((currentPassword && !newPassword) || (!currentPassword && newPassword)) {
                    alert('请同时填写当前密码和新密码');
                    return;
                }
                
                // 加密密码
                if (currentPassword && newPassword) {
                    document.getElementById('encryptedCurrentPassword').value = md5(currentPassword);
                    document.getElementById('encryptedNewPassword').value = md5(newPassword);
                    
                    // 清空原始密码字段（可选，安全考虑）
                    document.getElementById('currentPassword').value = '';
                    document.getElementById('newPassword').value = '';
                }
                
                // 提交表单
                this.submit();
            });
        });
    </script>
</body>
</html>