<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${isNew ? '新增服务' : '编辑服务'} + ' - 安全隔离单向输出模块'"></title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link rel="stylesheet" href="/css/main.css">
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('services')"></div>

    <div class="container">
        <div class="row mb-4">
            <div class="col">
                <h2><i class="bi bi-gear"></i> <span th:text="${isNew ? '新增服务' : '编辑服务'}"></span></h2>
            </div>
        </div>
        
        <div class="alert alert-danger" th:if="${error}" th:text="${error}"></div>
        
        <form th:action="@{/services/save}" method="post" th:object="${service}">
            <input type="hidden" name="isNew" th:value="${isNew}">
            <input type="hidden" th:field="*{id}" th:if="${!isNew}">
            <input type="hidden" th:field="*{status}" th:if="${!isNew}">
            <input type="hidden" id="network" name="network" th:field="*{network}">
            <input type="hidden" id="proxymode" name="proxymode" value="udp">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">基本信息</div>
                        <div class="card-body">
                            <!-- 服务名称 -->
                            <div class="mb-3">
                                <label for="name" class="form-label">服务名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required autocomplete="off" th:readonly="${service.network != null && service.network == 1}">
                                <div class="form-text" th:if="${service.network != null && service.network == 1}">服务名称由发送端同步，不可修改</div>
                                <div class="form-text" th:if="${service.network == null || service.network == 0}">请输入服务的唯一名称</div>
                            </div>
                            
                            <!-- 是否支持组播功能 -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="multicast" name="multicast" th:checked="${service.multicast == 'true'}" value="true" th:disabled="${service.network != null && service.network == 1}">
                                    <label class="form-check-label" for="multicast">是否支持组播功能</label>
                                    <span class="ms-2 text-muted" th:if="${service.network != null && service.network == 1}">(由发送端同步，不可修改)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 传输配置 - 仅发送端显示 -->
                    <div class="card mb-4" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                        <div class="card-header">传输配置</div>
                        <div class="card-body">
                            <!-- 传输IP(发送端监听地址) -->
                            <div class="mb-3">
                                <label for="proxyIp" class="form-label">传输IP <span class="text-danger">*</span></label>
                                <select class="form-select" id="proxyIp" name="proxyIp" th:field="*{proxyIp}" required>
                                    <option value="">请选择传输IP</option>
                                    <option th:each="netInterface : ${networkInterfaces}" 
                                            th:if="${!netInterface.ipAddress.startsWith('172')}"
                                            th:value="${netInterface.ipAddress}" 
                                            th:text="${netInterface.interfaceName + ' (' + netInterface.ipAddress + ')'}"
                                            th:selected="${netInterface.ipAddress == service.proxyIp}">
                                        网卡名 (IP地址)
                                    </option>
                                </select>
                            </div>
                            
                            <!-- 传输端口(发送端监听端口) -->
                            <div class="mb-3">
                                <label for="proxyPort" class="form-label">传输端口 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="proxyPort" th:field="*{proxyPort}" 
                                       min="1" max="65535" placeholder="1-65535">
                                <div class="form-text">端口必须在1-65535范围内</div>
                            </div>
                            
                            <!-- 组播配置 - 初始隐藏 -->
                            <div id="multicastConfig" style="display: none;">
                                <div class="mb-3">
                                    <label for="multicastIp" class="form-label">客户端组播地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="multicastIp" name="multicastIp" autocomplete="off" th:value="${service.multicastIp}" >
                                </div>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-sm btn-secondary" id="sourceIpBtn">设置指定源地址</button>
                                </div>
                                <div id="sourceIpList" class="mb-3">
                                    <!-- 这里会动态添加指定源IP地址 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 接收端服务器配置 - 仅接收端显示 -->
                    <div id="serverConfig" class="card mb-4" th:style="${service.network == null || service.network == 0} ? 'display:none;' : ''">
                        <div class="card-header">服务器配置</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label id="serverIpLabel" for="serverIp" class="form-label">
                                    <span th:text="${service.multicast == 'true'} ? '服务器组播地址' : '服务器地址'"></span>
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="serverIp" name="serverIp" th:value="${service.serverIp}" required autocomplete="off">
                            </div>
                            <div class="mb-3">
                                <label id="serverPortLabel" for="serverPort" class="form-label">
                                    <span th:text="${service.multicast == 'true'} ? '服务器组播端口' : '服务器端口'"></span>
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="serverPort" name="serverPort" 
                                       th:value="${service.serverPort}" min="1" max="65535" autocomplete="off" required>
                                <div class="form-text">端口必须在1-65535范围内</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">地址映射</label>
                                <button type="button" class="btn btn-sm btn-secondary" id="addrMapBtn">配置地址映射</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <!-- 包过滤规则 - 仅发送端显示 -->
                    <div class="card mb-4" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                        <div class="card-header">包过滤规则</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-primary" id="addRuleBtn">添加规则</button>
                                <button type="button" class="btn btn-sm btn-secondary" id="updateRuleSizeBtn">更改包大小</button>
                                <button type="button" class="btn btn-sm btn-secondary" id="joinRuleBtn">加入规则</button>
                                <button type="button" class="btn btn-sm btn-danger" id="deleteRuleBtn">删除</button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm" id="ruleTable">
                                    <thead>
                                        <tr>
                                            <th>内容范围</th>
                                            <th>包内容</th>
                                            <th>包大小</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 这里会动态加载规则 -->
                                    </tbody>
                                </table>
                            </div>
                            <input type="hidden" id="rules" name="rules" th:value="${service.rules}">
                            <!-- 添加隐藏字段用于初始加载指定源地址 -->
                            <input type="hidden" id="special_value" name="special_value" th:value="${service.special_value}">
                            <input type="hidden" id="sendaddrmap" name="sendaddrmap" th:value="${service.sendaddrmap}">
                        </div>
                    </div>

                    <!-- 安全配置 -->
                    <div class="card mb-4">
                        <div class="card-header">安全配置</div>
                        <div class="card-body">
                            <!-- 是否启用抗UDP flood攻击 - 仅发送端显示 -->
                            <div class="mb-3" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                                <label for="udpFlood" class="form-label">是否启用抗UDP flood攻击</label>
                                <select class="form-select" id="udpFlood" name="udpFlood" th:field="*{udpFlood}">
                                    <option value="1">启用</option>
                                    <option value="0">关闭</option>
                                </select>
                            </div>
                            
                            <!-- 流量限制 - 仅发送端显示 -->
                            <div class="mb-3" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                                <label for="flowLevel" class="form-label">流量限制(MB)</label>
                                <input type="number" class="form-control" id="flowLevel" name="flowLevel" 
                                       min="1" max="40" value="10" th:value="${service.flowLevel}">
                                <div class="form-text">设置服务的流量限制，取值范围1-40MB</div>
                            </div>
                            
                            <!-- 审计选项 - 发送端和接收端共用 -->
                            <div class="mb-3">
                                <label for="audit" class="form-label">是否审计</label>
                                <select class="form-select" id="audit" name="audit" th:field="*{audit}">
                                    <option value="1">审计</option>
                                    <option value="0">不审计</option>
                                </select>
                            </div>
                            
                            <!-- 关键字检测 - 仅发送端显示 -->
                            <div class="mb-3" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                                <label class="form-label">关键字检测</label>
                                <div class="border p-3 rounded">
                                    <div class="form-check">
                                        <input class="form-check-input detection-option" type="checkbox" id="vehicle_check" value="vehicle_check" th:checked="${service.vehiclePlateCheck == 1}">
                                        <label class="form-check-label" for="vehicle_check">机动车牌校验</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input detection-option" type="checkbox" id="idcard_check" value="idcard_check" th:checked="${service.idCardCheck == 1}">
                                        <label class="form-check-label" for="idcard_check">身份证校验</label>
                                    </div>
                                </div>
                                <input type="hidden" id="contentKeyCheck" name="contentKeyCheck" th:field="*{contentKeyCheck}">
                                <input type="hidden" id="vehiclePlateCheck" name="vehiclePlateCheck" th:field="*{vehiclePlateCheck}">
                                <input type="hidden" id="idCardCheck" name="idCardCheck" th:field="*{idCardCheck}">
                            </div>
                            
                            <!-- 协议过滤 - 仅发送端显示 -->
                            <div class="mb-3" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                                <label class="form-label">协议过滤</label>
                                <div class="border p-3 rounded">
                                    <div class="form-check">
                                        <input class="form-check-input protocol-filter-option" type="checkbox" id="crc16_filter" value="crc16_filter" th:checked="${service.crc16FormatCheck == 1}">
                                        <label class="form-check-label" for="crc16_filter">CRC16格式过滤</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input protocol-filter-option" type="checkbox" id="asn_filter" value="asn_filter" th:checked="${service.asnFormatCheck == 1}">
                                        <label class="form-check-label" for="asn_filter">ASN格式过滤</label>
                                    </div>
                                </div>
                                <input type="hidden" id="protocolFilter" name="protocolFilter" th:field="*{protocolFilter}">
                                <input type="hidden" id="crc16FormatCheck" name="crc16FormatCheck" th:field="*{crc16FormatCheck}">
                                <input type="hidden" id="asnFormatCheck" name="asnFormatCheck" th:field="*{asnFormatCheck}">
                            </div>
                            
                            <!-- 告警后处理 - 仅发送端显示 -->
                            <div class="mb-3" th:style="${service.network != null && service.network == 1} ? 'display:none;' : ''">
                                <label for="unpassDeal" class="form-label">后处理</label>
                                <select class="form-select" id="unpassDeal" name="unpassDeal" th:field="*{unpassDeal}" >
                                    <option value="0">报警并拦截</option>
                                    <option value="1">仅报警</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12 d-flex justify-content-between">
                    <button type="button" id="back-button" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> 保存
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- 设置指定源地址对话框 -->
    <div class="modal fade" id="sourceIpModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">设置指定源地址</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-success" id="addSourceIpBtn">添加</button>
                        <button type="button" class="btn btn-sm btn-warning" id="editSourceIpBtn">修改</button>
                        <button type="button" class="btn btn-sm btn-danger" id="deleteSourceIpBtn">删除</button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm" id="sourceIpTable">
                            <thead>
                                <tr>
                                    <th>指定源IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 这里会动态加载源IP列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加规则对话框 -->
    <div class="modal fade" id="ruleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ruleModalTitle">添加规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="ruleSize" class="form-label">包大小(B) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="ruleSize" min="1" required>
                        <div class="form-text">请输入包大小</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveRuleBtn">确定</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加入规则对话框 -->
    <div class="modal fade" id="joinRuleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">加入规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="ruleSelector" class="form-label">规则名称</label>
                        <select class="form-select" id="ruleSelector">
                            <option value="">请选择规则</option>
                            <option value="规则1">规则1</option>
                            <option value="规则2">规则2</option>
                        </select>
                        <div class="form-text">选择要加入的预设规则</div>
                    </div>
                    <div class="mb-3">
                        <label for="joinContentRange" class="form-label">内容范围 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="joinContentRange" placeholder="格式如: 0x02-0x02" required>
                        <div class="form-text">该框的值使用16进制表示，内容范围里的0x00-0x01表示开始位置，包内容提取时使用</div>
                    </div>
                    <div class="mb-3">
                        <label for="joinContentValue" class="form-label">包内容 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="joinContentValue" placeholder="格式如: 0x02" required>
                    </div>
                    <div class="mt-3" id="joinRuleDescription">
                        <p class="small">这里的值使用16进制表示，内容范围使用0x00-0x01表示第一位，用0x02-0x02表示第二位，用0x03-0x03表示第三位等（按位算），如[0x61-0x63]表示(a-c)中任一字符</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmJoinRuleBtn">确定</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加地址映射对话框 -->
    <div class="modal fade" id="addrMapModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">地址映射配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-success" id="addMapBtn">添加</button>
                        <button type="button" class="btn btn-sm btn-warning" id="editMapBtn">修改</button>
                        <button type="button" class="btn btn-sm btn-danger" id="deleteMapBtn">删除</button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm" id="addrMapTable">
                            <thead>
                                <tr>
                                    <th>IP地址</th>
                                    <th>端口</th>
                                    <th>映射地址</th>
                                    <th>映射端口</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 这里会动态加载地址映射列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加地址映射项对话框 -->
    <div class="modal fade" id="mapItemModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mapItemTitle">添加地址映射</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="srcIp" class="form-label">IP地址 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="srcIp" required>
                    </div>
                    <div class="mb-3">
                        <label for="srcPort" class="form-label">端口 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="srcPort" min="1" max="65535" required>
                    </div>
                    <div class="mb-3">
                        <label for="mapIp" class="form-label">映射地址 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="mapIp" required>
                    </div>
                    <div class="mb-3">
                        <label for="mapPort" class="form-label">映射端口 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="mapPort" min="1" max="65535" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveMapItemBtn">确定</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // 规则管理相关变量和数据结构
        let rules = [];
        let selectedRuleId = null;
        let sourceIps = [];
        // 规则分组数据结构
        let ruleGroups = {}; // 存储规则组，格式为: {规则1: [{rule1}, {rule2}], 规则2: [{rule1}]}
        let nextGroupId = 1; // 下一个规则组的编号
        
        // 地址映射相关变量
        let addrMaps = [];
        let selectedMapIndex = null;
        
        /**
         * 配置文件参数说明：
         * special_value: 指定源地址（IP之间使用逗号分隔，如:***********,***********）
         * multicast: 是否支持组播功能(true支持，false不支持)
         * multicastip: 客户端组播地址IP（开头必须是224到239中的一个）
         * rules: 包过滤规则（格式：包大小,包范围:包内容；包大小,包范围:包内容； 如：100,0x02-0x02:0x02;200,0x03-0x03:0x03）
         * udpFlood: 是否启用抗UDP flood攻击(0：关闭 1:开启)
         */
        
        $(document).ready(function() {
            // 获取网络类型并设置表单字段
            const networkType = parseInt($('#network').val()) || 0;
            //const networkType = 1;
            console.log("当前网络类型: " + (networkType === 1 ? "接收端" : "发送端"));
            
            // 设置网络类型值
            $('#network').val(networkType);
            
            // 根据网络类型显示/隐藏相应表单字段
            setupFormByNetworkType(networkType);
            
            // 初始化组播配置显示/隐藏
            toggleMulticastConfig();
            
            // 初始化已有的规则和源IP
            initializeRulesAndSourceIps();
            
            // 初始化地址映射
            initializeAddrMaps();
            
            // 添加组播复选框事件监听
            $('#multicast').on('change', function() {
                toggleMulticastConfig();
                // 当组播选项改变时，根据勾选状态处理组播配置
                if ($(this).is(':checked')) {
                    // 选中组播选项时清空组播IP，但保留已有的指定源地址
                    $('#multicastIp').val('');
                    
                    // 如果是接收端，更改服务器字段的标签
                    if (networkType === 1) {
                        $('#serverIpLabel').text('服务器组播地址');
                        $('#serverPortLabel').text('服务器组播端口');
                    }
                } else {
                    // 取消组播选项时清空组播IP和指定源地址
                    $('#multicastIp').val('');
                    // 清空指定源地址
                    sourceIps = [];
                    updateSourceIpTable();
                    // 更新隐藏字段
                    $('#special_value').val('');
                    console.log('取消组播功能，已清空指定源地址');
                    
                    // 如果是接收端，恢复服务器字段的标签
                    if (networkType === 1) {
                        $('#serverIpLabel').text('服务器地址');
                        $('#serverPortLabel').text('服务器端口');
                    }
                }
            });
            
            // 验证组播IP地址格式
            $('#multicastIp').on('blur', function() {
                const ip = $(this).val().trim();
                if (ip) {
                    // 检查是否是有效的组播地址（224-239开头）
                    const firstOctet = parseInt(ip.split('.')[0]);
                    if (isNaN(firstOctet) || firstOctet < 224 || firstOctet > 239) {
                        alert('组播地址必须以224-239之间的数字开头');
                        $(this).val('');
                        $(this).focus();
                    }
                }
            });
            
            // 初始化处理检测选项
            handleDetectionCheckboxes();
            
            // 添加检测选项checkbox事件监听
            $('.detection-option').on('change', handleDetectionCheckboxes);
            
            // 添加协议过滤选项checkbox事件监听
            $('.protocol-filter-option').on('change', handleProtocolFilterCheckboxes);
            
            // 初始化处理协议过滤选项
            handleProtocolFilterCheckboxes();
            
            // 为返回按钮添加点击事件处理
            $('#back-button').on('click', function() {
                window.location.href = '/services?tab=' + networkType;
            });
            
            // 设置指定源地址按钮点击事件
            $('#sourceIpBtn').on('click', function() {
                $('#sourceIpModal').modal('show');
            });
            
            // 添加源IP按钮点击事件
            $('#addSourceIpBtn').on('click', function() {
                const ip = prompt('请输入指定源IP地址:');
                if (ip) {
                    // 检查IP是否已存在
                    if (sourceIps.includes(ip)) {
                        alert('该IP地址已存在，不能重复添加');
                        return;
                    }
                    sourceIps.push(ip);
                    updateSourceIpTable();
                }
            });
            
            // 编辑源IP按钮点击事件
            $('#editSourceIpBtn').on('click', function() {
                const index = $('.source-ip-row.selected').data('index');
                if (index !== undefined) {
                    const oldIp = sourceIps[index];
                    const newIp = prompt('请修改指定源IP地址:', oldIp);
                    if (newIp) {
                        // 检查新IP是否与其他现有IP重复（排除自身）
                        if (newIp !== oldIp && sourceIps.includes(newIp)) {
                            alert('该IP地址已存在，不能重复添加');
                            return;
                        }
                        sourceIps[index] = newIp;
                        updateSourceIpTable();
                    }
                } else {
                    alert('请先选择要编辑的IP');
                }
            });
            
            // 删除源IP按钮点击事件
            $('#deleteSourceIpBtn').on('click', function() {
                const index = $('.source-ip-row.selected').data('index');
                if (index !== undefined) {
                    sourceIps.splice(index, 1);
                    updateSourceIpTable();
                } else {
                    alert('请先选择要删除的IP');
                }
            });
            
            // 在对话框关闭时更新隐藏字段，确保值的一致性
            $('#sourceIpModal').on('hidden.bs.modal', function () {
                // 更新隐藏字段，确保在对话框关闭时保存当前值
                $('#special_value').val(sourceIps.join(','));
                console.log('源IP对话框关闭，更新隐藏字段值: ' + sourceIps.join(','));
            });
            
            // 初始化规则组编号
            updateNextGroupId();
            


            // 更新包大小按钮点击事件
            $('#updateRuleSizeBtn').on('click', function() {
                if (selectedRuleId !== null) {
                    const ruleData = rules[selectedRuleId];
                    // 提示用户输入新的包大小
                    const newSize = prompt('请输入新的包大小(B):', ruleData.size);
                    if (newSize && !isNaN(newSize)) {
                        // 更新规则大小
                        ruleData.size = newSize;
                        updateRuleTable();
                    }
                } else {
                    alert('请先选择要更改的规则');
                }
            });
            
            // 添加规则按钮点击事件
            $('#addRuleBtn').on('click', function() {
                $('#ruleModalTitle').text('添加规则');
                $('#ruleSize').val('');
                $('#contentRange').val('');
                $('#contentValue').val('');
                selectedRuleId = null;
                $('#ruleModal').modal('show');
            });
            
            // 加入规则按钮点击事件
            $('#joinRuleBtn').on('click', function() {
                $('#joinRuleModal').modal('show');
                $('#joinContentRange').val('');
                $('#joinContentValue').val('');
            });
            
            // 规则选择器改变事件
            $('#ruleSelector').on('change', function() {
                // 保留该事件以备将来扩展
            });
            
            // 确认加入规则按钮点击事件
            $('#confirmJoinRuleBtn').on('click', function() {
                const selectedRule = $('#ruleSelector').val();
                const contentRange = $('#joinContentRange').val();
                const contentValue = $('#joinContentValue').val();
                
                if (!selectedRule) {
                    alert('请选择要加入的规则');
                    return;
                }
                
                if (!contentRange) {
                    alert('内容范围不能为空');
                    return;
                }
                
                if (!contentValue) {
                    alert('包内容不能为空');
                    return;
                }
                
                // 验证格式
                if (!(/^0x[0-9A-Fa-f]+-0x[0-9A-Fa-f]+$/.test(contentRange))) {
                    alert('内容范围格式不正确，应为如: 0x02-0x02');
                    return;
                }
                
                if (!(/^0x[0-9A-Fa-f]+$/.test(contentValue))) {
                    alert('包内容格式不正确，应为如: 0x02');
                    return;
                }
                
                const groupName = selectedRule;
                
                // 创建规则组（如果不存在）
                if (!ruleGroups[groupName]) {
                    ruleGroups[groupName] = [];
                }
                
                // 获取该组的包大小
                let size = '100'; // 默认包大小
                
                // 从该规则组中查找已有的规则，获取包大小
                for (let i = 0; i < rules.length; i++) {
                    if (rules[i].group === groupName && rules[i].size) {
                        size = rules[i].size;
                        break;
                    }
                }
                
                // 添加新规则到该组
                const newRule = { 
                    size: size, 
                    range: contentRange, 
                    content: contentValue, 
                    group: groupName 
                };
                
                // 添加到规则组
                ruleGroups[groupName].push(newRule);
                // 添加到规则列表
                rules.push(newRule);
                
                updateRuleTable();
                updateNextGroupId();
                $('#joinRuleModal').modal('hide');
            });
            
            // 规则表格行点击事件 - 委托给父元素
            $('#ruleTable').on('click', 'tbody tr', function() {
                $(this).addClass('table-primary').siblings().removeClass('table-primary');
                selectedRuleId = $(this).data('id');
            });
            
            // 源IP表格行点击事件 - 委托给父元素
            $('#sourceIpTable').on('click', 'tbody tr', function() {
                $(this).addClass('table-primary').siblings().removeClass('table-primary');
                $(this).addClass('selected').siblings().removeClass('selected');
            });
            
            // 删除规则按钮点击事件
            $('#deleteRuleBtn').on('click', function() {
                if (selectedRuleId !== null) {
                    // 获取要删除的规则所属组
                    const groupToDelete = rules[selectedRuleId].group;
                    
                    // 删除选中的规则
                    rules.splice(selectedRuleId, 1);
                    
                    // 检查该组中是否还有其他规则
                    const groupStillExists = rules.some(rule => rule.group === groupToDelete);
                    
                    // 如果该组中没有其他规则，从ruleGroups中删除该组
                    if (!groupStillExists && groupToDelete in ruleGroups) {
                        delete ruleGroups[groupToDelete];
                    }
                    
                    updateRuleTable();
                    updateRuleSelector();
                    selectedRuleId = null;
                } else {
                    alert('请先选择要删除的规则');
                }
            });
            

            // 表单提交前处理
            $('form').on('submit', function(e) {
                // 添加调试信息
                console.log("表单提交处理开始");
                console.log("网络类型: " + networkType);
                console.log("组播功能: " + $('#multicast').is(':checked'));
                console.log('提交时的sendaddrmap值:', $('#sendaddrmap').val());
                // 处理组播配置
                if ($('#multicast').is(':checked')) {
                    // 移除表单中所有multicast隐藏字段，以防止重复
                    $('input[type="hidden"][name="multicast"]').remove();
                    
                    // 添加组播设置为true
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'multicast',
                        value: 'true'
                    }).appendTo('form');
                } else {
                    // 移除表单中所有multicast隐藏字段，以防止重复
                    $('input[type="hidden"][name="multicast"]').remove();
                    
                    // 添加组播设置为false
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'multicast',
                        value: 'false'
                    }).appendTo('form');
                }
                
                // 如果是接收端，添加服务器地址和端口
                if (networkType === 1) {
                    // 确保服务器配置有效
                    const serverIp = $('#serverIp').val();
                    const serverPort = $('#serverPort').val();
                    
                    if (!serverIp) {
                        e.preventDefault();
                        alert('服务器地址不能为空');
                        return false;
                    }
                    
                    if (!serverPort || isNaN(serverPort) || serverPort < 1 || serverPort > 65535) {
                        e.preventDefault();
                        alert('服务器端口必须是1-65535之间的有效数字');
                        return false;
                    }
                    
                    // 更新地址映射配置
                    updateSendaddrmap();
                }else{
                    // 处理规则
                    if (rules.length > 0) {
                        // 按规则组合并规则
                        const groupedRules = {};
                        
                        console.log("正在合并规则组数据...");
                        // 按组收集规则
                        rules.forEach(rule => {
                            if (!groupedRules[rule.group]) {
                                console.log("  创建规则组: " + rule.group + ", 包大小: " + rule.size);
                                groupedRules[rule.group] = {
                                    size: rule.size,
                                    ranges: []
                                };
                            }
                            
                            groupedRules[rule.group].ranges.push({
                                range: rule.range || '',
                                content: rule.content || ''
                            });
                            console.log("  添加到组 " + rule.group + ": 范围=" + rule.range + ", 内容=" + rule.content);
                        });
                        
                        // 构建规则字符串
                        const rulesStrArray = [];
                        Object.keys(groupedRules).forEach(group => {
                            // 格式: 包大小,包范围1:包内容1|包范围2:包内容2
                            const rangesStr = groupedRules[group].ranges.map(r => 
                                `${r.range}:${r.content}`
                            ).join('|');
                            
                            const groupRuleStr = `${groupedRules[group].size},${rangesStr}`;
                            console.log("  生成组 " + group + " 的规则字符串: " + groupRuleStr);
                            rulesStrArray.push(groupRuleStr);
                        });
                        
                        const rulesStr = rulesStrArray.join(';');
                        console.log("生成的最终规则字符串: " + rulesStr);
                        $('#rules').val(rulesStr);
                    }
                }
                
                console.log("表单提交处理结束");
            });


            
            // 验证IP地址格式
            function isValidIpAddress(ip) {
                const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/;
                if (!ipPattern.test(ip)) {
                    return false;
                }
                const parts = ip.split('.');
                return parts.every(part => {
                    const num = parseInt(part, 10);
                    return num >= 0 && num <= 255;
                });
            }

            // 保存映射项按钮点击事件
            $('#saveMapItemBtn').on('click', function() {
                const srcIp = $('#srcIp').val().trim();
                const srcPort = $('#srcPort').val();
                const mapIp = $('#mapIp').val().trim();
                const mapPort = $('#mapPort').val();

                // 验证IP地址格式
                if (!isValidIpAddress(srcIp)) {
                    alert('请输入有效的IP地址');
                    $('#srcIp').focus();
                    return;
                }

                if (!isValidIpAddress(mapIp)) {
                    alert('请输入有效的映射地址');
                    $('#mapIp').focus();
                    return;
                }

                // 验证端口号
                //端口为空不校验
                if (srcPort && (isNaN(srcPort) || srcPort < 1 || srcPort > 65535)) {
                    alert('请输入有效的端口号(1-65535)');
                    $('#srcPort').focus();
                    return;
                }

                if (mapPort && (isNaN(mapPort) || mapPort < 1 || mapPort > 65535)) {
                    alert('请输入有效的映射端口号(1-65535)');
                    $('#mapPort').focus();
                    return;
                }

                const mapItem = {
                    srcIp: srcIp,
                    srcPort: srcPort,
                    mapIp: mapIp,
                    mapPort: mapPort
                };

                if (selectedMapIndex !== null) {
                    addrMaps[selectedMapIndex] = mapItem;
                } else {
                    addrMaps.push(mapItem);
                }

                updateAddrMapTable();
                updateSendaddrmap();
                $('#mapItemModal').modal('hide');
            });

            // 更新sendaddrmap隐藏字段
            function updateSendaddrmap() {
                const mappingStr = addrMaps.map(map => 
                    `${map.srcIp}:${map.srcPort},${map.mapIp}:${map.mapPort}`
                ).join(';');
                $('#sendaddrmap').val(mappingStr);
                console.log('更新地址映射配置:', mappingStr);
            }

            // 配置地址映射按钮点击事件
            $('#addrMapBtn').on('click', function() {
                $('#addrMapModal').modal('show');
            });
            // 保存规则按钮点击事件
            $('#saveRuleBtn').on('click', function() {
                const size = $('#ruleSize').val();
                
                if (!size) {
                    alert('请填写包大小');
                    return;
                }
                
                const groupName = '规则' + nextGroupId;
                
                if (selectedRuleId !== null) {
                    // 获取当前选中规则的组名
                    const currentGroup = rules[selectedRuleId].group;
                    
                    // 更新同组中所有规则的包大小
                    rules.forEach(rule => {
                        if (rule.group === currentGroup) {
                            rule.size = size;
                        }
                    });
                } else {
                    // 添加新规则，创建新规则组
                    if (!ruleGroups[groupName]) {
                        ruleGroups[groupName] = [];
                        nextGroupId++;
                        updateRuleSelector(); // 更新规则选择器
                    }
                    
                    const newRule = { 
                        size, 
                        range: '', 
                        content: '', 
                        group: groupName 
                    };
                    
                    // 添加到规则组
                    ruleGroups[groupName].push(newRule);
                    // 添加到规则列表
                    rules.push(newRule);
                }
                
                updateRuleTable();
                updateNextGroupId();
                $('#ruleModal').modal('hide');
            });
            // 添加地址映射按钮点击事件
            $('#addMapBtn').on('click', function() {
                selectedMapIndex = null;
                $('#mapItemTitle').text('添加地址映射');
                $('#srcIp').val('');
                $('#srcPort').val('');
                $('#mapIp').val('');
                $('#mapPort').val('');
                $('#mapItemModal').modal('show');
            });

            // 编辑地址映射按钮点击事件
            $('#editMapBtn').on('click', function() {
                const selectedRow = $('#addrMapTable tbody tr.selected');
                if (selectedRow.length === 0) {
                    alert('请先选择要编辑的映射项');
                    return;
                }
                selectedMapIndex = selectedRow.data('index');
                const mapItem = addrMaps[selectedMapIndex];
                
                $('#mapItemTitle').text('修改地址映射');
                $('#srcIp').val(mapItem.srcIp);
                $('#srcPort').val(mapItem.srcPort);
                $('#mapIp').val(mapItem.mapIp);
                $('#mapPort').val(mapItem.mapPort);
                $('#mapItemModal').modal('show');
            });

            // 删除地址映射按钮点击事件
            $('#deleteMapBtn').on('click', function() {
                const selectedRow = $('#addrMapTable tbody tr.selected');
                if (selectedRow.length === 0) {
                    alert('请先选择要删除的映射项');
                    return;
                }
                if (confirm('确定要删除选中的映射项吗？')) {
                    const index = selectedRow.data('index');
                    addrMaps.splice(index, 1);
                    updateAddrMapTable();
                    updateSendaddrmap();
                }
            });

            // 表格行选择事件
            $(document).on('click', '#addrMapTable tbody tr', function() {
                $(this).siblings().removeClass('selected');
                $(this).toggleClass('selected');
            });
        });
        
        // 根据网络类型设置表单
        function setupFormByNetworkType(networkType) {
            if (networkType === 1) {
                // 接收端配置
                console.log("配置接收端表单");
                
                // 隐藏发送端专用的配置选项
                $('.sender-only').hide();
                
                // 隐藏传输配置卡片
                $('#proxyIp').closest('.card').hide();
                
                // 隐藏包过滤规则卡片
                $('#ruleTable').closest('.card').hide();
                // 服务名称不可修改
                $('#name').prop('readonly', true)
                    .addClass('bg-light');
                
                // 组播功能不可修改
                $('#multicast').prop('disabled', true)
                    .closest('.form-check');
                
                // 显示接收端专用的配置选项
                $('#serverConfig').show();
                
                // 显示地址映射配置
                $('#addrMapBtn').closest('.mb-3').show();
                
                // 根据组播状态设置服务器字段标签
                if ($('#multicast').is(':checked')) {
                    $('#serverIpLabel').text('服务器组播地址');
                    $('#serverPortLabel').text('服务器组播端口');
                } else {
                    $('#serverIpLabel').text('服务器地址');
                    $('#serverPortLabel').text('服务器端口');
                }
                
                // 隐藏发送端特有的安全配置选项
                $('#udpFlood').closest('.mb-3').hide();
                $('#flowLevel').closest('.mb-3').hide();
                $('.detection-option').closest('.mb-3').hide();
                $('.protocol-filter-option').closest('.mb-3').hide();
                $('#unpassDeal').closest('.mb-3').hide();
                
                // 只显示审计选项
                $('#audit').closest('.mb-3').show();
                $('#proxyIp').remove();
                $('#proxyPort').remove();
                $('#udpFlood').remove();
                $('#flowLevel').remove();
            } else {
                // 发送端配置
                console.log("配置发送端表单");
                
                // 显示发送端专用的配置选项
                $('.sender-only').show();
                
                // 显示传输配置卡片
                $('#proxyIp').closest('.card').show();
                
                // 显示包过滤规则卡片
                $('#ruleTable').closest('.card').show();
                
                // 服务名称可修改
                $('#name').prop('readonly', false)
                    .removeClass('bg-light')
                    .next('.form-text.text-muted').remove();
                
                // 组播功能可修改
                $('#multicast').prop('disabled', false)
                    .closest('.form-check')
                    .find('.text-muted').remove();
                
                // 隐藏接收端专用的配置选项
                $('#serverConfig').hide();
                
                // 隐藏地址映射配置
                $('#addrMapBtn').closest('.mb-3').hide();
                
                // 显示发送端特有的安全配置选项
                $('#udpFlood').closest('.mb-3').show();
                $('#flowLevel').closest('.mb-3').show();
                $('.detection-option').closest('.mb-3').show();
                $('.protocol-filter-option').closest('.mb-3').show();
                $('#unpassDeal').closest('.mb-3').show();
                
                // 显示审计选项
                $('#audit').closest('.mb-3').show();
                $('#serverIp').remove();
                $('#serverPort').remove();
            }
        }
        
        // 切换组播配置显示/隐藏
        function toggleMulticastConfig() {
            if ($('#multicast').is(':checked')) {
                $('#multicastConfig').show();
            } else {
                $('#multicastConfig').hide();
            }
        }
        
        // 更新源IP表格
        function updateSourceIpTable() {
            const tbody = $('#sourceIpTable tbody');
            tbody.empty();
            
            sourceIps.forEach((ip, index) => {
                const tr = $('<tr>').addClass('source-ip-row').data('index', index);
                tr.append($('<td>').text(ip));
                tbody.append(tr);
            });
        }
        
        // 更新规则表格
        function updateRuleTable() {
            const tbody = $('#ruleTable tbody');
            tbody.empty();
            
            // 按规则组排序
            const groupedRules = {};
            rules.forEach((rule, index) => {
                const group = rule.group || '未分组';
                if (!groupedRules[group]) {
                    groupedRules[group] = [];
                }
                groupedRules[group].push({...rule, id: index});
            });
            
            // 添加规则组和规则
            Object.keys(groupedRules).sort().forEach(group => {
                // 每个组中的规则应该有相同的size
                const groupSize = groupedRules[group][0].size;
                
                // 添加规则组标题行
                const groupRow = $('<tr>').addClass('table-secondary group-header');
                groupRow.append($('<td colspan="3">').html(`<i class="bi bi-caret-down-fill"></i> ${group}`));
                tbody.append(groupRow);
                
                // 检查该组是否有任何规则项有内容
                const hasContentRules = groupedRules[group].some(rule => rule.range || rule.content);
                
                if (hasContentRules) {
                    // 添加规则项
                    groupedRules[group].forEach(rule => {
                        // 排除空的内容范围和包内容
                        if (rule.range || rule.content) {
                            const tr = $('<tr>').data('id', rule.id).addClass('rule-item');
                            tr.append($('<td>').text(rule.range || ''));
                            tr.append($('<td>').text(rule.content || ''));
                            tr.append($('<td>').text(rule.size || ''));
                            tbody.append(tr);
                        }
                    });
                } else {
                    // 如果组内没有有内容的规则项，添加一个只显示包大小的空白行
                    const tr = $('<tr>').data('id', groupedRules[group][0].id).addClass('rule-item');
                    tr.append($('<td>').text(''));
                    tr.append($('<td>').text(''));
                    tr.append($('<td>').text(groupSize || ''));
                    tbody.append(tr);
                }
            });
            
            // 为规则组标题添加点击事件
            $('.group-header').on('click', function() {
                $(this).nextUntil('.group-header').toggle();
                const icon = $(this).find('i');
                if (icon.hasClass('bi-caret-down-fill')) {
                    icon.removeClass('bi-caret-down-fill').addClass('bi-caret-right-fill');
                } else {
                    icon.removeClass('bi-caret-right-fill').addClass('bi-caret-down-fill');
                }
            });
        }
        
        // 更新规则选择器下拉列表
        function updateRuleSelector() {
            const selector = $('#ruleSelector');
            selector.empty();
            selector.append('<option value="">请选择规则</option>');
            
            // 找出当前存在于rules数组中的规则组
            const activeGroups = new Set();
            rules.forEach(rule => {
                if (rule.group) {
                    activeGroups.add(rule.group);
                }
            });
            
            // 只添加当前活跃的规则组作为选项
            activeGroups.forEach(groupName => {
                selector.append(`<option value="${groupName}">${groupName}</option>`);
            });
        }
        
        // 更新下一个规则组编号
        function updateNextGroupId() {
            // 查找已有的最大规则编号
            let maxId = 0;
            Object.keys(ruleGroups).forEach(group => {
                if (group.startsWith('规则')) {
                    const id = parseInt(group.substring(2));
                    if (!isNaN(id) && id > maxId) {
                        maxId = id;
                    }
                }
            });
            
            // 设置下一个编号为最大编号+1
            nextGroupId = maxId + 1;
            
            // 更新规则选择器
            updateRuleSelector();
        }
        
        // 处理检测选项checkbox
        function handleDetectionCheckboxes() {
            // 获取选中的检测选项
            const vehicleChecked = $('#vehicle_check').prop('checked') ? '1' : '0';
            const idCardChecked = $('#idcard_check').prop('checked') ? '1' : '0';
            
            // 如果有任何选项被选中，设置contentKeyCheck为1，否则为0
            const contentKeyCheckValue = (vehicleChecked === '1' || idCardChecked === '1') ? '1' : '0';
            
            // 更新隐藏字段值
            $('#contentKeyCheck').val(contentKeyCheckValue);
            $('#vehiclePlateCheck').val(vehicleChecked);
            $('#idCardCheck').val(idCardChecked);
        }
        
        // 处理协议过滤选项checkbox
        function handleProtocolFilterCheckboxes() {
            // 获取选中的协议过滤选项
            const crc16Checked = $('#crc16_filter').prop('checked') ? '1' : '0';
            const asnChecked = $('#asn_filter').prop('checked') ? '1' : '0';
            
            // 更新隐藏字段值
            $('#crc16FormatCheck').val(crc16Checked);
            $('#asnFormatCheck').val(asnChecked);
            
            // 将选项组合为协议过滤字符串值
            const selectedOptions = [];
            if (crc16Checked === '1') selectedOptions.push('crc16_filter');
            if (asnChecked === '1') selectedOptions.push('asn_filter');
            $('#protocolFilter').val(selectedOptions.join(','));
        }
        
        // 初始化规则和源IP
        function initializeRulesAndSourceIps() {
            console.log("初始化规则和源IP地址");
            
            // 初始化指定源地址
            const specialValue = $('#special_value').val() || '';
            if (specialValue && specialValue.trim() !== '') {
                console.log("发现已有指定源地址: " + specialValue);
                sourceIps = specialValue.split(',').filter(ip => ip.trim() !== '');
                updateSourceIpTable();
            }
            
            // 初始化规则
            const rulesValue = $('#rules').val();
            if (rulesValue && rulesValue.trim() !== '') {
                console.log("发现已有规则: " + rulesValue);
                const ruleItems = rulesValue.split(';');
                console.log("规则项数量: " + ruleItems.length);
                
                ruleItems.forEach((ruleItem, index) => {
                    if (ruleItem.trim() === '') return;
                    
                    console.log("处理规则项 " + index + ": " + ruleItem);
                    try {
                        // 解析规则格式: 包大小,包范围:包内容 或 包大小,包范围1:包内容1|包范围2:包内容2
                        const parts = ruleItem.split(',');
                        if (parts.length < 2) {
                            console.warn("规则格式不正确，缺少逗号分隔: " + ruleItem);
                            return;
                        }
                        
                        const size = parts[0];
                        console.log("  规则包大小: " + size);
                        
                        // 创建规则组名称
                        const groupName = '规则' + (nextGroupId++);
                        console.log("  创建规则组: " + groupName);
                        
                        // 初始化当前规则组
                        if (!ruleGroups[groupName]) {
                            ruleGroups[groupName] = [];
                        }
                        
                        // 分割可能包含多个范围:内容对的部分（使用|分隔）
                        const rangeContentPairs = parts[1].split('|');
                        console.log("  发现 " + rangeContentPairs.length + " 个范围:内容对");
                        
                        // 处理每一个范围:内容对
                        rangeContentPairs.forEach(pair => {
                            const rangeContent = pair.split(':');
                            if (rangeContent.length < 2) {
                                console.warn("  范围:内容对格式不正确: " + pair);
                                return;
                            }
                            
                            const range = rangeContent[0];
                            const content = rangeContent[1];
                            console.log("    添加范围: " + range + ", 内容: " + content);
                            
                            // 创建规则对象
                            const rule = {
                                size,
                                range,
                                content,
                                group: groupName
                            };
                            
                            // 添加到规则组和规则列表
                            ruleGroups[groupName].push(rule);
                            rules.push(rule);
                        });
                    } catch (e) {
                        console.error('解析规则失败: ' + ruleItem, e);
                        console.error(e);
                    }
                });
                
                // 更新规则表格和组选择器
                updateRuleTable();
                updateRuleSelector();
            }
        }

        // 初始化地址映射
        function initializeAddrMaps() {
            const sendaddrmap = $('#sendaddrmap').val();
            if (sendaddrmap && sendaddrmap.trim() !== '') {
                try {
                    // 格式：srcIp:srcPort,mapIp:mapPort;srcIp2:srcPort2,mapIp2:mapPort2
                    const mappings = sendaddrmap.split(';');
                    mappings.forEach(mapping => {
                        const [src, dest] = mapping.split(',');
                        if (src && dest) {
                            const [srcIp, srcPort] = src.split(':');
                            const [mapIp, mapPort] = dest.split(':');
                            addrMaps.push({
                                srcIp: srcIp,
                                srcPort: srcPort,
                                mapIp: mapIp,
                                mapPort: mapPort
                            });
                        }
                    });
                    updateAddrMapTable();
                } catch (e) {
                    console.error('解析地址映射失败:', e);
                }
            }
        }

        // 更新地址映射表格
        function updateAddrMapTable() {
            const tbody = $('#addrMapTable tbody');
            tbody.empty();

            addrMaps.forEach((map, index) => {
                const tr = $('<tr>')
                    .data('index', index)
                    .append($('<td>').text(map.srcIp))
                    .append($('<td>').text(map.srcPort))
                    .append($('<td>').text(map.mapIp))
                    .append($('<td>').text(map.mapPort));
                tbody.append(tr);
            });
        }
    </script>
</body>
</html>