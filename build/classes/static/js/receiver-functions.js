/**
 * 接收端服务相关JavaScript函数
 */

// 使用全局变量而非重新定义
// const token = $("meta[name='_csrf']").attr("content");
// const header = $("meta[name='_csrf_header']").attr("content");

/**
 * 创建接收端服务
 * @param {number} id 服务ID
 */
function createReceiverService(id) {
    if (!id) {
        alert("错误: 无效的服务ID");
        return;
    }
    
    console.log("尝试创建接收端服务，ID: " + id);
    
    // 先查询服务信息，确认同步状态
    checkServiceSyncStatus(id, function() {
        // 显示确认对话框
        if (!confirm("确定要创建此接收端服务吗？将基于发送端配置创建对应的接收端服务。")) {
            return;
        }
        
        // 显示加载中消息
        receiverShowLoading("正在创建接收端服务...");
        
        // 获取CSRF令牌
        const tokenValue = $("meta[name='_csrf']").attr("content");
        const headerName = $("meta[name='_csrf_header']").attr("content");
        
        // 发送AJAX请求
        $.ajax({
            url: `/services/create-receiver/${id}`,
            type: 'POST',
            beforeSend: function(xhr) {
                xhr.setRequestHeader(headerName, tokenValue);
            },
            success: function(response) {
                receiverHideLoading();
                
                console.log("创建接收端服务响应:", response);
                
                if (response.success) {
                    // 先显示成功消息
                    receiverShowMessage('success', '创建成功', '接收端服务创建成功，状态已更新为"未配置服务"');
                    
                    // 尝试直接更新页面上的状态显示，而不是立即刷新
                    try {
                        // 找到当前服务的行
                        const serviceRow = $('tr[data-service-id="' + id + '"]');
                        
                        if (serviceRow.length > 0) {
                            // 更新同步状态单元格
                            const syncStatusCell = serviceRow.find('td:eq(2)');
                            if (syncStatusCell.length > 0) {
                                syncStatusCell.html('<span class="badge bg-warning">未配置服务</span>');
                                console.log('已直接更新页面上的服务状态显示');
                            }
                            
                            // 延迟刷新页面
                            setTimeout(function() {
                                console.log('强制刷新页面，禁用缓存...');
                                // 添加时间戳防止缓存
                                window.location.href = window.location.pathname + '?tab=1&t=' + new Date().getTime();
                            }, 1500);
                        } else {
                            // 如果找不到服务行，立即刷新
                            console.log('未找到服务行，强制刷新页面...');
                            window.location.href = window.location.pathname + '?tab=1&t=' + new Date().getTime();
                        }
                    } catch (e) {
                        console.error('直接更新页面状态失败:', e);
                        // 发生错误时，立即刷新页面
                        console.log('出错，强制刷新页面...');
                        window.location.href = window.location.pathname + '?tab=1&t=' + new Date().getTime();
                    }
                } else {
                    console.error("创建失败:", response.message);
                    receiverShowMessage('error', '创建失败', '创建接收端服务失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                receiverHideLoading();
                console.error("创建接收端服务错误:", xhr.responseText);
                receiverShowMessage('error', '创建失败', '创建接收端服务时发生错误: ' + error);
            }
        });
    });
}

/**
 * 查询服务同步状态
 * @param {number} id 服务ID
 * @param {Function} callback 回调函数
 */
function checkServiceSyncStatus(id, callback) {
    // 获取CSRF令牌
    const tokenValue = $("meta[name='_csrf']").attr("content");
    const headerName = $("meta[name='_csrf_header']").attr("content");
    
    // 发送AJAX请求获取服务详情
    $.ajax({
        url: `/services/details/${id}`,
        type: 'GET',
        beforeSend: function(xhr) {
            xhr.setRequestHeader(headerName, tokenValue);
        },
        success: function(response) {
            console.log("服务详情:", response);
            if (response && response.service) {
                console.log("服务ID: " + response.service.id);
                console.log("服务名称: " + response.service.name);
                console.log("服务类型(network): " + response.service.network);
                console.log("同步状态: " + response.service.syncStatus);
            }
            if (callback) callback();
        },
        error: function(xhr, status, error) {
            console.error("获取服务详情错误:", error);
            if (callback) callback();
        }
    });
}

/**
 * 显示加载中消息 (接收端服务专用)
 * @param {string} message 消息内容
 */
function receiverShowLoading(message) {
    if ($('#receiver-loading-overlay').length === 0) {
        $('body').append(
            '<div id="receiver-loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">' +
            '<div class="card p-3"><div class="spinner-border text-primary me-2" role="status"></div><span id="receiver-loading-message">' + message + '</span></div>' +
            '</div>'
        );
    } else {
        $('#receiver-loading-message').text(message);
        $('#receiver-loading-overlay').show();
    }
}

/**
 * 隐藏加载中消息 (接收端服务专用)
 */
function receiverHideLoading() {
    $('#receiver-loading-overlay').hide();
}

/**
 * 显示消息通知 (接收端服务专用)
 * @param {string} type 消息类型 (success, error, warning, info)
 * @param {string} title 标题
 * @param {string} message 消息内容
 */
function receiverShowMessage(type, title, message) {
    var alertClass = 'alert-info';
    if (type === 'success') alertClass = 'alert-success';
    if (type === 'error') alertClass = 'alert-danger';
    if (type === 'warning') alertClass = 'alert-warning';
    
    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<strong>' + title + ':</strong> ' + message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>';
    
    $('#message-container').html(alertHtml);
    
    // 5秒后自动关闭
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

/**
 * 先同步配置再创建接收端服务
 * @param {number} id 服务ID
 */
function syncAndCreateReceiverService(id) {
    if (!id) {
        alert("错误: 无效的服务ID");
        return;
    }
    
    console.log("尝试先同步配置再创建接收端服务，ID: " + id);
    
    // 显示确认对话框
    if (!confirm("确定要创建此接收端服务吗？将先同步配置然后创建服务。")) {
        return;
    }
    
    // 显示加载中消息
    receiverShowLoading("正在同步配置...");
    
    // 获取CSRF令牌
    const tokenValue = $("meta[name='_csrf']").attr("content");
    const headerName = $("meta[name='_csrf_header']").attr("content");
    
    // 先执行同步
    $.ajax({
        url: `/services/sync-from-srcapps`,
        type: 'POST',
        beforeSend: function(xhr) {
            xhr.setRequestHeader(headerName, tokenValue);
        },
        success: function(syncResponse) {
            console.log("同步配置响应:", syncResponse);
            
            // 短暂延迟以确保同步处理完成
            setTimeout(function() {
                // 执行同步后，先查询服务状态
                receiverShowLoading("正在查询服务状态...");
                $.ajax({
                    url: `/services/details/${id}`,
                    type: 'GET',
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader(headerName, tokenValue);
                    },
                    success: function(detailsResponse) {
                        console.log("服务详情响应:", detailsResponse);
                        
                        if (detailsResponse && detailsResponse.service) {
                            const service = detailsResponse.service;
                            console.log("服务网络类型:", service.network);
                            console.log("服务同步状态:", service.syncStatus);
                            
                            // 现在尝试创建服务
                            createReceiver();
                        } else {
                            receiverHideLoading();
                            receiverShowMessage('error', '查询失败', '无法获取服务详情');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("获取服务详情失败:", xhr.responseText);
                        // 尽管出错，仍然尝试创建
                        createReceiver();
                    }
                });
            }, 800);
        },
        error: function(xhr, status, error) {
            receiverHideLoading();
            console.error("同步配置错误:", xhr.responseText);
            receiverShowMessage('error', '同步失败', '同步配置时发生错误: ' + error);
        }
    });
    
    // 创建接收端服务的核心函数，支持重试
    function createReceiver(retryCount = 0) {
        const maxRetries = 2; // 最多重试2次
        
        receiverShowLoading("正在创建接收端服务...");
        
        // 创建服务的AJAX请求
        $.ajax({
            url: `/services/create-receiver/${id}`,
            type: 'POST',
            beforeSend: function(xhr) {
                xhr.setRequestHeader(headerName, tokenValue);
            },
            success: function(response) {
                receiverHideLoading();
                console.log("创建接收端服务响应:", response);
                
                if (response.success) {
                    receiverShowMessage('success', '创建成功', '接收端服务创建成功，状态已更新为"未配置服务"');
                    // 强制刷新页面以显示更新后的服务列表
                    console.log('创建成功，强制刷新页面...');
                    window.location.href = window.location.pathname + '?tab=1&t=' + new Date().getTime();
                } else {
                    console.error("创建失败:", response.message);
                    
                    // 如果是因为网络类型或同步状态错误，且还有重试次数，则重试
                    if (retryCount < maxRetries && 
                        (response.message.includes("网络类型") || 
                         response.message.includes("同步状态"))) {
                        
                        console.log(`创建失败，将重试 (${retryCount + 1}/${maxRetries})...`);
                        receiverShowMessage('warning', '正在重试', `创建失败，正在重试 (${retryCount + 1}/${maxRetries})...`);
                        
                        // 延迟1秒后重试
                        setTimeout(function() {
                            createReceiver(retryCount + 1);
                        }, 1000);
                    } else {
                        receiverShowMessage('error', '创建失败', '创建接收端服务失败: ' + response.message);
                    }
                }
            },
            error: function(xhr, status, error) {
                receiverHideLoading();
                console.error("创建接收端服务错误:", xhr.responseText);
                
                if (retryCount < maxRetries) {
                    console.log(`创建出错，将重试 (${retryCount + 1}/${maxRetries})...`);
                    receiverShowMessage('warning', '正在重试', `创建出错，正在重试 (${retryCount + 1}/${maxRetries})...`);
                    
                    // 延迟1秒后重试
                    setTimeout(function() {
                        createReceiver(retryCount + 1);
                    }, 1000);
                } else {
                    receiverShowMessage('error', '创建失败', '创建接收端服务时发生错误: ' + error);
                }
            }
        });
    }
} 