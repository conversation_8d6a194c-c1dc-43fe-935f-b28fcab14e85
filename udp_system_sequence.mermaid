sequenceDiagram
    participant C as Client
    participant RC as ResourceController
    participant RS as ResourceService
    participant PC as ProxyController
    participant PS as ProxyService
    participant C<PERSON> as ConfigManager
    participant SD as ServiceDeployer
    participant DB as Database

    %% Resource Creation Flow
    C->>RC: POST /api/resources
    RC->>RS: createResource(request)
    RS->>DB: validateResourceName()
    DB-->>RS: name validation result
    RS->>DB: save resource
    DB-->>RS: saved resource
    RS-->>RC: ResourceDTO
    RC-->>C: 201 Created

    %% Resource Update Flow
    C->>RC: PUT /api/resources/{id}
    RC->>RS: updateResource(id, request)
    RS->>DB: findById(id)
    DB-->>RS: resource
    RS->>DB: save updated resource
    DB-->>RS: saved resource
    RS-->>RC: ResourceDTO
    RC-->>C: 200 OK

    %% Proxy Service Configuration
    C->>PC: POST /api/proxy-services
    PC->>PS: createService(request)
    PS->>RS: getResource(resourceId)
    RS->>DB: find resource
    DB-->>RS: resource
    RS-->>PS: resource
    PS->>DB: save service
    DB-->>PS: saved service
    PS->>CM: saveConfiguration(service)
    CM-->>PS: config saved
    PS-->>PC: ServiceDTO
    PC-->>C: 201 Created

    %% Service Deployment
    C->>PC: POST /api/proxy-services/{id}/deploy
    PC->>PS: deployService(id)
    PS->>DB: findById(id)
    DB-->>PS: service
    PS->>CM: loadConfiguration(id)
    CM-->>PS: configuration
    PS->>SD: deploy(service)
    activate SD
    SD->>SD: prepareDeployment
    SD->>SD: validateDeployment
    SD-->>PS: deployment result
    deactivate SD
    PS->>DB: updateStatus(DEPLOYED)
    DB-->>PS: updated service
    PS-->>PC: DeploymentResult
    PC-->>C: 200 OK

    %% Service Start
    C->>PC: POST /api/proxy-services/{id}/start
    PC->>PS: startService(id)
    PS->>SD: start(id)
    activate SD
    SD->>SD: checkDeployment
    SD->>SD: startProcess
    SD-->>PS: started
    deactivate SD
    PS->>DB: updateStatus(RUNNING)
    DB-->>PS: updated service
    PS-->>PC: ServiceStatus
    PC-->>C: 200 OK

    %% Status Monitoring
    C->>PC: GET /api/proxy-services/{id}/status
    PC->>PS: getServiceStatus(id)
    PS->>SD: checkStatus(id)
    SD-->>PS: current status
    PS->>DB: updateStatus(status)
    DB-->>PS: updated service
    PS-->>PC: ServiceStatus
    PC-->>C: 200 OK