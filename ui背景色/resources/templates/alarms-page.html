<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务报警</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
    <style>
        .table-container {
            overflow-x: auto;
        }
        
        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div th:replace="fragments/navigation :: navbar('alarms')"></div>
    
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-exclamation-triangle"></i> 服务报警</h2>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bi bi-funnel"></i> 筛选
                </button>
            </div>
        </div>
        
        <div class="collapse show filter-form" id="filterCollapse">
            <form th:action="@{/alarms/search}" method="get">
                <div class="row g-3">
                    <div class="col-md-2">
                        <label for="startTime" class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="startTime" name="startTime" th:value="${startTime}">
                    </div>
                    <div class="col-md-2">
                        <label for="endTime" class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="endTime" name="endTime" th:value="${endTime}">
                    </div>
                    <div class="col-md-3">
                        <label for="appname" class="form-label">服务</label>
                        <select class="form-select" id="appname" name="appname">
                            <option value="">所有服务</option>
                            <option th:each="service : ${services}" 
                                    th:value="${service.id}" 
                                    th:text="${service.name}"
                                    th:selected="${service.id == selectedAppName}"></option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="clientIp" class="form-label">客户端IP</label>
                        <input type="text" class="form-control" id="clientIp" name="clientIp" th:value="${selectedClientIp}" autocomplete="off">
                    </div>
                    <div class="col-md-2">
                        <label for="clientPort" class="form-label">端口号</label>
                        <input type="number" class="form-control" id="clientPort" name="clientPort" th:value="${selectedClientPort}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <a href="/alarms" class="btn btn-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>报警时间</th>
                        <th>报警信息</th>
                        <th>客户端IP</th>
                        <th>端口号</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${#lists.isEmpty(alarms) && (param.appname != null || param.clientIp != null || param.clientPort != null || param.startTime != null || param.endTime != null)}">
                        <td colspan="6" class="text-center">没有报警记录</td>
                    </tr>
                    <tr th:each="alarm : ${alarms}">
                        <td th:text="${alarm.serviceName}"></td>
                        <td th:text="${#temporals.format(alarm.alarmTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                        <td th:text="${alarm.alarmInfo}"></td>
                        <td th:text="${alarm.clientIp != null ? alarm.clientIp : '-'}"></td>
                        <td th:text="${alarm.clientPort != null ? alarm.clientPort : '-'}"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页控制 -->
        <div class="pagination-container" th:if="${totalPages > 0}">
            <div>
                显示 <span th:text="${currentPage * pageSize + 1}"></span> - 
                <span th:text="${(currentPage * pageSize) + #lists.size(alarms)}"></span> 
                共 <span th:text="${totalItems}"></span> 条记录
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=0, size=${pageSize})}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${currentPage - 1}, size=${pageSize})}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:each="i: ${#numbers.sequence(0, totalPages - 1)}" 
                        th:if="${i >= currentPage - 2 and i <= currentPage + 2}"
                        th:classappend="${i == currentPage ? 'active' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${i}, size=${pageSize})}" th:text="${i + 1}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${currentPage + 1}, size=${pageSize})}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${totalPages - 1}, size=${pageSize})}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
    
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 格式化日期和时间输入
            $("#startTime, #endTime").on("change", function() {
                const val = $(this).val();
                if (val) {
                    // 确保日期时间格式符合ISO标准
                    try {
                        const date = new Date(val);
                        const isoString = date.toISOString().slice(0, 16);
                        $(this).val(isoString);
                    } catch (e) {
                        console.error("Invalid date format", e);
                    }
                }
            });
            
            // 初始化筛选表单的折叠状态
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('appname') || urlParams.has('clientIp') || 
                urlParams.has('clientPort') || urlParams.has('startTime') || 
                urlParams.has('endTime')) {
                $('#filterCollapse').addClass('show');
            }

            // 添加报警导出功能
            $('#exportAlarmsBtn').on('click', function() {
                const appname = $('#appname').val();
                const clientIp = $('#clientIp').val();
                const clientPort = $('#clientPort').val();
                const startTime = $('#startTime').val();
                const endTime = $('#endTime').val();
                
                let url = '/alarms/export?';
                if (appname) url += `appname=${appname}&`;
                if (clientIp) url += `clientIp=${encodeURIComponent(clientIp)}&`;
                if (clientPort) url += `clientPort=${clientPort}&`;
                if (startTime) url += `startTime=${encodeURIComponent(startTime)}&`;
                if (endTime) url += `endTime=${encodeURIComponent(endTime)}&`;
                
                window.location.href = url;
            });
        });
    </script>
</body>
</html>