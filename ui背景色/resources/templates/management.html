<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/default}">
    <head>
        <meta charset="UTF-8">
        <title>管理平台设置 - 安全隔离单向输出模块</title>
        <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
        <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
        <link th:href="@{/css/main.css}" rel="stylesheet">
    </head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('management')"></div>
    <div layout:fragment="content">
        <div class="container">
            <!-- 页面标题 -->
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h2 class="h3 mb-0 text-gray-800">
                    <i class="bi bi-gear me-2"></i>管理平台设置
                </h2>
            </div>

            <!-- 错误/成功信息 -->
            <div class="alert alert-danger" th:if="${error}" th:text="${error}"></div>
            <div class="alert alert-success" th:if="${success}" th:text="${success}"></div>

            <!-- 管理平台设置表单 -->
            <form th:action="@{/management/save}" method="post">
                <!-- 报警配置 -->
                <div class="card form-section mb-4">
                    <div class="card-header">
                        <h6 class="m-0">管理平台配置</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="alarmEnabled" name="alarmEnabled"
                                       th:checked="${alarmEnabled}">
                                <label class="form-check-label" for="alarmEnabled">启用</label>
                            </div>
                            <div class="form-text">启用后，系统将上报状态信息到指定地址</div>
                        </div>

                        <div class="row alarm-settings">
                            <div class="col-md-6 mb-3">
                                <label for="alarmIp" class="form-label">管理平台IP</label>
                                <input type="text" class="form-control" id="alarmIp" name="alarmIp"
                                       th:value="${alarmIp}"
                                       placeholder="例如: ************" required autocomplete="off">
                                <div class="form-text">设置上报管理平台IP地址</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="alarmPort" class="form-label">端口</label>
                                <input type="number" class="form-control" id="alarmPort" name="alarmPort"
                                       th:value="${alarmPort}"
                                       min="1" max="65535" required>
                                <div class="form-text">设置上报管理平台的端口号 (1-65535)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 引擎端口配置 -->
                <div class="card form-section mb-4">
                    <div class="card-header">
                        <h6 class="m-0">引擎端口配置</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="enginePort" class="form-label">引擎端口</label>
                                <input type="number" class="form-control" id="enginePort" name="enginePort"
                                       th:value="${enginePort}"
                                       min="1" max="65535" required>
                                <div class="form-text">设置引擎端口号 (1-65535)，变更后需要重启进程</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="authPort" class="form-label">身份认证端口</label>
                                <input type="number" class="form-control" id="authPort" name="authPort"
                                       th:value="${authPort}"
                                       min="1" max="65535" required>
                                <div class="form-text">设置身份认证端口号 (1-65535)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 按钮区域 -->
                <div class="row mb-4">
                    <div class="col-12 d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-1"></i> 保存设置
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
        <script th:src="@{/js/jquery.min.js}"></script>
        <!-- 添加JavaScript -->
        <script layout:fragment="scripts">
            // 处理报警上报开关
            document.getElementById('alarmEnabled').addEventListener('change', function() {
                const ipInput = document.getElementById('alarmIp');
                const portInput = document.getElementById('alarmPort');

                if (!this.checked) {
                    // 如果未启用，清空IP和端口
                    ipInput.value = '';
                    portInput.value = '';
                    ipInput.disabled = true;
                    portInput.disabled = true;
                } else {
                    // 如果启用，启用输入框
                    ipInput.disabled = false;
                    portInput.disabled = false;
                }
            });

            // 页面加载时初始化报警配置输入框状态
            window.addEventListener('DOMContentLoaded', function() {
                const enabled = document.getElementById('alarmEnabled');
                const ipInput = document.getElementById('alarmIp');
                const portInput = document.getElementById('alarmPort');

                if (!enabled.checked) {
                    ipInput.disabled = true;
                    portInput.disabled = true;
                }
            });
        </script>
    </div>
</body>
</html>