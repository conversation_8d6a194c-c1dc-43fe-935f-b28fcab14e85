<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/default}">
<head>
    <meta charset="UTF-8">
    <title>用户管理</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
    <style>
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        .action-buttons {
            white-space: nowrap;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }
        .user-card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.25rem;
            margin-bottom: 1.5rem;
        }
        .user-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 0.75rem 1rem;
        }
        .user-table th, .user-table td {
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('users')"></div>
    
    <div layout:fragment="content">
        <div class="container">
            <!-- 页面标题和添加按钮 -->
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">用户管理</h1>
                <a href="/admin/users/add" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                    <i class="bi bi-plus-lg me-1"></i>添加用户
                </a>
            </div>
            
            <!-- 成功/错误提示 -->
            <div th:if="${success != null}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                <span th:text="${success}">操作成功!</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            
            <div th:if="${error != null}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <span th:text="${error}">操作失败!</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            
            <!-- 用户列表卡片 -->
            <div class="card user-card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">用户列表</h6>
                    <a href="/admin/users/add" class="d-block d-sm-none btn btn-sm btn-primary">
                        <i class="bi bi-plus-lg"></i>
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered user-table" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>用户名</th>
                                    <th style="width: 100px">角色</th>
                                    <th style="width: 80px">状态</th>
                                    <th>密码有效期</th>
                                    <th style="width: 120px">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="user : ${users}">
                                    <td th:text="${user.username}">admin</td>
                                    <td>
                                        <span th:if="${user.role == 'ADMIN'}" class="badge bg-danger status-badge">管理员</span>
                                        <span th:if="${user.role == 'USER'}" class="badge bg-primary status-badge">普通用户</span>
                                    </td>
                                    <td>
                                        <span th:if="${user.active}" class="badge bg-success status-badge">启用</span>
                                        <span th:unless="${user.active}" class="badge bg-secondary status-badge">禁用</span>
                                    </td>
                                    <td>
                                        <div th:if="${user.passwordModifiedAt != null && user.passwordExpiryDays != null}">
                                            <div th:if="${user.username.equalsIgnoreCase('admin') || user.role == 'ADMIN'}">
                                                <span class="badge bg-success status-badge">永久有效</span>
                                            </div>
                                            <div th:unless="${user.username.equalsIgnoreCase('admin') || user.role == 'ADMIN'}"
                                                 th:with="expiryDate=${user.passwordModifiedAt.plusDays(user.passwordExpiryDays)},
                                                         now=${T(java.time.LocalDateTime).now()},
                                                         isExpired=${now.isAfter(expiryDate) || now.isEqual(expiryDate)},
                                                         daysLeft=${T(java.time.temporal.ChronoUnit).DAYS.between(now, expiryDate)}">
                                                
                                                <span th:if="${isExpired}" class="badge bg-danger status-badge">已过期</span>
                                                <span th:if="${!isExpired && daysLeft <= 7}" class="badge bg-warning status-badge" 
                                                      th:text="${'剩余 ' + daysLeft + ' 天'}">剩余 5 天</span>
                                                <span th:if="${!isExpired && daysLeft > 7}" class="badge bg-success status-badge" 
                                                      th:text="${'剩余 ' + daysLeft + ' 天'}">剩余 25 天</span>
                                            </div>
                                        </div>
                                        <span th:unless="${user.passwordModifiedAt != null && user.passwordExpiryDays != null}">
                                            未设置
                                        </span>
                                    </td>
                                    <td class="action-buttons">
                                        <a th:href="@{/admin/users/edit/{id}(id=${user.id})}" class="btn btn-outline-primary btn-action" title="编辑">
                                            <i class="bi bi-pencil-square"></i>
                                        </a>
                                        
                                        <form th:action="@{/admin/users/delete/{id}(id=${user.id})}" method="post" class="d-inline-block">
                                            <button type="submit" class="btn btn-outline-danger btn-action" title="删除"
                                                    th:data-confirm-message="'确定要删除用户 ' + ${user.username} + ' 吗?'"
                                                    onclick="return confirm(this.getAttribute('data-confirm-message'))">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <tr th:if="${users.empty}">
                                    <td colspan="6" class="text-center py-4">
                                        <i class="bi bi-info-circle me-1"></i>暂无用户数据
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
</body>
</html>