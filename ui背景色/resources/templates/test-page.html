<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>测试页面</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
</head>
<body>
    <!-- 使用通用导航栏片段进行测试 -->
    <div th:replace="fragments/navigation :: navbar('test')"></div>

    <div class="container mt-5">
        <div class="alert alert-success">
            <h2>测试页面成功加载！</h2>
            <p>如果您能看到此页面，表示应用程序正常运行。</p>
            <p>当前时间：<span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}"></span></p>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>菜单功能测试</h5>
            </div>
            <div class="card-body">
                <p>请测试以下功能：</p>
                <ul>
                    <li>顶部导航栏的下拉菜单是否可以正常点击和显示</li>
                    <li>用户菜单（右上角）是否可以正常点击</li>
                    <li>各个导航链接是否可以正常跳转</li>
                </ul>

                <!-- 测试下拉菜单 -->
                <div class="dropdown mt-3">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="testDropdown"
                            data-bs-toggle="dropdown" aria-expanded="false"
                            onclick="toggleDropdown(this, event)">
                        测试下拉菜单
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="testDropdown">
                        <li><a class="dropdown-item" href="#">选项 1</a></li>
                        <li><a class="dropdown-item" href="#">选项 2</a></li>
                        <li><a class="dropdown-item" href="#">选项 3</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="mt-3">
            <a href="/login" class="btn btn-primary">返回登录页</a>
            <a href="/" class="btn btn-secondary">返回首页</a>
        </div>
    </div>

    <!-- JavaScript 引用 -->
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script src="/js/main.js"></script>
</body>
</html>