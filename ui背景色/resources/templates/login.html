<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>登录 - 安全隔离单向输出模块</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <script th:src="@{/js/crypto-js.min.js}"></script>
    <style>
        /* 登录页面背景渲染 */
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            position: relative;
            overflow-x: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 动态背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 200, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(100, 180, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(150, 220, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        /* 背景浮动动画 */
        @keyframes backgroundFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 1;
            }
            50% {
                transform: translateY(-20px) rotate(1deg);
                opacity: 0.8;
            }
        }

        /* 添加微妙的网格背景 */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: -1;
            opacity: 0.3;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
            margin: 0 auto;
            padding: 2rem;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-logo {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .login-logo i {
            font-size: 4rem;
            background: linear-gradient(135deg, #42a5f5, #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: iconGlow 3s ease-in-out infinite;
            filter: drop-shadow(0 4px 8px rgba(66, 165, 245, 0.3));
        }

        @keyframes iconGlow {
            0%, 100% {
                filter: drop-shadow(0 4px 8px rgba(66, 165, 245, 0.3));
            }
            50% {
                filter: drop-shadow(0 8px 16px rgba(66, 165, 245, 0.5));
            }
        }

        .login-logo h2 {
            color: #1976d2;
            font-weight: 600;
            margin-top: 1rem;
            text-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
        }

        .login-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 32px rgba(66, 165, 245, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .login-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #42a5f5, #64b5f6, #90caf9, #bbdefb);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid rgba(66, 165, 245, 0.2);
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            padding: 0.75rem 1rem;
        }

        .form-control:focus {
            border-color: #42a5f5;
            box-shadow: 0 0 0 0.2rem rgba(66, 165, 245, 0.25);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        .input-group-text {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid rgba(66, 165, 245, 0.2);
            border-right: none;
            border-radius: 12px 0 0 12px;
            color: #1976d2;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2196f3, #42a5f5);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1976d2, #2196f3);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }

        .alert {
            border-radius: 12px;
            border: none;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .alert-warning {
            background: rgba(255, 152, 0, 0.1);
            color: #ef6c00;
            border-left: 4px solid #ff9800;
        }

        .alert-danger {
            background: rgba(244, 67, 54, 0.1);
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .version-info {
            text-align: center;
            margin-top: 2rem;
            color: rgba(25, 118, 210, 0.7);
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 576px) {
            .login-container {
                padding: 1rem;
            }

            .login-form {
                padding: 1.5rem;
            }

            .login-logo i {
                font-size: 3rem;
            }
        }
        .password-expired-alert {
            background-color: #fff3cd;
            border-color: #ffecb5;
            color: #664d03;
        }
    </style>
</head>
<body>
    <div class="container login-container">
        <div class="login-logo">
            <i class="bi bi-diagram-3"></i>
            <h2>安全隔离单向输出模块</h2>
            <p class="text-muted">请登录以继续访问</p>
        </div>

        <div class="login-form">
            <!-- 密码过期错误 -->
            <div th:if="${param.error != null and param.error[0] == 'password_expired'}" class="alert alert-warning password-expired-alert">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <span th:text="${session.PASSWORD_EXPIRED_MESSAGE != null ? session.PASSWORD_EXPIRED_MESSAGE : '密码已过期，请联系管理员重置密码'}">密码已过期</span>
                <div class="mt-2 small">
                    <i class="bi bi-info-circle"></i> 您的密码已过期，请联系系统管理员重置密码有效期。
                </div>
                <div class="mt-2 small">
                    <i class="bi bi-clock-history"></i> 系统时间: <span th:text="${#temporals.format(T(java.time.LocalDateTime).now(), 'yyyy-MM-dd HH:mm:ss')}">2025-04-16 11:34:55</span>
                </div>
            </div>

            <!-- 其他登录错误 -->
            <div th:if="${param.error != null and param.error[0] != 'password_expired'}" class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <span th:if="${session.SPRING_SECURITY_LAST_EXCEPTION != null and session.SPRING_SECURITY_LAST_EXCEPTION.message == '账户已被禁用'}">
                    账户已被禁用，请联系管理员
                </span>
                <span th:unless="${session.SPRING_SECURITY_LAST_EXCEPTION != null and session.SPRING_SECURITY_LAST_EXCEPTION.message == '账户已被禁用'}">
                    用户名或密码不正确
                </span>
            </div>

            <!-- 退出成功 -->
            <div th:if="${param.logout}" class="alert alert-success">
                <i class="bi bi-check-circle-fill"></i> 您已成功退出
            </div>

            <form th:action="@{/login}" method="post" id="loginForm" onsubmit="return encryptPassword()">
                <!-- CSRF令牌会由Thymeleaf自动添加 -->
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                        <input type="text" class="form-control" id="username" name="username" required autofocus autocomplete="off">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-key"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required autocomplete="off">
                        <input type="hidden" id="encryptedPassword" name="encryptedPassword">
                    </div>
                </div>

                <input type="hidden" name="successUrl" value="/goto-home" />

                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </button>
            </form>

        </div>

    </div>

    <script>
        // 密码加密函数
        function encryptPassword() {
            // 获取明文密码
            var plainPassword = document.getElementById('password').value;
            if (!plainPassword) return true; // 如果没有密码，不做处理

            try {
                // 使用MD5加密密码
                var md5Password = CryptoJS.MD5(plainPassword).toString();

                // 将加密后的密码填入隐藏字段
                document.getElementById('encryptedPassword').value = md5Password;

                // 清空原始密码字段，不提交原始密码
                document.getElementById('password').value = '';
                document.getElementById('password').disabled = true;

                console.log("密码已MD5加密");
                return true;
            } catch (error) {
                console.error("密码加密失败:", error);
                alert("登录过程中出现错误，请刷新页面后重试");
                return false;
            }
        }

        // 如果登录表单已提交且URL中没有错误参数，则尝试跳转
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const loginForm = document.querySelector('form');

            // 登录表单提交时设置标记
            if (loginForm) {
                loginForm.addEventListener('submit', function() {
                    localStorage.setItem('login_submitted', 'true');
                });
            }

            // 检查是否刚提交过登录表单且不含错误参数
            if (localStorage.getItem('login_submitted') === 'true' && !urlParams.has('error')) {
                console.log('检测到登录提交，尝试跳转到首页');
                localStorage.removeItem('login_submitted');

                // 等待一段时间后，如果页面没有变化，强制跳转到首页
                setTimeout(function() {
                    if (window.location.pathname === '/login') {
                        console.log('强制跳转到首页');
                        window.location.href = '/index';
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>