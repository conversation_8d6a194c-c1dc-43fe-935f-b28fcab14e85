<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${isNew ? '新增网络配置' : '编辑网络配置'} + ' - 安全隔离单向输出模块'"></title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('network')"></div>

    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h2><i class="bi bi-ethernet me-2"></i><span th:text="${isNew ? '新增网络配置' : '编辑网络配置'}"></span></h2>
        </div>

        <div class="alert alert-danger" th:if="${error}" th:text="${error}"></div>

        <form th:action="@{/network/save}" method="post" th:object="${networkInterface}">
            <input type="hidden" name="isNew" th:value="${isNew}">
            <input type="hidden" th:field="*{id}" th:if="${!isNew}">

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">基本信息</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="interfaceName" class="form-label">网卡名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="interfaceName" th:field="*{interfaceName}" required autocomplete="off"
                                       placeholder="例如: eth0, ens33" readonly>
                                <div class="form-text">请输入操作系统识别的网卡名称</div>
                            </div>

                            <div class="mb-3">
                                <label for="enabled" class="form-label">状态</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enabled" th:field="*{enabled}">
                                    <label class="form-check-label" for="enabled">启用</label>
                                </div>
                                <div class="form-text">设置网络接口状态为启用或禁用</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">IP配置</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="ipAddress" class="form-label">IP地址 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ipAddress" th:field="*{ipAddress}" required autocomplete="off"
                                       pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                       title="请输入有效的IPv4地址"
                                       placeholder="例如: ***********">
                                <div class="form-text">请输入有效的IPv4地址</div>
                            </div>

                            <div class="mb-3">
                                <label for="subnetMask" class="form-label">子网掩码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="subnetMask" th:field="*{subnetMask}" required autocomplete="off"
                                       pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                       title="请输入有效的子网掩码"
                                       placeholder="例如: *************">
                                <div class="form-text">请输入有效的子网掩码</div>
                            </div>

                            <div class="mb-3">
                                <label for="gateway" class="form-label">网关</label>
                                <input type="text" class="form-control" id="gateway" th:field="*{gateway}" autocomplete="off"
                                       pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                       title="请输入有效的IPv4地址"
                                       placeholder="例如: *************">
                                <div class="form-text">请输入网关IP地址（可选）</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12 d-flex justify-content-between">
                    <a href="/network" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> 保存
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
</body>
</html>