<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>服务配置</title>
    <meta name="_csrf" th:content="${_csrf?.token}"/>
    <meta name="_csrf_header" th:content="${_csrf?.headerName}"/>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link th:href="@{/css/main.css}" rel="stylesheet">

    <!-- Custom JavaScript -->
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/service-functions.js}"></script>
    <script th:src="@{/js/receiver-functions.js}"></script>
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('services')"></div>

    <div class="container">
        <div id="message-container" class="mt-3">
            <!-- 操作消息将在这里动态显示 -->
        </div>

        <!-- 页面标题 -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="bi bi-hdd-network me-2"></i>服务配置</h2>
                    <p>配置和管理数据传输服务</p>
                </div>
                <div th:if="${networkType == 0}">
                    <a th:href="@{/services/new}" id="newService" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> 新增服务
                    </a>
                </div>
            </div>
        </div>

        <!-- 发送端服务内容 -->
        <div th:if="${networkType == 0}">
            <h4>发送端服务列表</h4>
            <!-- 发送端服务列表 -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>配置状态</th>
                            <th>运行状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="service : ${services}">
                            <td th:text="${service.id}"></td>
                            <td th:text="${service.name}"></td>

                            <!-- 配置状态列 -->
                            <td>
                                <span th:if="${service.status == null || service.status == '' || service.status == 'created'}"
                                      class="badge bg-secondary">未配置</span>
                                <span th:if="${service.status == 'configured'}"
                                      class="badge bg-info">已配置</span>
                                <span th:if="${service.status == 'deployed' || service.status == 'stopped' || service.status == 'running'}"
                                      class="badge bg-success">已部署</span>
                                <span th:if="${service.status == 'error'}"
                                      class="badge bg-danger">配置错误</span>
                            </td>

                            <!-- 运行状态列 -->
                            <td>
                                <span th:if="${service.status == 'running'}"
                                      class="badge bg-success">运行中</span>
                                <span th:if="${service.status == 'stopped'}"
                                      class="badge bg-warning">已停止</span>
                                <span th:if="${service.status == 'deployed'}"
                                      class="badge bg-info">就绪</span>
                                <span th:if="${service.status == null || service.status == '' || service.status == 'created' || service.status == 'configured' || service.status == 'error'}"
                                      class="badge bg-secondary">未运行</span>
                            </td>

                            <!-- 操作列 -->
                            <td>
                                <div class="btn-group" role="group">
                                    <!-- 查看详情按钮 - 始终显示 -->
                                    <button class="btn btn-sm btn-secondary view-service-details"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>

                                    <!-- 发送配置文件按钮 - 只要不在运行中状态即可发送 -->
                                    <button th:if="${service.status != 'running'}"
                                            class="btn btn-sm btn-outline-primary send-config"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-upload"></i> 发送配置
                                    </button>

                                    <!-- 部署按钮 - 只要不在运行中状态即可点击部署/重新部署 -->
                                    <button th:if="${service.status != 'running'}"
                                            class="btn btn-sm btn-primary deploy-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-arrow-up-circle"></i> 部署
                                    </button>

                                    <!-- 启动按钮 - 当服务已部署或已停止时显示 -->
                                    <button th:if="${service.status == 'deployed' || service.status == 'stopped'}"
                                            class="btn btn-sm btn-success start-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-play-fill"></i> 启动
                                    </button>

                                    <!-- 停止按钮 - 当服务正在运行时显示 -->
                                    <button th:if="${service.status == 'running'}"
                                            class="btn btn-sm btn-warning stop-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-stop-fill"></i> 停止
                                    </button>

                                    <!-- 编辑按钮 - 当服务未运行时显示 -->
                                    <a th:if="${service.status != 'running'}"
                                       th:href="@{/services/edit/{id}(id=${service.id})}"
                                       class="btn btn-sm btn-info">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </a>

                                    <!-- 删除按钮 - 当服务未运行时显示 -->
                                    <button th:if="${service.status != 'running'}"
                                            class="btn btn-sm btn-danger delete-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr th:if="${#lists.isEmpty(services)}">
                            <td colspan="5" class="text-center">暂无发送端服务配置</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 接收端服务内容 -->
        <div th:if="${networkType == 1}">
            <div class="d-flex justify-content-between mb-3">
                <div>
                    <h4>接收端服务列表</h4>
                </div>
                <div class="btn-group">
                    <button id="syncFromSrcappsBtn" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-repeat"></i> 同步配置
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>同步状态</th>
                            <th>配置状态</th>
                            <th>运行状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="service : ${services}" th:data-service-id="${service.id}">
                            <td th:text="${service.id}"></td>
                            <td th:text="${service.name}"></td>

                            <!-- 同步状态列 -->
                            <td>
                                <span th:if="${service.syncStatus == null || service.syncStatus == ''}"
                                      class="badge bg-secondary">未同步</span>
                                <span th:if="${service.syncStatus == 'SENDER_ADDED_NO_CONFIG'}"
                                      class="badge bg-warning">配置未发送</span>
                                <span th:if="${service.syncStatus == 'SENDER_ADDED_NO_SERVICE'}"
                                      class="badge bg-warning">未创建服务</span>
                                <span th:if="${service.syncStatus == 'SENDER_ADDED_NO_CONFIG_SERVICE'}"
                                      class="badge bg-warning">未配置服务</span>
                                <span th:if="${service.syncStatus == 'SENDER_MODIFIED_NO_CONFIG'}"
                                      class="badge bg-warning">更新配置未发送</span>
                                <span th:if="${service.syncStatus == 'SENDER_MODIFIED_NO_UPDATE'}"
                                      class="badge bg-warning">配置未更新</span>
                                <span th:if="${service.syncStatus == 'SENDER_DELETED_RECEIVER_EXIST'}"
                                      class="badge bg-danger">发送端已删除</span>
                                <span th:if="${service.syncStatus == 'CONFIG_COMPLETED'}"
                                      class="badge bg-success">配置完成</span>
                            </td>

                            <!-- 配置状态列 -->
                            <td>
                                <span th:if="${service.status == null || service.status == '' || service.status == 'created'}"
                                      class="badge bg-secondary">未配置</span>
                                <span th:if="${service.status == 'configured'}"
                                      class="badge bg-info">已配置</span>
                                <span th:if="${service.status == 'deployed' || service.status == 'stopped' || service.status == 'running'}"
                                      class="badge bg-success">已部署</span>
                                <span th:if="${service.status == 'error'}"
                                      class="badge bg-danger">配置错误</span>
                            </td>

                            <!-- 运行状态列 -->
                            <td>
                                <span th:if="${service.status == 'running'}"
                                      class="badge bg-success">运行中</span>
                                <span th:if="${service.status == 'stopped'}"
                                      class="badge bg-warning">已停止</span>
                                <span th:if="${service.status == 'deployed'}"
                                      class="badge bg-info">就绪</span>
                                <span th:if="${service.status == null || service.status == '' || service.status == 'created' || service.status == 'configured' || service.status == 'error'}"
                                      class="badge bg-secondary">未运行</span>
                            </td>

                            <!-- 操作列 -->
                            <td>
                                <div class="btn-group" role="group">
                                    <!-- 查看详情按钮 - 始终显示 -->
                                    <button class="btn btn-sm btn-secondary view-service-details"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>

                                    <!-- 创建按钮 - 仅当同步状态为未创建服务时显示 -->
                                    <button th:if="${service.syncStatus == 'SENDER_ADDED_NO_SERVICE'}"
                                            class="btn btn-sm btn-primary"
                                            th:onclick="'createReceiverService(' + ${service.id} + ')'"
                                            title="创建接收端服务">
                                        <i class="bi bi-plus-circle"></i> 创建
                                    </button>

                                    <!-- 部署按钮 - 只在同步状态不为未创建服务且不为配置未发送，且不在运行中状态时显示 -->
                                    <button th:if="${service.status != 'running' && service.syncStatus != 'SENDER_ADDED_NO_CONFIG' && service.syncStatus != 'SENDER_ADDED_NO_SERVICE' && (service.syncStatus == 'CONFIG_COMPLETED' || service.syncStatus == null)}"
                                            class="btn btn-sm btn-primary deploy-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-arrow-up-circle"></i> 部署
                                    </button>

                                    <!-- 启动按钮 - 当服务已部署或已停止，且同步状态不为未创建服务且不为配置未发送时显示 -->
                                    <button th:if="${(service.status == 'deployed' || service.status == 'stopped') && service.syncStatus != 'SENDER_ADDED_NO_CONFIG' && service.syncStatus != 'SENDER_ADDED_NO_SERVICE'}"
                                            class="btn btn-sm btn-success start-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-play-fill"></i> 启动
                                    </button>

                                    <!-- 停止按钮 - 当服务正在运行，且同步状态不为未创建服务且不为配置未发送时显示 -->
                                    <button th:if="${service.status == 'running' && service.syncStatus != 'SENDER_ADDED_NO_CONFIG' && service.syncStatus != 'SENDER_ADDED_NO_SERVICE'}"
                                            class="btn btn-sm btn-warning stop-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-stop-fill"></i> 停止
                                    </button>

                                    <!-- 编辑按钮 - 当同步状态不为未创建服务且不为配置未发送时显示 -->
                                    <a th:if="${service.status != 'running' && service.syncStatus != 'SENDER_ADDED_NO_CONFIG' && service.syncStatus != 'SENDER_ADDED_NO_SERVICE'}"
                                       th:href="@{/services/edit/{id}(id=${service.id}, network=1)}"
                                       class="btn btn-sm btn-info">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </a>

                                    <!-- 删除按钮 - 当服务未运行，且同步状态为发送端已删除时显示 -->
                                    <button th:if="${service.status != 'running' && service.syncStatus == 'SENDER_DELETED_RECEIVER_EXIST'}"
                                            class="btn btn-sm btn-danger delete-service"
                                            th:data-id="${service.id}"
                                            th:data-name="${service.name}">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr th:if="${#lists.isEmpty(services)}">
                            <td colspan="6" class="text-center">暂无接收端服务配置</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 服务详情模态框 -->
    <div class="modal fade" id="serviceDetailsModal" tabindex="-1" aria-labelledby="serviceDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="serviceDetailsModalLabel">服务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3" id="service-details-loading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p>正在加载服务详情...</p>
                    </div>

                    <div id="service-details-content" style="display: none;">
                        <!-- 基本信息 -->
                        <div class="card mb-3">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-info-circle"></i> 基本信息</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>服务ID:</strong> <span id="service-id"></span></p>
                                        <p><strong>服务名称:</strong> <span id="service-name"></span></p>
                                        <p><strong>网段类型:</strong> <span id="service-network"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>审计选项:</strong> <span id="service-audit"></span></p>
                                        <p><strong>流量限制 (MB/s):</strong> <span id="service-trafficLimit"></span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 状态信息 -->
                        <!--<div class="card mb-3">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="bi bi-activity"></i> 运行状态</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>当前状态:</strong> <span id="service-status" class="badge"></span></p>
                                        <p><strong>运行时间:</strong> <span id="service-uptime"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>连接数:</strong> <span id="service-connections"></span></p>
                                        <p><strong>流量信息:</strong> <span id="service-traffic"></span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 配置信息 -->
                        <div class="card mb-3" id="sender-specific-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="bi bi-gear"></i> 发送端配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>监听地址:</strong> <span id="service-proxy-ip"></span></p>
                                        <p><strong>监听端口:</strong> <span id="service-proxy-port"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>关键字检测:</strong>
                                            <span id="vehicle-check-option" style="display: none;">机动车牌校验;</span>
                                            <span id="idcard-check-option" style="display: none;">身份证校验;</span>
                                        </p>
                                        <p><strong>协议过滤:</strong>
                                             <span id="crc16-filter-option" style="display: none;">CRC16格式过滤;</span>
                                             <span id="asn-filter-option" style="display: none;">ASN格式过滤;</span>
                                        </p>
                                    </div>
                                </div>
                                <!-- 告警后处理相关信息 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <p><strong>后处理:</strong> <span id="service-unpass-deal"></span></p>
                                    </div>
                                </div>

                                <!-- 指定网段信息 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <p><strong>指定网段设置:</strong> <span id="service-iprange"></span></p>
                                    </div>
                                </div>

                                <div class="row mt-2" id="alarm-details-row" style="display: none;">
                                    <div class="col-12">
                                        <p><strong>其他告警处理选项可在此配置</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-3" id="receiver-specific-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="bi bi-gear"></i> 接收端配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>服务器地址:</strong> <span id="service-server-ip"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>服务器端口:</strong> <span id="service-server-port"></span></p>
                                    </div>
                                </div>
                            </div>
                        </div>


                    <div id="service-details-error" class="alert alert-danger" style="display: none;">
                        加载服务详情失败，请稍后重试。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // CSRF保护
        const token = $("meta[name='_csrf']").attr("content");
        const header = $("meta[name='_csrf_header']").attr("content");

        // 查看服务详情事件
        $('.view-service-details').on('click', function() {
            const serviceId = $(this).data('id');
            const serviceName = $(this).data('name');
            // 获取网络类型 - 根据所在Tab确定
            const networkType = $(this).closest('.tab-pane').attr('id') === 'sender-content' ? 0 : 1;

            console.log("查看服务详情 - ID: " + serviceId + ", 名称: " + serviceName + ", 网络类型: " + networkType);

            // 显示加载状态
            $('#service-details-loading').show();
            $('#service-details-content').hide();
            $('#service-details-error').hide();

            // 更新模态框标题
            $('#serviceDetailsModalLabel').text('服务详情 - ' + serviceName);

            // 打开模态框
            const serviceDetailsModal = new bootstrap.Modal(document.getElementById('serviceDetailsModal'));
            serviceDetailsModal.show();

            // 请求服务详情
            $.ajax({
                url: `/services/${serviceId}/details?network=${networkType}`,
                type: 'GET',
                success: function(data) {
                    // 隐藏加载状态
                    $('#service-details-loading').hide();

                    // 填充基本信息
                    $('#service-id').text(data.id);
                    $('#service-name').text(data.name);
                    $('#service-network').text(data.network === 1 ? '接收端' : '发送端');
                    $('#service-audit').text(data.audit == '1' ? '开启' : '关闭');
                    $('#service-trafficLimit').text(data.trafficLimit || '未设置');
                    //$('#service-created').text(data.createdAt || '未知');


                    //const statusText = getStatusText(data.status);
                    //const statusClass = getStatusClass(data.status);
                    //$('#service-status').text(statusText).removeClass().addClass('badge ' + statusClass);


                    // 根据网段类型显示不同的配置信息
                    if (data.network === 1) {
                        // 接收端
                        $('#sender-specific-info').hide();
                        $('#receiver-specific-info').show();

                        $('#service-server-ip').text(data.receiverInfo?.serverIp || '未配置');
                        $('#service-server-port').text(data.receiverInfo?.serverPort || '未配置');
                    } else {
                        // 发送端
                        $('#sender-specific-info').show();
                        $('#receiver-specific-info').hide();

                        $('#service-proxy-ip').text(data.senderInfo?.proxyIp || '未配置');
                        $('#service-proxy-port').text(data.senderInfo?.proxyPort || '未配置');


                        // 处理检测选项显示
                        const vehicleCheck = data.senderInfo?.vehiclePlateCheck === 1;
                        const idcardCheck = data.senderInfo?.idCardCheck === 1;

                        if (vehicleCheck || idcardCheck) {
                            $('#detection-options-row').show();

                            // 设置机动车牌校验状态
                            if (vehicleCheck) {
                                $('#vehicle-check-option').show();
                            } else {
                                $('#vehicle-check-option').hide();
                            }

                            // 设置身份证校验状态
                            if (idcardCheck) {
                                $('#idcard-check-option').show();
                            } else {
                                $('#idcard-check-option').hide();
                            }
                        } else {
                            $('#detection-options-row').hide();
                        }


                        // 处理协议过滤选项显示
                        const crc16Check = data.senderInfo?.crc16FormatCheck === 1;
                        const asnCheck = data.senderInfo?.asnFormatCheck === 1;

                        if (crc16Check || asnCheck) {
                            $('#protocol-filter-options-row').show();

                            // 设置CRC16格式过滤状态
                            if (crc16Check) {
                                $('#crc16-filter-option').show();
                            } else {
                                $('#crc16-filter-option').hide();
                            }

                            // 设置ASN格式过滤状态
                            if (asnCheck) {
                                $('#asn-filter-option').show();
                            } else {
                                $('#asn-filter-option').hide();
                            }
                        } else {
                            $('#protocol-filter-options-row').hide();
                        }

                        // 处理告警后处理字段
                        let unpassDealText = '报警并拦截';
                        if (data.senderInfo?.unpassDeal === '0') unpassDealText = '仅报警';
                        $('#service-unpass-deal').text(unpassDealText);

                        // 显示指定网段信息
                        const iprangeDisplay = data.senderInfo?.iprangeDisplay || '全网段';
                        $('#service-iprange').text(iprangeDisplay);

                        // 如果启用了告警后处理（非"0"），显示相关详细配置
                        if (data.senderInfo?.unpassDeal && data.senderInfo.unpassDeal !== '0') {
                            $('#alarm-details-row').show();
                        } else {
                            $('#alarm-details-row').hide();
                        }
                    }


                    // 显示内容
                    $('#service-details-content').show();
                },
                error: function(xhr, status, error) {
                    console.error('获取服务详情出错:', error);
                    $('#service-details-loading').hide();
                    $('#service-details-error').show();
                }
            });
        });

        $(document).ready(function() {
            console.log("初始化服务页面JS...");

            // 获取CSRF令牌
            var token = $("meta[name='_csrf']").attr("content");
            var header = $("meta[name='_csrf_header']").attr("content");

            // 设置AJAX的默认请求头
            $.ajaxSetup({
                beforeSend: function(xhr) {
                        xhr.setRequestHeader(header, token);
                    }
            });

            // 部署服务事件
            $('.deploy-service').click(function() {
                var serviceId = $(this).data('id');
                var serviceName = $(this).data('name');
                // 获取网络类型 - 根据所在Tab确定
                var networkType = $(this).closest('.tab-pane').attr('id') === 'sender-content' ? 0 : 1;

                console.log("准备部署服务，ID: " + serviceId + ", 名称: " + serviceName + ", 网络类型: " + networkType);

                if(confirm('确定要部署服务 "' + serviceName + '" 吗?')) {
                    showLoading('正在部署服务，请稍候...');

                    $.ajax({
                        url: '/services/deploy/' + serviceId + '?network=' + networkType,
                        type: 'POST',
                        success: function(response) {
                            hideLoading();
                            if(response.success) {
                                showMessage('success', '部署成功', '服务 "' + serviceName + '" 已成功部署');
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', '部署失败', response.message || '服务部署失败');
                            }
                        },
                        error: function(xhr) {
                            hideLoading();
                            showMessage('error', '部署失败', '请求服务器失败，请稍后再试');
                            console.error('部署服务失败', xhr);
                        }
                    });
                }
            });

            // 启动服务事件
            $('.start-service').click(function() {
                var serviceId = $(this).data('id');
                var serviceName = $(this).data('name');
                // 获取网络类型 - 根据所在Tab确定
                var networkType = $(this).closest('.tab-pane').attr('id') === 'sender-content' ? 0 : 1;

                console.log("开始启动服务，ID: " + serviceId + ", 名称: " + serviceName + ", 网络类型: " + networkType);

                if(confirm('确定要启动服务 "' + serviceName + '" 吗?')) {
                    showLoading('正在启动服务，请稍候...');

                    $.ajax({
                        url: '/services/start/' + serviceId + '?network=' + networkType,
                        type: 'POST',
                        success: function(response) {
                            console.log("启动服务响应:", JSON.stringify(response));
                            hideLoading();
                            if(response.success) {
                                showMessage('success', '启动成功', '服务 "' + serviceName + '" 已成功启动');
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', '启动失败', response.message || '服务启动失败');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('启动服务请求失败:', xhr.responseText, status, error);
                            hideLoading();
                            showMessage('error', '启动失败', '请求服务器失败，请稍后再试');
                        }
                    });
                }
            });

            // 停止服务事件
            $('.stop-service').click(function() {
                var serviceId = $(this).data('id');
                var serviceName = $(this).data('name');
                // 获取网络类型 - 根据所在Tab确定
                var networkType = $(this).closest('.tab-pane').attr('id') === 'sender-content' ? 0 : 1;

                console.log("准备停止服务，ID: " + serviceId + ", 名称: " + serviceName + ", 网络类型: " + networkType);

                if(confirm('确定要停止服务 "' + serviceName + '" 吗?')) {
                    showLoading('正在停止服务，请稍候...');

                    $.ajax({
                        url: '/services/stop/' + serviceId + '?network=' + networkType,
                        type: 'POST',
                        success: function(response) {
                            hideLoading();
                            if(response.success) {
                                showMessage('success', '停止成功', '服务 "' + serviceName + '" 已成功停止');
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', '停止失败', response.message || '服务停止失败');
                            }
                        },
                        error: function(xhr) {
                            hideLoading();
                            showMessage('error', '停止失败', '请求服务器失败，请稍后再试');
                            console.error('停止服务失败', xhr);
                        }
                    });
                }
            });

            // 删除服务事件
            $('.delete-service').click(function() {
                var serviceId = $(this).data('id');
                var serviceName = $(this).data('name');
                // 获取网络类型 - 根据所在Tab确定
                var networkType = $(this).closest('.tab-pane').attr('id') === 'sender-content' ? 0 : 1;

                console.log("准备删除服务，ID: " + serviceId + ", 名称: " + serviceName + ", 网络类型: " + networkType);

                if(confirm('确定要删除服务 "' + serviceName + '" 吗? 此操作不可恢复!')) {
                    showLoading('正在删除服务，请稍候...');

                    $.ajax({
                        url: '/services/delete/' + serviceId + '?network=' + networkType,
                        type: 'POST',
                        success: function(response) {
                            hideLoading();
                            if(response.success) {
                                showMessage('success', '删除成功', '服务 "' + serviceName + '" 已成功删除');
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', '删除失败', response.message || '服务删除失败');
                            }
                        },
                        error: function(xhr) {
                            hideLoading();
                            showMessage('error', '删除失败', '请求服务器失败，请稍后再试');
                            console.error('删除服务失败', xhr);
                        }
                    });
                }
            });

            // 事件处理：从srcapps同步配置
            $('#syncFromSrcappsBtn').on('click', function() {
                console.log("点击从srcapps同步配置按钮");

                // 显示加载中的消息
                showLoading('正在从源端同步配置...');

                // 发送请求到后端
                $.ajax({
                    url: '/services/sync-from-srcapps',
                    type: 'POST',
                    beforeSend: function(xhr) {
                        // 添加CSRF令牌
                        xhr.setRequestHeader(header, token);
                    },
                    success: function(response) {
                        hideLoading();
                        console.log("同步配置响应:", response);
                        if (response.success) {
                            showMessage('success', '同步成功', '已成功同步源端配置');
                            // 刷新页面以显示最新状态
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {
                            showMessage('error', '同步失败', response.message || '同步配置失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        hideLoading();
                        showMessage('error', '同步失败', '请求服务器失败，请稍后再试');
                        console.error('从srcapps同步配置失败', xhr);
                    }
                });
            });

            // 事件处理：发送配置到srcapps
            $('.send-config').on('click', function() {
                const serviceId = $(this).data('id');
                const serviceName = $(this).data('name');

                if(confirm('确定要发送服务 "' + serviceName + '" 的配置文件吗?')) {
                    // 显示加载中的消息
                    showLoading('正在发送配置文件，请稍候...');

                    // 发送请求到后端
                    $.ajax({
                        url: `/services/send-config/${serviceId}`,
                        type: 'POST',
                        beforeSend: function(xhr) {
                            // 添加CSRF令牌
                            xhr.setRequestHeader(header, token);
                        },
                        success: function(response) {
                            hideLoading();
                            if (response.success) {
                                showMessage('success', '发送成功', '服务 "' + serviceName + '" 的配置文件已成功发送');
                                // 可以选择刷新页面
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1500);
                            } else {
                                showMessage('error', '发送失败', response.message || '配置文件发送失败');
                            }
                        },
                        error: function(xhr, status, error) {
                            hideLoading();
                            showMessage('error', '发送失败', '请求服务器失败，请稍后再试');
                            console.error('发送配置文件失败', xhr);
                        }
                    });
                }
            });

            // 显示消息函数
            function showMessage(type, title, message) {
                var alertClass = 'alert-info';
                if (type === 'success') alertClass = 'alert-success';
                if (type === 'error') alertClass = 'alert-danger';

                var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                                '<strong>' + title + ':</strong> ' + message +
                                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                '</div>';

                $('#message-container').html(alertHtml);

                // 5秒后自动关闭
                setTimeout(function() {
                    $('.alert').alert('close');
                }, 5000);
            }

            // 显示加载中
            function showLoading(message) {
                if ($('#loading-overlay').length === 0) {
                    $('body').append(
                        '<div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">' +
                        '<div class="card p-3"><div class="spinner-border text-primary me-2" role="status"></div><span id="loading-message">' + message + '</span></div>' +
                        '</div>'
                    );
                } else {
                    $('#loading-message').text(message);
                    $('#loading-overlay').show();
                }
            }

            // 隐藏加载中
            function hideLoading() {
                $('#loading-overlay').hide();
            }

            // 显示状态调试信息
            $('.deploy-service').each(function() {
                var serviceId = $(this).data('id');
                var serviceName = $(this).data('name');
                var serviceRow = $(this).closest('tr');
                var statusCell = serviceRow.find('td:nth-child(4)'); // 假设状态是第4列
                var statusText = statusCell.text().trim();

                console.log("服务ID: " + serviceId + ", 名称: " + serviceName + ", 状态: '" + statusText + "'");
            });
        });
    </script>
</body>
</html>