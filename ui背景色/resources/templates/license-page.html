<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/default}">
      <head>
        <meta charset="UTF-8">
        <title>授权管理 - 安全隔离单向输出模块</title>
        <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
        <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
        <link th:href="@{/css/main.css}" rel="stylesheet">
        <script th:src="@{/js/jquery.min.js}"></script>
        <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    </head>
<body>
     <!-- 使用通用导航栏片段 -->
     <div th:replace="fragments/navigation :: navbar('license')"></div>
    <div layout:fragment="content">
        <div class="container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h2><i class="bi bi-key me-2"></i>授权管理</h2>
            </div>

            <!-- 显示消息 -->
            <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i> <span th:text="${success}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i> <span th:text="${error}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- 授权状态提示 -->
            <div th:if="${licenseStatus}" class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i> <span th:text="${licenseStatus}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- 授权信息卡片 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card license-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">授权信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">硬件序列码</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" th:value="${hardwareSerial}" readonly>
                                    <a href="/license/export-serial" class="btn btn-outline-primary">
                                        <i class="bi bi-download me-1"></i>导出
                                    </a>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">授权文件</label>
                                <form action="/license/import" method="post" enctype="multipart/form-data" class="input-group">
                                    <input type="file" class="form-control" id="licenseFile" name="file" required>
                                    <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-upload me-1"></i>导入
                                    </button>
                                </form>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card license-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">授权许可信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>模块名称</th>
                                            <th>状态</th>
                                            <th>有效期</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="module : ${baseModules}">
                                            <td th:text="${module.name}"></td>
                                            <td>
                                                <span class="module-status" th:classappend="${module.status}">
                                                    <i class="bi" th:classappend="${module.status == 'active' ? 'bi-check-circle' : 'bi-x-circle'}"></i>
                                                    <span th:text="${module.status == 'active' ? '已激活' : '未激活'}"></span>
                                                </span>
                                            </td>
                                            <td>
                                                <span th:if="${module.startDate != null and module.endDate != null}">
                                                    <span th:text="${#temporals.format(module.startDate, 'yyyy-MM-dd')}"></span>
                                                    -
                                                    <span th:text="${#temporals.format(module.endDate, 'yyyy-MM-dd')}"></span>
                                                </span>
                                                <span th:unless="${module.startDate != null and module.endDate != null}">
                                                    -
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block layout:fragment="scripts">
        <script>
            $(document).ready(function() {
                // 自动隐藏提示消息
                setTimeout(function() {
                    $('.alert').fadeOut('slow', function() {
                        $(this).remove();
                    });
                }, 3000);
            });
        </script>
    </th:block>
</body>
</html>