/* 主样式文件 */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
    background-attachment: fixed;
    position: relative;
    overflow-x: hidden;
}

/* 动态背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 200, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(100, 180, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(150, 220, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundFloat 20s ease-in-out infinite;
}

/* 背景浮动动画 */
@keyframes backgroundFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-20px) rotate(1deg);
        opacity: 0.8;
    }
}

/* 添加微妙的网格背景 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -1;
    opacity: 0.3;
}

.footer {
    margin-top: auto;
}

/* 状态指示器 */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.running {
    background-color: #28a745;
}

.status-indicator.stopped {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

/* 表格样式 */
.table th {
    background-color: #f8f9fa;
}

/* 卡片悬停效果 */
.card {
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #42a5f5, #64b5f6, #90caf9);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.98);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0,0,0,.03);
    border-top: 1px solid rgba(0,0,0,.125);
}

/* 向导样式 */
.nav-pills .nav-link.active {
    background-color: #007bff;
}

.tab-pane {
    padding: 20px 0;
}

/* 菜单高亮样式 */
.navbar-nav .nav-link.active {
    font-weight: bold;
    position: relative;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background-color: white;
}

/* 菜单项悬停效果 */
.navbar-nav .nav-link {
    transition: all 0.3s;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-2px);
}

/* 菜单图标样式 */
.navbar-nav .nav-link i {
    margin-right: 5px;
}

/* 用户下拉菜单 */
.user-dropdown {
    cursor: pointer;
}

/* 下拉菜单修复样式 */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 1050 !important;
    display: none !important;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff !important;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175) !important;
}

.dropdown-menu.show {
    display: block !important;
}

.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.25rem 1rem !important;
    clear: both !important;
    font-weight: 400 !important;
    color: #212529 !important;
    text-align: inherit !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
    cursor: pointer !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #1e2125 !important;
    background-color: #e9ecef !important;
}

.dropdown-toggle::after {
    display: inline-block !important;
    margin-left: 0.255em !important;
    vertical-align: 0.255em !important;
    content: "" !important;
    border-top: 0.3em solid !important;
    border-right: 0.3em solid transparent !important;
    border-bottom: 0 !important;
    border-left: 0.3em solid transparent !important;
}

/* 确保导航栏在最上层 */
.navbar {
    z-index: 1030 !important;
}

/* 修复侧边栏菜单点击问题 */
.sidebar .nav-link {
    cursor: pointer !important;
    pointer-events: auto !important;
    display: block !important;
    padding: 0.5rem 1rem !important;
    color: #495057 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

.sidebar .nav-link:hover {
    color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-radius: 0.25rem !important;
}

.sidebar .nav-link.active {
    color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-radius: 0.25rem !important;
    font-weight: 500 !important;
}

/* 修复Bootstrap下拉菜单兼容性 */
.dropdown-toggle:focus {
    outline: none !important;
    box-shadow: none !important;
}

/* 确保下拉菜单项可点击 */
.dropdown-item {
    cursor: pointer !important;
    user-select: none !important;
}

.dropdown-item:active {
    background-color: #007bff !important;
    color: white !important;
}

/* 修复导航栏响应式问题 */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(25, 118, 210, 0.95) !important;
        border-radius: 0.375rem !important;
        margin-top: 0.5rem !important;
        padding: 1rem !important;
    }

    .dropdown-menu {
        position: static !important;
        float: none !important;
        width: auto !important;
        margin-top: 0 !important;
        background-color: rgba(255, 255, 255, 0.95) !important;
        border: none !important;
        border-radius: 0.25rem !important;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }
}

/* 防止菜单被其他元素遮挡 */
.navbar-nav .dropdown-menu {
    z-index: 1055 !important;
}

/* 确保所有链接都可以点击 */
a {
    cursor: pointer !important;
}

a[href="#"] {
    cursor: default !important;
}

/* 修复可能的指针事件问题 */
* {
    pointer-events: auto !important;
}

.disabled,
.disabled * {
    pointer-events: none !important;
}

/* 页面内容容器 */
.content-container {
    padding-top: 20px;
    padding-bottom: 40px;
}

/* 卡片悬停效果 */
.hover-card {
    transition: all 0.3s;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* 查询按钮样式 */
.btn-query {
    min-width: 80px;
}

/* 表格响应式样式 */
.table-responsive {
    overflow-x: auto;
}

/* 筛选表单样式 */
.filter-form {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* 证书卡片样式 */
.certificate-card {
    margin-bottom: 20px;
    transition: all 0.3s;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.cert-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* 有效期样式 */
.validity-ok {
    color: green;
}

.validity-warning {
    color: orange;
}

.validity-expired {
    color: red;
}

/* 导航栏样式优化 */
.bg-custom-blue {
    background: linear-gradient(135deg, #1976d2 0%, #2196f3 50%, #42a5f5 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
}

/* 侧边栏样式优化 */
.sidebar {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(15px);
    border-right: 1px solid rgba(66, 165, 245, 0.2);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
}

/* 导航链接样式 */
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 0 2px;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    color: white !important;
    transform: translateY(-1px);
}

.navbar-dark .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.25);
    border-radius: 6px;
    color: white !important;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

/* 按钮样式优化 */
.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #2196f3, #42a5f5);
    border: none;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1976d2, #2196f3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
    border: none;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #388e3c, #4caf50);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f57c00, #ff9800);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #f44336, #ef5350);
    border: none;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d32f2f, #f44336);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

/* 表格样式优化 */
.table {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table th {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: none;
    font-weight: 600;
    color: #1976d2;
    padding: 15px;
}

.table td {
    border: none;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(66, 165, 245, 0.1);
}

.table tbody tr:hover {
    background: rgba(66, 165, 245, 0.05);
    transform: scale(1.01);
    transition: all 0.3s ease;
}

/* 进度条样式优化 */
.progress {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: linear-gradient(135deg, #42a5f5, #64b5f6);
    border-radius: 10px;
    transition: all 0.3s ease;
}

/* 徽章样式优化 */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 输入框样式优化 */
.form-control {
    border-radius: 8px;
    border: 2px solid rgba(66, 165, 245, 0.2);
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #42a5f5;
    box-shadow: 0 0 0 0.2rem rgba(66, 165, 245, 0.25);
    background: rgba(255, 255, 255, 1);
}

/* 模态框样式优化 */
.modal-content {
    border-radius: 16px;
    border: none;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid rgba(66, 165, 245, 0.2);
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 16px 16px 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(66, 165, 245, 0.2);
    border-radius: 0 0 16px 16px;
}

/* 页脚样式优化 */
footer {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(66, 165, 245, 0.2);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
    margin-top: 3rem;
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(66, 165, 245, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #42a5f5, #64b5f6);
    border-radius: 4px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .hero-section {
        padding: 1.5rem;
        text-align: center;
    }

    .hero-section .display-5 {
        font-size: 1.8rem;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .status-card:hover {
        transform: translateY(-3px) scale(1.01);
    }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .hero-section {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(2) { animation-delay: 0.1s; }
.card:nth-child(3) { animation-delay: 0.2s; }
.card:nth-child(4) { animation-delay: 0.3s; }

/* 文本渐变效果 */
.text-gradient {
    background: linear-gradient(135deg, #42a5f5, #1976d2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 悬浮提示优化 */
.tooltip-inner {
    background: rgba(25, 118, 210, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
}

/* 警告和成功消息样式 */
.alert {
    border-radius: 12px;
    border: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

.alert-danger {
    background: rgba(244, 67, 54, 0.1);
    color: #c62828;
    border-left: 4px solid #f44336;
}

.alert-warning {
    background: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
    border-left: 4px solid #ff9800;
}

.alert-info {
    background: rgba(33, 150, 243, 0.1);
    color: #1565c0;
    border-left: 4px solid #2196f3;
}

/* 通用页面标题样式 */
.page-header {
    background: linear-gradient(135deg, rgba(66, 165, 245, 0.1), rgba(144, 202, 249, 0.1));
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(66, 165, 245, 0.2);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #42a5f5, #64b5f6, #90caf9);
    opacity: 0.8;
}

.page-header h1,
.page-header h2,
.page-header h3 {
    color: #1976d2;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.page-header p {
    color: #666;
    margin-bottom: 0;
    font-size: 1.1rem;
}

.page-header .lead {
    color: #555;
    font-size: 1.2rem;
}

/* 容器美化 */
.container, .container-fluid {
    position: relative;
}

/* 内容区域美化 */
.content-section {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(66, 165, 245, 0.1);
    box-shadow: 0 4px 20px rgba(66, 165, 245, 0.08);
}

/* 表单美化 */
.form-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(66, 165, 245, 0.2);
    box-shadow: 0 8px 32px rgba(66, 165, 245, 0.1);
}

.form-section h3,
.form-section h4 {
    color: #1976d2;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(66, 165, 245, 0.2);
}

/* 列表组美化 */
.list-group-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(66, 165, 245, 0.1);
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background: rgba(66, 165, 245, 0.05);
    transform: translateX(5px);
}

/* 导航标签美化 */
.nav-tabs .nav-link {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(66, 165, 245, 0.2);
    color: #1976d2;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(66, 165, 245, 0.1);
    border-color: rgba(66, 165, 245, 0.3);
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #42a5f5;
    color: #1976d2;
    font-weight: 600;
}

/* 面包屑导航美化 */
.breadcrumb {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(66, 165, 245, 0.1);
}

.breadcrumb-item a {
    color: #1976d2;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #42a5f5;
}

/* 分页美化 */
.pagination .page-link {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(66, 165, 245, 0.2);
    color: #1976d2;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: rgba(66, 165, 245, 0.1);
    border-color: #42a5f5;
    color: #1976d2;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #42a5f5, #64b5f6);
    border-color: #42a5f5;
    color: white;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

/* 表单样式 */
.form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

/* 警告框样式 */
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #e9ecef;
    border-radius: .25rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    padding-left: .5rem;
    color: #6c757d;
    content: "/";
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* 辅助类 */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }

.text-white { color: #fff !important; }
.text-muted { color: #6c757d !important; }
.text-end { text-align: right !important; }

.bg-primary { background-color: #007bff !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-light { background-color: #f8f9fa !important; }

.d-flex { display: flex !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-end { justify-content: flex-end !important; }
.align-items-center { align-items: center !important; }