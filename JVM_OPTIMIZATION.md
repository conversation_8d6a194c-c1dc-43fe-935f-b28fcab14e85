# JVM优化配置建议

## 针对100MB补丁包上传的JVM优化

### 1. 内存配置
为了处理100MB的补丁包，建议增加JVM内存配置：

```bash
# 在启动脚本中添加以下JVM参数
-Xms512m          # 初始堆内存512MB
-Xmx2048m         # 最大堆内存2GB
-XX:MetaspaceSize=256m  # 元空间初始大小
-XX:MaxMetaspaceSize=512m  # 元空间最大大小
```

### 2. 垃圾回收优化
```bash
# 使用G1垃圾回收器，适合大内存应用
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
```

### 3. 文件上传优化
```bash
# 增加临时文件目录的内存映射
-Djava.io.tmpdir=/var/unimas/patch/temp
-XX:+UseLargePages  # 使用大页内存
```

### 4. 启动脚本示例
```bash
#!/bin/bash
java -Xms512m -Xmx2048m \
     -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
     -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:G1HeapRegionSize=16m \
     -Djava.io.tmpdir=/var/unimas/patch/temp \
     -jar your-application.jar
```

### 5. 系统级优化
```bash
# 增加文件描述符限制
ulimit -n 65536

# 增加进程内存限制
ulimit -v unlimited

# 确保临时目录有足够空间（至少500MB）
df -h /var/unimas/patch/temp
```

### 6. 监控建议
- 监控堆内存使用情况
- 监控GC频率和耗时
- 监控文件上传进度
- 监控磁盘空间使用

### 7. 性能优化总结
经过优化后的性能提升：
- 解密缓冲区：从16KB增加到64KB
- 解压缓冲区：从2KB增加到64KB
- 复制缓冲区：从1KB增加到8KB
- 解密块大小：从16KB增加到64KB
- 前端超时：从60秒增加到300秒
