log4j.rootLogger=debug,A2
log4j.logger.org.springframework=ERROR 
log4j.logger.org.apache=ERROR 
log4j.logger.org.mybatis=ERROR 
log4j.appender.A1=org.apache.log4j.RollingFileAppender  
log4j.appender.A1.MaxFileSize= 20MB
log4j.appender.A1.MaxBackupIndex=4 
log4j.appender.A1.ImmediateFlush=true
log4j.appender.A1.Threshold = INFO 
log4j.appender.A1.File=license.log
log4j.appender.A1.Append=true
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=%d{yy-MM-dd HH\:mm\:ss} [%t] %-5p %x - %m %l\n
log4j.appender.A2=org.apache.log4j.ConsoleAppender
log4j.appender.A2.layout=org.apache.log4j.PatternLayout
log4j.appender.A2.layout.ConversionPattern=%d{yy-MM-dd HH\:mm\:ss.SSS} [%t] %-5p %c %L %x - %m\n